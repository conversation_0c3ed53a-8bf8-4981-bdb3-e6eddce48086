%%{
    let module = system.modules['/drivers/gpmc/gpmc'];
    let gpmcUdmaInstanceCount = 0;
    for(let i=0; i < module.$instances.length; i++) {
        let instance = module.$instances[i];
        if(instance.dmaEnable == true) {
            gpmcUdmaInstanceCount++;
        }
    }
%%}
/*
 * GPMC
 */
#include <drivers/gpmc.h>

/* GPMC Instance Macros */
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
#define `instance.$name.toUpperCase()` (`i`U)
% }
#define CONFIG_GPMC_NUM_INSTANCES (`module.$instances.length`U)
#define CONFIG_GPMC_NUM_DMA_INSTANCES (`gpmcUdmaInstanceCount`U)