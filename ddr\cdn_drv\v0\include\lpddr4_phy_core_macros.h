/* parasoft-begin-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" parasoft-begin-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */

/**********************************************************************
 * Copyright (C) 2012-2022 Cadence Design Systems, Inc.
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 
 * 1. Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THIS FILE IS AUTOMATICALLY GENERATED, DO NOT EDIT
 *
 **********************************************************************/

#ifndef REG_LPDDR4_PHY_CORE_MACROS_H_
#define REG_LPDDR4_PHY_CORE_MACROS_H_

#define LPDDR4__DENALI_PHY_1280_READ_MASK                            0x00000003U
#define LPDDR4__DENALI_PHY_1280_WRITE_MASK                           0x00000003U
#define LPDDR4__DENALI_PHY_1280__PHY_FREQ_SEL_MASK                   0x00000003U
#define LPDDR4__DENALI_PHY_1280__PHY_FREQ_SEL_SHIFT                           0U
#define LPDDR4__DENALI_PHY_1280__PHY_FREQ_SEL_WIDTH                           2U
#define LPDDR4__PHY_FREQ_SEL__REG DENALI_PHY_1280
#define LPDDR4__PHY_FREQ_SEL__FLD LPDDR4__DENALI_PHY_1280__PHY_FREQ_SEL

#define LPDDR4__DENALI_PHY_1281_READ_MASK                            0x1F030101U
#define LPDDR4__DENALI_PHY_1281_WRITE_MASK                           0x1F030101U
#define LPDDR4__DENALI_PHY_1281__PHY_FREQ_SEL_FROM_REGIF_MASK        0x00000001U
#define LPDDR4__DENALI_PHY_1281__PHY_FREQ_SEL_FROM_REGIF_SHIFT                0U
#define LPDDR4__DENALI_PHY_1281__PHY_FREQ_SEL_FROM_REGIF_WIDTH                1U
#define LPDDR4__DENALI_PHY_1281__PHY_FREQ_SEL_FROM_REGIF_WOCLR                0U
#define LPDDR4__DENALI_PHY_1281__PHY_FREQ_SEL_FROM_REGIF_WOSET                0U
#define LPDDR4__PHY_FREQ_SEL_FROM_REGIF__REG DENALI_PHY_1281
#define LPDDR4__PHY_FREQ_SEL_FROM_REGIF__FLD LPDDR4__DENALI_PHY_1281__PHY_FREQ_SEL_FROM_REGIF

#define LPDDR4__DENALI_PHY_1281__PHY_FREQ_SEL_MULTICAST_EN_MASK      0x00000100U
#define LPDDR4__DENALI_PHY_1281__PHY_FREQ_SEL_MULTICAST_EN_SHIFT              8U
#define LPDDR4__DENALI_PHY_1281__PHY_FREQ_SEL_MULTICAST_EN_WIDTH              1U
#define LPDDR4__DENALI_PHY_1281__PHY_FREQ_SEL_MULTICAST_EN_WOCLR              0U
#define LPDDR4__DENALI_PHY_1281__PHY_FREQ_SEL_MULTICAST_EN_WOSET              0U
#define LPDDR4__PHY_FREQ_SEL_MULTICAST_EN__REG DENALI_PHY_1281
#define LPDDR4__PHY_FREQ_SEL_MULTICAST_EN__FLD LPDDR4__DENALI_PHY_1281__PHY_FREQ_SEL_MULTICAST_EN

#define LPDDR4__DENALI_PHY_1281__PHY_FREQ_SEL_INDEX_MASK             0x00030000U
#define LPDDR4__DENALI_PHY_1281__PHY_FREQ_SEL_INDEX_SHIFT                    16U
#define LPDDR4__DENALI_PHY_1281__PHY_FREQ_SEL_INDEX_WIDTH                     2U
#define LPDDR4__PHY_FREQ_SEL_INDEX__REG DENALI_PHY_1281
#define LPDDR4__PHY_FREQ_SEL_INDEX__FLD LPDDR4__DENALI_PHY_1281__PHY_FREQ_SEL_INDEX

#define LPDDR4__DENALI_PHY_1281__PHY_SW_GRP0_SHIFT_0_MASK            0x1F000000U
#define LPDDR4__DENALI_PHY_1281__PHY_SW_GRP0_SHIFT_0_SHIFT                   24U
#define LPDDR4__DENALI_PHY_1281__PHY_SW_GRP0_SHIFT_0_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP0_SHIFT_0__REG DENALI_PHY_1281
#define LPDDR4__PHY_SW_GRP0_SHIFT_0__FLD LPDDR4__DENALI_PHY_1281__PHY_SW_GRP0_SHIFT_0

#define LPDDR4__DENALI_PHY_1282_READ_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_1282_WRITE_MASK                           0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_1282__PHY_SW_GRP1_SHIFT_0_MASK            0x0000001FU
#define LPDDR4__DENALI_PHY_1282__PHY_SW_GRP1_SHIFT_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1282__PHY_SW_GRP1_SHIFT_0_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP1_SHIFT_0__REG DENALI_PHY_1282
#define LPDDR4__PHY_SW_GRP1_SHIFT_0__FLD LPDDR4__DENALI_PHY_1282__PHY_SW_GRP1_SHIFT_0

#define LPDDR4__DENALI_PHY_1282__PHY_SW_GRP2_SHIFT_0_MASK            0x00001F00U
#define LPDDR4__DENALI_PHY_1282__PHY_SW_GRP2_SHIFT_0_SHIFT                    8U
#define LPDDR4__DENALI_PHY_1282__PHY_SW_GRP2_SHIFT_0_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP2_SHIFT_0__REG DENALI_PHY_1282
#define LPDDR4__PHY_SW_GRP2_SHIFT_0__FLD LPDDR4__DENALI_PHY_1282__PHY_SW_GRP2_SHIFT_0

#define LPDDR4__DENALI_PHY_1282__PHY_SW_GRP3_SHIFT_0_MASK            0x001F0000U
#define LPDDR4__DENALI_PHY_1282__PHY_SW_GRP3_SHIFT_0_SHIFT                   16U
#define LPDDR4__DENALI_PHY_1282__PHY_SW_GRP3_SHIFT_0_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP3_SHIFT_0__REG DENALI_PHY_1282
#define LPDDR4__PHY_SW_GRP3_SHIFT_0__FLD LPDDR4__DENALI_PHY_1282__PHY_SW_GRP3_SHIFT_0

#define LPDDR4__DENALI_PHY_1282__PHY_SW_GRP0_SHIFT_1_MASK            0x1F000000U
#define LPDDR4__DENALI_PHY_1282__PHY_SW_GRP0_SHIFT_1_SHIFT                   24U
#define LPDDR4__DENALI_PHY_1282__PHY_SW_GRP0_SHIFT_1_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP0_SHIFT_1__REG DENALI_PHY_1282
#define LPDDR4__PHY_SW_GRP0_SHIFT_1__FLD LPDDR4__DENALI_PHY_1282__PHY_SW_GRP0_SHIFT_1

#define LPDDR4__DENALI_PHY_1283_READ_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_1283_WRITE_MASK                           0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_1283__PHY_SW_GRP1_SHIFT_1_MASK            0x0000001FU
#define LPDDR4__DENALI_PHY_1283__PHY_SW_GRP1_SHIFT_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1283__PHY_SW_GRP1_SHIFT_1_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP1_SHIFT_1__REG DENALI_PHY_1283
#define LPDDR4__PHY_SW_GRP1_SHIFT_1__FLD LPDDR4__DENALI_PHY_1283__PHY_SW_GRP1_SHIFT_1

#define LPDDR4__DENALI_PHY_1283__PHY_SW_GRP2_SHIFT_1_MASK            0x00001F00U
#define LPDDR4__DENALI_PHY_1283__PHY_SW_GRP2_SHIFT_1_SHIFT                    8U
#define LPDDR4__DENALI_PHY_1283__PHY_SW_GRP2_SHIFT_1_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP2_SHIFT_1__REG DENALI_PHY_1283
#define LPDDR4__PHY_SW_GRP2_SHIFT_1__FLD LPDDR4__DENALI_PHY_1283__PHY_SW_GRP2_SHIFT_1

#define LPDDR4__DENALI_PHY_1283__PHY_SW_GRP3_SHIFT_1_MASK            0x001F0000U
#define LPDDR4__DENALI_PHY_1283__PHY_SW_GRP3_SHIFT_1_SHIFT                   16U
#define LPDDR4__DENALI_PHY_1283__PHY_SW_GRP3_SHIFT_1_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP3_SHIFT_1__REG DENALI_PHY_1283
#define LPDDR4__PHY_SW_GRP3_SHIFT_1__FLD LPDDR4__DENALI_PHY_1283__PHY_SW_GRP3_SHIFT_1

#define LPDDR4__DENALI_PHY_1283__PHY_SW_GRP0_SHIFT_2_MASK            0x1F000000U
#define LPDDR4__DENALI_PHY_1283__PHY_SW_GRP0_SHIFT_2_SHIFT                   24U
#define LPDDR4__DENALI_PHY_1283__PHY_SW_GRP0_SHIFT_2_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP0_SHIFT_2__REG DENALI_PHY_1283
#define LPDDR4__PHY_SW_GRP0_SHIFT_2__FLD LPDDR4__DENALI_PHY_1283__PHY_SW_GRP0_SHIFT_2

#define LPDDR4__DENALI_PHY_1284_READ_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_1284_WRITE_MASK                           0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_1284__PHY_SW_GRP1_SHIFT_2_MASK            0x0000001FU
#define LPDDR4__DENALI_PHY_1284__PHY_SW_GRP1_SHIFT_2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1284__PHY_SW_GRP1_SHIFT_2_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP1_SHIFT_2__REG DENALI_PHY_1284
#define LPDDR4__PHY_SW_GRP1_SHIFT_2__FLD LPDDR4__DENALI_PHY_1284__PHY_SW_GRP1_SHIFT_2

#define LPDDR4__DENALI_PHY_1284__PHY_SW_GRP2_SHIFT_2_MASK            0x00001F00U
#define LPDDR4__DENALI_PHY_1284__PHY_SW_GRP2_SHIFT_2_SHIFT                    8U
#define LPDDR4__DENALI_PHY_1284__PHY_SW_GRP2_SHIFT_2_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP2_SHIFT_2__REG DENALI_PHY_1284
#define LPDDR4__PHY_SW_GRP2_SHIFT_2__FLD LPDDR4__DENALI_PHY_1284__PHY_SW_GRP2_SHIFT_2

#define LPDDR4__DENALI_PHY_1284__PHY_SW_GRP3_SHIFT_2_MASK            0x001F0000U
#define LPDDR4__DENALI_PHY_1284__PHY_SW_GRP3_SHIFT_2_SHIFT                   16U
#define LPDDR4__DENALI_PHY_1284__PHY_SW_GRP3_SHIFT_2_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP3_SHIFT_2__REG DENALI_PHY_1284
#define LPDDR4__PHY_SW_GRP3_SHIFT_2__FLD LPDDR4__DENALI_PHY_1284__PHY_SW_GRP3_SHIFT_2

#define LPDDR4__DENALI_PHY_1284__PHY_SW_GRP0_SHIFT_3_MASK            0x1F000000U
#define LPDDR4__DENALI_PHY_1284__PHY_SW_GRP0_SHIFT_3_SHIFT                   24U
#define LPDDR4__DENALI_PHY_1284__PHY_SW_GRP0_SHIFT_3_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP0_SHIFT_3__REG DENALI_PHY_1284
#define LPDDR4__PHY_SW_GRP0_SHIFT_3__FLD LPDDR4__DENALI_PHY_1284__PHY_SW_GRP0_SHIFT_3

#define LPDDR4__DENALI_PHY_1285_READ_MASK                            0x001F1F1FU
#define LPDDR4__DENALI_PHY_1285_WRITE_MASK                           0x001F1F1FU
#define LPDDR4__DENALI_PHY_1285__PHY_SW_GRP1_SHIFT_3_MASK            0x0000001FU
#define LPDDR4__DENALI_PHY_1285__PHY_SW_GRP1_SHIFT_3_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1285__PHY_SW_GRP1_SHIFT_3_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP1_SHIFT_3__REG DENALI_PHY_1285
#define LPDDR4__PHY_SW_GRP1_SHIFT_3__FLD LPDDR4__DENALI_PHY_1285__PHY_SW_GRP1_SHIFT_3

#define LPDDR4__DENALI_PHY_1285__PHY_SW_GRP2_SHIFT_3_MASK            0x00001F00U
#define LPDDR4__DENALI_PHY_1285__PHY_SW_GRP2_SHIFT_3_SHIFT                    8U
#define LPDDR4__DENALI_PHY_1285__PHY_SW_GRP2_SHIFT_3_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP2_SHIFT_3__REG DENALI_PHY_1285
#define LPDDR4__PHY_SW_GRP2_SHIFT_3__FLD LPDDR4__DENALI_PHY_1285__PHY_SW_GRP2_SHIFT_3

#define LPDDR4__DENALI_PHY_1285__PHY_SW_GRP3_SHIFT_3_MASK            0x001F0000U
#define LPDDR4__DENALI_PHY_1285__PHY_SW_GRP3_SHIFT_3_SHIFT                   16U
#define LPDDR4__DENALI_PHY_1285__PHY_SW_GRP3_SHIFT_3_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP3_SHIFT_3__REG DENALI_PHY_1285
#define LPDDR4__PHY_SW_GRP3_SHIFT_3__FLD LPDDR4__DENALI_PHY_1285__PHY_SW_GRP3_SHIFT_3

#define LPDDR4__DENALI_PHY_1286_READ_MASK                            0x011F07FFU
#define LPDDR4__DENALI_PHY_1286_WRITE_MASK                           0x011F07FFU
#define LPDDR4__DENALI_PHY_1286__PHY_GRP_BYPASS_SLAVE_DELAY_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_1286__PHY_GRP_BYPASS_SLAVE_DELAY_SHIFT             0U
#define LPDDR4__DENALI_PHY_1286__PHY_GRP_BYPASS_SLAVE_DELAY_WIDTH            11U
#define LPDDR4__PHY_GRP_BYPASS_SLAVE_DELAY__REG DENALI_PHY_1286
#define LPDDR4__PHY_GRP_BYPASS_SLAVE_DELAY__FLD LPDDR4__DENALI_PHY_1286__PHY_GRP_BYPASS_SLAVE_DELAY

#define LPDDR4__DENALI_PHY_1286__PHY_SW_GRP_BYPASS_SHIFT_MASK        0x001F0000U
#define LPDDR4__DENALI_PHY_1286__PHY_SW_GRP_BYPASS_SHIFT_SHIFT               16U
#define LPDDR4__DENALI_PHY_1286__PHY_SW_GRP_BYPASS_SHIFT_WIDTH                5U
#define LPDDR4__PHY_SW_GRP_BYPASS_SHIFT__REG DENALI_PHY_1286
#define LPDDR4__PHY_SW_GRP_BYPASS_SHIFT__FLD LPDDR4__DENALI_PHY_1286__PHY_SW_GRP_BYPASS_SHIFT

#define LPDDR4__DENALI_PHY_1286__PHY_GRP_BYPASS_OVERRIDE_MASK        0x01000000U
#define LPDDR4__DENALI_PHY_1286__PHY_GRP_BYPASS_OVERRIDE_SHIFT               24U
#define LPDDR4__DENALI_PHY_1286__PHY_GRP_BYPASS_OVERRIDE_WIDTH                1U
#define LPDDR4__DENALI_PHY_1286__PHY_GRP_BYPASS_OVERRIDE_WOCLR                0U
#define LPDDR4__DENALI_PHY_1286__PHY_GRP_BYPASS_OVERRIDE_WOSET                0U
#define LPDDR4__PHY_GRP_BYPASS_OVERRIDE__REG DENALI_PHY_1286
#define LPDDR4__PHY_GRP_BYPASS_OVERRIDE__FLD LPDDR4__DENALI_PHY_1286__PHY_GRP_BYPASS_OVERRIDE

#define LPDDR4__DENALI_PHY_1287_READ_MASK                            0x07FF0100U
#define LPDDR4__DENALI_PHY_1287_WRITE_MASK                           0x07FF0100U
#define LPDDR4__DENALI_PHY_1287__SC_PHY_MANUAL_UPDATE_MASK           0x00000001U
#define LPDDR4__DENALI_PHY_1287__SC_PHY_MANUAL_UPDATE_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1287__SC_PHY_MANUAL_UPDATE_WIDTH                   1U
#define LPDDR4__DENALI_PHY_1287__SC_PHY_MANUAL_UPDATE_WOCLR                   0U
#define LPDDR4__DENALI_PHY_1287__SC_PHY_MANUAL_UPDATE_WOSET                   0U
#define LPDDR4__SC_PHY_MANUAL_UPDATE__REG DENALI_PHY_1287
#define LPDDR4__SC_PHY_MANUAL_UPDATE__FLD LPDDR4__DENALI_PHY_1287__SC_PHY_MANUAL_UPDATE

#define LPDDR4__DENALI_PHY_1287__PHY_MANUAL_UPDATE_PHYUPD_ENABLE_MASK 0x00000100U
#define LPDDR4__DENALI_PHY_1287__PHY_MANUAL_UPDATE_PHYUPD_ENABLE_SHIFT        8U
#define LPDDR4__DENALI_PHY_1287__PHY_MANUAL_UPDATE_PHYUPD_ENABLE_WIDTH        1U
#define LPDDR4__DENALI_PHY_1287__PHY_MANUAL_UPDATE_PHYUPD_ENABLE_WOCLR        0U
#define LPDDR4__DENALI_PHY_1287__PHY_MANUAL_UPDATE_PHYUPD_ENABLE_WOSET        0U
#define LPDDR4__PHY_MANUAL_UPDATE_PHYUPD_ENABLE__REG DENALI_PHY_1287
#define LPDDR4__PHY_MANUAL_UPDATE_PHYUPD_ENABLE__FLD LPDDR4__DENALI_PHY_1287__PHY_MANUAL_UPDATE_PHYUPD_ENABLE

#define LPDDR4__DENALI_PHY_1287__PHY_CSLVL_START_MASK                0x07FF0000U
#define LPDDR4__DENALI_PHY_1287__PHY_CSLVL_START_SHIFT                       16U
#define LPDDR4__DENALI_PHY_1287__PHY_CSLVL_START_WIDTH                       11U
#define LPDDR4__PHY_CSLVL_START__REG DENALI_PHY_1287
#define LPDDR4__PHY_CSLVL_START__FLD LPDDR4__DENALI_PHY_1287__PHY_CSLVL_START

#define LPDDR4__DENALI_PHY_1288_READ_MASK                            0x000107FFU
#define LPDDR4__DENALI_PHY_1288_WRITE_MASK                           0x000107FFU
#define LPDDR4__DENALI_PHY_1288__PHY_CSLVL_COARSE_DLY_MASK           0x000007FFU
#define LPDDR4__DENALI_PHY_1288__PHY_CSLVL_COARSE_DLY_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1288__PHY_CSLVL_COARSE_DLY_WIDTH                  11U
#define LPDDR4__PHY_CSLVL_COARSE_DLY__REG DENALI_PHY_1288
#define LPDDR4__PHY_CSLVL_COARSE_DLY__FLD LPDDR4__DENALI_PHY_1288__PHY_CSLVL_COARSE_DLY

#define LPDDR4__DENALI_PHY_1288__PHY_CSLVL_DEBUG_MODE_MASK           0x00010000U
#define LPDDR4__DENALI_PHY_1288__PHY_CSLVL_DEBUG_MODE_SHIFT                  16U
#define LPDDR4__DENALI_PHY_1288__PHY_CSLVL_DEBUG_MODE_WIDTH                   1U
#define LPDDR4__DENALI_PHY_1288__PHY_CSLVL_DEBUG_MODE_WOCLR                   0U
#define LPDDR4__DENALI_PHY_1288__PHY_CSLVL_DEBUG_MODE_WOSET                   0U
#define LPDDR4__PHY_CSLVL_DEBUG_MODE__REG DENALI_PHY_1288
#define LPDDR4__PHY_CSLVL_DEBUG_MODE__FLD LPDDR4__DENALI_PHY_1288__PHY_CSLVL_DEBUG_MODE

#define LPDDR4__DENALI_PHY_1288__SC_PHY_CSLVL_DEBUG_CONT_MASK        0x01000000U
#define LPDDR4__DENALI_PHY_1288__SC_PHY_CSLVL_DEBUG_CONT_SHIFT               24U
#define LPDDR4__DENALI_PHY_1288__SC_PHY_CSLVL_DEBUG_CONT_WIDTH                1U
#define LPDDR4__DENALI_PHY_1288__SC_PHY_CSLVL_DEBUG_CONT_WOCLR                0U
#define LPDDR4__DENALI_PHY_1288__SC_PHY_CSLVL_DEBUG_CONT_WOSET                0U
#define LPDDR4__SC_PHY_CSLVL_DEBUG_CONT__REG DENALI_PHY_1288
#define LPDDR4__SC_PHY_CSLVL_DEBUG_CONT__FLD LPDDR4__DENALI_PHY_1288__SC_PHY_CSLVL_DEBUG_CONT

#define LPDDR4__DENALI_PHY_1289__SC_PHY_CSLVL_ERROR_CLR_MASK         0x00000001U
#define LPDDR4__DENALI_PHY_1289__SC_PHY_CSLVL_ERROR_CLR_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1289__SC_PHY_CSLVL_ERROR_CLR_WIDTH                 1U
#define LPDDR4__DENALI_PHY_1289__SC_PHY_CSLVL_ERROR_CLR_WOCLR                 0U
#define LPDDR4__DENALI_PHY_1289__SC_PHY_CSLVL_ERROR_CLR_WOSET                 0U
#define LPDDR4__SC_PHY_CSLVL_ERROR_CLR__REG DENALI_PHY_1289
#define LPDDR4__SC_PHY_CSLVL_ERROR_CLR__FLD LPDDR4__DENALI_PHY_1289__SC_PHY_CSLVL_ERROR_CLR

#define LPDDR4__DENALI_PHY_1290_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1290_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1290__PHY_CSLVL_OBS0_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1290__PHY_CSLVL_OBS0_SHIFT                         0U
#define LPDDR4__DENALI_PHY_1290__PHY_CSLVL_OBS0_WIDTH                        32U
#define LPDDR4__PHY_CSLVL_OBS0__REG DENALI_PHY_1290
#define LPDDR4__PHY_CSLVL_OBS0__FLD LPDDR4__DENALI_PHY_1290__PHY_CSLVL_OBS0

#define LPDDR4__DENALI_PHY_1291_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1291_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1291__PHY_CSLVL_OBS1_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1291__PHY_CSLVL_OBS1_SHIFT                         0U
#define LPDDR4__DENALI_PHY_1291__PHY_CSLVL_OBS1_WIDTH                        32U
#define LPDDR4__PHY_CSLVL_OBS1__REG DENALI_PHY_1291
#define LPDDR4__PHY_CSLVL_OBS1__FLD LPDDR4__DENALI_PHY_1291__PHY_CSLVL_OBS1

#define LPDDR4__DENALI_PHY_1292_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1292_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1292__PHY_CSLVL_OBS2_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1292__PHY_CSLVL_OBS2_SHIFT                         0U
#define LPDDR4__DENALI_PHY_1292__PHY_CSLVL_OBS2_WIDTH                        32U
#define LPDDR4__PHY_CSLVL_OBS2__REG DENALI_PHY_1292
#define LPDDR4__PHY_CSLVL_OBS2__FLD LPDDR4__DENALI_PHY_1292__PHY_CSLVL_OBS2

#define LPDDR4__DENALI_PHY_1293_READ_MASK                            0x0101FF01U
#define LPDDR4__DENALI_PHY_1293_WRITE_MASK                           0x0101FF01U
#define LPDDR4__DENALI_PHY_1293__PHY_CSLVL_ENABLE_MASK               0x00000001U
#define LPDDR4__DENALI_PHY_1293__PHY_CSLVL_ENABLE_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1293__PHY_CSLVL_ENABLE_WIDTH                       1U
#define LPDDR4__DENALI_PHY_1293__PHY_CSLVL_ENABLE_WOCLR                       0U
#define LPDDR4__DENALI_PHY_1293__PHY_CSLVL_ENABLE_WOSET                       0U
#define LPDDR4__PHY_CSLVL_ENABLE__REG DENALI_PHY_1293
#define LPDDR4__PHY_CSLVL_ENABLE__FLD LPDDR4__DENALI_PHY_1293__PHY_CSLVL_ENABLE

#define LPDDR4__DENALI_PHY_1293__PHY_CSLVL_PERIODIC_START_OFFSET_MASK 0x0001FF00U
#define LPDDR4__DENALI_PHY_1293__PHY_CSLVL_PERIODIC_START_OFFSET_SHIFT        8U
#define LPDDR4__DENALI_PHY_1293__PHY_CSLVL_PERIODIC_START_OFFSET_WIDTH        9U
#define LPDDR4__PHY_CSLVL_PERIODIC_START_OFFSET__REG DENALI_PHY_1293
#define LPDDR4__PHY_CSLVL_PERIODIC_START_OFFSET__FLD LPDDR4__DENALI_PHY_1293__PHY_CSLVL_PERIODIC_START_OFFSET

#define LPDDR4__DENALI_PHY_1293__PHY_LP4_BOOT_DISABLE_MASK           0x01000000U
#define LPDDR4__DENALI_PHY_1293__PHY_LP4_BOOT_DISABLE_SHIFT                  24U
#define LPDDR4__DENALI_PHY_1293__PHY_LP4_BOOT_DISABLE_WIDTH                   1U
#define LPDDR4__DENALI_PHY_1293__PHY_LP4_BOOT_DISABLE_WOCLR                   0U
#define LPDDR4__DENALI_PHY_1293__PHY_LP4_BOOT_DISABLE_WOSET                   0U
#define LPDDR4__PHY_LP4_BOOT_DISABLE__REG DENALI_PHY_1293
#define LPDDR4__PHY_LP4_BOOT_DISABLE__FLD LPDDR4__DENALI_PHY_1293__PHY_LP4_BOOT_DISABLE

#define LPDDR4__DENALI_PHY_1294_READ_MASK                            0x0007FF03U
#define LPDDR4__DENALI_PHY_1294_WRITE_MASK                           0x0007FF03U
#define LPDDR4__DENALI_PHY_1294__PHY_CSLVL_CS_MAP_MASK               0x00000003U
#define LPDDR4__DENALI_PHY_1294__PHY_CSLVL_CS_MAP_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1294__PHY_CSLVL_CS_MAP_WIDTH                       2U
#define LPDDR4__PHY_CSLVL_CS_MAP__REG DENALI_PHY_1294
#define LPDDR4__PHY_CSLVL_CS_MAP__FLD LPDDR4__DENALI_PHY_1294__PHY_CSLVL_CS_MAP

#define LPDDR4__DENALI_PHY_1294__PHY_CSLVL_QTR_MASK                  0x0007FF00U
#define LPDDR4__DENALI_PHY_1294__PHY_CSLVL_QTR_SHIFT                          8U
#define LPDDR4__DENALI_PHY_1294__PHY_CSLVL_QTR_WIDTH                         11U
#define LPDDR4__PHY_CSLVL_QTR__REG DENALI_PHY_1294
#define LPDDR4__PHY_CSLVL_QTR__FLD LPDDR4__DENALI_PHY_1294__PHY_CSLVL_QTR

#define LPDDR4__DENALI_PHY_1295_READ_MASK                            0x070F07FFU
#define LPDDR4__DENALI_PHY_1295_WRITE_MASK                           0x070F07FFU
#define LPDDR4__DENALI_PHY_1295__PHY_CSLVL_COARSE_CHK_MASK           0x000007FFU
#define LPDDR4__DENALI_PHY_1295__PHY_CSLVL_COARSE_CHK_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1295__PHY_CSLVL_COARSE_CHK_WIDTH                  11U
#define LPDDR4__PHY_CSLVL_COARSE_CHK__REG DENALI_PHY_1295
#define LPDDR4__PHY_CSLVL_COARSE_CHK__FLD LPDDR4__DENALI_PHY_1295__PHY_CSLVL_COARSE_CHK

#define LPDDR4__DENALI_PHY_1295__PHY_CSLVL_COARSE_CAPTURE_CNT_MASK   0x000F0000U
#define LPDDR4__DENALI_PHY_1295__PHY_CSLVL_COARSE_CAPTURE_CNT_SHIFT          16U
#define LPDDR4__DENALI_PHY_1295__PHY_CSLVL_COARSE_CAPTURE_CNT_WIDTH           4U
#define LPDDR4__PHY_CSLVL_COARSE_CAPTURE_CNT__REG DENALI_PHY_1295
#define LPDDR4__PHY_CSLVL_COARSE_CAPTURE_CNT__FLD LPDDR4__DENALI_PHY_1295__PHY_CSLVL_COARSE_CAPTURE_CNT

#define LPDDR4__DENALI_PHY_1295__PHY_ADRCTL_SLAVE_LOOP_CNT_UPDATE_MASK 0x07000000U
#define LPDDR4__DENALI_PHY_1295__PHY_ADRCTL_SLAVE_LOOP_CNT_UPDATE_SHIFT      24U
#define LPDDR4__DENALI_PHY_1295__PHY_ADRCTL_SLAVE_LOOP_CNT_UPDATE_WIDTH       3U
#define LPDDR4__PHY_ADRCTL_SLAVE_LOOP_CNT_UPDATE__REG DENALI_PHY_1295
#define LPDDR4__PHY_ADRCTL_SLAVE_LOOP_CNT_UPDATE__FLD LPDDR4__DENALI_PHY_1295__PHY_ADRCTL_SLAVE_LOOP_CNT_UPDATE

#define LPDDR4__DENALI_PHY_1296_READ_MASK                            0x01010300U
#define LPDDR4__DENALI_PHY_1296_WRITE_MASK                           0x01010300U
#define LPDDR4__DENALI_PHY_1296__PHY_ADRCTL_SNAP_OBS_REGS_MASK       0x00000001U
#define LPDDR4__DENALI_PHY_1296__PHY_ADRCTL_SNAP_OBS_REGS_SHIFT               0U
#define LPDDR4__DENALI_PHY_1296__PHY_ADRCTL_SNAP_OBS_REGS_WIDTH               1U
#define LPDDR4__DENALI_PHY_1296__PHY_ADRCTL_SNAP_OBS_REGS_WOCLR               0U
#define LPDDR4__DENALI_PHY_1296__PHY_ADRCTL_SNAP_OBS_REGS_WOSET               0U
#define LPDDR4__PHY_ADRCTL_SNAP_OBS_REGS__REG DENALI_PHY_1296
#define LPDDR4__PHY_ADRCTL_SNAP_OBS_REGS__FLD LPDDR4__DENALI_PHY_1296__PHY_ADRCTL_SNAP_OBS_REGS

#define LPDDR4__DENALI_PHY_1296__PHY_DFI_PHYUPD_TYPE_MASK            0x00000300U
#define LPDDR4__DENALI_PHY_1296__PHY_DFI_PHYUPD_TYPE_SHIFT                    8U
#define LPDDR4__DENALI_PHY_1296__PHY_DFI_PHYUPD_TYPE_WIDTH                    2U
#define LPDDR4__PHY_DFI_PHYUPD_TYPE__REG DENALI_PHY_1296
#define LPDDR4__PHY_DFI_PHYUPD_TYPE__FLD LPDDR4__DENALI_PHY_1296__PHY_DFI_PHYUPD_TYPE

#define LPDDR4__DENALI_PHY_1296__PHY_ADRCTL_LPDDR_MASK               0x00010000U
#define LPDDR4__DENALI_PHY_1296__PHY_ADRCTL_LPDDR_SHIFT                      16U
#define LPDDR4__DENALI_PHY_1296__PHY_ADRCTL_LPDDR_WIDTH                       1U
#define LPDDR4__DENALI_PHY_1296__PHY_ADRCTL_LPDDR_WOCLR                       0U
#define LPDDR4__DENALI_PHY_1296__PHY_ADRCTL_LPDDR_WOSET                       0U
#define LPDDR4__PHY_ADRCTL_LPDDR__REG DENALI_PHY_1296
#define LPDDR4__PHY_ADRCTL_LPDDR__FLD LPDDR4__DENALI_PHY_1296__PHY_ADRCTL_LPDDR

#define LPDDR4__DENALI_PHY_1296__PHY_LP4_ACTIVE_MASK                 0x01000000U
#define LPDDR4__DENALI_PHY_1296__PHY_LP4_ACTIVE_SHIFT                        24U
#define LPDDR4__DENALI_PHY_1296__PHY_LP4_ACTIVE_WIDTH                         1U
#define LPDDR4__DENALI_PHY_1296__PHY_LP4_ACTIVE_WOCLR                         0U
#define LPDDR4__DENALI_PHY_1296__PHY_LP4_ACTIVE_WOSET                         0U
#define LPDDR4__PHY_LP4_ACTIVE__REG DENALI_PHY_1296
#define LPDDR4__PHY_LP4_ACTIVE__FLD LPDDR4__DENALI_PHY_1296__PHY_LP4_ACTIVE

#define LPDDR4__DENALI_PHY_1297_READ_MASK                            0x0F010001U
#define LPDDR4__DENALI_PHY_1297_WRITE_MASK                           0x0F010001U
#define LPDDR4__DENALI_PHY_1297__PHY_LPDDR3_CS_MASK                  0x00000001U
#define LPDDR4__DENALI_PHY_1297__PHY_LPDDR3_CS_SHIFT                          0U
#define LPDDR4__DENALI_PHY_1297__PHY_LPDDR3_CS_WIDTH                          1U
#define LPDDR4__DENALI_PHY_1297__PHY_LPDDR3_CS_WOCLR                          0U
#define LPDDR4__DENALI_PHY_1297__PHY_LPDDR3_CS_WOSET                          0U
#define LPDDR4__PHY_LPDDR3_CS__REG DENALI_PHY_1297
#define LPDDR4__PHY_LPDDR3_CS__FLD LPDDR4__DENALI_PHY_1297__PHY_LPDDR3_CS

#define LPDDR4__DENALI_PHY_1297__SC_PHY_UPDATE_CLK_CAL_VALUES_MASK   0x00000100U
#define LPDDR4__DENALI_PHY_1297__SC_PHY_UPDATE_CLK_CAL_VALUES_SHIFT           8U
#define LPDDR4__DENALI_PHY_1297__SC_PHY_UPDATE_CLK_CAL_VALUES_WIDTH           1U
#define LPDDR4__DENALI_PHY_1297__SC_PHY_UPDATE_CLK_CAL_VALUES_WOCLR           0U
#define LPDDR4__DENALI_PHY_1297__SC_PHY_UPDATE_CLK_CAL_VALUES_WOSET           0U
#define LPDDR4__SC_PHY_UPDATE_CLK_CAL_VALUES__REG DENALI_PHY_1297
#define LPDDR4__SC_PHY_UPDATE_CLK_CAL_VALUES__FLD LPDDR4__DENALI_PHY_1297__SC_PHY_UPDATE_CLK_CAL_VALUES

#define LPDDR4__DENALI_PHY_1297__PHY_CONTINUOUS_CLK_CAL_UPDATE_MASK  0x00010000U
#define LPDDR4__DENALI_PHY_1297__PHY_CONTINUOUS_CLK_CAL_UPDATE_SHIFT         16U
#define LPDDR4__DENALI_PHY_1297__PHY_CONTINUOUS_CLK_CAL_UPDATE_WIDTH          1U
#define LPDDR4__DENALI_PHY_1297__PHY_CONTINUOUS_CLK_CAL_UPDATE_WOCLR          0U
#define LPDDR4__DENALI_PHY_1297__PHY_CONTINUOUS_CLK_CAL_UPDATE_WOSET          0U
#define LPDDR4__PHY_CONTINUOUS_CLK_CAL_UPDATE__REG DENALI_PHY_1297
#define LPDDR4__PHY_CONTINUOUS_CLK_CAL_UPDATE__FLD LPDDR4__DENALI_PHY_1297__PHY_CONTINUOUS_CLK_CAL_UPDATE

#define LPDDR4__DENALI_PHY_1297__PHY_SW_TXIO_CTRL_0_MASK             0x0F000000U
#define LPDDR4__DENALI_PHY_1297__PHY_SW_TXIO_CTRL_0_SHIFT                    24U
#define LPDDR4__DENALI_PHY_1297__PHY_SW_TXIO_CTRL_0_WIDTH                     4U
#define LPDDR4__PHY_SW_TXIO_CTRL_0__REG DENALI_PHY_1297
#define LPDDR4__PHY_SW_TXIO_CTRL_0__FLD LPDDR4__DENALI_PHY_1297__PHY_SW_TXIO_CTRL_0

#define LPDDR4__DENALI_PHY_1298_READ_MASK                            0x010F0F0FU
#define LPDDR4__DENALI_PHY_1298_WRITE_MASK                           0x010F0F0FU
#define LPDDR4__DENALI_PHY_1298__PHY_SW_TXIO_CTRL_1_MASK             0x0000000FU
#define LPDDR4__DENALI_PHY_1298__PHY_SW_TXIO_CTRL_1_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1298__PHY_SW_TXIO_CTRL_1_WIDTH                     4U
#define LPDDR4__PHY_SW_TXIO_CTRL_1__REG DENALI_PHY_1298
#define LPDDR4__PHY_SW_TXIO_CTRL_1__FLD LPDDR4__DENALI_PHY_1298__PHY_SW_TXIO_CTRL_1

#define LPDDR4__DENALI_PHY_1298__PHY_SW_TXIO_CTRL_2_MASK             0x00000F00U
#define LPDDR4__DENALI_PHY_1298__PHY_SW_TXIO_CTRL_2_SHIFT                     8U
#define LPDDR4__DENALI_PHY_1298__PHY_SW_TXIO_CTRL_2_WIDTH                     4U
#define LPDDR4__PHY_SW_TXIO_CTRL_2__REG DENALI_PHY_1298
#define LPDDR4__PHY_SW_TXIO_CTRL_2__FLD LPDDR4__DENALI_PHY_1298__PHY_SW_TXIO_CTRL_2

#define LPDDR4__DENALI_PHY_1298__PHY_SW_TXIO_CTRL_3_MASK             0x000F0000U
#define LPDDR4__DENALI_PHY_1298__PHY_SW_TXIO_CTRL_3_SHIFT                    16U
#define LPDDR4__DENALI_PHY_1298__PHY_SW_TXIO_CTRL_3_WIDTH                     4U
#define LPDDR4__PHY_SW_TXIO_CTRL_3__REG DENALI_PHY_1298
#define LPDDR4__PHY_SW_TXIO_CTRL_3__FLD LPDDR4__DENALI_PHY_1298__PHY_SW_TXIO_CTRL_3

#define LPDDR4__DENALI_PHY_1298__PHY_MEMCLK_SW_TXIO_CTRL_MASK        0x01000000U
#define LPDDR4__DENALI_PHY_1298__PHY_MEMCLK_SW_TXIO_CTRL_SHIFT               24U
#define LPDDR4__DENALI_PHY_1298__PHY_MEMCLK_SW_TXIO_CTRL_WIDTH                1U
#define LPDDR4__DENALI_PHY_1298__PHY_MEMCLK_SW_TXIO_CTRL_WOCLR                0U
#define LPDDR4__DENALI_PHY_1298__PHY_MEMCLK_SW_TXIO_CTRL_WOSET                0U
#define LPDDR4__PHY_MEMCLK_SW_TXIO_CTRL__REG DENALI_PHY_1298
#define LPDDR4__PHY_MEMCLK_SW_TXIO_CTRL__FLD LPDDR4__DENALI_PHY_1298__PHY_MEMCLK_SW_TXIO_CTRL

#define LPDDR4__DENALI_PHY_1299_READ_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_1299_WRITE_MASK                           0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_1299__PHY_ADRCTL_SW_TXPWR_CTRL_0_MASK     0x0000000FU
#define LPDDR4__DENALI_PHY_1299__PHY_ADRCTL_SW_TXPWR_CTRL_0_SHIFT             0U
#define LPDDR4__DENALI_PHY_1299__PHY_ADRCTL_SW_TXPWR_CTRL_0_WIDTH             4U
#define LPDDR4__PHY_ADRCTL_SW_TXPWR_CTRL_0__REG DENALI_PHY_1299
#define LPDDR4__PHY_ADRCTL_SW_TXPWR_CTRL_0__FLD LPDDR4__DENALI_PHY_1299__PHY_ADRCTL_SW_TXPWR_CTRL_0

#define LPDDR4__DENALI_PHY_1299__PHY_ADRCTL_SW_TXPWR_CTRL_1_MASK     0x00000F00U
#define LPDDR4__DENALI_PHY_1299__PHY_ADRCTL_SW_TXPWR_CTRL_1_SHIFT             8U
#define LPDDR4__DENALI_PHY_1299__PHY_ADRCTL_SW_TXPWR_CTRL_1_WIDTH             4U
#define LPDDR4__PHY_ADRCTL_SW_TXPWR_CTRL_1__REG DENALI_PHY_1299
#define LPDDR4__PHY_ADRCTL_SW_TXPWR_CTRL_1__FLD LPDDR4__DENALI_PHY_1299__PHY_ADRCTL_SW_TXPWR_CTRL_1

#define LPDDR4__DENALI_PHY_1299__PHY_ADRCTL_SW_TXPWR_CTRL_2_MASK     0x000F0000U
#define LPDDR4__DENALI_PHY_1299__PHY_ADRCTL_SW_TXPWR_CTRL_2_SHIFT            16U
#define LPDDR4__DENALI_PHY_1299__PHY_ADRCTL_SW_TXPWR_CTRL_2_WIDTH             4U
#define LPDDR4__PHY_ADRCTL_SW_TXPWR_CTRL_2__REG DENALI_PHY_1299
#define LPDDR4__PHY_ADRCTL_SW_TXPWR_CTRL_2__FLD LPDDR4__DENALI_PHY_1299__PHY_ADRCTL_SW_TXPWR_CTRL_2

#define LPDDR4__DENALI_PHY_1299__PHY_ADRCTL_SW_TXPWR_CTRL_3_MASK     0x0F000000U
#define LPDDR4__DENALI_PHY_1299__PHY_ADRCTL_SW_TXPWR_CTRL_3_SHIFT            24U
#define LPDDR4__DENALI_PHY_1299__PHY_ADRCTL_SW_TXPWR_CTRL_3_WIDTH             4U
#define LPDDR4__PHY_ADRCTL_SW_TXPWR_CTRL_3__REG DENALI_PHY_1299
#define LPDDR4__PHY_ADRCTL_SW_TXPWR_CTRL_3__FLD LPDDR4__DENALI_PHY_1299__PHY_ADRCTL_SW_TXPWR_CTRL_3

#define LPDDR4__DENALI_PHY_1300_READ_MASK                            0x00010101U
#define LPDDR4__DENALI_PHY_1300_WRITE_MASK                           0x00010101U
#define LPDDR4__DENALI_PHY_1300__PHY_MEMCLK_SW_TXPWR_CTRL_MASK       0x00000001U
#define LPDDR4__DENALI_PHY_1300__PHY_MEMCLK_SW_TXPWR_CTRL_SHIFT               0U
#define LPDDR4__DENALI_PHY_1300__PHY_MEMCLK_SW_TXPWR_CTRL_WIDTH               1U
#define LPDDR4__DENALI_PHY_1300__PHY_MEMCLK_SW_TXPWR_CTRL_WOCLR               0U
#define LPDDR4__DENALI_PHY_1300__PHY_MEMCLK_SW_TXPWR_CTRL_WOSET               0U
#define LPDDR4__PHY_MEMCLK_SW_TXPWR_CTRL__REG DENALI_PHY_1300
#define LPDDR4__PHY_MEMCLK_SW_TXPWR_CTRL__FLD LPDDR4__DENALI_PHY_1300__PHY_MEMCLK_SW_TXPWR_CTRL

#define LPDDR4__DENALI_PHY_1300__PHY_TOP_STATIC_TOG_DISABLE_MASK     0x00000100U
#define LPDDR4__DENALI_PHY_1300__PHY_TOP_STATIC_TOG_DISABLE_SHIFT             8U
#define LPDDR4__DENALI_PHY_1300__PHY_TOP_STATIC_TOG_DISABLE_WIDTH             1U
#define LPDDR4__DENALI_PHY_1300__PHY_TOP_STATIC_TOG_DISABLE_WOCLR             0U
#define LPDDR4__DENALI_PHY_1300__PHY_TOP_STATIC_TOG_DISABLE_WOSET             0U
#define LPDDR4__PHY_TOP_STATIC_TOG_DISABLE__REG DENALI_PHY_1300
#define LPDDR4__PHY_TOP_STATIC_TOG_DISABLE__FLD LPDDR4__DENALI_PHY_1300__PHY_TOP_STATIC_TOG_DISABLE

#define LPDDR4__DENALI_PHY_1300__PHY_BYTE_DISABLE_STATIC_TOG_DISABLE_MASK 0x00010000U
#define LPDDR4__DENALI_PHY_1300__PHY_BYTE_DISABLE_STATIC_TOG_DISABLE_SHIFT   16U
#define LPDDR4__DENALI_PHY_1300__PHY_BYTE_DISABLE_STATIC_TOG_DISABLE_WIDTH    1U
#define LPDDR4__DENALI_PHY_1300__PHY_BYTE_DISABLE_STATIC_TOG_DISABLE_WOCLR    0U
#define LPDDR4__DENALI_PHY_1300__PHY_BYTE_DISABLE_STATIC_TOG_DISABLE_WOSET    0U
#define LPDDR4__PHY_BYTE_DISABLE_STATIC_TOG_DISABLE__REG DENALI_PHY_1300
#define LPDDR4__PHY_BYTE_DISABLE_STATIC_TOG_DISABLE__FLD LPDDR4__DENALI_PHY_1300__PHY_BYTE_DISABLE_STATIC_TOG_DISABLE

#define LPDDR4__DENALI_PHY_1301_READ_MASK                            0x010FFFFFU
#define LPDDR4__DENALI_PHY_1301_WRITE_MASK                           0x010FFFFFU
#define LPDDR4__DENALI_PHY_1301__PHY_STATIC_TOG_CONTROL_MASK         0x0000FFFFU
#define LPDDR4__DENALI_PHY_1301__PHY_STATIC_TOG_CONTROL_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1301__PHY_STATIC_TOG_CONTROL_WIDTH                16U
#define LPDDR4__PHY_STATIC_TOG_CONTROL__REG DENALI_PHY_1301
#define LPDDR4__PHY_STATIC_TOG_CONTROL__FLD LPDDR4__DENALI_PHY_1301__PHY_STATIC_TOG_CONTROL

#define LPDDR4__DENALI_PHY_1301__PHY_ADRCTL_STATIC_TOG_DISABLE_MASK  0x000F0000U
#define LPDDR4__DENALI_PHY_1301__PHY_ADRCTL_STATIC_TOG_DISABLE_SHIFT         16U
#define LPDDR4__DENALI_PHY_1301__PHY_ADRCTL_STATIC_TOG_DISABLE_WIDTH          4U
#define LPDDR4__PHY_ADRCTL_STATIC_TOG_DISABLE__REG DENALI_PHY_1301
#define LPDDR4__PHY_ADRCTL_STATIC_TOG_DISABLE__FLD LPDDR4__DENALI_PHY_1301__PHY_ADRCTL_STATIC_TOG_DISABLE

#define LPDDR4__DENALI_PHY_1301__PHY_MEMCLK_STATIC_TOG_DISABLE_MASK  0x01000000U
#define LPDDR4__DENALI_PHY_1301__PHY_MEMCLK_STATIC_TOG_DISABLE_SHIFT         24U
#define LPDDR4__DENALI_PHY_1301__PHY_MEMCLK_STATIC_TOG_DISABLE_WIDTH          1U
#define LPDDR4__DENALI_PHY_1301__PHY_MEMCLK_STATIC_TOG_DISABLE_WOCLR          0U
#define LPDDR4__DENALI_PHY_1301__PHY_MEMCLK_STATIC_TOG_DISABLE_WOSET          0U
#define LPDDR4__PHY_MEMCLK_STATIC_TOG_DISABLE__REG DENALI_PHY_1301
#define LPDDR4__PHY_MEMCLK_STATIC_TOG_DISABLE__FLD LPDDR4__DENALI_PHY_1301__PHY_MEMCLK_STATIC_TOG_DISABLE

#define LPDDR4__DENALI_PHY_1302_READ_MASK                            0x00000001U
#define LPDDR4__DENALI_PHY_1302_WRITE_MASK                           0x00000001U
#define LPDDR4__DENALI_PHY_1302__PHY_LP4_BOOT_PLL_BYPASS_MASK        0x00000001U
#define LPDDR4__DENALI_PHY_1302__PHY_LP4_BOOT_PLL_BYPASS_SHIFT                0U
#define LPDDR4__DENALI_PHY_1302__PHY_LP4_BOOT_PLL_BYPASS_WIDTH                1U
#define LPDDR4__DENALI_PHY_1302__PHY_LP4_BOOT_PLL_BYPASS_WOCLR                0U
#define LPDDR4__DENALI_PHY_1302__PHY_LP4_BOOT_PLL_BYPASS_WOSET                0U
#define LPDDR4__PHY_LP4_BOOT_PLL_BYPASS__REG DENALI_PHY_1302
#define LPDDR4__PHY_LP4_BOOT_PLL_BYPASS__FLD LPDDR4__DENALI_PHY_1302__PHY_LP4_BOOT_PLL_BYPASS

#define LPDDR4__DENALI_PHY_1303_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1303_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1303__PHY_CLK_SWITCH_OBS_MASK             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1303__PHY_CLK_SWITCH_OBS_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1303__PHY_CLK_SWITCH_OBS_WIDTH                    32U
#define LPDDR4__PHY_CLK_SWITCH_OBS__REG DENALI_PHY_1303
#define LPDDR4__PHY_CLK_SWITCH_OBS__FLD LPDDR4__DENALI_PHY_1303__PHY_CLK_SWITCH_OBS

#define LPDDR4__DENALI_PHY_1304_READ_MASK                            0x0000FFFFU
#define LPDDR4__DENALI_PHY_1304_WRITE_MASK                           0x0000FFFFU
#define LPDDR4__DENALI_PHY_1304__PHY_PLL_WAIT_MASK                   0x0000FFFFU
#define LPDDR4__DENALI_PHY_1304__PHY_PLL_WAIT_SHIFT                           0U
#define LPDDR4__DENALI_PHY_1304__PHY_PLL_WAIT_WIDTH                          16U
#define LPDDR4__PHY_PLL_WAIT__REG DENALI_PHY_1304
#define LPDDR4__PHY_PLL_WAIT__FLD LPDDR4__DENALI_PHY_1304__PHY_PLL_WAIT

#define LPDDR4__DENALI_PHY_1305_READ_MASK                            0x00000001U
#define LPDDR4__DENALI_PHY_1305_WRITE_MASK                           0x00000001U
#define LPDDR4__DENALI_PHY_1305__PHY_SW_PLL_BYPASS_MASK              0x00000001U
#define LPDDR4__DENALI_PHY_1305__PHY_SW_PLL_BYPASS_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1305__PHY_SW_PLL_BYPASS_WIDTH                      1U
#define LPDDR4__DENALI_PHY_1305__PHY_SW_PLL_BYPASS_WOCLR                      0U
#define LPDDR4__DENALI_PHY_1305__PHY_SW_PLL_BYPASS_WOSET                      0U
#define LPDDR4__PHY_SW_PLL_BYPASS__REG DENALI_PHY_1305
#define LPDDR4__PHY_SW_PLL_BYPASS__FLD LPDDR4__DENALI_PHY_1305__PHY_SW_PLL_BYPASS

#define LPDDR4__DENALI_PHY_1306_READ_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_1306_WRITE_MASK                           0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_1306__PHY_SET_DFI_INPUT_0_MASK            0x0000000FU
#define LPDDR4__DENALI_PHY_1306__PHY_SET_DFI_INPUT_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1306__PHY_SET_DFI_INPUT_0_WIDTH                    4U
#define LPDDR4__PHY_SET_DFI_INPUT_0__REG DENALI_PHY_1306
#define LPDDR4__PHY_SET_DFI_INPUT_0__FLD LPDDR4__DENALI_PHY_1306__PHY_SET_DFI_INPUT_0

#define LPDDR4__DENALI_PHY_1306__PHY_SET_DFI_INPUT_1_MASK            0x00000F00U
#define LPDDR4__DENALI_PHY_1306__PHY_SET_DFI_INPUT_1_SHIFT                    8U
#define LPDDR4__DENALI_PHY_1306__PHY_SET_DFI_INPUT_1_WIDTH                    4U
#define LPDDR4__PHY_SET_DFI_INPUT_1__REG DENALI_PHY_1306
#define LPDDR4__PHY_SET_DFI_INPUT_1__FLD LPDDR4__DENALI_PHY_1306__PHY_SET_DFI_INPUT_1

#define LPDDR4__DENALI_PHY_1306__PHY_SET_DFI_INPUT_2_MASK            0x000F0000U
#define LPDDR4__DENALI_PHY_1306__PHY_SET_DFI_INPUT_2_SHIFT                   16U
#define LPDDR4__DENALI_PHY_1306__PHY_SET_DFI_INPUT_2_WIDTH                    4U
#define LPDDR4__PHY_SET_DFI_INPUT_2__REG DENALI_PHY_1306
#define LPDDR4__PHY_SET_DFI_INPUT_2__FLD LPDDR4__DENALI_PHY_1306__PHY_SET_DFI_INPUT_2

#define LPDDR4__DENALI_PHY_1306__PHY_SET_DFI_INPUT_3_MASK            0x0F000000U
#define LPDDR4__DENALI_PHY_1306__PHY_SET_DFI_INPUT_3_SHIFT                   24U
#define LPDDR4__DENALI_PHY_1306__PHY_SET_DFI_INPUT_3_WIDTH                    4U
#define LPDDR4__PHY_SET_DFI_INPUT_3__REG DENALI_PHY_1306
#define LPDDR4__PHY_SET_DFI_INPUT_3__FLD LPDDR4__DENALI_PHY_1306__PHY_SET_DFI_INPUT_3

#define LPDDR4__DENALI_PHY_1307_READ_MASK                            0x03030303U
#define LPDDR4__DENALI_PHY_1307_WRITE_MASK                           0x03030303U
#define LPDDR4__DENALI_PHY_1307__PHY_CS_ACS_ALLOCATION_BIT0_0_MASK   0x00000003U
#define LPDDR4__DENALI_PHY_1307__PHY_CS_ACS_ALLOCATION_BIT0_0_SHIFT           0U
#define LPDDR4__DENALI_PHY_1307__PHY_CS_ACS_ALLOCATION_BIT0_0_WIDTH           2U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT0_0__REG DENALI_PHY_1307
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT0_0__FLD LPDDR4__DENALI_PHY_1307__PHY_CS_ACS_ALLOCATION_BIT0_0

#define LPDDR4__DENALI_PHY_1307__PHY_CS_ACS_ALLOCATION_BIT1_0_MASK   0x00000300U
#define LPDDR4__DENALI_PHY_1307__PHY_CS_ACS_ALLOCATION_BIT1_0_SHIFT           8U
#define LPDDR4__DENALI_PHY_1307__PHY_CS_ACS_ALLOCATION_BIT1_0_WIDTH           2U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT1_0__REG DENALI_PHY_1307
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT1_0__FLD LPDDR4__DENALI_PHY_1307__PHY_CS_ACS_ALLOCATION_BIT1_0

#define LPDDR4__DENALI_PHY_1307__PHY_CS_ACS_ALLOCATION_BIT2_0_MASK   0x00030000U
#define LPDDR4__DENALI_PHY_1307__PHY_CS_ACS_ALLOCATION_BIT2_0_SHIFT          16U
#define LPDDR4__DENALI_PHY_1307__PHY_CS_ACS_ALLOCATION_BIT2_0_WIDTH           2U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT2_0__REG DENALI_PHY_1307
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT2_0__FLD LPDDR4__DENALI_PHY_1307__PHY_CS_ACS_ALLOCATION_BIT2_0

#define LPDDR4__DENALI_PHY_1307__PHY_CS_ACS_ALLOCATION_BIT3_0_MASK   0x03000000U
#define LPDDR4__DENALI_PHY_1307__PHY_CS_ACS_ALLOCATION_BIT3_0_SHIFT          24U
#define LPDDR4__DENALI_PHY_1307__PHY_CS_ACS_ALLOCATION_BIT3_0_WIDTH           2U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT3_0__REG DENALI_PHY_1307
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT3_0__FLD LPDDR4__DENALI_PHY_1307__PHY_CS_ACS_ALLOCATION_BIT3_0

#define LPDDR4__DENALI_PHY_1308_READ_MASK                            0x03030303U
#define LPDDR4__DENALI_PHY_1308_WRITE_MASK                           0x03030303U
#define LPDDR4__DENALI_PHY_1308__PHY_CS_ACS_ALLOCATION_BIT0_1_MASK   0x00000003U
#define LPDDR4__DENALI_PHY_1308__PHY_CS_ACS_ALLOCATION_BIT0_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_1308__PHY_CS_ACS_ALLOCATION_BIT0_1_WIDTH           2U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT0_1__REG DENALI_PHY_1308
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT0_1__FLD LPDDR4__DENALI_PHY_1308__PHY_CS_ACS_ALLOCATION_BIT0_1

#define LPDDR4__DENALI_PHY_1308__PHY_CS_ACS_ALLOCATION_BIT1_1_MASK   0x00000300U
#define LPDDR4__DENALI_PHY_1308__PHY_CS_ACS_ALLOCATION_BIT1_1_SHIFT           8U
#define LPDDR4__DENALI_PHY_1308__PHY_CS_ACS_ALLOCATION_BIT1_1_WIDTH           2U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT1_1__REG DENALI_PHY_1308
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT1_1__FLD LPDDR4__DENALI_PHY_1308__PHY_CS_ACS_ALLOCATION_BIT1_1

#define LPDDR4__DENALI_PHY_1308__PHY_CS_ACS_ALLOCATION_BIT2_1_MASK   0x00030000U
#define LPDDR4__DENALI_PHY_1308__PHY_CS_ACS_ALLOCATION_BIT2_1_SHIFT          16U
#define LPDDR4__DENALI_PHY_1308__PHY_CS_ACS_ALLOCATION_BIT2_1_WIDTH           2U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT2_1__REG DENALI_PHY_1308
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT2_1__FLD LPDDR4__DENALI_PHY_1308__PHY_CS_ACS_ALLOCATION_BIT2_1

#define LPDDR4__DENALI_PHY_1308__PHY_CS_ACS_ALLOCATION_BIT3_1_MASK   0x03000000U
#define LPDDR4__DENALI_PHY_1308__PHY_CS_ACS_ALLOCATION_BIT3_1_SHIFT          24U
#define LPDDR4__DENALI_PHY_1308__PHY_CS_ACS_ALLOCATION_BIT3_1_WIDTH           2U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT3_1__REG DENALI_PHY_1308
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT3_1__FLD LPDDR4__DENALI_PHY_1308__PHY_CS_ACS_ALLOCATION_BIT3_1

#define LPDDR4__DENALI_PHY_1309_READ_MASK                            0x03030303U
#define LPDDR4__DENALI_PHY_1309_WRITE_MASK                           0x03030303U
#define LPDDR4__DENALI_PHY_1309__PHY_CS_ACS_ALLOCATION_BIT0_2_MASK   0x00000003U
#define LPDDR4__DENALI_PHY_1309__PHY_CS_ACS_ALLOCATION_BIT0_2_SHIFT           0U
#define LPDDR4__DENALI_PHY_1309__PHY_CS_ACS_ALLOCATION_BIT0_2_WIDTH           2U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT0_2__REG DENALI_PHY_1309
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT0_2__FLD LPDDR4__DENALI_PHY_1309__PHY_CS_ACS_ALLOCATION_BIT0_2

#define LPDDR4__DENALI_PHY_1309__PHY_CS_ACS_ALLOCATION_BIT1_2_MASK   0x00000300U
#define LPDDR4__DENALI_PHY_1309__PHY_CS_ACS_ALLOCATION_BIT1_2_SHIFT           8U
#define LPDDR4__DENALI_PHY_1309__PHY_CS_ACS_ALLOCATION_BIT1_2_WIDTH           2U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT1_2__REG DENALI_PHY_1309
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT1_2__FLD LPDDR4__DENALI_PHY_1309__PHY_CS_ACS_ALLOCATION_BIT1_2

#define LPDDR4__DENALI_PHY_1309__PHY_CS_ACS_ALLOCATION_BIT2_2_MASK   0x00030000U
#define LPDDR4__DENALI_PHY_1309__PHY_CS_ACS_ALLOCATION_BIT2_2_SHIFT          16U
#define LPDDR4__DENALI_PHY_1309__PHY_CS_ACS_ALLOCATION_BIT2_2_WIDTH           2U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT2_2__REG DENALI_PHY_1309
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT2_2__FLD LPDDR4__DENALI_PHY_1309__PHY_CS_ACS_ALLOCATION_BIT2_2

#define LPDDR4__DENALI_PHY_1309__PHY_CS_ACS_ALLOCATION_BIT3_2_MASK   0x03000000U
#define LPDDR4__DENALI_PHY_1309__PHY_CS_ACS_ALLOCATION_BIT3_2_SHIFT          24U
#define LPDDR4__DENALI_PHY_1309__PHY_CS_ACS_ALLOCATION_BIT3_2_WIDTH           2U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT3_2__REG DENALI_PHY_1309
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT3_2__FLD LPDDR4__DENALI_PHY_1309__PHY_CS_ACS_ALLOCATION_BIT3_2

#define LPDDR4__DENALI_PHY_1310_READ_MASK                            0x03030303U
#define LPDDR4__DENALI_PHY_1310_WRITE_MASK                           0x03030303U
#define LPDDR4__DENALI_PHY_1310__PHY_CS_ACS_ALLOCATION_BIT0_3_MASK   0x00000003U
#define LPDDR4__DENALI_PHY_1310__PHY_CS_ACS_ALLOCATION_BIT0_3_SHIFT           0U
#define LPDDR4__DENALI_PHY_1310__PHY_CS_ACS_ALLOCATION_BIT0_3_WIDTH           2U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT0_3__REG DENALI_PHY_1310
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT0_3__FLD LPDDR4__DENALI_PHY_1310__PHY_CS_ACS_ALLOCATION_BIT0_3

#define LPDDR4__DENALI_PHY_1310__PHY_CS_ACS_ALLOCATION_BIT1_3_MASK   0x00000300U
#define LPDDR4__DENALI_PHY_1310__PHY_CS_ACS_ALLOCATION_BIT1_3_SHIFT           8U
#define LPDDR4__DENALI_PHY_1310__PHY_CS_ACS_ALLOCATION_BIT1_3_WIDTH           2U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT1_3__REG DENALI_PHY_1310
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT1_3__FLD LPDDR4__DENALI_PHY_1310__PHY_CS_ACS_ALLOCATION_BIT1_3

#define LPDDR4__DENALI_PHY_1310__PHY_CS_ACS_ALLOCATION_BIT2_3_MASK   0x00030000U
#define LPDDR4__DENALI_PHY_1310__PHY_CS_ACS_ALLOCATION_BIT2_3_SHIFT          16U
#define LPDDR4__DENALI_PHY_1310__PHY_CS_ACS_ALLOCATION_BIT2_3_WIDTH           2U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT2_3__REG DENALI_PHY_1310
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT2_3__FLD LPDDR4__DENALI_PHY_1310__PHY_CS_ACS_ALLOCATION_BIT2_3

#define LPDDR4__DENALI_PHY_1310__PHY_CS_ACS_ALLOCATION_BIT3_3_MASK   0x03000000U
#define LPDDR4__DENALI_PHY_1310__PHY_CS_ACS_ALLOCATION_BIT3_3_SHIFT          24U
#define LPDDR4__DENALI_PHY_1310__PHY_CS_ACS_ALLOCATION_BIT3_3_WIDTH           2U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT3_3__REG DENALI_PHY_1310
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT3_3__FLD LPDDR4__DENALI_PHY_1310__PHY_CS_ACS_ALLOCATION_BIT3_3

#define LPDDR4__DENALI_PHY_1311_READ_MASK                            0xFFFF1FFFU
#define LPDDR4__DENALI_PHY_1311_WRITE_MASK                           0xFFFF1FFFU
#define LPDDR4__DENALI_PHY_1311__PHY_LP4_BOOT_PLL_CTRL_MASK          0x00001FFFU
#define LPDDR4__DENALI_PHY_1311__PHY_LP4_BOOT_PLL_CTRL_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1311__PHY_LP4_BOOT_PLL_CTRL_WIDTH                 13U
#define LPDDR4__PHY_LP4_BOOT_PLL_CTRL__REG DENALI_PHY_1311
#define LPDDR4__PHY_LP4_BOOT_PLL_CTRL__FLD LPDDR4__DENALI_PHY_1311__PHY_LP4_BOOT_PLL_CTRL

#define LPDDR4__DENALI_PHY_1311__PHY_PLL_CTRL_OVERRIDE_MASK          0xFFFF0000U
#define LPDDR4__DENALI_PHY_1311__PHY_PLL_CTRL_OVERRIDE_SHIFT                 16U
#define LPDDR4__DENALI_PHY_1311__PHY_PLL_CTRL_OVERRIDE_WIDTH                 16U
#define LPDDR4__PHY_PLL_CTRL_OVERRIDE__REG DENALI_PHY_1311
#define LPDDR4__PHY_PLL_CTRL_OVERRIDE__FLD LPDDR4__DENALI_PHY_1311__PHY_PLL_CTRL_OVERRIDE

#define LPDDR4__DENALI_PHY_1312_READ_MASK                            0x0000FF01U
#define LPDDR4__DENALI_PHY_1312_WRITE_MASK                           0x0000FF01U
#define LPDDR4__DENALI_PHY_1312__PHY_USE_PLL_DSKEWCALLOCK_MASK       0x00000001U
#define LPDDR4__DENALI_PHY_1312__PHY_USE_PLL_DSKEWCALLOCK_SHIFT               0U
#define LPDDR4__DENALI_PHY_1312__PHY_USE_PLL_DSKEWCALLOCK_WIDTH               1U
#define LPDDR4__DENALI_PHY_1312__PHY_USE_PLL_DSKEWCALLOCK_WOCLR               0U
#define LPDDR4__DENALI_PHY_1312__PHY_USE_PLL_DSKEWCALLOCK_WOSET               0U
#define LPDDR4__PHY_USE_PLL_DSKEWCALLOCK__REG DENALI_PHY_1312
#define LPDDR4__PHY_USE_PLL_DSKEWCALLOCK__FLD LPDDR4__DENALI_PHY_1312__PHY_USE_PLL_DSKEWCALLOCK

#define LPDDR4__DENALI_PHY_1312__PHY_PLL_SPO_CAL_CTRL_MASK           0x0000FF00U
#define LPDDR4__DENALI_PHY_1312__PHY_PLL_SPO_CAL_CTRL_SHIFT                   8U
#define LPDDR4__DENALI_PHY_1312__PHY_PLL_SPO_CAL_CTRL_WIDTH                   8U
#define LPDDR4__PHY_PLL_SPO_CAL_CTRL__REG DENALI_PHY_1312
#define LPDDR4__PHY_PLL_SPO_CAL_CTRL__FLD LPDDR4__DENALI_PHY_1312__PHY_PLL_SPO_CAL_CTRL

#define LPDDR4__DENALI_PHY_1312__SC_PHY_PLL_SPO_CAL_SNAP_OBS_MASK    0x00030000U
#define LPDDR4__DENALI_PHY_1312__SC_PHY_PLL_SPO_CAL_SNAP_OBS_SHIFT           16U
#define LPDDR4__DENALI_PHY_1312__SC_PHY_PLL_SPO_CAL_SNAP_OBS_WIDTH            2U
#define LPDDR4__SC_PHY_PLL_SPO_CAL_SNAP_OBS__REG DENALI_PHY_1312
#define LPDDR4__SC_PHY_PLL_SPO_CAL_SNAP_OBS__FLD LPDDR4__DENALI_PHY_1312__SC_PHY_PLL_SPO_CAL_SNAP_OBS

#define LPDDR4__DENALI_PHY_1313_READ_MASK                            0x0000FFFFU
#define LPDDR4__DENALI_PHY_1313_WRITE_MASK                           0x0000FFFFU
#define LPDDR4__DENALI_PHY_1313__PHY_PLL_OBS_0_MASK                  0x0000FFFFU
#define LPDDR4__DENALI_PHY_1313__PHY_PLL_OBS_0_SHIFT                          0U
#define LPDDR4__DENALI_PHY_1313__PHY_PLL_OBS_0_WIDTH                         16U
#define LPDDR4__PHY_PLL_OBS_0__REG DENALI_PHY_1313
#define LPDDR4__PHY_PLL_OBS_0__FLD LPDDR4__DENALI_PHY_1313__PHY_PLL_OBS_0

#define LPDDR4__DENALI_PHY_1314_READ_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_PHY_1314_WRITE_MASK                           0x0001FFFFU
#define LPDDR4__DENALI_PHY_1314__PHY_PLL_SPO_CAL_OBS_0_MASK          0x0001FFFFU
#define LPDDR4__DENALI_PHY_1314__PHY_PLL_SPO_CAL_OBS_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1314__PHY_PLL_SPO_CAL_OBS_0_WIDTH                 17U
#define LPDDR4__PHY_PLL_SPO_CAL_OBS_0__REG DENALI_PHY_1314
#define LPDDR4__PHY_PLL_SPO_CAL_OBS_0__FLD LPDDR4__DENALI_PHY_1314__PHY_PLL_SPO_CAL_OBS_0

#define LPDDR4__DENALI_PHY_1315_READ_MASK                            0x0FFF0FFFU
#define LPDDR4__DENALI_PHY_1315_WRITE_MASK                           0x0FFF0FFFU
#define LPDDR4__DENALI_PHY_1315__PHY_PLL_DESKEWCALIN_0_MASK          0x00000FFFU
#define LPDDR4__DENALI_PHY_1315__PHY_PLL_DESKEWCALIN_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1315__PHY_PLL_DESKEWCALIN_0_WIDTH                 12U
#define LPDDR4__PHY_PLL_DESKEWCALIN_0__REG DENALI_PHY_1315
#define LPDDR4__PHY_PLL_DESKEWCALIN_0__FLD LPDDR4__DENALI_PHY_1315__PHY_PLL_DESKEWCALIN_0

#define LPDDR4__DENALI_PHY_1315__PHY_LP4_BOOT_PLL_DESKEWCALIN_0_MASK 0x0FFF0000U
#define LPDDR4__DENALI_PHY_1315__PHY_LP4_BOOT_PLL_DESKEWCALIN_0_SHIFT        16U
#define LPDDR4__DENALI_PHY_1315__PHY_LP4_BOOT_PLL_DESKEWCALIN_0_WIDTH        12U
#define LPDDR4__PHY_LP4_BOOT_PLL_DESKEWCALIN_0__REG DENALI_PHY_1315
#define LPDDR4__PHY_LP4_BOOT_PLL_DESKEWCALIN_0__FLD LPDDR4__DENALI_PHY_1315__PHY_LP4_BOOT_PLL_DESKEWCALIN_0

#define LPDDR4__DENALI_PHY_1316_READ_MASK                            0x0000FFFFU
#define LPDDR4__DENALI_PHY_1316_WRITE_MASK                           0x0000FFFFU
#define LPDDR4__DENALI_PHY_1316__PHY_PLL_OBS_1_MASK                  0x0000FFFFU
#define LPDDR4__DENALI_PHY_1316__PHY_PLL_OBS_1_SHIFT                          0U
#define LPDDR4__DENALI_PHY_1316__PHY_PLL_OBS_1_WIDTH                         16U
#define LPDDR4__PHY_PLL_OBS_1__REG DENALI_PHY_1316
#define LPDDR4__PHY_PLL_OBS_1__FLD LPDDR4__DENALI_PHY_1316__PHY_PLL_OBS_1

#define LPDDR4__DENALI_PHY_1317_READ_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_PHY_1317_WRITE_MASK                           0x0001FFFFU
#define LPDDR4__DENALI_PHY_1317__PHY_PLL_SPO_CAL_OBS_1_MASK          0x0001FFFFU
#define LPDDR4__DENALI_PHY_1317__PHY_PLL_SPO_CAL_OBS_1_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1317__PHY_PLL_SPO_CAL_OBS_1_WIDTH                 17U
#define LPDDR4__PHY_PLL_SPO_CAL_OBS_1__REG DENALI_PHY_1317
#define LPDDR4__PHY_PLL_SPO_CAL_OBS_1__FLD LPDDR4__DENALI_PHY_1317__PHY_PLL_SPO_CAL_OBS_1

#define LPDDR4__DENALI_PHY_1318_READ_MASK                            0x0FFF0FFFU
#define LPDDR4__DENALI_PHY_1318_WRITE_MASK                           0x0FFF0FFFU
#define LPDDR4__DENALI_PHY_1318__PHY_PLL_DESKEWCALIN_1_MASK          0x00000FFFU
#define LPDDR4__DENALI_PHY_1318__PHY_PLL_DESKEWCALIN_1_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1318__PHY_PLL_DESKEWCALIN_1_WIDTH                 12U
#define LPDDR4__PHY_PLL_DESKEWCALIN_1__REG DENALI_PHY_1318
#define LPDDR4__PHY_PLL_DESKEWCALIN_1__FLD LPDDR4__DENALI_PHY_1318__PHY_PLL_DESKEWCALIN_1

#define LPDDR4__DENALI_PHY_1318__PHY_LP4_BOOT_PLL_DESKEWCALIN_1_MASK 0x0FFF0000U
#define LPDDR4__DENALI_PHY_1318__PHY_LP4_BOOT_PLL_DESKEWCALIN_1_SHIFT        16U
#define LPDDR4__DENALI_PHY_1318__PHY_LP4_BOOT_PLL_DESKEWCALIN_1_WIDTH        12U
#define LPDDR4__PHY_LP4_BOOT_PLL_DESKEWCALIN_1__REG DENALI_PHY_1318
#define LPDDR4__PHY_LP4_BOOT_PLL_DESKEWCALIN_1__FLD LPDDR4__DENALI_PHY_1318__PHY_LP4_BOOT_PLL_DESKEWCALIN_1

#define LPDDR4__DENALI_PHY_1319_READ_MASK                            0xFF0F0101U
#define LPDDR4__DENALI_PHY_1319_WRITE_MASK                           0xFF0F0101U
#define LPDDR4__DENALI_PHY_1319__PHY_PLL_REFOUT_SEL_MASK             0x00000001U
#define LPDDR4__DENALI_PHY_1319__PHY_PLL_REFOUT_SEL_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1319__PHY_PLL_REFOUT_SEL_WIDTH                     1U
#define LPDDR4__DENALI_PHY_1319__PHY_PLL_REFOUT_SEL_WOCLR                     0U
#define LPDDR4__DENALI_PHY_1319__PHY_PLL_REFOUT_SEL_WOSET                     0U
#define LPDDR4__PHY_PLL_REFOUT_SEL__REG DENALI_PHY_1319
#define LPDDR4__PHY_PLL_REFOUT_SEL__FLD LPDDR4__DENALI_PHY_1319__PHY_PLL_REFOUT_SEL

#define LPDDR4__DENALI_PHY_1319__PHY_LP4_BOOT_LOW_FREQ_SEL_MASK      0x00000100U
#define LPDDR4__DENALI_PHY_1319__PHY_LP4_BOOT_LOW_FREQ_SEL_SHIFT              8U
#define LPDDR4__DENALI_PHY_1319__PHY_LP4_BOOT_LOW_FREQ_SEL_WIDTH              1U
#define LPDDR4__DENALI_PHY_1319__PHY_LP4_BOOT_LOW_FREQ_SEL_WOCLR              0U
#define LPDDR4__DENALI_PHY_1319__PHY_LP4_BOOT_LOW_FREQ_SEL_WOSET              0U
#define LPDDR4__PHY_LP4_BOOT_LOW_FREQ_SEL__REG DENALI_PHY_1319
#define LPDDR4__PHY_LP4_BOOT_LOW_FREQ_SEL__FLD LPDDR4__DENALI_PHY_1319__PHY_LP4_BOOT_LOW_FREQ_SEL

#define LPDDR4__DENALI_PHY_1319__PHY_TCKSRE_WAIT_MASK                0x000F0000U
#define LPDDR4__DENALI_PHY_1319__PHY_TCKSRE_WAIT_SHIFT                       16U
#define LPDDR4__DENALI_PHY_1319__PHY_TCKSRE_WAIT_WIDTH                        4U
#define LPDDR4__PHY_TCKSRE_WAIT__REG DENALI_PHY_1319
#define LPDDR4__PHY_TCKSRE_WAIT__FLD LPDDR4__DENALI_PHY_1319__PHY_TCKSRE_WAIT

#define LPDDR4__DENALI_PHY_1319__PHY_LP_WAKEUP_MASK                  0xFF000000U
#define LPDDR4__DENALI_PHY_1319__PHY_LP_WAKEUP_SHIFT                         24U
#define LPDDR4__DENALI_PHY_1319__PHY_LP_WAKEUP_WIDTH                          8U
#define LPDDR4__PHY_LP_WAKEUP__REG DENALI_PHY_1319
#define LPDDR4__PHY_LP_WAKEUP__FLD LPDDR4__DENALI_PHY_1319__PHY_LP_WAKEUP

#define LPDDR4__DENALI_PHY_1320_READ_MASK                            0x0003FF01U
#define LPDDR4__DENALI_PHY_1320_WRITE_MASK                           0x0003FF01U
#define LPDDR4__DENALI_PHY_1320__PHY_LS_IDLE_EN_MASK                 0x00000001U
#define LPDDR4__DENALI_PHY_1320__PHY_LS_IDLE_EN_SHIFT                         0U
#define LPDDR4__DENALI_PHY_1320__PHY_LS_IDLE_EN_WIDTH                         1U
#define LPDDR4__DENALI_PHY_1320__PHY_LS_IDLE_EN_WOCLR                         0U
#define LPDDR4__DENALI_PHY_1320__PHY_LS_IDLE_EN_WOSET                         0U
#define LPDDR4__PHY_LS_IDLE_EN__REG DENALI_PHY_1320
#define LPDDR4__PHY_LS_IDLE_EN__FLD LPDDR4__DENALI_PHY_1320__PHY_LS_IDLE_EN

#define LPDDR4__DENALI_PHY_1320__PHY_LP_CTRLUPD_CNTR_CFG_MASK        0x0003FF00U
#define LPDDR4__DENALI_PHY_1320__PHY_LP_CTRLUPD_CNTR_CFG_SHIFT                8U
#define LPDDR4__DENALI_PHY_1320__PHY_LP_CTRLUPD_CNTR_CFG_WIDTH               10U
#define LPDDR4__PHY_LP_CTRLUPD_CNTR_CFG__REG DENALI_PHY_1320
#define LPDDR4__PHY_LP_CTRLUPD_CNTR_CFG__FLD LPDDR4__DENALI_PHY_1320__PHY_LP_CTRLUPD_CNTR_CFG

#define LPDDR4__DENALI_PHY_1321_READ_MASK                            0x0101FFFFU
#define LPDDR4__DENALI_PHY_1321_WRITE_MASK                           0x0101FFFFU
#define LPDDR4__DENALI_PHY_1321__PHY_DS_EXIT_CTRL_MASK               0x0001FFFFU
#define LPDDR4__DENALI_PHY_1321__PHY_DS_EXIT_CTRL_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1321__PHY_DS_EXIT_CTRL_WIDTH                      17U
#define LPDDR4__PHY_DS_EXIT_CTRL__REG DENALI_PHY_1321
#define LPDDR4__PHY_DS_EXIT_CTRL__FLD LPDDR4__DENALI_PHY_1321__PHY_DS_EXIT_CTRL

#define LPDDR4__DENALI_PHY_1321__PHY_TDFI_PHY_WRDELAY_MASK           0x01000000U
#define LPDDR4__DENALI_PHY_1321__PHY_TDFI_PHY_WRDELAY_SHIFT                  24U
#define LPDDR4__DENALI_PHY_1321__PHY_TDFI_PHY_WRDELAY_WIDTH                   1U
#define LPDDR4__DENALI_PHY_1321__PHY_TDFI_PHY_WRDELAY_WOCLR                   0U
#define LPDDR4__DENALI_PHY_1321__PHY_TDFI_PHY_WRDELAY_WOSET                   0U
#define LPDDR4__PHY_TDFI_PHY_WRDELAY__REG DENALI_PHY_1321
#define LPDDR4__PHY_TDFI_PHY_WRDELAY__FLD LPDDR4__DENALI_PHY_1321__PHY_TDFI_PHY_WRDELAY

#define LPDDR4__DENALI_PHY_1322_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1322_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1322__PHY_PAD_FDBK_TERM_MASK              0x0003FFFFU
#define LPDDR4__DENALI_PHY_1322__PHY_PAD_FDBK_TERM_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1322__PHY_PAD_FDBK_TERM_WIDTH                     18U
#define LPDDR4__PHY_PAD_FDBK_TERM__REG DENALI_PHY_1322
#define LPDDR4__PHY_PAD_FDBK_TERM__FLD LPDDR4__DENALI_PHY_1322__PHY_PAD_FDBK_TERM

#define LPDDR4__DENALI_PHY_1323_READ_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_PHY_1323_WRITE_MASK                           0x0001FFFFU
#define LPDDR4__DENALI_PHY_1323__PHY_PAD_DATA_TERM_MASK              0x0001FFFFU
#define LPDDR4__DENALI_PHY_1323__PHY_PAD_DATA_TERM_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1323__PHY_PAD_DATA_TERM_WIDTH                     17U
#define LPDDR4__PHY_PAD_DATA_TERM__REG DENALI_PHY_1323
#define LPDDR4__PHY_PAD_DATA_TERM__FLD LPDDR4__DENALI_PHY_1323__PHY_PAD_DATA_TERM

#define LPDDR4__DENALI_PHY_1324_READ_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_PHY_1324_WRITE_MASK                           0x0001FFFFU
#define LPDDR4__DENALI_PHY_1324__PHY_PAD_DQS_TERM_MASK               0x0001FFFFU
#define LPDDR4__DENALI_PHY_1324__PHY_PAD_DQS_TERM_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1324__PHY_PAD_DQS_TERM_WIDTH                      17U
#define LPDDR4__PHY_PAD_DQS_TERM__REG DENALI_PHY_1324
#define LPDDR4__PHY_PAD_DQS_TERM__FLD LPDDR4__DENALI_PHY_1324__PHY_PAD_DQS_TERM

#define LPDDR4__DENALI_PHY_1325_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1325_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1325__PHY_PAD_ADDR_TERM_MASK              0x0003FFFFU
#define LPDDR4__DENALI_PHY_1325__PHY_PAD_ADDR_TERM_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1325__PHY_PAD_ADDR_TERM_WIDTH                     18U
#define LPDDR4__PHY_PAD_ADDR_TERM__REG DENALI_PHY_1325
#define LPDDR4__PHY_PAD_ADDR_TERM__FLD LPDDR4__DENALI_PHY_1325__PHY_PAD_ADDR_TERM

#define LPDDR4__DENALI_PHY_1326_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1326_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1326__PHY_PAD_CLK_TERM_MASK               0x0003FFFFU
#define LPDDR4__DENALI_PHY_1326__PHY_PAD_CLK_TERM_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1326__PHY_PAD_CLK_TERM_WIDTH                      18U
#define LPDDR4__PHY_PAD_CLK_TERM__REG DENALI_PHY_1326
#define LPDDR4__PHY_PAD_CLK_TERM__FLD LPDDR4__DENALI_PHY_1326__PHY_PAD_CLK_TERM

#define LPDDR4__DENALI_PHY_1327_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1327_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1327__PHY_PAD_ERR_TERM_MASK               0x0003FFFFU
#define LPDDR4__DENALI_PHY_1327__PHY_PAD_ERR_TERM_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1327__PHY_PAD_ERR_TERM_WIDTH                      18U
#define LPDDR4__PHY_PAD_ERR_TERM__REG DENALI_PHY_1327
#define LPDDR4__PHY_PAD_ERR_TERM__FLD LPDDR4__DENALI_PHY_1327__PHY_PAD_ERR_TERM

#define LPDDR4__DENALI_PHY_1328_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1328_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1328__PHY_PAD_CKE_TERM_MASK               0x0003FFFFU
#define LPDDR4__DENALI_PHY_1328__PHY_PAD_CKE_TERM_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1328__PHY_PAD_CKE_TERM_WIDTH                      18U
#define LPDDR4__PHY_PAD_CKE_TERM__REG DENALI_PHY_1328
#define LPDDR4__PHY_PAD_CKE_TERM__FLD LPDDR4__DENALI_PHY_1328__PHY_PAD_CKE_TERM

#define LPDDR4__DENALI_PHY_1329_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1329_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1329__PHY_PAD_RST_TERM_MASK               0x0003FFFFU
#define LPDDR4__DENALI_PHY_1329__PHY_PAD_RST_TERM_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1329__PHY_PAD_RST_TERM_WIDTH                      18U
#define LPDDR4__PHY_PAD_RST_TERM__REG DENALI_PHY_1329
#define LPDDR4__PHY_PAD_RST_TERM__FLD LPDDR4__DENALI_PHY_1329__PHY_PAD_RST_TERM

#define LPDDR4__DENALI_PHY_1330_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1330_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1330__PHY_PAD_CS_TERM_MASK                0x0003FFFFU
#define LPDDR4__DENALI_PHY_1330__PHY_PAD_CS_TERM_SHIFT                        0U
#define LPDDR4__DENALI_PHY_1330__PHY_PAD_CS_TERM_WIDTH                       18U
#define LPDDR4__PHY_PAD_CS_TERM__REG DENALI_PHY_1330
#define LPDDR4__PHY_PAD_CS_TERM__FLD LPDDR4__DENALI_PHY_1330__PHY_PAD_CS_TERM

#define LPDDR4__DENALI_PHY_1331_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1331_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1331__PHY_PAD_ODT_TERM_MASK               0x0003FFFFU
#define LPDDR4__DENALI_PHY_1331__PHY_PAD_ODT_TERM_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1331__PHY_PAD_ODT_TERM_WIDTH                      18U
#define LPDDR4__PHY_PAD_ODT_TERM__REG DENALI_PHY_1331
#define LPDDR4__PHY_PAD_ODT_TERM__FLD LPDDR4__DENALI_PHY_1331__PHY_PAD_ODT_TERM

#define LPDDR4__DENALI_PHY_1332_READ_MASK                            0x1FFF03FFU
#define LPDDR4__DENALI_PHY_1332_WRITE_MASK                           0x1FFF03FFU
#define LPDDR4__DENALI_PHY_1332__PHY_ADRCTL_RX_CAL_MASK              0x000003FFU
#define LPDDR4__DENALI_PHY_1332__PHY_ADRCTL_RX_CAL_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1332__PHY_ADRCTL_RX_CAL_WIDTH                     10U
#define LPDDR4__PHY_ADRCTL_RX_CAL__REG DENALI_PHY_1332
#define LPDDR4__PHY_ADRCTL_RX_CAL__FLD LPDDR4__DENALI_PHY_1332__PHY_ADRCTL_RX_CAL

#define LPDDR4__DENALI_PHY_1332__PHY_ADRCTL_LP3_RX_CAL_MASK          0x1FFF0000U
#define LPDDR4__DENALI_PHY_1332__PHY_ADRCTL_LP3_RX_CAL_SHIFT                 16U
#define LPDDR4__DENALI_PHY_1332__PHY_ADRCTL_LP3_RX_CAL_WIDTH                 13U
#define LPDDR4__PHY_ADRCTL_LP3_RX_CAL__REG DENALI_PHY_1332
#define LPDDR4__PHY_ADRCTL_LP3_RX_CAL__FLD LPDDR4__DENALI_PHY_1332__PHY_ADRCTL_LP3_RX_CAL

#define LPDDR4__DENALI_PHY_1333_READ_MASK                            0x00001FFFU
#define LPDDR4__DENALI_PHY_1333_WRITE_MASK                           0x00001FFFU
#define LPDDR4__DENALI_PHY_1333__PHY_CAL_MODE_0_MASK                 0x00001FFFU
#define LPDDR4__DENALI_PHY_1333__PHY_CAL_MODE_0_SHIFT                         0U
#define LPDDR4__DENALI_PHY_1333__PHY_CAL_MODE_0_WIDTH                        13U
#define LPDDR4__PHY_CAL_MODE_0__REG DENALI_PHY_1333
#define LPDDR4__PHY_CAL_MODE_0__FLD LPDDR4__DENALI_PHY_1333__PHY_CAL_MODE_0

#define LPDDR4__DENALI_PHY_1333__PHY_CAL_CLEAR_0_MASK                0x00010000U
#define LPDDR4__DENALI_PHY_1333__PHY_CAL_CLEAR_0_SHIFT                       16U
#define LPDDR4__DENALI_PHY_1333__PHY_CAL_CLEAR_0_WIDTH                        1U
#define LPDDR4__DENALI_PHY_1333__PHY_CAL_CLEAR_0_WOCLR                        0U
#define LPDDR4__DENALI_PHY_1333__PHY_CAL_CLEAR_0_WOSET                        0U
#define LPDDR4__PHY_CAL_CLEAR_0__REG DENALI_PHY_1333
#define LPDDR4__PHY_CAL_CLEAR_0__FLD LPDDR4__DENALI_PHY_1333__PHY_CAL_CLEAR_0

#define LPDDR4__DENALI_PHY_1333__PHY_CAL_START_0_MASK                0x01000000U
#define LPDDR4__DENALI_PHY_1333__PHY_CAL_START_0_SHIFT                       24U
#define LPDDR4__DENALI_PHY_1333__PHY_CAL_START_0_WIDTH                        1U
#define LPDDR4__DENALI_PHY_1333__PHY_CAL_START_0_WOCLR                        0U
#define LPDDR4__DENALI_PHY_1333__PHY_CAL_START_0_WOSET                        0U
#define LPDDR4__PHY_CAL_START_0__REG DENALI_PHY_1333
#define LPDDR4__PHY_CAL_START_0__FLD LPDDR4__DENALI_PHY_1333__PHY_CAL_START_0

#define LPDDR4__DENALI_PHY_1334_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1334_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1334__PHY_CAL_INTERVAL_COUNT_0_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1334__PHY_CAL_INTERVAL_COUNT_0_SHIFT               0U
#define LPDDR4__DENALI_PHY_1334__PHY_CAL_INTERVAL_COUNT_0_WIDTH              32U
#define LPDDR4__PHY_CAL_INTERVAL_COUNT_0__REG DENALI_PHY_1334
#define LPDDR4__PHY_CAL_INTERVAL_COUNT_0__FLD LPDDR4__DENALI_PHY_1334__PHY_CAL_INTERVAL_COUNT_0

#define LPDDR4__DENALI_PHY_1335_READ_MASK                            0x000007FFU
#define LPDDR4__DENALI_PHY_1335_WRITE_MASK                           0x000007FFU
#define LPDDR4__DENALI_PHY_1335__PHY_CAL_SAMPLE_WAIT_0_MASK          0x000000FFU
#define LPDDR4__DENALI_PHY_1335__PHY_CAL_SAMPLE_WAIT_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1335__PHY_CAL_SAMPLE_WAIT_0_WIDTH                  8U
#define LPDDR4__PHY_CAL_SAMPLE_WAIT_0__REG DENALI_PHY_1335
#define LPDDR4__PHY_CAL_SAMPLE_WAIT_0__FLD LPDDR4__DENALI_PHY_1335__PHY_CAL_SAMPLE_WAIT_0

#define LPDDR4__DENALI_PHY_1335__PHY_LP4_BOOT_CAL_CLK_SELECT_0_MASK  0x00000700U
#define LPDDR4__DENALI_PHY_1335__PHY_LP4_BOOT_CAL_CLK_SELECT_0_SHIFT          8U
#define LPDDR4__DENALI_PHY_1335__PHY_LP4_BOOT_CAL_CLK_SELECT_0_WIDTH          3U
#define LPDDR4__PHY_LP4_BOOT_CAL_CLK_SELECT_0__REG DENALI_PHY_1335
#define LPDDR4__PHY_LP4_BOOT_CAL_CLK_SELECT_0__FLD LPDDR4__DENALI_PHY_1335__PHY_LP4_BOOT_CAL_CLK_SELECT_0

#define LPDDR4__DENALI_PHY_1336_READ_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1336_WRITE_MASK                           0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1336__PHY_CAL_RESULT_OBS_0_MASK           0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1336__PHY_CAL_RESULT_OBS_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1336__PHY_CAL_RESULT_OBS_0_WIDTH                  24U
#define LPDDR4__PHY_CAL_RESULT_OBS_0__REG DENALI_PHY_1336
#define LPDDR4__PHY_CAL_RESULT_OBS_0__FLD LPDDR4__DENALI_PHY_1336__PHY_CAL_RESULT_OBS_0

#define LPDDR4__DENALI_PHY_1337_READ_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1337_WRITE_MASK                           0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1337__PHY_CAL_RESULT2_OBS_0_MASK          0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1337__PHY_CAL_RESULT2_OBS_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1337__PHY_CAL_RESULT2_OBS_0_WIDTH                 24U
#define LPDDR4__PHY_CAL_RESULT2_OBS_0__REG DENALI_PHY_1337
#define LPDDR4__PHY_CAL_RESULT2_OBS_0__FLD LPDDR4__DENALI_PHY_1337__PHY_CAL_RESULT2_OBS_0

#define LPDDR4__DENALI_PHY_1338_READ_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1338_WRITE_MASK                           0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1338__PHY_CAL_RESULT4_OBS_0_MASK          0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1338__PHY_CAL_RESULT4_OBS_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1338__PHY_CAL_RESULT4_OBS_0_WIDTH                 24U
#define LPDDR4__PHY_CAL_RESULT4_OBS_0__REG DENALI_PHY_1338
#define LPDDR4__PHY_CAL_RESULT4_OBS_0__FLD LPDDR4__DENALI_PHY_1338__PHY_CAL_RESULT4_OBS_0

#define LPDDR4__DENALI_PHY_1339_READ_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1339_WRITE_MASK                           0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1339__PHY_CAL_RESULT5_OBS_0_MASK          0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1339__PHY_CAL_RESULT5_OBS_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1339__PHY_CAL_RESULT5_OBS_0_WIDTH                 24U
#define LPDDR4__PHY_CAL_RESULT5_OBS_0__REG DENALI_PHY_1339
#define LPDDR4__PHY_CAL_RESULT5_OBS_0__FLD LPDDR4__DENALI_PHY_1339__PHY_CAL_RESULT5_OBS_0

#define LPDDR4__DENALI_PHY_1340_READ_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1340_WRITE_MASK                           0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1340__PHY_CAL_RESULT6_OBS_0_MASK          0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1340__PHY_CAL_RESULT6_OBS_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1340__PHY_CAL_RESULT6_OBS_0_WIDTH                 24U
#define LPDDR4__PHY_CAL_RESULT6_OBS_0__REG DENALI_PHY_1340
#define LPDDR4__PHY_CAL_RESULT6_OBS_0__FLD LPDDR4__DENALI_PHY_1340__PHY_CAL_RESULT6_OBS_0

#define LPDDR4__DENALI_PHY_1341_READ_MASK                            0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_1341_WRITE_MASK                           0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_1341__PHY_CAL_RESULT7_OBS_0_MASK          0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1341__PHY_CAL_RESULT7_OBS_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1341__PHY_CAL_RESULT7_OBS_0_WIDTH                 24U
#define LPDDR4__PHY_CAL_RESULT7_OBS_0__REG DENALI_PHY_1341
#define LPDDR4__PHY_CAL_RESULT7_OBS_0__FLD LPDDR4__DENALI_PHY_1341__PHY_CAL_RESULT7_OBS_0

#define LPDDR4__DENALI_PHY_1341__PHY_CAL_CPTR_CNT_0_MASK             0x7F000000U
#define LPDDR4__DENALI_PHY_1341__PHY_CAL_CPTR_CNT_0_SHIFT                    24U
#define LPDDR4__DENALI_PHY_1341__PHY_CAL_CPTR_CNT_0_WIDTH                     7U
#define LPDDR4__PHY_CAL_CPTR_CNT_0__REG DENALI_PHY_1341
#define LPDDR4__PHY_CAL_CPTR_CNT_0__FLD LPDDR4__DENALI_PHY_1341__PHY_CAL_CPTR_CNT_0

#define LPDDR4__DENALI_PHY_1342_READ_MASK                            0x01FFFFFFU
#define LPDDR4__DENALI_PHY_1342_WRITE_MASK                           0x01FFFFFFU
#define LPDDR4__DENALI_PHY_1342__PHY_CAL_PU_FINE_ADJ_0_MASK          0x000000FFU
#define LPDDR4__DENALI_PHY_1342__PHY_CAL_PU_FINE_ADJ_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1342__PHY_CAL_PU_FINE_ADJ_0_WIDTH                  8U
#define LPDDR4__PHY_CAL_PU_FINE_ADJ_0__REG DENALI_PHY_1342
#define LPDDR4__PHY_CAL_PU_FINE_ADJ_0__FLD LPDDR4__DENALI_PHY_1342__PHY_CAL_PU_FINE_ADJ_0

#define LPDDR4__DENALI_PHY_1342__PHY_CAL_PD_FINE_ADJ_0_MASK          0x0000FF00U
#define LPDDR4__DENALI_PHY_1342__PHY_CAL_PD_FINE_ADJ_0_SHIFT                  8U
#define LPDDR4__DENALI_PHY_1342__PHY_CAL_PD_FINE_ADJ_0_WIDTH                  8U
#define LPDDR4__PHY_CAL_PD_FINE_ADJ_0__REG DENALI_PHY_1342
#define LPDDR4__PHY_CAL_PD_FINE_ADJ_0__FLD LPDDR4__DENALI_PHY_1342__PHY_CAL_PD_FINE_ADJ_0

#define LPDDR4__DENALI_PHY_1342__PHY_CAL_RCV_FINE_ADJ_0_MASK         0x00FF0000U
#define LPDDR4__DENALI_PHY_1342__PHY_CAL_RCV_FINE_ADJ_0_SHIFT                16U
#define LPDDR4__DENALI_PHY_1342__PHY_CAL_RCV_FINE_ADJ_0_WIDTH                 8U
#define LPDDR4__PHY_CAL_RCV_FINE_ADJ_0__REG DENALI_PHY_1342
#define LPDDR4__PHY_CAL_RCV_FINE_ADJ_0__FLD LPDDR4__DENALI_PHY_1342__PHY_CAL_RCV_FINE_ADJ_0

#define LPDDR4__DENALI_PHY_1342__PHY_CAL_DBG_CFG_0_MASK              0x01000000U
#define LPDDR4__DENALI_PHY_1342__PHY_CAL_DBG_CFG_0_SHIFT                     24U
#define LPDDR4__DENALI_PHY_1342__PHY_CAL_DBG_CFG_0_WIDTH                      1U
#define LPDDR4__DENALI_PHY_1342__PHY_CAL_DBG_CFG_0_WOCLR                      0U
#define LPDDR4__DENALI_PHY_1342__PHY_CAL_DBG_CFG_0_WOSET                      0U
#define LPDDR4__PHY_CAL_DBG_CFG_0__REG DENALI_PHY_1342
#define LPDDR4__PHY_CAL_DBG_CFG_0__FLD LPDDR4__DENALI_PHY_1342__PHY_CAL_DBG_CFG_0

#define LPDDR4__DENALI_PHY_1343__SC_PHY_PAD_DBG_CONT_0_MASK          0x00000001U
#define LPDDR4__DENALI_PHY_1343__SC_PHY_PAD_DBG_CONT_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1343__SC_PHY_PAD_DBG_CONT_0_WIDTH                  1U
#define LPDDR4__DENALI_PHY_1343__SC_PHY_PAD_DBG_CONT_0_WOCLR                  0U
#define LPDDR4__DENALI_PHY_1343__SC_PHY_PAD_DBG_CONT_0_WOSET                  0U
#define LPDDR4__SC_PHY_PAD_DBG_CONT_0__REG DENALI_PHY_1343
#define LPDDR4__SC_PHY_PAD_DBG_CONT_0__FLD LPDDR4__DENALI_PHY_1343__SC_PHY_PAD_DBG_CONT_0

#define LPDDR4__DENALI_PHY_1344_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1344_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1344__PHY_CAL_RESULT3_OBS_0_MASK          0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1344__PHY_CAL_RESULT3_OBS_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1344__PHY_CAL_RESULT3_OBS_0_WIDTH                 32U
#define LPDDR4__PHY_CAL_RESULT3_OBS_0__REG DENALI_PHY_1344
#define LPDDR4__PHY_CAL_RESULT3_OBS_0__FLD LPDDR4__DENALI_PHY_1344__PHY_CAL_RESULT3_OBS_0

#define LPDDR4__DENALI_PHY_1345_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1345_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1345__PHY_ADRCTL_PVT_MAP_0_MASK           0x000000FFU
#define LPDDR4__DENALI_PHY_1345__PHY_ADRCTL_PVT_MAP_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1345__PHY_ADRCTL_PVT_MAP_0_WIDTH                   8U
#define LPDDR4__PHY_ADRCTL_PVT_MAP_0__REG DENALI_PHY_1345
#define LPDDR4__PHY_ADRCTL_PVT_MAP_0__FLD LPDDR4__DENALI_PHY_1345__PHY_ADRCTL_PVT_MAP_0

#define LPDDR4__DENALI_PHY_1345__PHY_CAL_SLOPE_ADJ_0_MASK            0x0FFFFF00U
#define LPDDR4__DENALI_PHY_1345__PHY_CAL_SLOPE_ADJ_0_SHIFT                    8U
#define LPDDR4__DENALI_PHY_1345__PHY_CAL_SLOPE_ADJ_0_WIDTH                   20U
#define LPDDR4__PHY_CAL_SLOPE_ADJ_0__REG DENALI_PHY_1345
#define LPDDR4__PHY_CAL_SLOPE_ADJ_0__FLD LPDDR4__DENALI_PHY_1345__PHY_CAL_SLOPE_ADJ_0

#define LPDDR4__DENALI_PHY_1346_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1346_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1346__PHY_CAL_SLOPE_ADJ_PASS2_0_MASK      0x000FFFFFU
#define LPDDR4__DENALI_PHY_1346__PHY_CAL_SLOPE_ADJ_PASS2_0_SHIFT              0U
#define LPDDR4__DENALI_PHY_1346__PHY_CAL_SLOPE_ADJ_PASS2_0_WIDTH             20U
#define LPDDR4__PHY_CAL_SLOPE_ADJ_PASS2_0__REG DENALI_PHY_1346
#define LPDDR4__PHY_CAL_SLOPE_ADJ_PASS2_0__FLD LPDDR4__DENALI_PHY_1346__PHY_CAL_SLOPE_ADJ_PASS2_0

#define LPDDR4__DENALI_PHY_1347_READ_MASK                            0x01FFFFFFU
#define LPDDR4__DENALI_PHY_1347_WRITE_MASK                           0x01FFFFFFU
#define LPDDR4__DENALI_PHY_1347__PHY_CAL_TWO_PASS_CFG_0_MASK         0x01FFFFFFU
#define LPDDR4__DENALI_PHY_1347__PHY_CAL_TWO_PASS_CFG_0_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1347__PHY_CAL_TWO_PASS_CFG_0_WIDTH                25U
#define LPDDR4__PHY_CAL_TWO_PASS_CFG_0__REG DENALI_PHY_1347
#define LPDDR4__PHY_CAL_TWO_PASS_CFG_0__FLD LPDDR4__DENALI_PHY_1347__PHY_CAL_TWO_PASS_CFG_0

#define LPDDR4__DENALI_PHY_1348_READ_MASK                            0x3F7FFFFFU
#define LPDDR4__DENALI_PHY_1348_WRITE_MASK                           0x3F7FFFFFU
#define LPDDR4__DENALI_PHY_1348__PHY_CAL_SW_CAL_CFG_0_MASK           0x007FFFFFU
#define LPDDR4__DENALI_PHY_1348__PHY_CAL_SW_CAL_CFG_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1348__PHY_CAL_SW_CAL_CFG_0_WIDTH                  23U
#define LPDDR4__PHY_CAL_SW_CAL_CFG_0__REG DENALI_PHY_1348
#define LPDDR4__PHY_CAL_SW_CAL_CFG_0__FLD LPDDR4__DENALI_PHY_1348__PHY_CAL_SW_CAL_CFG_0

#define LPDDR4__DENALI_PHY_1348__PHY_CAL_RANGE_PASS1_PU_MAX_DELTA_0_MASK 0x3F000000U
#define LPDDR4__DENALI_PHY_1348__PHY_CAL_RANGE_PASS1_PU_MAX_DELTA_0_SHIFT    24U
#define LPDDR4__DENALI_PHY_1348__PHY_CAL_RANGE_PASS1_PU_MAX_DELTA_0_WIDTH     6U
#define LPDDR4__PHY_CAL_RANGE_PASS1_PU_MAX_DELTA_0__REG DENALI_PHY_1348
#define LPDDR4__PHY_CAL_RANGE_PASS1_PU_MAX_DELTA_0__FLD LPDDR4__DENALI_PHY_1348__PHY_CAL_RANGE_PASS1_PU_MAX_DELTA_0

#define LPDDR4__DENALI_PHY_1349_READ_MASK                            0x3F3F1F3FU
#define LPDDR4__DENALI_PHY_1349_WRITE_MASK                           0x3F3F1F3FU
#define LPDDR4__DENALI_PHY_1349__PHY_CAL_RANGE_PASS1_PD_MAX_DELTA_0_MASK 0x0000003FU
#define LPDDR4__DENALI_PHY_1349__PHY_CAL_RANGE_PASS1_PD_MAX_DELTA_0_SHIFT     0U
#define LPDDR4__DENALI_PHY_1349__PHY_CAL_RANGE_PASS1_PD_MAX_DELTA_0_WIDTH     6U
#define LPDDR4__PHY_CAL_RANGE_PASS1_PD_MAX_DELTA_0__REG DENALI_PHY_1349
#define LPDDR4__PHY_CAL_RANGE_PASS1_PD_MAX_DELTA_0__FLD LPDDR4__DENALI_PHY_1349__PHY_CAL_RANGE_PASS1_PD_MAX_DELTA_0

#define LPDDR4__DENALI_PHY_1349__PHY_CAL_RANGE_PASS1_RX_MAX_DELTA_0_MASK 0x00001F00U
#define LPDDR4__DENALI_PHY_1349__PHY_CAL_RANGE_PASS1_RX_MAX_DELTA_0_SHIFT     8U
#define LPDDR4__DENALI_PHY_1349__PHY_CAL_RANGE_PASS1_RX_MAX_DELTA_0_WIDTH     5U
#define LPDDR4__PHY_CAL_RANGE_PASS1_RX_MAX_DELTA_0__REG DENALI_PHY_1349
#define LPDDR4__PHY_CAL_RANGE_PASS1_RX_MAX_DELTA_0__FLD LPDDR4__DENALI_PHY_1349__PHY_CAL_RANGE_PASS1_RX_MAX_DELTA_0

#define LPDDR4__DENALI_PHY_1349__PHY_CAL_RANGE_PASS2_PU_MAX_DELTA_0_MASK 0x003F0000U
#define LPDDR4__DENALI_PHY_1349__PHY_CAL_RANGE_PASS2_PU_MAX_DELTA_0_SHIFT    16U
#define LPDDR4__DENALI_PHY_1349__PHY_CAL_RANGE_PASS2_PU_MAX_DELTA_0_WIDTH     6U
#define LPDDR4__PHY_CAL_RANGE_PASS2_PU_MAX_DELTA_0__REG DENALI_PHY_1349
#define LPDDR4__PHY_CAL_RANGE_PASS2_PU_MAX_DELTA_0__FLD LPDDR4__DENALI_PHY_1349__PHY_CAL_RANGE_PASS2_PU_MAX_DELTA_0

#define LPDDR4__DENALI_PHY_1349__PHY_CAL_RANGE_PASS2_PD_MAX_DELTA_0_MASK 0x3F000000U
#define LPDDR4__DENALI_PHY_1349__PHY_CAL_RANGE_PASS2_PD_MAX_DELTA_0_SHIFT    24U
#define LPDDR4__DENALI_PHY_1349__PHY_CAL_RANGE_PASS2_PD_MAX_DELTA_0_WIDTH     6U
#define LPDDR4__PHY_CAL_RANGE_PASS2_PD_MAX_DELTA_0__REG DENALI_PHY_1349
#define LPDDR4__PHY_CAL_RANGE_PASS2_PD_MAX_DELTA_0__FLD LPDDR4__DENALI_PHY_1349__PHY_CAL_RANGE_PASS2_PD_MAX_DELTA_0

#define LPDDR4__DENALI_PHY_1350_READ_MASK                            0x1F3F3F1FU
#define LPDDR4__DENALI_PHY_1350_WRITE_MASK                           0x1F3F3F1FU
#define LPDDR4__DENALI_PHY_1350__PHY_CAL_RANGE_PASS2_RX_MAX_DELTA_0_MASK 0x0000001FU
#define LPDDR4__DENALI_PHY_1350__PHY_CAL_RANGE_PASS2_RX_MAX_DELTA_0_SHIFT     0U
#define LPDDR4__DENALI_PHY_1350__PHY_CAL_RANGE_PASS2_RX_MAX_DELTA_0_WIDTH     5U
#define LPDDR4__PHY_CAL_RANGE_PASS2_RX_MAX_DELTA_0__REG DENALI_PHY_1350
#define LPDDR4__PHY_CAL_RANGE_PASS2_RX_MAX_DELTA_0__FLD LPDDR4__DENALI_PHY_1350__PHY_CAL_RANGE_PASS2_RX_MAX_DELTA_0

#define LPDDR4__DENALI_PHY_1350__PHY_CAL_RANGE_PASS1_PU_MIN_DELTA_0_MASK 0x00003F00U
#define LPDDR4__DENALI_PHY_1350__PHY_CAL_RANGE_PASS1_PU_MIN_DELTA_0_SHIFT     8U
#define LPDDR4__DENALI_PHY_1350__PHY_CAL_RANGE_PASS1_PU_MIN_DELTA_0_WIDTH     6U
#define LPDDR4__PHY_CAL_RANGE_PASS1_PU_MIN_DELTA_0__REG DENALI_PHY_1350
#define LPDDR4__PHY_CAL_RANGE_PASS1_PU_MIN_DELTA_0__FLD LPDDR4__DENALI_PHY_1350__PHY_CAL_RANGE_PASS1_PU_MIN_DELTA_0

#define LPDDR4__DENALI_PHY_1350__PHY_CAL_RANGE_PASS1_PD_MIN_DELTA_0_MASK 0x003F0000U
#define LPDDR4__DENALI_PHY_1350__PHY_CAL_RANGE_PASS1_PD_MIN_DELTA_0_SHIFT    16U
#define LPDDR4__DENALI_PHY_1350__PHY_CAL_RANGE_PASS1_PD_MIN_DELTA_0_WIDTH     6U
#define LPDDR4__PHY_CAL_RANGE_PASS1_PD_MIN_DELTA_0__REG DENALI_PHY_1350
#define LPDDR4__PHY_CAL_RANGE_PASS1_PD_MIN_DELTA_0__FLD LPDDR4__DENALI_PHY_1350__PHY_CAL_RANGE_PASS1_PD_MIN_DELTA_0

#define LPDDR4__DENALI_PHY_1350__PHY_CAL_RANGE_PASS1_RX_MIN_DELTA_0_MASK 0x1F000000U
#define LPDDR4__DENALI_PHY_1350__PHY_CAL_RANGE_PASS1_RX_MIN_DELTA_0_SHIFT    24U
#define LPDDR4__DENALI_PHY_1350__PHY_CAL_RANGE_PASS1_RX_MIN_DELTA_0_WIDTH     5U
#define LPDDR4__PHY_CAL_RANGE_PASS1_RX_MIN_DELTA_0__REG DENALI_PHY_1350
#define LPDDR4__PHY_CAL_RANGE_PASS1_RX_MIN_DELTA_0__FLD LPDDR4__DENALI_PHY_1350__PHY_CAL_RANGE_PASS1_RX_MIN_DELTA_0

#define LPDDR4__DENALI_PHY_1351_READ_MASK                            0x001F3F3FU
#define LPDDR4__DENALI_PHY_1351_WRITE_MASK                           0x001F3F3FU
#define LPDDR4__DENALI_PHY_1351__PHY_CAL_RANGE_PASS2_PU_MIN_DELTA_0_MASK 0x0000003FU
#define LPDDR4__DENALI_PHY_1351__PHY_CAL_RANGE_PASS2_PU_MIN_DELTA_0_SHIFT     0U
#define LPDDR4__DENALI_PHY_1351__PHY_CAL_RANGE_PASS2_PU_MIN_DELTA_0_WIDTH     6U
#define LPDDR4__PHY_CAL_RANGE_PASS2_PU_MIN_DELTA_0__REG DENALI_PHY_1351
#define LPDDR4__PHY_CAL_RANGE_PASS2_PU_MIN_DELTA_0__FLD LPDDR4__DENALI_PHY_1351__PHY_CAL_RANGE_PASS2_PU_MIN_DELTA_0

#define LPDDR4__DENALI_PHY_1351__PHY_CAL_RANGE_PASS2_PD_MIN_DELTA_0_MASK 0x00003F00U
#define LPDDR4__DENALI_PHY_1351__PHY_CAL_RANGE_PASS2_PD_MIN_DELTA_0_SHIFT     8U
#define LPDDR4__DENALI_PHY_1351__PHY_CAL_RANGE_PASS2_PD_MIN_DELTA_0_WIDTH     6U
#define LPDDR4__PHY_CAL_RANGE_PASS2_PD_MIN_DELTA_0__REG DENALI_PHY_1351
#define LPDDR4__PHY_CAL_RANGE_PASS2_PD_MIN_DELTA_0__FLD LPDDR4__DENALI_PHY_1351__PHY_CAL_RANGE_PASS2_PD_MIN_DELTA_0

#define LPDDR4__DENALI_PHY_1351__PHY_CAL_RANGE_PASS2_RX_MIN_DELTA_0_MASK 0x001F0000U
#define LPDDR4__DENALI_PHY_1351__PHY_CAL_RANGE_PASS2_RX_MIN_DELTA_0_SHIFT    16U
#define LPDDR4__DENALI_PHY_1351__PHY_CAL_RANGE_PASS2_RX_MIN_DELTA_0_WIDTH     5U
#define LPDDR4__PHY_CAL_RANGE_PASS2_RX_MIN_DELTA_0__REG DENALI_PHY_1351
#define LPDDR4__PHY_CAL_RANGE_PASS2_RX_MIN_DELTA_0__FLD LPDDR4__DENALI_PHY_1351__PHY_CAL_RANGE_PASS2_RX_MIN_DELTA_0

#define LPDDR4__DENALI_PHY_1352_READ_MASK                            0x0000FFFFU
#define LPDDR4__DENALI_PHY_1352_WRITE_MASK                           0x0000FFFFU
#define LPDDR4__DENALI_PHY_1352__PHY_PAD_ATB_CTRL_MASK               0x0000FFFFU
#define LPDDR4__DENALI_PHY_1352__PHY_PAD_ATB_CTRL_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1352__PHY_PAD_ATB_CTRL_WIDTH                      16U
#define LPDDR4__PHY_PAD_ATB_CTRL__REG DENALI_PHY_1352
#define LPDDR4__PHY_PAD_ATB_CTRL__FLD LPDDR4__DENALI_PHY_1352__PHY_PAD_ATB_CTRL

#define LPDDR4__DENALI_PHY_1352__PHY_ADRCTL_MANUAL_UPDATE_MASK       0x00010000U
#define LPDDR4__DENALI_PHY_1352__PHY_ADRCTL_MANUAL_UPDATE_SHIFT              16U
#define LPDDR4__DENALI_PHY_1352__PHY_ADRCTL_MANUAL_UPDATE_WIDTH               1U
#define LPDDR4__DENALI_PHY_1352__PHY_ADRCTL_MANUAL_UPDATE_WOCLR               0U
#define LPDDR4__DENALI_PHY_1352__PHY_ADRCTL_MANUAL_UPDATE_WOSET               0U
#define LPDDR4__PHY_ADRCTL_MANUAL_UPDATE__REG DENALI_PHY_1352
#define LPDDR4__PHY_ADRCTL_MANUAL_UPDATE__FLD LPDDR4__DENALI_PHY_1352__PHY_ADRCTL_MANUAL_UPDATE

#define LPDDR4__DENALI_PHY_1352__PHY_AC_LPBK_ERR_CLEAR_MASK          0x01000000U
#define LPDDR4__DENALI_PHY_1352__PHY_AC_LPBK_ERR_CLEAR_SHIFT                 24U
#define LPDDR4__DENALI_PHY_1352__PHY_AC_LPBK_ERR_CLEAR_WIDTH                  1U
#define LPDDR4__DENALI_PHY_1352__PHY_AC_LPBK_ERR_CLEAR_WOCLR                  0U
#define LPDDR4__DENALI_PHY_1352__PHY_AC_LPBK_ERR_CLEAR_WOSET                  0U
#define LPDDR4__PHY_AC_LPBK_ERR_CLEAR__REG DENALI_PHY_1352
#define LPDDR4__PHY_AC_LPBK_ERR_CLEAR__FLD LPDDR4__DENALI_PHY_1352__PHY_AC_LPBK_ERR_CLEAR

#define LPDDR4__DENALI_PHY_1353_READ_MASK                            0x01FF0F03U
#define LPDDR4__DENALI_PHY_1353_WRITE_MASK                           0x01FF0F03U
#define LPDDR4__DENALI_PHY_1353__PHY_AC_LPBK_OBS_SELECT_MASK         0x00000003U
#define LPDDR4__DENALI_PHY_1353__PHY_AC_LPBK_OBS_SELECT_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1353__PHY_AC_LPBK_OBS_SELECT_WIDTH                 2U
#define LPDDR4__PHY_AC_LPBK_OBS_SELECT__REG DENALI_PHY_1353
#define LPDDR4__PHY_AC_LPBK_OBS_SELECT__FLD LPDDR4__DENALI_PHY_1353__PHY_AC_LPBK_OBS_SELECT

#define LPDDR4__DENALI_PHY_1353__PHY_AC_LPBK_ENABLE_MASK             0x00000F00U
#define LPDDR4__DENALI_PHY_1353__PHY_AC_LPBK_ENABLE_SHIFT                     8U
#define LPDDR4__DENALI_PHY_1353__PHY_AC_LPBK_ENABLE_WIDTH                     4U
#define LPDDR4__PHY_AC_LPBK_ENABLE__REG DENALI_PHY_1353
#define LPDDR4__PHY_AC_LPBK_ENABLE__FLD LPDDR4__DENALI_PHY_1353__PHY_AC_LPBK_ENABLE

#define LPDDR4__DENALI_PHY_1353__PHY_AC_LPBK_CONTROL_MASK            0x01FF0000U
#define LPDDR4__DENALI_PHY_1353__PHY_AC_LPBK_CONTROL_SHIFT                   16U
#define LPDDR4__DENALI_PHY_1353__PHY_AC_LPBK_CONTROL_WIDTH                    9U
#define LPDDR4__PHY_AC_LPBK_CONTROL__REG DENALI_PHY_1353
#define LPDDR4__PHY_AC_LPBK_CONTROL__FLD LPDDR4__DENALI_PHY_1353__PHY_AC_LPBK_CONTROL

#define LPDDR4__DENALI_PHY_1354_READ_MASK                            0x00000F7FU
#define LPDDR4__DENALI_PHY_1354_WRITE_MASK                           0x00000F7FU
#define LPDDR4__DENALI_PHY_1354__PHY_AC_PRBS_PATTERN_START_MASK      0x0000007FU
#define LPDDR4__DENALI_PHY_1354__PHY_AC_PRBS_PATTERN_START_SHIFT              0U
#define LPDDR4__DENALI_PHY_1354__PHY_AC_PRBS_PATTERN_START_WIDTH              7U
#define LPDDR4__PHY_AC_PRBS_PATTERN_START__REG DENALI_PHY_1354
#define LPDDR4__PHY_AC_PRBS_PATTERN_START__FLD LPDDR4__DENALI_PHY_1354__PHY_AC_PRBS_PATTERN_START

#define LPDDR4__DENALI_PHY_1354__PHY_AC_PRBS_PATTERN_MASK_MASK       0x00000F00U
#define LPDDR4__DENALI_PHY_1354__PHY_AC_PRBS_PATTERN_MASK_SHIFT               8U
#define LPDDR4__DENALI_PHY_1354__PHY_AC_PRBS_PATTERN_MASK_WIDTH               4U
#define LPDDR4__PHY_AC_PRBS_PATTERN_MASK__REG DENALI_PHY_1354
#define LPDDR4__PHY_AC_PRBS_PATTERN_MASK__FLD LPDDR4__DENALI_PHY_1354__PHY_AC_PRBS_PATTERN_MASK

#define LPDDR4__DENALI_PHY_1355_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1355_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1355__PHY_AC_LPBK_RESULT_OBS_MASK         0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1355__PHY_AC_LPBK_RESULT_OBS_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1355__PHY_AC_LPBK_RESULT_OBS_WIDTH                32U
#define LPDDR4__PHY_AC_LPBK_RESULT_OBS__REG DENALI_PHY_1355
#define LPDDR4__PHY_AC_LPBK_RESULT_OBS__FLD LPDDR4__DENALI_PHY_1355__PHY_AC_LPBK_RESULT_OBS

#define LPDDR4__DENALI_PHY_1356_READ_MASK                            0x003F0101U
#define LPDDR4__DENALI_PHY_1356_WRITE_MASK                           0x003F0101U
#define LPDDR4__DENALI_PHY_1356__PHY_AC_CLK_LPBK_OBS_SELECT_MASK     0x00000001U
#define LPDDR4__DENALI_PHY_1356__PHY_AC_CLK_LPBK_OBS_SELECT_SHIFT             0U
#define LPDDR4__DENALI_PHY_1356__PHY_AC_CLK_LPBK_OBS_SELECT_WIDTH             1U
#define LPDDR4__DENALI_PHY_1356__PHY_AC_CLK_LPBK_OBS_SELECT_WOCLR             0U
#define LPDDR4__DENALI_PHY_1356__PHY_AC_CLK_LPBK_OBS_SELECT_WOSET             0U
#define LPDDR4__PHY_AC_CLK_LPBK_OBS_SELECT__REG DENALI_PHY_1356
#define LPDDR4__PHY_AC_CLK_LPBK_OBS_SELECT__FLD LPDDR4__DENALI_PHY_1356__PHY_AC_CLK_LPBK_OBS_SELECT

#define LPDDR4__DENALI_PHY_1356__PHY_AC_CLK_LPBK_ENABLE_MASK         0x00000100U
#define LPDDR4__DENALI_PHY_1356__PHY_AC_CLK_LPBK_ENABLE_SHIFT                 8U
#define LPDDR4__DENALI_PHY_1356__PHY_AC_CLK_LPBK_ENABLE_WIDTH                 1U
#define LPDDR4__DENALI_PHY_1356__PHY_AC_CLK_LPBK_ENABLE_WOCLR                 0U
#define LPDDR4__DENALI_PHY_1356__PHY_AC_CLK_LPBK_ENABLE_WOSET                 0U
#define LPDDR4__PHY_AC_CLK_LPBK_ENABLE__REG DENALI_PHY_1356
#define LPDDR4__PHY_AC_CLK_LPBK_ENABLE__FLD LPDDR4__DENALI_PHY_1356__PHY_AC_CLK_LPBK_ENABLE

#define LPDDR4__DENALI_PHY_1356__PHY_AC_CLK_LPBK_CONTROL_MASK        0x003F0000U
#define LPDDR4__DENALI_PHY_1356__PHY_AC_CLK_LPBK_CONTROL_SHIFT               16U
#define LPDDR4__DENALI_PHY_1356__PHY_AC_CLK_LPBK_CONTROL_WIDTH                6U
#define LPDDR4__PHY_AC_CLK_LPBK_CONTROL__REG DENALI_PHY_1356
#define LPDDR4__PHY_AC_CLK_LPBK_CONTROL__FLD LPDDR4__DENALI_PHY_1356__PHY_AC_CLK_LPBK_CONTROL

#define LPDDR4__DENALI_PHY_1357_READ_MASK                            0x0101FFFFU
#define LPDDR4__DENALI_PHY_1357_WRITE_MASK                           0x0101FFFFU
#define LPDDR4__DENALI_PHY_1357__PHY_AC_CLK_LPBK_RESULT_OBS_MASK     0x0000FFFFU
#define LPDDR4__DENALI_PHY_1357__PHY_AC_CLK_LPBK_RESULT_OBS_SHIFT             0U
#define LPDDR4__DENALI_PHY_1357__PHY_AC_CLK_LPBK_RESULT_OBS_WIDTH            16U
#define LPDDR4__PHY_AC_CLK_LPBK_RESULT_OBS__REG DENALI_PHY_1357
#define LPDDR4__PHY_AC_CLK_LPBK_RESULT_OBS__FLD LPDDR4__DENALI_PHY_1357__PHY_AC_CLK_LPBK_RESULT_OBS

#define LPDDR4__DENALI_PHY_1357__PHY_AC_PWR_RDC_DISABLE_MASK         0x00010000U
#define LPDDR4__DENALI_PHY_1357__PHY_AC_PWR_RDC_DISABLE_SHIFT                16U
#define LPDDR4__DENALI_PHY_1357__PHY_AC_PWR_RDC_DISABLE_WIDTH                 1U
#define LPDDR4__DENALI_PHY_1357__PHY_AC_PWR_RDC_DISABLE_WOCLR                 0U
#define LPDDR4__DENALI_PHY_1357__PHY_AC_PWR_RDC_DISABLE_WOSET                 0U
#define LPDDR4__PHY_AC_PWR_RDC_DISABLE__REG DENALI_PHY_1357
#define LPDDR4__PHY_AC_PWR_RDC_DISABLE__FLD LPDDR4__DENALI_PHY_1357__PHY_AC_PWR_RDC_DISABLE

#define LPDDR4__DENALI_PHY_1357__PHY_TOP_PWR_RDC_DISABLE_MASK        0x01000000U
#define LPDDR4__DENALI_PHY_1357__PHY_TOP_PWR_RDC_DISABLE_SHIFT               24U
#define LPDDR4__DENALI_PHY_1357__PHY_TOP_PWR_RDC_DISABLE_WIDTH                1U
#define LPDDR4__DENALI_PHY_1357__PHY_TOP_PWR_RDC_DISABLE_WOCLR                0U
#define LPDDR4__DENALI_PHY_1357__PHY_TOP_PWR_RDC_DISABLE_WOSET                0U
#define LPDDR4__PHY_TOP_PWR_RDC_DISABLE__REG DENALI_PHY_1357
#define LPDDR4__PHY_TOP_PWR_RDC_DISABLE__FLD LPDDR4__DENALI_PHY_1357__PHY_TOP_PWR_RDC_DISABLE

#define LPDDR4__DENALI_PHY_1358_READ_MASK                            0x00000001U
#define LPDDR4__DENALI_PHY_1358_WRITE_MASK                           0x00000001U
#define LPDDR4__DENALI_PHY_1358__PHY_AC_SLV_DLY_CTRL_GATE_DISABLE_MASK 0x00000001U
#define LPDDR4__DENALI_PHY_1358__PHY_AC_SLV_DLY_CTRL_GATE_DISABLE_SHIFT       0U
#define LPDDR4__DENALI_PHY_1358__PHY_AC_SLV_DLY_CTRL_GATE_DISABLE_WIDTH       1U
#define LPDDR4__DENALI_PHY_1358__PHY_AC_SLV_DLY_CTRL_GATE_DISABLE_WOCLR       0U
#define LPDDR4__DENALI_PHY_1358__PHY_AC_SLV_DLY_CTRL_GATE_DISABLE_WOSET       0U
#define LPDDR4__PHY_AC_SLV_DLY_CTRL_GATE_DISABLE__REG DENALI_PHY_1358
#define LPDDR4__PHY_AC_SLV_DLY_CTRL_GATE_DISABLE__FLD LPDDR4__DENALI_PHY_1358__PHY_AC_SLV_DLY_CTRL_GATE_DISABLE

#define LPDDR4__DENALI_PHY_1359_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1359_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1359__PHY_DATA_BYTE_ORDER_SEL_MASK        0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1359__PHY_DATA_BYTE_ORDER_SEL_SHIFT                0U
#define LPDDR4__DENALI_PHY_1359__PHY_DATA_BYTE_ORDER_SEL_WIDTH               32U
#define LPDDR4__PHY_DATA_BYTE_ORDER_SEL__REG DENALI_PHY_1359
#define LPDDR4__PHY_DATA_BYTE_ORDER_SEL__FLD LPDDR4__DENALI_PHY_1359__PHY_DATA_BYTE_ORDER_SEL

#define LPDDR4__DENALI_PHY_1360_READ_MASK                            0x03071FFFU
#define LPDDR4__DENALI_PHY_1360_WRITE_MASK                           0x03071FFFU
#define LPDDR4__DENALI_PHY_1360__PHY_DATA_BYTE_ORDER_SEL_HIGH_MASK   0x000000FFU
#define LPDDR4__DENALI_PHY_1360__PHY_DATA_BYTE_ORDER_SEL_HIGH_SHIFT           0U
#define LPDDR4__DENALI_PHY_1360__PHY_DATA_BYTE_ORDER_SEL_HIGH_WIDTH           8U
#define LPDDR4__PHY_DATA_BYTE_ORDER_SEL_HIGH__REG DENALI_PHY_1360
#define LPDDR4__PHY_DATA_BYTE_ORDER_SEL_HIGH__FLD LPDDR4__DENALI_PHY_1360__PHY_DATA_BYTE_ORDER_SEL_HIGH

#define LPDDR4__DENALI_PHY_1360__PHY_CALVL_DEVICE_MAP_MASK           0x00001F00U
#define LPDDR4__DENALI_PHY_1360__PHY_CALVL_DEVICE_MAP_SHIFT                   8U
#define LPDDR4__DENALI_PHY_1360__PHY_CALVL_DEVICE_MAP_WIDTH                   5U
#define LPDDR4__PHY_CALVL_DEVICE_MAP__REG DENALI_PHY_1360
#define LPDDR4__PHY_CALVL_DEVICE_MAP__FLD LPDDR4__DENALI_PHY_1360__PHY_CALVL_DEVICE_MAP

#define LPDDR4__DENALI_PHY_1360__PHY_ADR_DISABLE_MASK                0x00070000U
#define LPDDR4__DENALI_PHY_1360__PHY_ADR_DISABLE_SHIFT                       16U
#define LPDDR4__DENALI_PHY_1360__PHY_ADR_DISABLE_WIDTH                        3U
#define LPDDR4__PHY_ADR_DISABLE__REG DENALI_PHY_1360
#define LPDDR4__PHY_ADR_DISABLE__FLD LPDDR4__DENALI_PHY_1360__PHY_ADR_DISABLE

#define LPDDR4__DENALI_PHY_1360__PHY_ADRCTL_MSTR_DLY_ENC_SEL_0_MASK  0x03000000U
#define LPDDR4__DENALI_PHY_1360__PHY_ADRCTL_MSTR_DLY_ENC_SEL_0_SHIFT         24U
#define LPDDR4__DENALI_PHY_1360__PHY_ADRCTL_MSTR_DLY_ENC_SEL_0_WIDTH          2U
#define LPDDR4__PHY_ADRCTL_MSTR_DLY_ENC_SEL_0__REG DENALI_PHY_1360
#define LPDDR4__PHY_ADRCTL_MSTR_DLY_ENC_SEL_0__FLD LPDDR4__DENALI_PHY_1360__PHY_ADRCTL_MSTR_DLY_ENC_SEL_0

#define LPDDR4__DENALI_PHY_1361_READ_MASK                            0x00030303U
#define LPDDR4__DENALI_PHY_1361_WRITE_MASK                           0x00030303U
#define LPDDR4__DENALI_PHY_1361__PHY_ADRCTL_MSTR_DLY_ENC_SEL_1_MASK  0x00000003U
#define LPDDR4__DENALI_PHY_1361__PHY_ADRCTL_MSTR_DLY_ENC_SEL_1_SHIFT          0U
#define LPDDR4__DENALI_PHY_1361__PHY_ADRCTL_MSTR_DLY_ENC_SEL_1_WIDTH          2U
#define LPDDR4__PHY_ADRCTL_MSTR_DLY_ENC_SEL_1__REG DENALI_PHY_1361
#define LPDDR4__PHY_ADRCTL_MSTR_DLY_ENC_SEL_1__FLD LPDDR4__DENALI_PHY_1361__PHY_ADRCTL_MSTR_DLY_ENC_SEL_1

#define LPDDR4__DENALI_PHY_1361__PHY_ADRCTL_MSTR_DLY_ENC_SEL_2_MASK  0x00000300U
#define LPDDR4__DENALI_PHY_1361__PHY_ADRCTL_MSTR_DLY_ENC_SEL_2_SHIFT          8U
#define LPDDR4__DENALI_PHY_1361__PHY_ADRCTL_MSTR_DLY_ENC_SEL_2_WIDTH          2U
#define LPDDR4__PHY_ADRCTL_MSTR_DLY_ENC_SEL_2__REG DENALI_PHY_1361
#define LPDDR4__PHY_ADRCTL_MSTR_DLY_ENC_SEL_2__FLD LPDDR4__DENALI_PHY_1361__PHY_ADRCTL_MSTR_DLY_ENC_SEL_2

#define LPDDR4__DENALI_PHY_1361__PHY_ADRCTL_MSTR_DLY_ENC_SEL_3_MASK  0x00030000U
#define LPDDR4__DENALI_PHY_1361__PHY_ADRCTL_MSTR_DLY_ENC_SEL_3_SHIFT         16U
#define LPDDR4__DENALI_PHY_1361__PHY_ADRCTL_MSTR_DLY_ENC_SEL_3_WIDTH          2U
#define LPDDR4__PHY_ADRCTL_MSTR_DLY_ENC_SEL_3__REG DENALI_PHY_1361
#define LPDDR4__PHY_ADRCTL_MSTR_DLY_ENC_SEL_3__FLD LPDDR4__DENALI_PHY_1361__PHY_ADRCTL_MSTR_DLY_ENC_SEL_3

#define LPDDR4__DENALI_PHY_1362_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1362_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1362__PHY_DDL_AC_ENABLE_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1362__PHY_DDL_AC_ENABLE_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1362__PHY_DDL_AC_ENABLE_WIDTH                     32U
#define LPDDR4__PHY_DDL_AC_ENABLE__REG DENALI_PHY_1362
#define LPDDR4__PHY_DDL_AC_ENABLE__FLD LPDDR4__DENALI_PHY_1362__PHY_DDL_AC_ENABLE

#define LPDDR4__DENALI_PHY_1363_READ_MASK                            0x03FFFFFFU
#define LPDDR4__DENALI_PHY_1363_WRITE_MASK                           0x03FFFFFFU
#define LPDDR4__DENALI_PHY_1363__PHY_DDL_AC_MODE_MASK                0x03FFFFFFU
#define LPDDR4__DENALI_PHY_1363__PHY_DDL_AC_MODE_SHIFT                        0U
#define LPDDR4__DENALI_PHY_1363__PHY_DDL_AC_MODE_WIDTH                       26U
#define LPDDR4__PHY_DDL_AC_MODE__REG DENALI_PHY_1363
#define LPDDR4__PHY_DDL_AC_MODE__FLD LPDDR4__DENALI_PHY_1363__PHY_DDL_AC_MODE

#define LPDDR4__DENALI_PHY_1364_READ_MASK                            0x00FF073FU
#define LPDDR4__DENALI_PHY_1364_WRITE_MASK                           0x00FF073FU
#define LPDDR4__DENALI_PHY_1364__PHY_DDL_AC_MASK_MASK                0x0000003FU
#define LPDDR4__DENALI_PHY_1364__PHY_DDL_AC_MASK_SHIFT                        0U
#define LPDDR4__DENALI_PHY_1364__PHY_DDL_AC_MASK_WIDTH                        6U
#define LPDDR4__PHY_DDL_AC_MASK__REG DENALI_PHY_1364
#define LPDDR4__PHY_DDL_AC_MASK__FLD LPDDR4__DENALI_PHY_1364__PHY_DDL_AC_MASK

#define LPDDR4__DENALI_PHY_1364__PHY_INIT_UPDATE_CONFIG_MASK         0x00000700U
#define LPDDR4__DENALI_PHY_1364__PHY_INIT_UPDATE_CONFIG_SHIFT                 8U
#define LPDDR4__DENALI_PHY_1364__PHY_INIT_UPDATE_CONFIG_WIDTH                 3U
#define LPDDR4__PHY_INIT_UPDATE_CONFIG__REG DENALI_PHY_1364
#define LPDDR4__PHY_INIT_UPDATE_CONFIG__FLD LPDDR4__DENALI_PHY_1364__PHY_INIT_UPDATE_CONFIG

#define LPDDR4__DENALI_PHY_1364__PHY_DDL_TRACK_UPD_THRESHOLD_AC_MASK 0x00FF0000U
#define LPDDR4__DENALI_PHY_1364__PHY_DDL_TRACK_UPD_THRESHOLD_AC_SHIFT        16U
#define LPDDR4__DENALI_PHY_1364__PHY_DDL_TRACK_UPD_THRESHOLD_AC_WIDTH         8U
#define LPDDR4__PHY_DDL_TRACK_UPD_THRESHOLD_AC__REG DENALI_PHY_1364
#define LPDDR4__PHY_DDL_TRACK_UPD_THRESHOLD_AC__FLD LPDDR4__DENALI_PHY_1364__PHY_DDL_TRACK_UPD_THRESHOLD_AC

#define LPDDR4__DENALI_PHY_1365_READ_MASK                            0x0707FFFFU
#define LPDDR4__DENALI_PHY_1365_WRITE_MASK                           0x0707FFFFU
#define LPDDR4__DENALI_PHY_1365__PHY_CA_PARITY_ERR_PULSE_MIN_MASK    0x0000FFFFU
#define LPDDR4__DENALI_PHY_1365__PHY_CA_PARITY_ERR_PULSE_MIN_SHIFT            0U
#define LPDDR4__DENALI_PHY_1365__PHY_CA_PARITY_ERR_PULSE_MIN_WIDTH           16U
#define LPDDR4__PHY_CA_PARITY_ERR_PULSE_MIN__REG DENALI_PHY_1365
#define LPDDR4__PHY_CA_PARITY_ERR_PULSE_MIN__FLD LPDDR4__DENALI_PHY_1365__PHY_CA_PARITY_ERR_PULSE_MIN

#define LPDDR4__DENALI_PHY_1365__PHY_ERR_MASK_EN_MASK                0x00070000U
#define LPDDR4__DENALI_PHY_1365__PHY_ERR_MASK_EN_SHIFT                       16U
#define LPDDR4__DENALI_PHY_1365__PHY_ERR_MASK_EN_WIDTH                        3U
#define LPDDR4__PHY_ERR_MASK_EN__REG DENALI_PHY_1365
#define LPDDR4__PHY_ERR_MASK_EN__FLD LPDDR4__DENALI_PHY_1365__PHY_ERR_MASK_EN

#define LPDDR4__DENALI_PHY_1365__PHY_ERR_STATUS_MASK                 0x07000000U
#define LPDDR4__DENALI_PHY_1365__PHY_ERR_STATUS_SHIFT                        24U
#define LPDDR4__DENALI_PHY_1365__PHY_ERR_STATUS_WIDTH                         3U
#define LPDDR4__PHY_ERR_STATUS__REG DENALI_PHY_1365
#define LPDDR4__PHY_ERR_STATUS__FLD LPDDR4__DENALI_PHY_1365__PHY_ERR_STATUS

#define LPDDR4__DENALI_PHY_1366_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1366_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1366__PHY_DS0_DQS_ERR_COUNTER_MASK        0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1366__PHY_DS0_DQS_ERR_COUNTER_SHIFT                0U
#define LPDDR4__DENALI_PHY_1366__PHY_DS0_DQS_ERR_COUNTER_WIDTH               32U
#define LPDDR4__PHY_DS0_DQS_ERR_COUNTER__REG DENALI_PHY_1366
#define LPDDR4__PHY_DS0_DQS_ERR_COUNTER__FLD LPDDR4__DENALI_PHY_1366__PHY_DS0_DQS_ERR_COUNTER

#define LPDDR4__DENALI_PHY_1367_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1367_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1367__PHY_DS1_DQS_ERR_COUNTER_MASK        0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1367__PHY_DS1_DQS_ERR_COUNTER_SHIFT                0U
#define LPDDR4__DENALI_PHY_1367__PHY_DS1_DQS_ERR_COUNTER_WIDTH               32U
#define LPDDR4__PHY_DS1_DQS_ERR_COUNTER__REG DENALI_PHY_1367
#define LPDDR4__PHY_DS1_DQS_ERR_COUNTER__FLD LPDDR4__DENALI_PHY_1367__PHY_DS1_DQS_ERR_COUNTER

#define LPDDR4__DENALI_PHY_1368_READ_MASK                            0x030FFF03U
#define LPDDR4__DENALI_PHY_1368_WRITE_MASK                           0x030FFF03U
#define LPDDR4__DENALI_PHY_1368__PHY_DLL_RST_EN_MASK                 0x00000003U
#define LPDDR4__DENALI_PHY_1368__PHY_DLL_RST_EN_SHIFT                         0U
#define LPDDR4__DENALI_PHY_1368__PHY_DLL_RST_EN_WIDTH                         2U
#define LPDDR4__PHY_DLL_RST_EN__REG DENALI_PHY_1368
#define LPDDR4__PHY_DLL_RST_EN__FLD LPDDR4__DENALI_PHY_1368__PHY_DLL_RST_EN

#define LPDDR4__DENALI_PHY_1368__PHY_AC_INIT_COMPLETE_OBS_MASK       0x000FFF00U
#define LPDDR4__DENALI_PHY_1368__PHY_AC_INIT_COMPLETE_OBS_SHIFT               8U
#define LPDDR4__DENALI_PHY_1368__PHY_AC_INIT_COMPLETE_OBS_WIDTH              12U
#define LPDDR4__PHY_AC_INIT_COMPLETE_OBS__REG DENALI_PHY_1368
#define LPDDR4__PHY_AC_INIT_COMPLETE_OBS__FLD LPDDR4__DENALI_PHY_1368__PHY_AC_INIT_COMPLETE_OBS

#define LPDDR4__DENALI_PHY_1368__PHY_DS_INIT_COMPLETE_OBS_MASK       0x03000000U
#define LPDDR4__DENALI_PHY_1368__PHY_DS_INIT_COMPLETE_OBS_SHIFT              24U
#define LPDDR4__DENALI_PHY_1368__PHY_DS_INIT_COMPLETE_OBS_WIDTH               2U
#define LPDDR4__PHY_DS_INIT_COMPLETE_OBS__REG DENALI_PHY_1368
#define LPDDR4__PHY_DS_INIT_COMPLETE_OBS__FLD LPDDR4__DENALI_PHY_1368__PHY_DS_INIT_COMPLETE_OBS

#define LPDDR4__DENALI_PHY_1369_READ_MASK                            0x0F1F0101U
#define LPDDR4__DENALI_PHY_1369_WRITE_MASK                           0x0F1F0101U
#define LPDDR4__DENALI_PHY_1369__PHY_UPDATE_MASK_MASK                0x00000001U
#define LPDDR4__DENALI_PHY_1369__PHY_UPDATE_MASK_SHIFT                        0U
#define LPDDR4__DENALI_PHY_1369__PHY_UPDATE_MASK_WIDTH                        1U
#define LPDDR4__DENALI_PHY_1369__PHY_UPDATE_MASK_WOCLR                        0U
#define LPDDR4__DENALI_PHY_1369__PHY_UPDATE_MASK_WOSET                        0U
#define LPDDR4__PHY_UPDATE_MASK__REG DENALI_PHY_1369
#define LPDDR4__PHY_UPDATE_MASK__FLD LPDDR4__DENALI_PHY_1369__PHY_UPDATE_MASK

#define LPDDR4__DENALI_PHY_1369__PHY_ERR_IE_MASK                     0x00000100U
#define LPDDR4__DENALI_PHY_1369__PHY_ERR_IE_SHIFT                             8U
#define LPDDR4__DENALI_PHY_1369__PHY_ERR_IE_WIDTH                             1U
#define LPDDR4__DENALI_PHY_1369__PHY_ERR_IE_WOCLR                             0U
#define LPDDR4__DENALI_PHY_1369__PHY_ERR_IE_WOSET                             0U
#define LPDDR4__PHY_ERR_IE__REG DENALI_PHY_1369
#define LPDDR4__PHY_ERR_IE__FLD LPDDR4__DENALI_PHY_1369__PHY_ERR_IE

#define LPDDR4__DENALI_PHY_1369__PHY_GRP_SLV_DLY_ENC_OBS_SELECT_MASK 0x001F0000U
#define LPDDR4__DENALI_PHY_1369__PHY_GRP_SLV_DLY_ENC_OBS_SELECT_SHIFT        16U
#define LPDDR4__DENALI_PHY_1369__PHY_GRP_SLV_DLY_ENC_OBS_SELECT_WIDTH         5U
#define LPDDR4__PHY_GRP_SLV_DLY_ENC_OBS_SELECT__REG DENALI_PHY_1369
#define LPDDR4__PHY_GRP_SLV_DLY_ENC_OBS_SELECT__FLD LPDDR4__DENALI_PHY_1369__PHY_GRP_SLV_DLY_ENC_OBS_SELECT

#define LPDDR4__DENALI_PHY_1369__PHY_GRP_SHIFT_OBS_SELECT_MASK       0x0F000000U
#define LPDDR4__DENALI_PHY_1369__PHY_GRP_SHIFT_OBS_SELECT_SHIFT              24U
#define LPDDR4__DENALI_PHY_1369__PHY_GRP_SHIFT_OBS_SELECT_WIDTH               4U
#define LPDDR4__PHY_GRP_SHIFT_OBS_SELECT__REG DENALI_PHY_1369
#define LPDDR4__PHY_GRP_SHIFT_OBS_SELECT__FLD LPDDR4__DENALI_PHY_1369__PHY_GRP_SHIFT_OBS_SELECT

#define LPDDR4__DENALI_PHY_1370_READ_MASK                            0x000707FFU
#define LPDDR4__DENALI_PHY_1370_WRITE_MASK                           0x000707FFU
#define LPDDR4__DENALI_PHY_1370__PHY_GRP_SLV_DLY_ENC_OBS_MASK        0x000007FFU
#define LPDDR4__DENALI_PHY_1370__PHY_GRP_SLV_DLY_ENC_OBS_SHIFT                0U
#define LPDDR4__DENALI_PHY_1370__PHY_GRP_SLV_DLY_ENC_OBS_WIDTH               11U
#define LPDDR4__PHY_GRP_SLV_DLY_ENC_OBS__REG DENALI_PHY_1370
#define LPDDR4__PHY_GRP_SLV_DLY_ENC_OBS__FLD LPDDR4__DENALI_PHY_1370__PHY_GRP_SLV_DLY_ENC_OBS

#define LPDDR4__DENALI_PHY_1370__PHY_GRP_SHIFT_OBS_MASK              0x00070000U
#define LPDDR4__DENALI_PHY_1370__PHY_GRP_SHIFT_OBS_SHIFT                     16U
#define LPDDR4__DENALI_PHY_1370__PHY_GRP_SHIFT_OBS_WIDTH                      3U
#define LPDDR4__PHY_GRP_SHIFT_OBS__REG DENALI_PHY_1370
#define LPDDR4__PHY_GRP_SHIFT_OBS__FLD LPDDR4__DENALI_PHY_1370__PHY_GRP_SHIFT_OBS

#define LPDDR4__DENALI_PHY_1371_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1371_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1371__PHY_PAD_CAL_IO_CFG_0_MASK           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1371__PHY_PAD_CAL_IO_CFG_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1371__PHY_PAD_CAL_IO_CFG_0_WIDTH                  18U
#define LPDDR4__PHY_PAD_CAL_IO_CFG_0__REG DENALI_PHY_1371
#define LPDDR4__PHY_PAD_CAL_IO_CFG_0__FLD LPDDR4__DENALI_PHY_1371__PHY_PAD_CAL_IO_CFG_0

#define LPDDR4__DENALI_PHY_1372_READ_MASK                            0x0007FFFFU
#define LPDDR4__DENALI_PHY_1372_WRITE_MASK                           0x0007FFFFU
#define LPDDR4__DENALI_PHY_1372__PHY_PAD_ACS_IO_CFG_MASK             0x0000FFFFU
#define LPDDR4__DENALI_PHY_1372__PHY_PAD_ACS_IO_CFG_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1372__PHY_PAD_ACS_IO_CFG_WIDTH                    16U
#define LPDDR4__PHY_PAD_ACS_IO_CFG__REG DENALI_PHY_1372
#define LPDDR4__PHY_PAD_ACS_IO_CFG__FLD LPDDR4__DENALI_PHY_1372__PHY_PAD_ACS_IO_CFG

#define LPDDR4__DENALI_PHY_1372__PHY_PAD_ACS_RX_PCLK_CLK_SEL_MASK    0x00070000U
#define LPDDR4__DENALI_PHY_1372__PHY_PAD_ACS_RX_PCLK_CLK_SEL_SHIFT           16U
#define LPDDR4__DENALI_PHY_1372__PHY_PAD_ACS_RX_PCLK_CLK_SEL_WIDTH            3U
#define LPDDR4__PHY_PAD_ACS_RX_PCLK_CLK_SEL__REG DENALI_PHY_1372
#define LPDDR4__PHY_PAD_ACS_RX_PCLK_CLK_SEL__FLD LPDDR4__DENALI_PHY_1372__PHY_PAD_ACS_RX_PCLK_CLK_SEL

#define LPDDR4__DENALI_PHY_1373_READ_MASK                            0x00000001U
#define LPDDR4__DENALI_PHY_1373_WRITE_MASK                           0x00000001U
#define LPDDR4__DENALI_PHY_1373__PHY_PLL_BYPASS_MASK                 0x00000001U
#define LPDDR4__DENALI_PHY_1373__PHY_PLL_BYPASS_SHIFT                         0U
#define LPDDR4__DENALI_PHY_1373__PHY_PLL_BYPASS_WIDTH                         1U
#define LPDDR4__DENALI_PHY_1373__PHY_PLL_BYPASS_WOCLR                         0U
#define LPDDR4__DENALI_PHY_1373__PHY_PLL_BYPASS_WOSET                         0U
#define LPDDR4__PHY_PLL_BYPASS__REG DENALI_PHY_1373
#define LPDDR4__PHY_PLL_BYPASS__FLD LPDDR4__DENALI_PHY_1373__PHY_PLL_BYPASS

#define LPDDR4__DENALI_PHY_1374_READ_MASK                            0x00011FFFU
#define LPDDR4__DENALI_PHY_1374_WRITE_MASK                           0x00011FFFU
#define LPDDR4__DENALI_PHY_1374__PHY_PLL_CTRL_MASK                   0x00001FFFU
#define LPDDR4__DENALI_PHY_1374__PHY_PLL_CTRL_SHIFT                           0U
#define LPDDR4__DENALI_PHY_1374__PHY_PLL_CTRL_WIDTH                          13U
#define LPDDR4__PHY_PLL_CTRL__REG DENALI_PHY_1374
#define LPDDR4__PHY_PLL_CTRL__FLD LPDDR4__DENALI_PHY_1374__PHY_PLL_CTRL

#define LPDDR4__DENALI_PHY_1374__PHY_LOW_FREQ_SEL_MASK               0x00010000U
#define LPDDR4__DENALI_PHY_1374__PHY_LOW_FREQ_SEL_SHIFT                      16U
#define LPDDR4__DENALI_PHY_1374__PHY_LOW_FREQ_SEL_WIDTH                       1U
#define LPDDR4__DENALI_PHY_1374__PHY_LOW_FREQ_SEL_WOCLR                       0U
#define LPDDR4__DENALI_PHY_1374__PHY_LOW_FREQ_SEL_WOSET                       0U
#define LPDDR4__PHY_LOW_FREQ_SEL__REG DENALI_PHY_1374
#define LPDDR4__PHY_LOW_FREQ_SEL__FLD LPDDR4__DENALI_PHY_1374__PHY_LOW_FREQ_SEL

#define LPDDR4__DENALI_PHY_1375_READ_MASK                            0x0F0F0FFFU
#define LPDDR4__DENALI_PHY_1375_WRITE_MASK                           0x0F0F0FFFU
#define LPDDR4__DENALI_PHY_1375__PHY_PAD_VREF_CTRL_AC_MASK           0x00000FFFU
#define LPDDR4__DENALI_PHY_1375__PHY_PAD_VREF_CTRL_AC_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1375__PHY_PAD_VREF_CTRL_AC_WIDTH                  12U
#define LPDDR4__PHY_PAD_VREF_CTRL_AC__REG DENALI_PHY_1375
#define LPDDR4__PHY_PAD_VREF_CTRL_AC__FLD LPDDR4__DENALI_PHY_1375__PHY_PAD_VREF_CTRL_AC

#define LPDDR4__DENALI_PHY_1375__PHY_CSLVL_CAPTURE_CNT_MASK          0x000F0000U
#define LPDDR4__DENALI_PHY_1375__PHY_CSLVL_CAPTURE_CNT_SHIFT                 16U
#define LPDDR4__DENALI_PHY_1375__PHY_CSLVL_CAPTURE_CNT_WIDTH                  4U
#define LPDDR4__PHY_CSLVL_CAPTURE_CNT__REG DENALI_PHY_1375
#define LPDDR4__PHY_CSLVL_CAPTURE_CNT__FLD LPDDR4__DENALI_PHY_1375__PHY_CSLVL_CAPTURE_CNT

#define LPDDR4__DENALI_PHY_1375__PHY_CSLVL_DLY_STEP_MASK             0x0F000000U
#define LPDDR4__DENALI_PHY_1375__PHY_CSLVL_DLY_STEP_SHIFT                    24U
#define LPDDR4__DENALI_PHY_1375__PHY_CSLVL_DLY_STEP_WIDTH                     4U
#define LPDDR4__PHY_CSLVL_DLY_STEP__REG DENALI_PHY_1375
#define LPDDR4__PHY_CSLVL_DLY_STEP__FLD LPDDR4__DENALI_PHY_1375__PHY_CSLVL_DLY_STEP

#define LPDDR4__DENALI_PHY_1376_READ_MASK                            0x010103FFU
#define LPDDR4__DENALI_PHY_1376_WRITE_MASK                           0x010103FFU
#define LPDDR4__DENALI_PHY_1376__PHY_SW_CSLVL_DVW_MIN_MASK           0x000003FFU
#define LPDDR4__DENALI_PHY_1376__PHY_SW_CSLVL_DVW_MIN_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1376__PHY_SW_CSLVL_DVW_MIN_WIDTH                  10U
#define LPDDR4__PHY_SW_CSLVL_DVW_MIN__REG DENALI_PHY_1376
#define LPDDR4__PHY_SW_CSLVL_DVW_MIN__FLD LPDDR4__DENALI_PHY_1376__PHY_SW_CSLVL_DVW_MIN

#define LPDDR4__DENALI_PHY_1376__PHY_SW_CSLVL_DVW_MIN_EN_MASK        0x00010000U
#define LPDDR4__DENALI_PHY_1376__PHY_SW_CSLVL_DVW_MIN_EN_SHIFT               16U
#define LPDDR4__DENALI_PHY_1376__PHY_SW_CSLVL_DVW_MIN_EN_WIDTH                1U
#define LPDDR4__DENALI_PHY_1376__PHY_SW_CSLVL_DVW_MIN_EN_WOCLR                0U
#define LPDDR4__DENALI_PHY_1376__PHY_SW_CSLVL_DVW_MIN_EN_WOSET                0U
#define LPDDR4__PHY_SW_CSLVL_DVW_MIN_EN__REG DENALI_PHY_1376
#define LPDDR4__PHY_SW_CSLVL_DVW_MIN_EN__FLD LPDDR4__DENALI_PHY_1376__PHY_SW_CSLVL_DVW_MIN_EN

#define LPDDR4__DENALI_PHY_1376__PHY_LVL_MEAS_DLY_STEP_ENABLE_MASK   0x01000000U
#define LPDDR4__DENALI_PHY_1376__PHY_LVL_MEAS_DLY_STEP_ENABLE_SHIFT          24U
#define LPDDR4__DENALI_PHY_1376__PHY_LVL_MEAS_DLY_STEP_ENABLE_WIDTH           1U
#define LPDDR4__DENALI_PHY_1376__PHY_LVL_MEAS_DLY_STEP_ENABLE_WOCLR           0U
#define LPDDR4__DENALI_PHY_1376__PHY_LVL_MEAS_DLY_STEP_ENABLE_WOSET           0U
#define LPDDR4__PHY_LVL_MEAS_DLY_STEP_ENABLE__REG DENALI_PHY_1376
#define LPDDR4__PHY_LVL_MEAS_DLY_STEP_ENABLE__FLD LPDDR4__DENALI_PHY_1376__PHY_LVL_MEAS_DLY_STEP_ENABLE

#define LPDDR4__DENALI_PHY_1377_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1377_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1377__PHY_GRP0_SLAVE_DELAY_0_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1377__PHY_GRP0_SLAVE_DELAY_0_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1377__PHY_GRP0_SLAVE_DELAY_0_WIDTH                11U
#define LPDDR4__PHY_GRP0_SLAVE_DELAY_0__REG DENALI_PHY_1377
#define LPDDR4__PHY_GRP0_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_1377__PHY_GRP0_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_1377__PHY_GRP1_SLAVE_DELAY_0_MASK         0x07FF0000U
#define LPDDR4__DENALI_PHY_1377__PHY_GRP1_SLAVE_DELAY_0_SHIFT                16U
#define LPDDR4__DENALI_PHY_1377__PHY_GRP1_SLAVE_DELAY_0_WIDTH                11U
#define LPDDR4__PHY_GRP1_SLAVE_DELAY_0__REG DENALI_PHY_1377
#define LPDDR4__PHY_GRP1_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_1377__PHY_GRP1_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_1378_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1378_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1378__PHY_GRP2_SLAVE_DELAY_0_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1378__PHY_GRP2_SLAVE_DELAY_0_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1378__PHY_GRP2_SLAVE_DELAY_0_WIDTH                11U
#define LPDDR4__PHY_GRP2_SLAVE_DELAY_0__REG DENALI_PHY_1378
#define LPDDR4__PHY_GRP2_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_1378__PHY_GRP2_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_1378__PHY_GRP3_SLAVE_DELAY_0_MASK         0x07FF0000U
#define LPDDR4__DENALI_PHY_1378__PHY_GRP3_SLAVE_DELAY_0_SHIFT                16U
#define LPDDR4__DENALI_PHY_1378__PHY_GRP3_SLAVE_DELAY_0_WIDTH                11U
#define LPDDR4__PHY_GRP3_SLAVE_DELAY_0__REG DENALI_PHY_1378
#define LPDDR4__PHY_GRP3_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_1378__PHY_GRP3_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_1379_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1379_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1379__PHY_GRP0_SLAVE_DELAY_1_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1379__PHY_GRP0_SLAVE_DELAY_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1379__PHY_GRP0_SLAVE_DELAY_1_WIDTH                11U
#define LPDDR4__PHY_GRP0_SLAVE_DELAY_1__REG DENALI_PHY_1379
#define LPDDR4__PHY_GRP0_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_1379__PHY_GRP0_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_1379__PHY_GRP1_SLAVE_DELAY_1_MASK         0x07FF0000U
#define LPDDR4__DENALI_PHY_1379__PHY_GRP1_SLAVE_DELAY_1_SHIFT                16U
#define LPDDR4__DENALI_PHY_1379__PHY_GRP1_SLAVE_DELAY_1_WIDTH                11U
#define LPDDR4__PHY_GRP1_SLAVE_DELAY_1__REG DENALI_PHY_1379
#define LPDDR4__PHY_GRP1_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_1379__PHY_GRP1_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_1380_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1380_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1380__PHY_GRP2_SLAVE_DELAY_1_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1380__PHY_GRP2_SLAVE_DELAY_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1380__PHY_GRP2_SLAVE_DELAY_1_WIDTH                11U
#define LPDDR4__PHY_GRP2_SLAVE_DELAY_1__REG DENALI_PHY_1380
#define LPDDR4__PHY_GRP2_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_1380__PHY_GRP2_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_1380__PHY_GRP3_SLAVE_DELAY_1_MASK         0x07FF0000U
#define LPDDR4__DENALI_PHY_1380__PHY_GRP3_SLAVE_DELAY_1_SHIFT                16U
#define LPDDR4__DENALI_PHY_1380__PHY_GRP3_SLAVE_DELAY_1_WIDTH                11U
#define LPDDR4__PHY_GRP3_SLAVE_DELAY_1__REG DENALI_PHY_1380
#define LPDDR4__PHY_GRP3_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_1380__PHY_GRP3_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_1381_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1381_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1381__PHY_GRP0_SLAVE_DELAY_2_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1381__PHY_GRP0_SLAVE_DELAY_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1381__PHY_GRP0_SLAVE_DELAY_2_WIDTH                11U
#define LPDDR4__PHY_GRP0_SLAVE_DELAY_2__REG DENALI_PHY_1381
#define LPDDR4__PHY_GRP0_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1381__PHY_GRP0_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1381__PHY_GRP1_SLAVE_DELAY_2_MASK         0x07FF0000U
#define LPDDR4__DENALI_PHY_1381__PHY_GRP1_SLAVE_DELAY_2_SHIFT                16U
#define LPDDR4__DENALI_PHY_1381__PHY_GRP1_SLAVE_DELAY_2_WIDTH                11U
#define LPDDR4__PHY_GRP1_SLAVE_DELAY_2__REG DENALI_PHY_1381
#define LPDDR4__PHY_GRP1_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1381__PHY_GRP1_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1382_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1382_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1382__PHY_GRP2_SLAVE_DELAY_2_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1382__PHY_GRP2_SLAVE_DELAY_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1382__PHY_GRP2_SLAVE_DELAY_2_WIDTH                11U
#define LPDDR4__PHY_GRP2_SLAVE_DELAY_2__REG DENALI_PHY_1382
#define LPDDR4__PHY_GRP2_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1382__PHY_GRP2_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1382__PHY_GRP3_SLAVE_DELAY_2_MASK         0x07FF0000U
#define LPDDR4__DENALI_PHY_1382__PHY_GRP3_SLAVE_DELAY_2_SHIFT                16U
#define LPDDR4__DENALI_PHY_1382__PHY_GRP3_SLAVE_DELAY_2_WIDTH                11U
#define LPDDR4__PHY_GRP3_SLAVE_DELAY_2__REG DENALI_PHY_1382
#define LPDDR4__PHY_GRP3_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1382__PHY_GRP3_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1383_READ_MASK                            0x000007FFU
#define LPDDR4__DENALI_PHY_1383_WRITE_MASK                           0x000007FFU
#define LPDDR4__DENALI_PHY_1383__PHY_GRP0_SLAVE_DELAY_3_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1383__PHY_GRP0_SLAVE_DELAY_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1383__PHY_GRP0_SLAVE_DELAY_3_WIDTH                11U
#define LPDDR4__PHY_GRP0_SLAVE_DELAY_3__REG DENALI_PHY_1383
#define LPDDR4__PHY_GRP0_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_1383__PHY_GRP0_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_1384_READ_MASK                            0x000007FFU
#define LPDDR4__DENALI_PHY_1384_WRITE_MASK                           0x000007FFU
#define LPDDR4__DENALI_PHY_1384__PHY_GRP1_SLAVE_DELAY_3_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1384__PHY_GRP1_SLAVE_DELAY_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1384__PHY_GRP1_SLAVE_DELAY_3_WIDTH                11U
#define LPDDR4__PHY_GRP1_SLAVE_DELAY_3__REG DENALI_PHY_1384
#define LPDDR4__PHY_GRP1_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_1384__PHY_GRP1_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_1385_READ_MASK                            0x000007FFU
#define LPDDR4__DENALI_PHY_1385_WRITE_MASK                           0x000007FFU
#define LPDDR4__DENALI_PHY_1385__PHY_GRP2_SLAVE_DELAY_3_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1385__PHY_GRP2_SLAVE_DELAY_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1385__PHY_GRP2_SLAVE_DELAY_3_WIDTH                11U
#define LPDDR4__PHY_GRP2_SLAVE_DELAY_3__REG DENALI_PHY_1385
#define LPDDR4__PHY_GRP2_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_1385__PHY_GRP2_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_1386_READ_MASK                            0x000007FFU
#define LPDDR4__DENALI_PHY_1386_WRITE_MASK                           0x000007FFU
#define LPDDR4__DENALI_PHY_1386__PHY_GRP3_SLAVE_DELAY_3_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1386__PHY_GRP3_SLAVE_DELAY_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1386__PHY_GRP3_SLAVE_DELAY_3_WIDTH                11U
#define LPDDR4__PHY_GRP3_SLAVE_DELAY_3__REG DENALI_PHY_1386
#define LPDDR4__PHY_GRP3_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_1386__PHY_GRP3_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_1387_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1387_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1387__PHY_PAD_FDBK_DRIVE_MASK             0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1387__PHY_PAD_FDBK_DRIVE_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1387__PHY_PAD_FDBK_DRIVE_WIDTH                    30U
#define LPDDR4__PHY_PAD_FDBK_DRIVE__REG DENALI_PHY_1387
#define LPDDR4__PHY_PAD_FDBK_DRIVE__FLD LPDDR4__DENALI_PHY_1387__PHY_PAD_FDBK_DRIVE

#define LPDDR4__DENALI_PHY_1388_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1388_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1388__PHY_PAD_FDBK_DRIVE2_MASK            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1388__PHY_PAD_FDBK_DRIVE2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1388__PHY_PAD_FDBK_DRIVE2_WIDTH                   18U
#define LPDDR4__PHY_PAD_FDBK_DRIVE2__REG DENALI_PHY_1388
#define LPDDR4__PHY_PAD_FDBK_DRIVE2__FLD LPDDR4__DENALI_PHY_1388__PHY_PAD_FDBK_DRIVE2

#define LPDDR4__DENALI_PHY_1389_READ_MASK                            0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_1389_WRITE_MASK                           0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_1389__PHY_PAD_DATA_DRIVE_MASK             0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_1389__PHY_PAD_DATA_DRIVE_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1389__PHY_PAD_DATA_DRIVE_WIDTH                    31U
#define LPDDR4__PHY_PAD_DATA_DRIVE__REG DENALI_PHY_1389
#define LPDDR4__PHY_PAD_DATA_DRIVE__FLD LPDDR4__DENALI_PHY_1389__PHY_PAD_DATA_DRIVE

#define LPDDR4__DENALI_PHY_1390_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1390_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1390__PHY_PAD_DQS_DRIVE_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1390__PHY_PAD_DQS_DRIVE_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1390__PHY_PAD_DQS_DRIVE_WIDTH                     32U
#define LPDDR4__PHY_PAD_DQS_DRIVE__REG DENALI_PHY_1390
#define LPDDR4__PHY_PAD_DQS_DRIVE__FLD LPDDR4__DENALI_PHY_1390__PHY_PAD_DQS_DRIVE

#define LPDDR4__DENALI_PHY_1391_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1391_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1391__PHY_PAD_ADDR_DRIVE_MASK             0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1391__PHY_PAD_ADDR_DRIVE_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1391__PHY_PAD_ADDR_DRIVE_WIDTH                    30U
#define LPDDR4__PHY_PAD_ADDR_DRIVE__REG DENALI_PHY_1391
#define LPDDR4__PHY_PAD_ADDR_DRIVE__FLD LPDDR4__DENALI_PHY_1391__PHY_PAD_ADDR_DRIVE

#define LPDDR4__DENALI_PHY_1392_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1392_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1392__PHY_PAD_ADDR_DRIVE2_MASK            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1392__PHY_PAD_ADDR_DRIVE2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1392__PHY_PAD_ADDR_DRIVE2_WIDTH                   28U
#define LPDDR4__PHY_PAD_ADDR_DRIVE2__REG DENALI_PHY_1392
#define LPDDR4__PHY_PAD_ADDR_DRIVE2__FLD LPDDR4__DENALI_PHY_1392__PHY_PAD_ADDR_DRIVE2

#define LPDDR4__DENALI_PHY_1393_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1393_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1393__PHY_PAD_CLK_DRIVE_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1393__PHY_PAD_CLK_DRIVE_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1393__PHY_PAD_CLK_DRIVE_WIDTH                     32U
#define LPDDR4__PHY_PAD_CLK_DRIVE__REG DENALI_PHY_1393
#define LPDDR4__PHY_PAD_CLK_DRIVE__FLD LPDDR4__DENALI_PHY_1393__PHY_PAD_CLK_DRIVE

#define LPDDR4__DENALI_PHY_1394_READ_MASK                            0x0007FFFFU
#define LPDDR4__DENALI_PHY_1394_WRITE_MASK                           0x0007FFFFU
#define LPDDR4__DENALI_PHY_1394__PHY_PAD_CLK_DRIVE2_MASK             0x0007FFFFU
#define LPDDR4__DENALI_PHY_1394__PHY_PAD_CLK_DRIVE2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1394__PHY_PAD_CLK_DRIVE2_WIDTH                    19U
#define LPDDR4__PHY_PAD_CLK_DRIVE2__REG DENALI_PHY_1394
#define LPDDR4__PHY_PAD_CLK_DRIVE2__FLD LPDDR4__DENALI_PHY_1394__PHY_PAD_CLK_DRIVE2

#define LPDDR4__DENALI_PHY_1395_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1395_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1395__PHY_PAD_ERR_DRIVE_MASK              0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1395__PHY_PAD_ERR_DRIVE_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1395__PHY_PAD_ERR_DRIVE_WIDTH                     30U
#define LPDDR4__PHY_PAD_ERR_DRIVE__REG DENALI_PHY_1395
#define LPDDR4__PHY_PAD_ERR_DRIVE__FLD LPDDR4__DENALI_PHY_1395__PHY_PAD_ERR_DRIVE

#define LPDDR4__DENALI_PHY_1396_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1396_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1396__PHY_PAD_ERR_DRIVE2_MASK             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1396__PHY_PAD_ERR_DRIVE2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1396__PHY_PAD_ERR_DRIVE2_WIDTH                    28U
#define LPDDR4__PHY_PAD_ERR_DRIVE2__REG DENALI_PHY_1396
#define LPDDR4__PHY_PAD_ERR_DRIVE2__FLD LPDDR4__DENALI_PHY_1396__PHY_PAD_ERR_DRIVE2

#define LPDDR4__DENALI_PHY_1397_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1397_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1397__PHY_PAD_CKE_DRIVE_MASK              0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1397__PHY_PAD_CKE_DRIVE_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1397__PHY_PAD_CKE_DRIVE_WIDTH                     30U
#define LPDDR4__PHY_PAD_CKE_DRIVE__REG DENALI_PHY_1397
#define LPDDR4__PHY_PAD_CKE_DRIVE__FLD LPDDR4__DENALI_PHY_1397__PHY_PAD_CKE_DRIVE

#define LPDDR4__DENALI_PHY_1398_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1398_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1398__PHY_PAD_CKE_DRIVE2_MASK             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1398__PHY_PAD_CKE_DRIVE2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1398__PHY_PAD_CKE_DRIVE2_WIDTH                    28U
#define LPDDR4__PHY_PAD_CKE_DRIVE2__REG DENALI_PHY_1398
#define LPDDR4__PHY_PAD_CKE_DRIVE2__FLD LPDDR4__DENALI_PHY_1398__PHY_PAD_CKE_DRIVE2

#define LPDDR4__DENALI_PHY_1399_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1399_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1399__PHY_PAD_RST_DRIVE_MASK              0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1399__PHY_PAD_RST_DRIVE_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1399__PHY_PAD_RST_DRIVE_WIDTH                     30U
#define LPDDR4__PHY_PAD_RST_DRIVE__REG DENALI_PHY_1399
#define LPDDR4__PHY_PAD_RST_DRIVE__FLD LPDDR4__DENALI_PHY_1399__PHY_PAD_RST_DRIVE

#define LPDDR4__DENALI_PHY_1400_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1400_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1400__PHY_PAD_RST_DRIVE2_MASK             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1400__PHY_PAD_RST_DRIVE2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1400__PHY_PAD_RST_DRIVE2_WIDTH                    28U
#define LPDDR4__PHY_PAD_RST_DRIVE2__REG DENALI_PHY_1400
#define LPDDR4__PHY_PAD_RST_DRIVE2__FLD LPDDR4__DENALI_PHY_1400__PHY_PAD_RST_DRIVE2

#define LPDDR4__DENALI_PHY_1401_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1401_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1401__PHY_PAD_CS_DRIVE_MASK               0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1401__PHY_PAD_CS_DRIVE_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1401__PHY_PAD_CS_DRIVE_WIDTH                      30U
#define LPDDR4__PHY_PAD_CS_DRIVE__REG DENALI_PHY_1401
#define LPDDR4__PHY_PAD_CS_DRIVE__FLD LPDDR4__DENALI_PHY_1401__PHY_PAD_CS_DRIVE

#define LPDDR4__DENALI_PHY_1402_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1402_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1402__PHY_PAD_CS_DRIVE2_MASK              0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1402__PHY_PAD_CS_DRIVE2_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1402__PHY_PAD_CS_DRIVE2_WIDTH                     28U
#define LPDDR4__PHY_PAD_CS_DRIVE2__REG DENALI_PHY_1402
#define LPDDR4__PHY_PAD_CS_DRIVE2__FLD LPDDR4__DENALI_PHY_1402__PHY_PAD_CS_DRIVE2

#define LPDDR4__DENALI_PHY_1403_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1403_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1403__PHY_PAD_ODT_DRIVE_MASK              0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1403__PHY_PAD_ODT_DRIVE_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1403__PHY_PAD_ODT_DRIVE_WIDTH                     30U
#define LPDDR4__PHY_PAD_ODT_DRIVE__REG DENALI_PHY_1403
#define LPDDR4__PHY_PAD_ODT_DRIVE__FLD LPDDR4__DENALI_PHY_1403__PHY_PAD_ODT_DRIVE

#define LPDDR4__DENALI_PHY_1404_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1404_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1404__PHY_PAD_ODT_DRIVE2_MASK             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1404__PHY_PAD_ODT_DRIVE2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1404__PHY_PAD_ODT_DRIVE2_WIDTH                    28U
#define LPDDR4__PHY_PAD_ODT_DRIVE2__REG DENALI_PHY_1404
#define LPDDR4__PHY_PAD_ODT_DRIVE2__FLD LPDDR4__DENALI_PHY_1404__PHY_PAD_ODT_DRIVE2

#define LPDDR4__DENALI_PHY_1405_READ_MASK                            0x7FFFFF07U
#define LPDDR4__DENALI_PHY_1405_WRITE_MASK                           0x7FFFFF07U
#define LPDDR4__DENALI_PHY_1405__PHY_CAL_CLK_SELECT_0_MASK           0x00000007U
#define LPDDR4__DENALI_PHY_1405__PHY_CAL_CLK_SELECT_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1405__PHY_CAL_CLK_SELECT_0_WIDTH                   3U
#define LPDDR4__PHY_CAL_CLK_SELECT_0__REG DENALI_PHY_1405
#define LPDDR4__PHY_CAL_CLK_SELECT_0__FLD LPDDR4__DENALI_PHY_1405__PHY_CAL_CLK_SELECT_0

#define LPDDR4__DENALI_PHY_1405__PHY_CAL_VREF_SWITCH_TIMER_0_MASK    0x00FFFF00U
#define LPDDR4__DENALI_PHY_1405__PHY_CAL_VREF_SWITCH_TIMER_0_SHIFT            8U
#define LPDDR4__DENALI_PHY_1405__PHY_CAL_VREF_SWITCH_TIMER_0_WIDTH           16U
#define LPDDR4__PHY_CAL_VREF_SWITCH_TIMER_0__REG DENALI_PHY_1405
#define LPDDR4__PHY_CAL_VREF_SWITCH_TIMER_0__FLD LPDDR4__DENALI_PHY_1405__PHY_CAL_VREF_SWITCH_TIMER_0

#define LPDDR4__DENALI_PHY_1405__PHY_CAL_SETTLING_PRD_0_MASK         0x7F000000U
#define LPDDR4__DENALI_PHY_1405__PHY_CAL_SETTLING_PRD_0_SHIFT                24U
#define LPDDR4__DENALI_PHY_1405__PHY_CAL_SETTLING_PRD_0_WIDTH                 7U
#define LPDDR4__PHY_CAL_SETTLING_PRD_0__REG DENALI_PHY_1405
#define LPDDR4__PHY_CAL_SETTLING_PRD_0__FLD LPDDR4__DENALI_PHY_1405__PHY_CAL_SETTLING_PRD_0

#endif /* REG_LPDDR4_PHY_CORE_MACROS_H_ */

/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */

