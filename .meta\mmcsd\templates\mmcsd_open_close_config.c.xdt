%%{
    let common = system.getScript("/common");
    let soc = system.getScript(`/drivers/soc/drivers_${common.getSocName()}`);
    let driverVer = soc.getDriverVer("mmcsd");
    let module = system.modules['/drivers/mmcsd/mmcsd'];
%%}
/*
 * MMCSD
 */
/* MMCSD Device Data structures */
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let config = module.getInstanceConfig(instance);
        %if(config.cardType == "EMMC") {
MMCSD_EmmcDeviceData gEmmcData`i`;
        %}
        %else if(config.cardType == "SD") {
MMCSD_SdDeviceData gSdData`i`;
        %}
%}

/* MMCSD temporary data buffers */
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let config = module.getInstanceConfig(instance);
    % if((config.cardType == "EMMC") || (config.cardType == "SD")) {
uint8_t gMmcsdDataBuf`i`[512U] __attribute__((aligned(128U)));;
    % }
%}


/* MMCSD Driver handles */
MMCSD_Handle gMmcsdHandle[CONFIG_MMCSD_NUM_INSTANCES];

/* MMCSD Driver Parameters */
MMCSD_Params gMmcsdParams[CONFIG_MMCSD_NUM_INSTANCES] =
{
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let config = module.getInstanceConfig(instance);
    {
        %if(config.cardType == "EMMC") {
        .deviceData = &gEmmcData`i`,
        %}
        %else if(config.cardType == "SD") {
        .deviceData = &gSdData`i`,
        %}
        %if((config.cardType == "EMMC") || (config.cardType == "SD")) {
        .dataBuf    = &gMmcsdDataBuf`i`[0],
        %}
    },
% }
};

% if (common.isDMWithBootSupported()) {
static uint32_t gMmcsdAddedByBootloader[CONFIG_MMCSD_NUM_INSTANCES] =
{
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let config = module.getInstanceConfig(instance);
    `config.addedByBootloader.toString(10).toUpperCase()`,
% }
};
% }

void Drivers_mmcsdOpen(void)
{
    uint32_t instCnt;
    int32_t  status = SystemP_SUCCESS;

    for(instCnt = 0U; instCnt < CONFIG_MMCSD_NUM_INSTANCES; instCnt++)
    {
        gMmcsdHandle[instCnt] = NULL;   /* Init to NULL so that we can exit gracefully */
    }

    /* Open all instances */
    for(instCnt = 0U; instCnt < CONFIG_MMCSD_NUM_INSTANCES; instCnt++)
    {
        % if (common.isDMWithBootSupported()) {
        if(!gMmcsdAddedByBootloader[instCnt])
        {
            gMmcsdHandle[instCnt] = MMCSD_open(instCnt, &gMmcsdParams[instCnt]);
            if(NULL == gMmcsdHandle[instCnt])
            {
                DebugP_logError("MMCSD open failed for instance %d !!!\r\n", instCnt);
                status = SystemP_FAILURE;
                break;
            }
        }
        % }
        % else {
        gMmcsdHandle[instCnt] = MMCSD_open(instCnt, &gMmcsdParams[instCnt]);
        if(NULL == gMmcsdHandle[instCnt])
        {
            DebugP_logError("MMCSD open failed for instance %d !!!\r\n", instCnt);
            status = SystemP_FAILURE;
            break;
        }
        % }
    }

    if(SystemP_FAILURE == status)
    {
        Drivers_mmcsdClose();   /* Exit gracefully */
    }

    return;
}

void Drivers_mmcsdClose(void)
{
    uint32_t instCnt;

    /* Close all instances that are open */
    for(instCnt = 0U; instCnt < CONFIG_MMCSD_NUM_INSTANCES; instCnt++)
    {
        % if (common.isDMWithBootSupported()) {
        if(!gMmcsdAddedByBootloader[instCnt])
        {
            if(gMmcsdHandle[instCnt] != NULL)
            {
                MMCSD_close(gMmcsdHandle[instCnt]);
                gMmcsdHandle[instCnt] = NULL;
            }
        }
        % }
        % else {
            if(gMmcsdHandle[instCnt] != NULL)
            {
                MMCSD_close(gMmcsdHandle[instCnt]);
                gMmcsdHandle[instCnt] = NULL;
            }
        % }

    }

    return;
}
