%%{
    let common = system.getScript("/common");
    let soc = system.getScript(`/drivers/mcu_bist/soc/mcu_bist_${common.getSocName()}`);
    let module = system.modules['/drivers/mcu_bist/mcu_bist'];
    let instance = module.$instances[0];
%%}
% if (instance.enableMcuLbist){
/*
 * LBIST
 */
#include <kernel/dpl/AddrTranslateP.h>
#include <sdl/include/sdl_types.h>
#include <sdl/dpl/sdl_dpl.h>
#include <sdl/sdl_lbist.h>
% }
% if (instance.enableMcuPbist){
/*
 * PBIST
 */
#include <kernel/dpl/AddrTranslateP.h>
#include <sdl/include/sdl_types.h>
#include <sdl/dpl/sdl_dpl.h>
#include <sdl/sdl_pbist.h>
% }