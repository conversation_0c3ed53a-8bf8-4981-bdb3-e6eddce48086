%%{
    let common = system.getScript("/common");
    let soc = system.getScript(`/drivers/mcu_bist/soc/mcu_bist_${common.getSocName()}`);
    let module = system.modules['/drivers/mcu_bist/mcu_bist'];
    let instance = module.$instances[0];

%%}
% if (instance.enableMcuLbist || instance.enableMcuPbist ){
    /* BIST */
    {
        int32_t status;

        /* Initialize SDL DPL */
        status = SDL_DPL_init(&dpl_interface);
        DebugP_assert(status == SDL_PASS);

        /* Unlock MMRs */
        SOC_controlModuleUnlockMMR(SOC_DOMAIN_ID_MAIN, 4u);
        SOC_controlModuleUnlockMMR(SOC_DOMAIN_ID_MCU, 3u);


        /* Start MCU BIST only if the reset isolation is disabled */
        if (!Bootloader_socIsMCUResetIsoEnabled())
        {
    % if (instance.enableMcuLbist){
            /* Start MCU LBIST */
            status = SDL_LBIST_selfTest(`soc.getSdlMcuLbistInstance()`, SDL_LBIST_TEST);
    % }
    % if (instance.enableMcuPbist){
            /* Start MCU PBIST */
            status = SDL_SBL_PBIST_selfTest(`soc.getSdlMcuPbistInstance()`);
    % }
            DebugP_assert(status == SDL_PASS);
        }
    }
% }