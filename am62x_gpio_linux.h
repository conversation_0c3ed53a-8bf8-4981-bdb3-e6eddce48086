/*
 * AM62x GPIO Driver Header - Linux Kernel Implementation
 * 
 * 定义AM62x GPIO驱动的数据结构、常量和函数声明
 */

#ifndef __AM62X_GPIO_LINUX_H__
#define __AM62X_GPIO_LINUX_H__

#include <linux/types.h>
#include <linux/gpio/driver.h>
#include <linux/spinlock.h>
#include <linux/clk.h>

/* AM62x GPIO寄存器偏移定义 */
/* GPIO v0 寄存器偏移 (主要GPIO控制器) */
#define GPIO_PID                    0x00
#define GPIO_PCR                    0x04
#define GPIO_BINTEN                 0x08
#define GPIO_DIR(n)                 (0x10 + ((n) * 0x28))
#define GPIO_OUT_DATA(n)            (0x14 + ((n) * 0x28))
#define GPIO_SET_DATA(n)            (0x18 + ((n) * 0x28))
#define GPIO_CLR_DATA(n)            (0x1C + ((n) * 0x28))
#define GPIO_IN_DATA(n)             (0x20 + ((n) * 0x28))
#define GPIO_SET_RIS_TRIG(n)        (0x24 + ((n) * 0x28))
#define GPIO_SET_FAL_TRIG(n)        (0x28 + ((n) * 0x28))
#define GPIO_INTSTAT(n)             (0x2C + ((n) * 0x28))

/* GPIO v1 寄存器偏移 (MCU GPIO控制器) */
#define GPIO_V1_GIODIR(n)           (0x34 + ((n) * 0x20))
#define GPIO_V1_GIODIN(n)           (0x38 + ((n) * 0x20))
#define GPIO_V1_GIODOUT(n)          (0x3C + ((n) * 0x20))
#define GPIO_V1_GIOSET(n)           (0x40 + ((n) * 0x20))
#define GPIO_V1_GIOCLR(n)           (0x44 + ((n) * 0x20))
#define GPIO_V1_GIOPDR(n)           (0x48 + ((n) * 0x20))
#define GPIO_V1_GIOPULDIS(n)        (0x4C + ((n) * 0x20))
#define GPIO_V1_GIOPSL(n)           (0x50 + ((n) * 0x20))
#define GPIO_V1_GIOPOL              0x2C
#define GPIO_V1_GIOENASET           0x30
#define GPIO_V1_GIOENACLR           0x34
#define GPIO_V1_GIOLVLSET           0x38
#define GPIO_V1_GIOLVLCLR           0x3C
#define GPIO_V1_GIOFLG              0x40
#define GPIO_V1_GIOOFF1             0x44
#define GPIO_V1_GIOOFF2             0x48
#define GPIO_V1_GIOEMU1             0x4C
#define GPIO_V1_GIOEMU2             0x50

/* GPIO方向定义 */
#define GPIO_DIRECTION_INPUT        0x0
#define GPIO_DIRECTION_OUTPUT       0x1

/* GPIO触发类型定义 */
#define GPIO_TRIG_TYPE_NONE         0x0
#define GPIO_TRIG_TYPE_RISING       0x1
#define GPIO_TRIG_TYPE_FALLING      0x2
#define GPIO_TRIG_TYPE_BOTH         0x3

/* GPIO引脚计算宏 */
#define GPIO_PINS_PER_REG_SHIFT     5U
#define GPIO_PINS_PER_BANK_SHIFT    4U
#define GPIO_MAX_PINS_PER_PORT      8U
#define GPIO_MAX_PORT               8U
#define GPIO_MAX_PIN_PER_BANK       16U

/* v0版本宏 */
#define GPIO_GET_BANK_INDEX(pinNum)     (((uint32_t) pinNum) >> GPIO_PINS_PER_BANK_SHIFT)
#define GPIO_GET_REG_INDEX(pinNum)      (((uint32_t) pinNum) >> GPIO_PINS_PER_REG_SHIFT)
#define GPIO_GET_BIT_POS(pinNum)        (pinNum - ((GPIO_GET_REG_INDEX(pinNum)) << GPIO_PINS_PER_REG_SHIFT))
#define GPIO_GET_BIT_MASK(pinNum)       (((uint32_t) 1U) << GPIO_GET_BIT_POS(pinNum))

/* v1版本宏 */
#define GPIO_GET_PIN_OFFSET(pinNum)     (((uint32_t)pinNum) % (GPIO_MAX_PORT * GPIO_MAX_PINS_PER_PORT))
#define GPIO_GET_PORT_NUM(pinNum)       ((GPIO_GET_PIN_OFFSET(pinNum)) / GPIO_MAX_PINS_PER_PORT)
#define GPIO_GET_PIN_INDEX(pinNum)      ((GPIO_GET_PIN_OFFSET(pinNum)) % GPIO_MAX_PINS_PER_PORT)

/* AM62x GPIO基地址定义 */
#define AM62X_GPIO0_BASE            0x00600000UL
#define AM62X_GPIO1_BASE            0x00601000UL
#define AM62X_MCU_GPIO0_BASE        0x04201000UL

/* GPIO控制器类型 */
enum am62x_gpio_type {
    AM62X_GPIO_TYPE_V0,     /* 主域GPIO */
    AM62X_GPIO_TYPE_V1,     /* MCU域GPIO */
};

/* GPIO中断类型 */
enum am62x_gpio_irq_type {
    AM62X_GPIO_IRQ_LEVEL_LOW,
    AM62X_GPIO_IRQ_LEVEL_HIGH,
    AM62X_GPIO_IRQ_EDGE_FALLING,
    AM62X_GPIO_IRQ_EDGE_RISING,
    AM62X_GPIO_IRQ_EDGE_BOTH,
};

/* GPIO控制器结构体 */
struct am62x_gpio_chip {
    struct gpio_chip chip;
    void __iomem *base;
    struct device *dev;
    struct clk *clk;
    enum am62x_gpio_type type;
    int irq;
    spinlock_t lock;
    
    /* 中断相关 */
    struct irq_chip irq_chip;
    struct irq_domain *irq_domain;
    
    /* 电源管理相关 */
    u32 *saved_regs;
    u32 saved_regs_cnt;
};

/* GPIO配置结构体 */
struct am62x_gpio_config {
    u32 pin;
    u32 direction;
    u32 value;
    u32 pull_type;
    u32 drive_strength;
};

/* 寄存器读写内联函数 */
static inline uint32_t am62x_gpio_read_reg(void __iomem *base, uint32_t offset)
{
    return readl(base + offset);
}

static inline void am62x_gpio_write_reg(void __iomem *base, uint32_t offset, uint32_t value)
{
    writel(value, base + offset);
}

/* 位域操作内联函数 */
static inline uint32_t am62x_gpio_extract_field(uint32_t reg, uint32_t msb, uint32_t lsb)
{
    return ((reg) >> (lsb)) & ((((uint32_t)1U) << ((msb) - (lsb) + ((uint32_t)1U))) - ((uint32_t)1U));
}

static inline uint32_t am62x_gpio_insert_field(uint32_t reg, uint32_t msb, uint32_t lsb, uint32_t val)
{
    uint32_t mask = ((((uint32_t)1U) << ((msb) - (lsb) + ((uint32_t)1U))) - ((uint32_t)1U)) << (lsb);
    return (reg & (~mask)) | (((val) << (lsb)) & mask);
}

/* 函数声明 */
int am62x_gpio_set_direction(struct am62x_gpio_chip *gpio_chip, uint32_t pin_num, uint32_t pin_dir);
int am62x_gpio_get_value(struct am62x_gpio_chip *gpio_chip, uint32_t pin_num);
void am62x_gpio_set_value(struct am62x_gpio_chip *gpio_chip, uint32_t pin_num, int value);
int am62x_gpio_set_trigger_type(struct am62x_gpio_chip *gpio_chip, uint32_t pin_num, uint32_t trig_type);
uint32_t am62x_gpio_get_interrupt_status(struct am62x_gpio_chip *gpio_chip, uint32_t pin_num);
void am62x_gpio_clear_interrupt_status(struct am62x_gpio_chip *gpio_chip, uint32_t pin_num);

/* 设备树兼容性字符串 */
#define AM62X_GPIO_COMPAT_V0        "ti,am62x-gpio"
#define AM62X_GPIO_COMPAT_V1        "ti,am62x-mcu-gpio"

/* 错误码定义 */
#define AM62X_GPIO_SUCCESS          0
#define AM62X_GPIO_ERROR_INVALID    -EINVAL
#define AM62X_GPIO_ERROR_BUSY       -EBUSY
#define AM62X_GPIO_ERROR_TIMEOUT    -ETIMEDOUT
#define AM62X_GPIO_ERROR_NO_MEM     -ENOMEM

/* 调试宏 */
#ifdef DEBUG
#define am62x_gpio_dbg(dev, fmt, ...) \
    dev_dbg(dev, "[AM62X-GPIO] " fmt, ##__VA_ARGS__)
#else
#define am62x_gpio_dbg(dev, fmt, ...) do { } while (0)
#endif

#define am62x_gpio_info(dev, fmt, ...) \
    dev_info(dev, "[AM62X-GPIO] " fmt, ##__VA_ARGS__)

#define am62x_gpio_warn(dev, fmt, ...) \
    dev_warn(dev, "[AM62X-GPIO] " fmt, ##__VA_ARGS__)

#define am62x_gpio_err(dev, fmt, ...) \
    dev_err(dev, "[AM62X-GPIO] " fmt, ##__VA_ARGS__)

#endif /* __AM62X_GPIO_LINUX_H__ */
