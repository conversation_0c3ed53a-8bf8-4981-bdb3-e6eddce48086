/* parasoft-begin-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" parasoft-begin-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */

/**********************************************************************
 * Copyright (C) 2012-2022 Cadence Design Systems, Inc.
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 
 * 1. Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THIS FILE IS AUTOMATICALLY GENERATED, DO NOT EDIT
 *
 **********************************************************************/

#ifndef REG_LPDDR4_DDR_CONTROLLER_MACROS_H_
#define REG_LPDDR4_DDR_CONTROLLER_MACROS_H_

#define LPDDR4__DENALI_CTL_0_READ_MASK                               0xFFFF0F01U
#define LPDDR4__DENALI_CTL_0_WRITE_MASK                              0xFFFF0F01U
#define LPDDR4__DENALI_CTL_0__START_MASK                             0x00000001U
#define LPDDR4__DENALI_CTL_0__START_SHIFT                                     0U
#define LPDDR4__DENALI_CTL_0__START_WIDTH                                     1U
#define LPDDR4__DENALI_CTL_0__START_WOCLR                                     0U
#define LPDDR4__DENALI_CTL_0__START_WOSET                                     0U
#define LPDDR4__START__REG DENALI_CTL_0
#define LPDDR4__START__FLD LPDDR4__DENALI_CTL_0__START

#define LPDDR4__DENALI_CTL_0__DRAM_CLASS_MASK                        0x00000F00U
#define LPDDR4__DENALI_CTL_0__DRAM_CLASS_SHIFT                                8U
#define LPDDR4__DENALI_CTL_0__DRAM_CLASS_WIDTH                                4U
#define LPDDR4__DRAM_CLASS__REG DENALI_CTL_0
#define LPDDR4__DRAM_CLASS__FLD LPDDR4__DENALI_CTL_0__DRAM_CLASS

#define LPDDR4__DENALI_CTL_0__CONTROLLER_ID_MASK                     0xFFFF0000U
#define LPDDR4__DENALI_CTL_0__CONTROLLER_ID_SHIFT                            16U
#define LPDDR4__DENALI_CTL_0__CONTROLLER_ID_WIDTH                            16U
#define LPDDR4__CONTROLLER_ID__REG DENALI_CTL_0
#define LPDDR4__CONTROLLER_ID__FLD LPDDR4__DENALI_CTL_0__CONTROLLER_ID

#define LPDDR4__DENALI_CTL_1_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_1_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_1__CONTROLLER_VERSION_0_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_1__CONTROLLER_VERSION_0_SHIFT                      0U
#define LPDDR4__DENALI_CTL_1__CONTROLLER_VERSION_0_WIDTH                     32U
#define LPDDR4__CONTROLLER_VERSION_0__REG DENALI_CTL_1
#define LPDDR4__CONTROLLER_VERSION_0__FLD LPDDR4__DENALI_CTL_1__CONTROLLER_VERSION_0

#define LPDDR4__DENALI_CTL_2_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_2_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_2__CONTROLLER_VERSION_1_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_2__CONTROLLER_VERSION_1_SHIFT                      0U
#define LPDDR4__DENALI_CTL_2__CONTROLLER_VERSION_1_WIDTH                     32U
#define LPDDR4__CONTROLLER_VERSION_1__REG DENALI_CTL_2
#define LPDDR4__CONTROLLER_VERSION_1__FLD LPDDR4__DENALI_CTL_2__CONTROLLER_VERSION_1

#define LPDDR4__DENALI_CTL_3_READ_MASK                               0xFF030F1FU
#define LPDDR4__DENALI_CTL_3_WRITE_MASK                              0xFF030F1FU
#define LPDDR4__DENALI_CTL_3__MAX_ROW_REG_MASK                       0x0000001FU
#define LPDDR4__DENALI_CTL_3__MAX_ROW_REG_SHIFT                               0U
#define LPDDR4__DENALI_CTL_3__MAX_ROW_REG_WIDTH                               5U
#define LPDDR4__MAX_ROW_REG__REG DENALI_CTL_3
#define LPDDR4__MAX_ROW_REG__FLD LPDDR4__DENALI_CTL_3__MAX_ROW_REG

#define LPDDR4__DENALI_CTL_3__MAX_COL_REG_MASK                       0x00000F00U
#define LPDDR4__DENALI_CTL_3__MAX_COL_REG_SHIFT                               8U
#define LPDDR4__DENALI_CTL_3__MAX_COL_REG_WIDTH                               4U
#define LPDDR4__MAX_COL_REG__REG DENALI_CTL_3
#define LPDDR4__MAX_COL_REG__FLD LPDDR4__DENALI_CTL_3__MAX_COL_REG

#define LPDDR4__DENALI_CTL_3__MAX_CS_REG_MASK                        0x00030000U
#define LPDDR4__DENALI_CTL_3__MAX_CS_REG_SHIFT                               16U
#define LPDDR4__DENALI_CTL_3__MAX_CS_REG_WIDTH                                2U
#define LPDDR4__MAX_CS_REG__REG DENALI_CTL_3
#define LPDDR4__MAX_CS_REG__FLD LPDDR4__DENALI_CTL_3__MAX_CS_REG

#define LPDDR4__DENALI_CTL_3__READ_DATA_FIFO_DEPTH_MASK              0xFF000000U
#define LPDDR4__DENALI_CTL_3__READ_DATA_FIFO_DEPTH_SHIFT                     24U
#define LPDDR4__DENALI_CTL_3__READ_DATA_FIFO_DEPTH_WIDTH                      8U
#define LPDDR4__READ_DATA_FIFO_DEPTH__REG DENALI_CTL_3
#define LPDDR4__READ_DATA_FIFO_DEPTH__FLD LPDDR4__DENALI_CTL_3__READ_DATA_FIFO_DEPTH

#define LPDDR4__DENALI_CTL_4_READ_MASK                               0x00FFFFFFU
#define LPDDR4__DENALI_CTL_4_WRITE_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_CTL_4__READ_DATA_FIFO_PTR_WIDTH_MASK          0x000000FFU
#define LPDDR4__DENALI_CTL_4__READ_DATA_FIFO_PTR_WIDTH_SHIFT                  0U
#define LPDDR4__DENALI_CTL_4__READ_DATA_FIFO_PTR_WIDTH_WIDTH                  8U
#define LPDDR4__READ_DATA_FIFO_PTR_WIDTH__REG DENALI_CTL_4
#define LPDDR4__READ_DATA_FIFO_PTR_WIDTH__FLD LPDDR4__DENALI_CTL_4__READ_DATA_FIFO_PTR_WIDTH

#define LPDDR4__DENALI_CTL_4__WRITE_DATA_FIFO_DEPTH_MASK             0x0000FF00U
#define LPDDR4__DENALI_CTL_4__WRITE_DATA_FIFO_DEPTH_SHIFT                     8U
#define LPDDR4__DENALI_CTL_4__WRITE_DATA_FIFO_DEPTH_WIDTH                     8U
#define LPDDR4__WRITE_DATA_FIFO_DEPTH__REG DENALI_CTL_4
#define LPDDR4__WRITE_DATA_FIFO_DEPTH__FLD LPDDR4__DENALI_CTL_4__WRITE_DATA_FIFO_DEPTH

#define LPDDR4__DENALI_CTL_4__WRITE_DATA_FIFO_PTR_WIDTH_MASK         0x00FF0000U
#define LPDDR4__DENALI_CTL_4__WRITE_DATA_FIFO_PTR_WIDTH_SHIFT                16U
#define LPDDR4__DENALI_CTL_4__WRITE_DATA_FIFO_PTR_WIDTH_WIDTH                 8U
#define LPDDR4__WRITE_DATA_FIFO_PTR_WIDTH__REG DENALI_CTL_4
#define LPDDR4__WRITE_DATA_FIFO_PTR_WIDTH__FLD LPDDR4__DENALI_CTL_4__WRITE_DATA_FIFO_PTR_WIDTH

#define LPDDR4__DENALI_CTL_5_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_5_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_5__MEMCD_RMODW_FIFO_DEPTH_MASK            0x0000FFFFU
#define LPDDR4__DENALI_CTL_5__MEMCD_RMODW_FIFO_DEPTH_SHIFT                    0U
#define LPDDR4__DENALI_CTL_5__MEMCD_RMODW_FIFO_DEPTH_WIDTH                   16U
#define LPDDR4__MEMCD_RMODW_FIFO_DEPTH__REG DENALI_CTL_5
#define LPDDR4__MEMCD_RMODW_FIFO_DEPTH__FLD LPDDR4__DENALI_CTL_5__MEMCD_RMODW_FIFO_DEPTH

#define LPDDR4__DENALI_CTL_5__MEMCD_RMODW_FIFO_PTR_WIDTH_MASK        0x00FF0000U
#define LPDDR4__DENALI_CTL_5__MEMCD_RMODW_FIFO_PTR_WIDTH_SHIFT               16U
#define LPDDR4__DENALI_CTL_5__MEMCD_RMODW_FIFO_PTR_WIDTH_WIDTH                8U
#define LPDDR4__MEMCD_RMODW_FIFO_PTR_WIDTH__REG DENALI_CTL_5
#define LPDDR4__MEMCD_RMODW_FIFO_PTR_WIDTH__FLD LPDDR4__DENALI_CTL_5__MEMCD_RMODW_FIFO_PTR_WIDTH

#define LPDDR4__DENALI_CTL_5__ASYNC_CDC_STAGES_MASK                  0xFF000000U
#define LPDDR4__DENALI_CTL_5__ASYNC_CDC_STAGES_SHIFT                         24U
#define LPDDR4__DENALI_CTL_5__ASYNC_CDC_STAGES_WIDTH                          8U
#define LPDDR4__ASYNC_CDC_STAGES__REG DENALI_CTL_5
#define LPDDR4__ASYNC_CDC_STAGES__FLD LPDDR4__DENALI_CTL_5__ASYNC_CDC_STAGES

#define LPDDR4__DENALI_CTL_6_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_6_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_6__AXI0_CMDFIFO_LOG2_DEPTH_MASK           0x000000FFU
#define LPDDR4__DENALI_CTL_6__AXI0_CMDFIFO_LOG2_DEPTH_SHIFT                   0U
#define LPDDR4__DENALI_CTL_6__AXI0_CMDFIFO_LOG2_DEPTH_WIDTH                   8U
#define LPDDR4__AXI0_CMDFIFO_LOG2_DEPTH__REG DENALI_CTL_6
#define LPDDR4__AXI0_CMDFIFO_LOG2_DEPTH__FLD LPDDR4__DENALI_CTL_6__AXI0_CMDFIFO_LOG2_DEPTH

#define LPDDR4__DENALI_CTL_6__AXI0_RDFIFO_LOG2_DEPTH_MASK            0x0000FF00U
#define LPDDR4__DENALI_CTL_6__AXI0_RDFIFO_LOG2_DEPTH_SHIFT                    8U
#define LPDDR4__DENALI_CTL_6__AXI0_RDFIFO_LOG2_DEPTH_WIDTH                    8U
#define LPDDR4__AXI0_RDFIFO_LOG2_DEPTH__REG DENALI_CTL_6
#define LPDDR4__AXI0_RDFIFO_LOG2_DEPTH__FLD LPDDR4__DENALI_CTL_6__AXI0_RDFIFO_LOG2_DEPTH

#define LPDDR4__DENALI_CTL_6__AXI0_WR_ARRAY_LOG2_DEPTH_MASK          0x00FF0000U
#define LPDDR4__DENALI_CTL_6__AXI0_WR_ARRAY_LOG2_DEPTH_SHIFT                 16U
#define LPDDR4__DENALI_CTL_6__AXI0_WR_ARRAY_LOG2_DEPTH_WIDTH                  8U
#define LPDDR4__AXI0_WR_ARRAY_LOG2_DEPTH__REG DENALI_CTL_6
#define LPDDR4__AXI0_WR_ARRAY_LOG2_DEPTH__FLD LPDDR4__DENALI_CTL_6__AXI0_WR_ARRAY_LOG2_DEPTH

#define LPDDR4__DENALI_CTL_6__AXI0_WRCMD_PROC_FIFO_LOG2_DEPTH_MASK   0xFF000000U
#define LPDDR4__DENALI_CTL_6__AXI0_WRCMD_PROC_FIFO_LOG2_DEPTH_SHIFT          24U
#define LPDDR4__DENALI_CTL_6__AXI0_WRCMD_PROC_FIFO_LOG2_DEPTH_WIDTH           8U
#define LPDDR4__AXI0_WRCMD_PROC_FIFO_LOG2_DEPTH__REG DENALI_CTL_6
#define LPDDR4__AXI0_WRCMD_PROC_FIFO_LOG2_DEPTH__FLD LPDDR4__DENALI_CTL_6__AXI0_WRCMD_PROC_FIFO_LOG2_DEPTH

#define LPDDR4__DENALI_CTL_7_READ_MASK                               0x00FFFFFFU
#define LPDDR4__DENALI_CTL_7_WRITE_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_CTL_7__TINIT_F0_MASK                          0x00FFFFFFU
#define LPDDR4__DENALI_CTL_7__TINIT_F0_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_7__TINIT_F0_WIDTH                                 24U
#define LPDDR4__TINIT_F0__REG DENALI_CTL_7
#define LPDDR4__TINIT_F0__FLD LPDDR4__DENALI_CTL_7__TINIT_F0

#define LPDDR4__DENALI_CTL_8_READ_MASK                               0x00FFFFFFU
#define LPDDR4__DENALI_CTL_8_WRITE_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_CTL_8__TINIT3_F0_MASK                         0x00FFFFFFU
#define LPDDR4__DENALI_CTL_8__TINIT3_F0_SHIFT                                 0U
#define LPDDR4__DENALI_CTL_8__TINIT3_F0_WIDTH                                24U
#define LPDDR4__TINIT3_F0__REG DENALI_CTL_8
#define LPDDR4__TINIT3_F0__FLD LPDDR4__DENALI_CTL_8__TINIT3_F0

#define LPDDR4__DENALI_CTL_9_READ_MASK                               0x00FFFFFFU
#define LPDDR4__DENALI_CTL_9_WRITE_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_CTL_9__TINIT4_F0_MASK                         0x00FFFFFFU
#define LPDDR4__DENALI_CTL_9__TINIT4_F0_SHIFT                                 0U
#define LPDDR4__DENALI_CTL_9__TINIT4_F0_WIDTH                                24U
#define LPDDR4__TINIT4_F0__REG DENALI_CTL_9
#define LPDDR4__TINIT4_F0__FLD LPDDR4__DENALI_CTL_9__TINIT4_F0

#define LPDDR4__DENALI_CTL_10_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_CTL_10_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_10__TINIT5_F0_MASK                        0x00FFFFFFU
#define LPDDR4__DENALI_CTL_10__TINIT5_F0_SHIFT                                0U
#define LPDDR4__DENALI_CTL_10__TINIT5_F0_WIDTH                               24U
#define LPDDR4__TINIT5_F0__REG DENALI_CTL_10
#define LPDDR4__TINIT5_F0__FLD LPDDR4__DENALI_CTL_10__TINIT5_F0

#define LPDDR4__DENALI_CTL_11_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_CTL_11_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_11__TINIT_F1_MASK                         0x00FFFFFFU
#define LPDDR4__DENALI_CTL_11__TINIT_F1_SHIFT                                 0U
#define LPDDR4__DENALI_CTL_11__TINIT_F1_WIDTH                                24U
#define LPDDR4__TINIT_F1__REG DENALI_CTL_11
#define LPDDR4__TINIT_F1__FLD LPDDR4__DENALI_CTL_11__TINIT_F1

#define LPDDR4__DENALI_CTL_12_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_CTL_12_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_12__TINIT3_F1_MASK                        0x00FFFFFFU
#define LPDDR4__DENALI_CTL_12__TINIT3_F1_SHIFT                                0U
#define LPDDR4__DENALI_CTL_12__TINIT3_F1_WIDTH                               24U
#define LPDDR4__TINIT3_F1__REG DENALI_CTL_12
#define LPDDR4__TINIT3_F1__FLD LPDDR4__DENALI_CTL_12__TINIT3_F1

#define LPDDR4__DENALI_CTL_13_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_CTL_13_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_13__TINIT4_F1_MASK                        0x00FFFFFFU
#define LPDDR4__DENALI_CTL_13__TINIT4_F1_SHIFT                                0U
#define LPDDR4__DENALI_CTL_13__TINIT4_F1_WIDTH                               24U
#define LPDDR4__TINIT4_F1__REG DENALI_CTL_13
#define LPDDR4__TINIT4_F1__FLD LPDDR4__DENALI_CTL_13__TINIT4_F1

#define LPDDR4__DENALI_CTL_14_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_CTL_14_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_14__TINIT5_F1_MASK                        0x00FFFFFFU
#define LPDDR4__DENALI_CTL_14__TINIT5_F1_SHIFT                                0U
#define LPDDR4__DENALI_CTL_14__TINIT5_F1_WIDTH                               24U
#define LPDDR4__TINIT5_F1__REG DENALI_CTL_14
#define LPDDR4__TINIT5_F1__FLD LPDDR4__DENALI_CTL_14__TINIT5_F1

#define LPDDR4__DENALI_CTL_15_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_CTL_15_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_15__TINIT_F2_MASK                         0x00FFFFFFU
#define LPDDR4__DENALI_CTL_15__TINIT_F2_SHIFT                                 0U
#define LPDDR4__DENALI_CTL_15__TINIT_F2_WIDTH                                24U
#define LPDDR4__TINIT_F2__REG DENALI_CTL_15
#define LPDDR4__TINIT_F2__FLD LPDDR4__DENALI_CTL_15__TINIT_F2

#define LPDDR4__DENALI_CTL_16_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_CTL_16_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_16__TINIT3_F2_MASK                        0x00FFFFFFU
#define LPDDR4__DENALI_CTL_16__TINIT3_F2_SHIFT                                0U
#define LPDDR4__DENALI_CTL_16__TINIT3_F2_WIDTH                               24U
#define LPDDR4__TINIT3_F2__REG DENALI_CTL_16
#define LPDDR4__TINIT3_F2__FLD LPDDR4__DENALI_CTL_16__TINIT3_F2

#define LPDDR4__DENALI_CTL_17_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_CTL_17_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_17__TINIT4_F2_MASK                        0x00FFFFFFU
#define LPDDR4__DENALI_CTL_17__TINIT4_F2_SHIFT                                0U
#define LPDDR4__DENALI_CTL_17__TINIT4_F2_WIDTH                               24U
#define LPDDR4__TINIT4_F2__REG DENALI_CTL_17
#define LPDDR4__TINIT4_F2__FLD LPDDR4__DENALI_CTL_17__TINIT4_F2

#define LPDDR4__DENALI_CTL_18_READ_MASK                              0x01FFFFFFU
#define LPDDR4__DENALI_CTL_18_WRITE_MASK                             0x01FFFFFFU
#define LPDDR4__DENALI_CTL_18__TINIT5_F2_MASK                        0x00FFFFFFU
#define LPDDR4__DENALI_CTL_18__TINIT5_F2_SHIFT                                0U
#define LPDDR4__DENALI_CTL_18__TINIT5_F2_WIDTH                               24U
#define LPDDR4__TINIT5_F2__REG DENALI_CTL_18
#define LPDDR4__TINIT5_F2__FLD LPDDR4__DENALI_CTL_18__TINIT5_F2

#define LPDDR4__DENALI_CTL_18__NO_AUTO_MRR_INIT_MASK                 0x01000000U
#define LPDDR4__DENALI_CTL_18__NO_AUTO_MRR_INIT_SHIFT                        24U
#define LPDDR4__DENALI_CTL_18__NO_AUTO_MRR_INIT_WIDTH                         1U
#define LPDDR4__DENALI_CTL_18__NO_AUTO_MRR_INIT_WOCLR                         0U
#define LPDDR4__DENALI_CTL_18__NO_AUTO_MRR_INIT_WOSET                         0U
#define LPDDR4__NO_AUTO_MRR_INIT__REG DENALI_CTL_18
#define LPDDR4__NO_AUTO_MRR_INIT__FLD LPDDR4__DENALI_CTL_18__NO_AUTO_MRR_INIT

#define LPDDR4__DENALI_CTL_19_READ_MASK                              0x03030301U
#define LPDDR4__DENALI_CTL_19_WRITE_MASK                             0x03030301U
#define LPDDR4__DENALI_CTL_19__MRR_ERROR_STATUS_MASK                 0x00000001U
#define LPDDR4__DENALI_CTL_19__MRR_ERROR_STATUS_SHIFT                         0U
#define LPDDR4__DENALI_CTL_19__MRR_ERROR_STATUS_WIDTH                         1U
#define LPDDR4__DENALI_CTL_19__MRR_ERROR_STATUS_WOCLR                         0U
#define LPDDR4__DENALI_CTL_19__MRR_ERROR_STATUS_WOSET                         0U
#define LPDDR4__MRR_ERROR_STATUS__REG DENALI_CTL_19
#define LPDDR4__MRR_ERROR_STATUS__FLD LPDDR4__DENALI_CTL_19__MRR_ERROR_STATUS

#define LPDDR4__DENALI_CTL_19__DFI_FREQ_RATIO_F0_MASK                0x00000300U
#define LPDDR4__DENALI_CTL_19__DFI_FREQ_RATIO_F0_SHIFT                        8U
#define LPDDR4__DENALI_CTL_19__DFI_FREQ_RATIO_F0_WIDTH                        2U
#define LPDDR4__DFI_FREQ_RATIO_F0__REG DENALI_CTL_19
#define LPDDR4__DFI_FREQ_RATIO_F0__FLD LPDDR4__DENALI_CTL_19__DFI_FREQ_RATIO_F0

#define LPDDR4__DENALI_CTL_19__DFI_FREQ_RATIO_F1_MASK                0x00030000U
#define LPDDR4__DENALI_CTL_19__DFI_FREQ_RATIO_F1_SHIFT                       16U
#define LPDDR4__DENALI_CTL_19__DFI_FREQ_RATIO_F1_WIDTH                        2U
#define LPDDR4__DFI_FREQ_RATIO_F1__REG DENALI_CTL_19
#define LPDDR4__DFI_FREQ_RATIO_F1__FLD LPDDR4__DENALI_CTL_19__DFI_FREQ_RATIO_F1

#define LPDDR4__DENALI_CTL_19__DFI_FREQ_RATIO_F2_MASK                0x03000000U
#define LPDDR4__DENALI_CTL_19__DFI_FREQ_RATIO_F2_SHIFT                       24U
#define LPDDR4__DENALI_CTL_19__DFI_FREQ_RATIO_F2_WIDTH                        2U
#define LPDDR4__DFI_FREQ_RATIO_F2__REG DENALI_CTL_19
#define LPDDR4__DFI_FREQ_RATIO_F2__FLD LPDDR4__DENALI_CTL_19__DFI_FREQ_RATIO_F2

#define LPDDR4__DENALI_CTL_20_READ_MASK                              0x01030101U
#define LPDDR4__DENALI_CTL_20_WRITE_MASK                             0x01030101U
#define LPDDR4__DENALI_CTL_20__DFI_CMD_RATIO_MASK                    0x00000001U
#define LPDDR4__DENALI_CTL_20__DFI_CMD_RATIO_SHIFT                            0U
#define LPDDR4__DENALI_CTL_20__DFI_CMD_RATIO_WIDTH                            1U
#define LPDDR4__DENALI_CTL_20__DFI_CMD_RATIO_WOCLR                            0U
#define LPDDR4__DENALI_CTL_20__DFI_CMD_RATIO_WOSET                            0U
#define LPDDR4__DFI_CMD_RATIO__REG DENALI_CTL_20
#define LPDDR4__DFI_CMD_RATIO__FLD LPDDR4__DENALI_CTL_20__DFI_CMD_RATIO

#define LPDDR4__DENALI_CTL_20__NO_MRW_INIT_MASK                      0x00000100U
#define LPDDR4__DENALI_CTL_20__NO_MRW_INIT_SHIFT                              8U
#define LPDDR4__DENALI_CTL_20__NO_MRW_INIT_WIDTH                              1U
#define LPDDR4__DENALI_CTL_20__NO_MRW_INIT_WOCLR                              0U
#define LPDDR4__DENALI_CTL_20__NO_MRW_INIT_WOSET                              0U
#define LPDDR4__NO_MRW_INIT__REG DENALI_CTL_20
#define LPDDR4__NO_MRW_INIT__FLD LPDDR4__DENALI_CTL_20__NO_MRW_INIT

#define LPDDR4__DENALI_CTL_20__ODT_VALUE_MASK                        0x00030000U
#define LPDDR4__DENALI_CTL_20__ODT_VALUE_SHIFT                               16U
#define LPDDR4__DENALI_CTL_20__ODT_VALUE_WIDTH                                2U
#define LPDDR4__ODT_VALUE__REG DENALI_CTL_20
#define LPDDR4__ODT_VALUE__FLD LPDDR4__DENALI_CTL_20__ODT_VALUE

#define LPDDR4__DENALI_CTL_20__PHY_INDEP_TRAIN_MODE_MASK             0x01000000U
#define LPDDR4__DENALI_CTL_20__PHY_INDEP_TRAIN_MODE_SHIFT                    24U
#define LPDDR4__DENALI_CTL_20__PHY_INDEP_TRAIN_MODE_WIDTH                     1U
#define LPDDR4__DENALI_CTL_20__PHY_INDEP_TRAIN_MODE_WOCLR                     0U
#define LPDDR4__DENALI_CTL_20__PHY_INDEP_TRAIN_MODE_WOSET                     0U
#define LPDDR4__PHY_INDEP_TRAIN_MODE__REG DENALI_CTL_20
#define LPDDR4__PHY_INDEP_TRAIN_MODE__FLD LPDDR4__DENALI_CTL_20__PHY_INDEP_TRAIN_MODE

#define LPDDR4__DENALI_CTL_21_READ_MASK                              0x1F1F013FU
#define LPDDR4__DENALI_CTL_21_WRITE_MASK                             0x1F1F013FU
#define LPDDR4__DENALI_CTL_21__TSREF2PHYMSTR_MASK                    0x0000003FU
#define LPDDR4__DENALI_CTL_21__TSREF2PHYMSTR_SHIFT                            0U
#define LPDDR4__DENALI_CTL_21__TSREF2PHYMSTR_WIDTH                            6U
#define LPDDR4__TSREF2PHYMSTR__REG DENALI_CTL_21
#define LPDDR4__TSREF2PHYMSTR__FLD LPDDR4__DENALI_CTL_21__TSREF2PHYMSTR

#define LPDDR4__DENALI_CTL_21__PHY_INDEP_INIT_MODE_MASK              0x00000100U
#define LPDDR4__DENALI_CTL_21__PHY_INDEP_INIT_MODE_SHIFT                      8U
#define LPDDR4__DENALI_CTL_21__PHY_INDEP_INIT_MODE_WIDTH                      1U
#define LPDDR4__DENALI_CTL_21__PHY_INDEP_INIT_MODE_WOCLR                      0U
#define LPDDR4__DENALI_CTL_21__PHY_INDEP_INIT_MODE_WOSET                      0U
#define LPDDR4__PHY_INDEP_INIT_MODE__REG DENALI_CTL_21
#define LPDDR4__PHY_INDEP_INIT_MODE__FLD LPDDR4__DENALI_CTL_21__PHY_INDEP_INIT_MODE

#define LPDDR4__DENALI_CTL_21__DFIBUS_FREQ_F0_MASK                   0x001F0000U
#define LPDDR4__DENALI_CTL_21__DFIBUS_FREQ_F0_SHIFT                          16U
#define LPDDR4__DENALI_CTL_21__DFIBUS_FREQ_F0_WIDTH                           5U
#define LPDDR4__DFIBUS_FREQ_F0__REG DENALI_CTL_21
#define LPDDR4__DFIBUS_FREQ_F0__FLD LPDDR4__DENALI_CTL_21__DFIBUS_FREQ_F0

#define LPDDR4__DENALI_CTL_21__DFIBUS_FREQ_F1_MASK                   0x1F000000U
#define LPDDR4__DENALI_CTL_21__DFIBUS_FREQ_F1_SHIFT                          24U
#define LPDDR4__DENALI_CTL_21__DFIBUS_FREQ_F1_WIDTH                           5U
#define LPDDR4__DFIBUS_FREQ_F1__REG DENALI_CTL_21
#define LPDDR4__DFIBUS_FREQ_F1__FLD LPDDR4__DENALI_CTL_21__DFIBUS_FREQ_F1

#define LPDDR4__DENALI_CTL_22_READ_MASK                              0x0303031FU
#define LPDDR4__DENALI_CTL_22_WRITE_MASK                             0x0303031FU
#define LPDDR4__DENALI_CTL_22__DFIBUS_FREQ_F2_MASK                   0x0000001FU
#define LPDDR4__DENALI_CTL_22__DFIBUS_FREQ_F2_SHIFT                           0U
#define LPDDR4__DENALI_CTL_22__DFIBUS_FREQ_F2_WIDTH                           5U
#define LPDDR4__DFIBUS_FREQ_F2__REG DENALI_CTL_22
#define LPDDR4__DFIBUS_FREQ_F2__FLD LPDDR4__DENALI_CTL_22__DFIBUS_FREQ_F2

#define LPDDR4__DENALI_CTL_22__FREQ_CHANGE_TYPE_F0_MASK              0x00000300U
#define LPDDR4__DENALI_CTL_22__FREQ_CHANGE_TYPE_F0_SHIFT                      8U
#define LPDDR4__DENALI_CTL_22__FREQ_CHANGE_TYPE_F0_WIDTH                      2U
#define LPDDR4__FREQ_CHANGE_TYPE_F0__REG DENALI_CTL_22
#define LPDDR4__FREQ_CHANGE_TYPE_F0__FLD LPDDR4__DENALI_CTL_22__FREQ_CHANGE_TYPE_F0

#define LPDDR4__DENALI_CTL_22__FREQ_CHANGE_TYPE_F1_MASK              0x00030000U
#define LPDDR4__DENALI_CTL_22__FREQ_CHANGE_TYPE_F1_SHIFT                     16U
#define LPDDR4__DENALI_CTL_22__FREQ_CHANGE_TYPE_F1_WIDTH                      2U
#define LPDDR4__FREQ_CHANGE_TYPE_F1__REG DENALI_CTL_22
#define LPDDR4__FREQ_CHANGE_TYPE_F1__FLD LPDDR4__DENALI_CTL_22__FREQ_CHANGE_TYPE_F1

#define LPDDR4__DENALI_CTL_22__FREQ_CHANGE_TYPE_F2_MASK              0x03000000U
#define LPDDR4__DENALI_CTL_22__FREQ_CHANGE_TYPE_F2_SHIFT                     24U
#define LPDDR4__DENALI_CTL_22__FREQ_CHANGE_TYPE_F2_WIDTH                      2U
#define LPDDR4__FREQ_CHANGE_TYPE_F2__REG DENALI_CTL_22
#define LPDDR4__FREQ_CHANGE_TYPE_F2__FLD LPDDR4__DENALI_CTL_22__FREQ_CHANGE_TYPE_F2

#define LPDDR4__DENALI_CTL_23_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_23_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_23__TRST_PWRON_MASK                       0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_23__TRST_PWRON_SHIFT                               0U
#define LPDDR4__DENALI_CTL_23__TRST_PWRON_WIDTH                              32U
#define LPDDR4__TRST_PWRON__REG DENALI_CTL_23
#define LPDDR4__TRST_PWRON__FLD LPDDR4__DENALI_CTL_23__TRST_PWRON

#define LPDDR4__DENALI_CTL_24_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_24_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_24__CKE_INACTIVE_MASK                     0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_24__CKE_INACTIVE_SHIFT                             0U
#define LPDDR4__DENALI_CTL_24__CKE_INACTIVE_WIDTH                            32U
#define LPDDR4__CKE_INACTIVE__REG DENALI_CTL_24
#define LPDDR4__CKE_INACTIVE__FLD LPDDR4__DENALI_CTL_24__CKE_INACTIVE

#define LPDDR4__DENALI_CTL_25_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_25_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_25__TDLL_F0_MASK                          0x0000FFFFU
#define LPDDR4__DENALI_CTL_25__TDLL_F0_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_25__TDLL_F0_WIDTH                                 16U
#define LPDDR4__TDLL_F0__REG DENALI_CTL_25
#define LPDDR4__TDLL_F0__FLD LPDDR4__DENALI_CTL_25__TDLL_F0

#define LPDDR4__DENALI_CTL_25__TDLL_F1_MASK                          0xFFFF0000U
#define LPDDR4__DENALI_CTL_25__TDLL_F1_SHIFT                                 16U
#define LPDDR4__DENALI_CTL_25__TDLL_F1_WIDTH                                 16U
#define LPDDR4__TDLL_F1__REG DENALI_CTL_25
#define LPDDR4__TDLL_F1__FLD LPDDR4__DENALI_CTL_25__TDLL_F1

#define LPDDR4__DENALI_CTL_26_READ_MASK                              0x0301FFFFU
#define LPDDR4__DENALI_CTL_26_WRITE_MASK                             0x0301FFFFU
#define LPDDR4__DENALI_CTL_26__TDLL_F2_MASK                          0x0000FFFFU
#define LPDDR4__DENALI_CTL_26__TDLL_F2_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_26__TDLL_F2_WIDTH                                 16U
#define LPDDR4__TDLL_F2__REG DENALI_CTL_26
#define LPDDR4__TDLL_F2__FLD LPDDR4__DENALI_CTL_26__TDLL_F2

#define LPDDR4__DENALI_CTL_26__LPC_SW_ENTER_DQS_OSC_IN_PROGRESS_ERR_STATUS_MASK 0x00010000U
#define LPDDR4__DENALI_CTL_26__LPC_SW_ENTER_DQS_OSC_IN_PROGRESS_ERR_STATUS_SHIFT 16U
#define LPDDR4__DENALI_CTL_26__LPC_SW_ENTER_DQS_OSC_IN_PROGRESS_ERR_STATUS_WIDTH 1U
#define LPDDR4__DENALI_CTL_26__LPC_SW_ENTER_DQS_OSC_IN_PROGRESS_ERR_STATUS_WOCLR 0U
#define LPDDR4__DENALI_CTL_26__LPC_SW_ENTER_DQS_OSC_IN_PROGRESS_ERR_STATUS_WOSET 0U
#define LPDDR4__LPC_SW_ENTER_DQS_OSC_IN_PROGRESS_ERR_STATUS__REG DENALI_CTL_26
#define LPDDR4__LPC_SW_ENTER_DQS_OSC_IN_PROGRESS_ERR_STATUS__FLD LPDDR4__DENALI_CTL_26__LPC_SW_ENTER_DQS_OSC_IN_PROGRESS_ERR_STATUS

#define LPDDR4__DENALI_CTL_26__DQS_OSC_PER_CS_OOV_TRAINING_STATUS_MASK 0x03000000U
#define LPDDR4__DENALI_CTL_26__DQS_OSC_PER_CS_OOV_TRAINING_STATUS_SHIFT      24U
#define LPDDR4__DENALI_CTL_26__DQS_OSC_PER_CS_OOV_TRAINING_STATUS_WIDTH       2U
#define LPDDR4__DQS_OSC_PER_CS_OOV_TRAINING_STATUS__REG DENALI_CTL_26
#define LPDDR4__DQS_OSC_PER_CS_OOV_TRAINING_STATUS__FLD LPDDR4__DENALI_CTL_26__DQS_OSC_PER_CS_OOV_TRAINING_STATUS

#define LPDDR4__DENALI_CTL_27_READ_MASK                              0xFFFFFF01U
#define LPDDR4__DENALI_CTL_27_WRITE_MASK                             0xFFFFFF01U
#define LPDDR4__DENALI_CTL_27__DQS_OSC_TST_MASK                      0x00000001U
#define LPDDR4__DENALI_CTL_27__DQS_OSC_TST_SHIFT                              0U
#define LPDDR4__DENALI_CTL_27__DQS_OSC_TST_WIDTH                              1U
#define LPDDR4__DENALI_CTL_27__DQS_OSC_TST_WOCLR                              0U
#define LPDDR4__DENALI_CTL_27__DQS_OSC_TST_WOSET                              0U
#define LPDDR4__DQS_OSC_TST__REG DENALI_CTL_27
#define LPDDR4__DQS_OSC_TST__FLD LPDDR4__DENALI_CTL_27__DQS_OSC_TST

#define LPDDR4__DENALI_CTL_27__DQS_OSC_MPC_CMD_MASK                  0xFFFFFF00U
#define LPDDR4__DENALI_CTL_27__DQS_OSC_MPC_CMD_SHIFT                          8U
#define LPDDR4__DENALI_CTL_27__DQS_OSC_MPC_CMD_WIDTH                         24U
#define LPDDR4__DQS_OSC_MPC_CMD__REG DENALI_CTL_27
#define LPDDR4__DQS_OSC_MPC_CMD__FLD LPDDR4__DENALI_CTL_27__DQS_OSC_MPC_CMD

#define LPDDR4__DENALI_CTL_28_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_CTL_28_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_28__MRR_LSB_REG_MASK                      0x000000FFU
#define LPDDR4__DENALI_CTL_28__MRR_LSB_REG_SHIFT                              0U
#define LPDDR4__DENALI_CTL_28__MRR_LSB_REG_WIDTH                              8U
#define LPDDR4__MRR_LSB_REG__REG DENALI_CTL_28
#define LPDDR4__MRR_LSB_REG__FLD LPDDR4__DENALI_CTL_28__MRR_LSB_REG

#define LPDDR4__DENALI_CTL_28__MRR_MSB_REG_MASK                      0x0000FF00U
#define LPDDR4__DENALI_CTL_28__MRR_MSB_REG_SHIFT                              8U
#define LPDDR4__DENALI_CTL_28__MRR_MSB_REG_WIDTH                              8U
#define LPDDR4__MRR_MSB_REG__REG DENALI_CTL_28
#define LPDDR4__MRR_MSB_REG__FLD LPDDR4__DENALI_CTL_28__MRR_MSB_REG

#define LPDDR4__DENALI_CTL_28__DQS_OSC_ENABLE_MASK                   0x00010000U
#define LPDDR4__DENALI_CTL_28__DQS_OSC_ENABLE_SHIFT                          16U
#define LPDDR4__DENALI_CTL_28__DQS_OSC_ENABLE_WIDTH                           1U
#define LPDDR4__DENALI_CTL_28__DQS_OSC_ENABLE_WOCLR                           0U
#define LPDDR4__DENALI_CTL_28__DQS_OSC_ENABLE_WOSET                           0U
#define LPDDR4__DQS_OSC_ENABLE__REG DENALI_CTL_28
#define LPDDR4__DQS_OSC_ENABLE__FLD LPDDR4__DENALI_CTL_28__DQS_OSC_ENABLE

#define LPDDR4__DENALI_CTL_29_READ_MASK                              0x000F7FFFU
#define LPDDR4__DENALI_CTL_29_WRITE_MASK                             0x000F7FFFU
#define LPDDR4__DENALI_CTL_29__DQS_OSC_PERIOD_MASK                   0x00007FFFU
#define LPDDR4__DENALI_CTL_29__DQS_OSC_PERIOD_SHIFT                           0U
#define LPDDR4__DENALI_CTL_29__DQS_OSC_PERIOD_WIDTH                          15U
#define LPDDR4__DQS_OSC_PERIOD__REG DENALI_CTL_29
#define LPDDR4__DQS_OSC_PERIOD__FLD LPDDR4__DENALI_CTL_29__DQS_OSC_PERIOD

#define LPDDR4__DENALI_CTL_29__FUNC_VALID_CYCLES_MASK                0x000F0000U
#define LPDDR4__DENALI_CTL_29__FUNC_VALID_CYCLES_SHIFT                       16U
#define LPDDR4__DENALI_CTL_29__FUNC_VALID_CYCLES_WIDTH                        4U
#define LPDDR4__FUNC_VALID_CYCLES__REG DENALI_CTL_29
#define LPDDR4__FUNC_VALID_CYCLES__FLD LPDDR4__DENALI_CTL_29__FUNC_VALID_CYCLES

#define LPDDR4__DENALI_CTL_30_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_30_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_30__DQS_OSC_NORM_THRESHOLD_MASK           0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_30__DQS_OSC_NORM_THRESHOLD_SHIFT                   0U
#define LPDDR4__DENALI_CTL_30__DQS_OSC_NORM_THRESHOLD_WIDTH                  32U
#define LPDDR4__DQS_OSC_NORM_THRESHOLD__REG DENALI_CTL_30
#define LPDDR4__DQS_OSC_NORM_THRESHOLD__FLD LPDDR4__DENALI_CTL_30__DQS_OSC_NORM_THRESHOLD

#define LPDDR4__DENALI_CTL_31_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_31_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_31__DQS_OSC_HIGH_THRESHOLD_MASK           0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_31__DQS_OSC_HIGH_THRESHOLD_SHIFT                   0U
#define LPDDR4__DENALI_CTL_31__DQS_OSC_HIGH_THRESHOLD_WIDTH                  32U
#define LPDDR4__DQS_OSC_HIGH_THRESHOLD__REG DENALI_CTL_31
#define LPDDR4__DQS_OSC_HIGH_THRESHOLD__FLD LPDDR4__DENALI_CTL_31__DQS_OSC_HIGH_THRESHOLD

#define LPDDR4__DENALI_CTL_32_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_32_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_32__DQS_OSC_TIMEOUT_MASK                  0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_32__DQS_OSC_TIMEOUT_SHIFT                          0U
#define LPDDR4__DENALI_CTL_32__DQS_OSC_TIMEOUT_WIDTH                         32U
#define LPDDR4__DQS_OSC_TIMEOUT__REG DENALI_CTL_32
#define LPDDR4__DQS_OSC_TIMEOUT__FLD LPDDR4__DENALI_CTL_32__DQS_OSC_TIMEOUT

#define LPDDR4__DENALI_CTL_33_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_33_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_33__DQS_OSC_PROMOTE_THRESHOLD_MASK        0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_33__DQS_OSC_PROMOTE_THRESHOLD_SHIFT                0U
#define LPDDR4__DENALI_CTL_33__DQS_OSC_PROMOTE_THRESHOLD_WIDTH               32U
#define LPDDR4__DQS_OSC_PROMOTE_THRESHOLD__REG DENALI_CTL_33
#define LPDDR4__DQS_OSC_PROMOTE_THRESHOLD__FLD LPDDR4__DENALI_CTL_33__DQS_OSC_PROMOTE_THRESHOLD

#define LPDDR4__DENALI_CTL_34_READ_MASK                              0xFF00FFFFU
#define LPDDR4__DENALI_CTL_34_WRITE_MASK                             0xFF00FFFFU
#define LPDDR4__DENALI_CTL_34__OSC_VARIANCE_LIMIT_MASK               0x0000FFFFU
#define LPDDR4__DENALI_CTL_34__OSC_VARIANCE_LIMIT_SHIFT                       0U
#define LPDDR4__DENALI_CTL_34__OSC_VARIANCE_LIMIT_WIDTH                      16U
#define LPDDR4__OSC_VARIANCE_LIMIT__REG DENALI_CTL_34
#define LPDDR4__OSC_VARIANCE_LIMIT__FLD LPDDR4__DENALI_CTL_34__OSC_VARIANCE_LIMIT

#define LPDDR4__DENALI_CTL_34__DQS_OSC_REQUEST_MASK                  0x00010000U
#define LPDDR4__DENALI_CTL_34__DQS_OSC_REQUEST_SHIFT                         16U
#define LPDDR4__DENALI_CTL_34__DQS_OSC_REQUEST_WIDTH                          1U
#define LPDDR4__DENALI_CTL_34__DQS_OSC_REQUEST_WOCLR                          0U
#define LPDDR4__DENALI_CTL_34__DQS_OSC_REQUEST_WOSET                          0U
#define LPDDR4__DQS_OSC_REQUEST__REG DENALI_CTL_34
#define LPDDR4__DQS_OSC_REQUEST__FLD LPDDR4__DENALI_CTL_34__DQS_OSC_REQUEST

#define LPDDR4__DENALI_CTL_34__TOSCO_F0_MASK                         0xFF000000U
#define LPDDR4__DENALI_CTL_34__TOSCO_F0_SHIFT                                24U
#define LPDDR4__DENALI_CTL_34__TOSCO_F0_WIDTH                                 8U
#define LPDDR4__TOSCO_F0__REG DENALI_CTL_34
#define LPDDR4__TOSCO_F0__FLD LPDDR4__DENALI_CTL_34__TOSCO_F0

#define LPDDR4__DENALI_CTL_35_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_35_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_35__TOSCO_F1_MASK                         0x000000FFU
#define LPDDR4__DENALI_CTL_35__TOSCO_F1_SHIFT                                 0U
#define LPDDR4__DENALI_CTL_35__TOSCO_F1_WIDTH                                 8U
#define LPDDR4__TOSCO_F1__REG DENALI_CTL_35
#define LPDDR4__TOSCO_F1__FLD LPDDR4__DENALI_CTL_35__TOSCO_F1

#define LPDDR4__DENALI_CTL_35__TOSCO_F2_MASK                         0x0000FF00U
#define LPDDR4__DENALI_CTL_35__TOSCO_F2_SHIFT                                 8U
#define LPDDR4__DENALI_CTL_35__TOSCO_F2_WIDTH                                 8U
#define LPDDR4__TOSCO_F2__REG DENALI_CTL_35
#define LPDDR4__TOSCO_F2__FLD LPDDR4__DENALI_CTL_35__TOSCO_F2

#define LPDDR4__DENALI_CTL_35__DQS_OSC_BASE_VALUE_0_CS0_MASK         0xFFFF0000U
#define LPDDR4__DENALI_CTL_35__DQS_OSC_BASE_VALUE_0_CS0_SHIFT                16U
#define LPDDR4__DENALI_CTL_35__DQS_OSC_BASE_VALUE_0_CS0_WIDTH                16U
#define LPDDR4__DQS_OSC_BASE_VALUE_0_CS0__REG DENALI_CTL_35
#define LPDDR4__DQS_OSC_BASE_VALUE_0_CS0__FLD LPDDR4__DENALI_CTL_35__DQS_OSC_BASE_VALUE_0_CS0

#define LPDDR4__DENALI_CTL_36_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_36_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_36__DQS_OSC_BASE_VALUE_1_CS0_MASK         0x0000FFFFU
#define LPDDR4__DENALI_CTL_36__DQS_OSC_BASE_VALUE_1_CS0_SHIFT                 0U
#define LPDDR4__DENALI_CTL_36__DQS_OSC_BASE_VALUE_1_CS0_WIDTH                16U
#define LPDDR4__DQS_OSC_BASE_VALUE_1_CS0__REG DENALI_CTL_36
#define LPDDR4__DQS_OSC_BASE_VALUE_1_CS0__FLD LPDDR4__DENALI_CTL_36__DQS_OSC_BASE_VALUE_1_CS0

#define LPDDR4__DENALI_CTL_36__DQS_OSC_BASE_VALUE_2_CS0_MASK         0xFFFF0000U
#define LPDDR4__DENALI_CTL_36__DQS_OSC_BASE_VALUE_2_CS0_SHIFT                16U
#define LPDDR4__DENALI_CTL_36__DQS_OSC_BASE_VALUE_2_CS0_WIDTH                16U
#define LPDDR4__DQS_OSC_BASE_VALUE_2_CS0__REG DENALI_CTL_36
#define LPDDR4__DQS_OSC_BASE_VALUE_2_CS0__FLD LPDDR4__DENALI_CTL_36__DQS_OSC_BASE_VALUE_2_CS0

#define LPDDR4__DENALI_CTL_37_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_37_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_37__DQS_OSC_BASE_VALUE_3_CS0_MASK         0x0000FFFFU
#define LPDDR4__DENALI_CTL_37__DQS_OSC_BASE_VALUE_3_CS0_SHIFT                 0U
#define LPDDR4__DENALI_CTL_37__DQS_OSC_BASE_VALUE_3_CS0_WIDTH                16U
#define LPDDR4__DQS_OSC_BASE_VALUE_3_CS0__REG DENALI_CTL_37
#define LPDDR4__DQS_OSC_BASE_VALUE_3_CS0__FLD LPDDR4__DENALI_CTL_37__DQS_OSC_BASE_VALUE_3_CS0

#define LPDDR4__DENALI_CTL_37__DQS_OSC_BASE_VALUE_0_CS1_MASK         0xFFFF0000U
#define LPDDR4__DENALI_CTL_37__DQS_OSC_BASE_VALUE_0_CS1_SHIFT                16U
#define LPDDR4__DENALI_CTL_37__DQS_OSC_BASE_VALUE_0_CS1_WIDTH                16U
#define LPDDR4__DQS_OSC_BASE_VALUE_0_CS1__REG DENALI_CTL_37
#define LPDDR4__DQS_OSC_BASE_VALUE_0_CS1__FLD LPDDR4__DENALI_CTL_37__DQS_OSC_BASE_VALUE_0_CS1

#define LPDDR4__DENALI_CTL_38_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_38_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_38__DQS_OSC_BASE_VALUE_1_CS1_MASK         0x0000FFFFU
#define LPDDR4__DENALI_CTL_38__DQS_OSC_BASE_VALUE_1_CS1_SHIFT                 0U
#define LPDDR4__DENALI_CTL_38__DQS_OSC_BASE_VALUE_1_CS1_WIDTH                16U
#define LPDDR4__DQS_OSC_BASE_VALUE_1_CS1__REG DENALI_CTL_38
#define LPDDR4__DQS_OSC_BASE_VALUE_1_CS1__FLD LPDDR4__DENALI_CTL_38__DQS_OSC_BASE_VALUE_1_CS1

#define LPDDR4__DENALI_CTL_38__DQS_OSC_BASE_VALUE_2_CS1_MASK         0xFFFF0000U
#define LPDDR4__DENALI_CTL_38__DQS_OSC_BASE_VALUE_2_CS1_SHIFT                16U
#define LPDDR4__DENALI_CTL_38__DQS_OSC_BASE_VALUE_2_CS1_WIDTH                16U
#define LPDDR4__DQS_OSC_BASE_VALUE_2_CS1__REG DENALI_CTL_38
#define LPDDR4__DQS_OSC_BASE_VALUE_2_CS1__FLD LPDDR4__DENALI_CTL_38__DQS_OSC_BASE_VALUE_2_CS1

#define LPDDR4__DENALI_CTL_39_READ_MASK                              0x010FFFFFU
#define LPDDR4__DENALI_CTL_39_WRITE_MASK                             0x010FFFFFU
#define LPDDR4__DENALI_CTL_39__DQS_OSC_BASE_VALUE_3_CS1_MASK         0x0000FFFFU
#define LPDDR4__DENALI_CTL_39__DQS_OSC_BASE_VALUE_3_CS1_SHIFT                 0U
#define LPDDR4__DENALI_CTL_39__DQS_OSC_BASE_VALUE_3_CS1_WIDTH                16U
#define LPDDR4__DQS_OSC_BASE_VALUE_3_CS1__REG DENALI_CTL_39
#define LPDDR4__DQS_OSC_BASE_VALUE_3_CS1__FLD LPDDR4__DENALI_CTL_39__DQS_OSC_BASE_VALUE_3_CS1

#define LPDDR4__DENALI_CTL_39__DQS_OSC_STATUS_MASK                   0x000F0000U
#define LPDDR4__DENALI_CTL_39__DQS_OSC_STATUS_SHIFT                          16U
#define LPDDR4__DENALI_CTL_39__DQS_OSC_STATUS_WIDTH                           4U
#define LPDDR4__DQS_OSC_STATUS__REG DENALI_CTL_39
#define LPDDR4__DQS_OSC_STATUS__FLD LPDDR4__DENALI_CTL_39__DQS_OSC_STATUS

#define LPDDR4__DENALI_CTL_39__DQS_OSC_IN_PROGRESS_STATUS_MASK       0x01000000U
#define LPDDR4__DENALI_CTL_39__DQS_OSC_IN_PROGRESS_STATUS_SHIFT              24U
#define LPDDR4__DENALI_CTL_39__DQS_OSC_IN_PROGRESS_STATUS_WIDTH               1U
#define LPDDR4__DENALI_CTL_39__DQS_OSC_IN_PROGRESS_STATUS_WOCLR               0U
#define LPDDR4__DENALI_CTL_39__DQS_OSC_IN_PROGRESS_STATUS_WOSET               0U
#define LPDDR4__DQS_OSC_IN_PROGRESS_STATUS__REG DENALI_CTL_39
#define LPDDR4__DQS_OSC_IN_PROGRESS_STATUS__FLD LPDDR4__DENALI_CTL_39__DQS_OSC_IN_PROGRESS_STATUS

#define LPDDR4__DENALI_CTL_40_READ_MASK                              0x0F3F7F7FU
#define LPDDR4__DENALI_CTL_40_WRITE_MASK                             0x0F3F7F7FU
#define LPDDR4__DENALI_CTL_40__CASLAT_LIN_F0_MASK                    0x0000007FU
#define LPDDR4__DENALI_CTL_40__CASLAT_LIN_F0_SHIFT                            0U
#define LPDDR4__DENALI_CTL_40__CASLAT_LIN_F0_WIDTH                            7U
#define LPDDR4__CASLAT_LIN_F0__REG DENALI_CTL_40
#define LPDDR4__CASLAT_LIN_F0__FLD LPDDR4__DENALI_CTL_40__CASLAT_LIN_F0

#define LPDDR4__DENALI_CTL_40__WRLAT_F0_MASK                         0x00007F00U
#define LPDDR4__DENALI_CTL_40__WRLAT_F0_SHIFT                                 8U
#define LPDDR4__DENALI_CTL_40__WRLAT_F0_WIDTH                                 7U
#define LPDDR4__WRLAT_F0__REG DENALI_CTL_40
#define LPDDR4__WRLAT_F0__FLD LPDDR4__DENALI_CTL_40__WRLAT_F0

#define LPDDR4__DENALI_CTL_40__ADDITIVE_LAT_F0_MASK                  0x003F0000U
#define LPDDR4__DENALI_CTL_40__ADDITIVE_LAT_F0_SHIFT                         16U
#define LPDDR4__DENALI_CTL_40__ADDITIVE_LAT_F0_WIDTH                          6U
#define LPDDR4__ADDITIVE_LAT_F0__REG DENALI_CTL_40
#define LPDDR4__ADDITIVE_LAT_F0__FLD LPDDR4__DENALI_CTL_40__ADDITIVE_LAT_F0

#define LPDDR4__DENALI_CTL_40__CA_PARITY_LAT_F0_MASK                 0x0F000000U
#define LPDDR4__DENALI_CTL_40__CA_PARITY_LAT_F0_SHIFT                        24U
#define LPDDR4__DENALI_CTL_40__CA_PARITY_LAT_F0_WIDTH                         4U
#define LPDDR4__CA_PARITY_LAT_F0__REG DENALI_CTL_40
#define LPDDR4__CA_PARITY_LAT_F0__FLD LPDDR4__DENALI_CTL_40__CA_PARITY_LAT_F0

#define LPDDR4__DENALI_CTL_41_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_41_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_41__TMOD_PAR_F0_MASK                      0x000000FFU
#define LPDDR4__DENALI_CTL_41__TMOD_PAR_F0_SHIFT                              0U
#define LPDDR4__DENALI_CTL_41__TMOD_PAR_F0_WIDTH                              8U
#define LPDDR4__TMOD_PAR_F0__REG DENALI_CTL_41
#define LPDDR4__TMOD_PAR_F0__FLD LPDDR4__DENALI_CTL_41__TMOD_PAR_F0

#define LPDDR4__DENALI_CTL_41__TMRD_PAR_F0_MASK                      0x0000FF00U
#define LPDDR4__DENALI_CTL_41__TMRD_PAR_F0_SHIFT                              8U
#define LPDDR4__DENALI_CTL_41__TMRD_PAR_F0_WIDTH                              8U
#define LPDDR4__TMRD_PAR_F0__REG DENALI_CTL_41
#define LPDDR4__TMRD_PAR_F0__FLD LPDDR4__DENALI_CTL_41__TMRD_PAR_F0

#define LPDDR4__DENALI_CTL_41__TMOD_PAR_MAX_PL_F0_MASK               0x00FF0000U
#define LPDDR4__DENALI_CTL_41__TMOD_PAR_MAX_PL_F0_SHIFT                      16U
#define LPDDR4__DENALI_CTL_41__TMOD_PAR_MAX_PL_F0_WIDTH                       8U
#define LPDDR4__TMOD_PAR_MAX_PL_F0__REG DENALI_CTL_41
#define LPDDR4__TMOD_PAR_MAX_PL_F0__FLD LPDDR4__DENALI_CTL_41__TMOD_PAR_MAX_PL_F0

#define LPDDR4__DENALI_CTL_41__TMRD_PAR_MAX_PL_F0_MASK               0xFF000000U
#define LPDDR4__DENALI_CTL_41__TMRD_PAR_MAX_PL_F0_SHIFT                      24U
#define LPDDR4__DENALI_CTL_41__TMRD_PAR_MAX_PL_F0_WIDTH                       8U
#define LPDDR4__TMRD_PAR_MAX_PL_F0__REG DENALI_CTL_41
#define LPDDR4__TMRD_PAR_MAX_PL_F0__FLD LPDDR4__DENALI_CTL_41__TMRD_PAR_MAX_PL_F0

#define LPDDR4__DENALI_CTL_42_READ_MASK                              0x0F3F7F7FU
#define LPDDR4__DENALI_CTL_42_WRITE_MASK                             0x0F3F7F7FU
#define LPDDR4__DENALI_CTL_42__CASLAT_LIN_F1_MASK                    0x0000007FU
#define LPDDR4__DENALI_CTL_42__CASLAT_LIN_F1_SHIFT                            0U
#define LPDDR4__DENALI_CTL_42__CASLAT_LIN_F1_WIDTH                            7U
#define LPDDR4__CASLAT_LIN_F1__REG DENALI_CTL_42
#define LPDDR4__CASLAT_LIN_F1__FLD LPDDR4__DENALI_CTL_42__CASLAT_LIN_F1

#define LPDDR4__DENALI_CTL_42__WRLAT_F1_MASK                         0x00007F00U
#define LPDDR4__DENALI_CTL_42__WRLAT_F1_SHIFT                                 8U
#define LPDDR4__DENALI_CTL_42__WRLAT_F1_WIDTH                                 7U
#define LPDDR4__WRLAT_F1__REG DENALI_CTL_42
#define LPDDR4__WRLAT_F1__FLD LPDDR4__DENALI_CTL_42__WRLAT_F1

#define LPDDR4__DENALI_CTL_42__ADDITIVE_LAT_F1_MASK                  0x003F0000U
#define LPDDR4__DENALI_CTL_42__ADDITIVE_LAT_F1_SHIFT                         16U
#define LPDDR4__DENALI_CTL_42__ADDITIVE_LAT_F1_WIDTH                          6U
#define LPDDR4__ADDITIVE_LAT_F1__REG DENALI_CTL_42
#define LPDDR4__ADDITIVE_LAT_F1__FLD LPDDR4__DENALI_CTL_42__ADDITIVE_LAT_F1

#define LPDDR4__DENALI_CTL_42__CA_PARITY_LAT_F1_MASK                 0x0F000000U
#define LPDDR4__DENALI_CTL_42__CA_PARITY_LAT_F1_SHIFT                        24U
#define LPDDR4__DENALI_CTL_42__CA_PARITY_LAT_F1_WIDTH                         4U
#define LPDDR4__CA_PARITY_LAT_F1__REG DENALI_CTL_42
#define LPDDR4__CA_PARITY_LAT_F1__FLD LPDDR4__DENALI_CTL_42__CA_PARITY_LAT_F1

#define LPDDR4__DENALI_CTL_43_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_43_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_43__TMOD_PAR_F1_MASK                      0x000000FFU
#define LPDDR4__DENALI_CTL_43__TMOD_PAR_F1_SHIFT                              0U
#define LPDDR4__DENALI_CTL_43__TMOD_PAR_F1_WIDTH                              8U
#define LPDDR4__TMOD_PAR_F1__REG DENALI_CTL_43
#define LPDDR4__TMOD_PAR_F1__FLD LPDDR4__DENALI_CTL_43__TMOD_PAR_F1

#define LPDDR4__DENALI_CTL_43__TMRD_PAR_F1_MASK                      0x0000FF00U
#define LPDDR4__DENALI_CTL_43__TMRD_PAR_F1_SHIFT                              8U
#define LPDDR4__DENALI_CTL_43__TMRD_PAR_F1_WIDTH                              8U
#define LPDDR4__TMRD_PAR_F1__REG DENALI_CTL_43
#define LPDDR4__TMRD_PAR_F1__FLD LPDDR4__DENALI_CTL_43__TMRD_PAR_F1

#define LPDDR4__DENALI_CTL_43__TMOD_PAR_MAX_PL_F1_MASK               0x00FF0000U
#define LPDDR4__DENALI_CTL_43__TMOD_PAR_MAX_PL_F1_SHIFT                      16U
#define LPDDR4__DENALI_CTL_43__TMOD_PAR_MAX_PL_F1_WIDTH                       8U
#define LPDDR4__TMOD_PAR_MAX_PL_F1__REG DENALI_CTL_43
#define LPDDR4__TMOD_PAR_MAX_PL_F1__FLD LPDDR4__DENALI_CTL_43__TMOD_PAR_MAX_PL_F1

#define LPDDR4__DENALI_CTL_43__TMRD_PAR_MAX_PL_F1_MASK               0xFF000000U
#define LPDDR4__DENALI_CTL_43__TMRD_PAR_MAX_PL_F1_SHIFT                      24U
#define LPDDR4__DENALI_CTL_43__TMRD_PAR_MAX_PL_F1_WIDTH                       8U
#define LPDDR4__TMRD_PAR_MAX_PL_F1__REG DENALI_CTL_43
#define LPDDR4__TMRD_PAR_MAX_PL_F1__FLD LPDDR4__DENALI_CTL_43__TMRD_PAR_MAX_PL_F1

#define LPDDR4__DENALI_CTL_44_READ_MASK                              0x0F3F7F7FU
#define LPDDR4__DENALI_CTL_44_WRITE_MASK                             0x0F3F7F7FU
#define LPDDR4__DENALI_CTL_44__CASLAT_LIN_F2_MASK                    0x0000007FU
#define LPDDR4__DENALI_CTL_44__CASLAT_LIN_F2_SHIFT                            0U
#define LPDDR4__DENALI_CTL_44__CASLAT_LIN_F2_WIDTH                            7U
#define LPDDR4__CASLAT_LIN_F2__REG DENALI_CTL_44
#define LPDDR4__CASLAT_LIN_F2__FLD LPDDR4__DENALI_CTL_44__CASLAT_LIN_F2

#define LPDDR4__DENALI_CTL_44__WRLAT_F2_MASK                         0x00007F00U
#define LPDDR4__DENALI_CTL_44__WRLAT_F2_SHIFT                                 8U
#define LPDDR4__DENALI_CTL_44__WRLAT_F2_WIDTH                                 7U
#define LPDDR4__WRLAT_F2__REG DENALI_CTL_44
#define LPDDR4__WRLAT_F2__FLD LPDDR4__DENALI_CTL_44__WRLAT_F2

#define LPDDR4__DENALI_CTL_44__ADDITIVE_LAT_F2_MASK                  0x003F0000U
#define LPDDR4__DENALI_CTL_44__ADDITIVE_LAT_F2_SHIFT                         16U
#define LPDDR4__DENALI_CTL_44__ADDITIVE_LAT_F2_WIDTH                          6U
#define LPDDR4__ADDITIVE_LAT_F2__REG DENALI_CTL_44
#define LPDDR4__ADDITIVE_LAT_F2__FLD LPDDR4__DENALI_CTL_44__ADDITIVE_LAT_F2

#define LPDDR4__DENALI_CTL_44__CA_PARITY_LAT_F2_MASK                 0x0F000000U
#define LPDDR4__DENALI_CTL_44__CA_PARITY_LAT_F2_SHIFT                        24U
#define LPDDR4__DENALI_CTL_44__CA_PARITY_LAT_F2_WIDTH                         4U
#define LPDDR4__CA_PARITY_LAT_F2__REG DENALI_CTL_44
#define LPDDR4__CA_PARITY_LAT_F2__FLD LPDDR4__DENALI_CTL_44__CA_PARITY_LAT_F2

#define LPDDR4__DENALI_CTL_45_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_45_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_45__TMOD_PAR_F2_MASK                      0x000000FFU
#define LPDDR4__DENALI_CTL_45__TMOD_PAR_F2_SHIFT                              0U
#define LPDDR4__DENALI_CTL_45__TMOD_PAR_F2_WIDTH                              8U
#define LPDDR4__TMOD_PAR_F2__REG DENALI_CTL_45
#define LPDDR4__TMOD_PAR_F2__FLD LPDDR4__DENALI_CTL_45__TMOD_PAR_F2

#define LPDDR4__DENALI_CTL_45__TMRD_PAR_F2_MASK                      0x0000FF00U
#define LPDDR4__DENALI_CTL_45__TMRD_PAR_F2_SHIFT                              8U
#define LPDDR4__DENALI_CTL_45__TMRD_PAR_F2_WIDTH                              8U
#define LPDDR4__TMRD_PAR_F2__REG DENALI_CTL_45
#define LPDDR4__TMRD_PAR_F2__FLD LPDDR4__DENALI_CTL_45__TMRD_PAR_F2

#define LPDDR4__DENALI_CTL_45__TMOD_PAR_MAX_PL_F2_MASK               0x00FF0000U
#define LPDDR4__DENALI_CTL_45__TMOD_PAR_MAX_PL_F2_SHIFT                      16U
#define LPDDR4__DENALI_CTL_45__TMOD_PAR_MAX_PL_F2_WIDTH                       8U
#define LPDDR4__TMOD_PAR_MAX_PL_F2__REG DENALI_CTL_45
#define LPDDR4__TMOD_PAR_MAX_PL_F2__FLD LPDDR4__DENALI_CTL_45__TMOD_PAR_MAX_PL_F2

#define LPDDR4__DENALI_CTL_45__TMRD_PAR_MAX_PL_F2_MASK               0xFF000000U
#define LPDDR4__DENALI_CTL_45__TMRD_PAR_MAX_PL_F2_SHIFT                      24U
#define LPDDR4__DENALI_CTL_45__TMRD_PAR_MAX_PL_F2_WIDTH                       8U
#define LPDDR4__TMRD_PAR_MAX_PL_F2__REG DENALI_CTL_45
#define LPDDR4__TMRD_PAR_MAX_PL_F2__FLD LPDDR4__DENALI_CTL_45__TMRD_PAR_MAX_PL_F2

#define LPDDR4__DENALI_CTL_46_READ_MASK                              0xFF1F1F07U
#define LPDDR4__DENALI_CTL_46_WRITE_MASK                             0xFF1F1F07U
#define LPDDR4__DENALI_CTL_46__TBST_INT_INTERVAL_MASK                0x00000007U
#define LPDDR4__DENALI_CTL_46__TBST_INT_INTERVAL_SHIFT                        0U
#define LPDDR4__DENALI_CTL_46__TBST_INT_INTERVAL_WIDTH                        3U
#define LPDDR4__TBST_INT_INTERVAL__REG DENALI_CTL_46
#define LPDDR4__TBST_INT_INTERVAL__FLD LPDDR4__DENALI_CTL_46__TBST_INT_INTERVAL

#define LPDDR4__DENALI_CTL_46__TCCD_MASK                             0x00001F00U
#define LPDDR4__DENALI_CTL_46__TCCD_SHIFT                                     8U
#define LPDDR4__DENALI_CTL_46__TCCD_WIDTH                                     5U
#define LPDDR4__TCCD__REG DENALI_CTL_46
#define LPDDR4__TCCD__FLD LPDDR4__DENALI_CTL_46__TCCD

#define LPDDR4__DENALI_CTL_46__TCCD_L_F0_MASK                        0x001F0000U
#define LPDDR4__DENALI_CTL_46__TCCD_L_F0_SHIFT                               16U
#define LPDDR4__DENALI_CTL_46__TCCD_L_F0_WIDTH                                5U
#define LPDDR4__TCCD_L_F0__REG DENALI_CTL_46
#define LPDDR4__TCCD_L_F0__FLD LPDDR4__DENALI_CTL_46__TCCD_L_F0

#define LPDDR4__DENALI_CTL_46__TRRD_F0_MASK                          0xFF000000U
#define LPDDR4__DENALI_CTL_46__TRRD_F0_SHIFT                                 24U
#define LPDDR4__DENALI_CTL_46__TRRD_F0_WIDTH                                  8U
#define LPDDR4__TRRD_F0__REG DENALI_CTL_46
#define LPDDR4__TRRD_F0__FLD LPDDR4__DENALI_CTL_46__TRRD_F0

#define LPDDR4__DENALI_CTL_47_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_CTL_47_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_47__TRRD_L_F0_MASK                        0x000000FFU
#define LPDDR4__DENALI_CTL_47__TRRD_L_F0_SHIFT                                0U
#define LPDDR4__DENALI_CTL_47__TRRD_L_F0_WIDTH                                8U
#define LPDDR4__TRRD_L_F0__REG DENALI_CTL_47
#define LPDDR4__TRRD_L_F0__FLD LPDDR4__DENALI_CTL_47__TRRD_L_F0

#define LPDDR4__DENALI_CTL_47__TRC_F0_MASK                           0x0001FF00U
#define LPDDR4__DENALI_CTL_47__TRC_F0_SHIFT                                   8U
#define LPDDR4__DENALI_CTL_47__TRC_F0_WIDTH                                   9U
#define LPDDR4__TRC_F0__REG DENALI_CTL_47
#define LPDDR4__TRC_F0__FLD LPDDR4__DENALI_CTL_47__TRC_F0

#define LPDDR4__DENALI_CTL_48_READ_MASK                              0x3F3F01FFU
#define LPDDR4__DENALI_CTL_48_WRITE_MASK                             0x3F3F01FFU
#define LPDDR4__DENALI_CTL_48__TRAS_MIN_F0_MASK                      0x000001FFU
#define LPDDR4__DENALI_CTL_48__TRAS_MIN_F0_SHIFT                              0U
#define LPDDR4__DENALI_CTL_48__TRAS_MIN_F0_WIDTH                              9U
#define LPDDR4__TRAS_MIN_F0__REG DENALI_CTL_48
#define LPDDR4__TRAS_MIN_F0__FLD LPDDR4__DENALI_CTL_48__TRAS_MIN_F0

#define LPDDR4__DENALI_CTL_48__TWTR_F0_MASK                          0x003F0000U
#define LPDDR4__DENALI_CTL_48__TWTR_F0_SHIFT                                 16U
#define LPDDR4__DENALI_CTL_48__TWTR_F0_WIDTH                                  6U
#define LPDDR4__TWTR_F0__REG DENALI_CTL_48
#define LPDDR4__TWTR_F0__FLD LPDDR4__DENALI_CTL_48__TWTR_F0

#define LPDDR4__DENALI_CTL_48__TWTR_L_F0_MASK                        0x3F000000U
#define LPDDR4__DENALI_CTL_48__TWTR_L_F0_SHIFT                               24U
#define LPDDR4__DENALI_CTL_48__TWTR_L_F0_WIDTH                                6U
#define LPDDR4__TWTR_L_F0__REG DENALI_CTL_48
#define LPDDR4__TWTR_L_F0__FLD LPDDR4__DENALI_CTL_48__TWTR_L_F0

#define LPDDR4__DENALI_CTL_49_READ_MASK                              0x1F01FFFFU
#define LPDDR4__DENALI_CTL_49_WRITE_MASK                             0x1F01FFFFU
#define LPDDR4__DENALI_CTL_49__TRP_F0_MASK                           0x000000FFU
#define LPDDR4__DENALI_CTL_49__TRP_F0_SHIFT                                   0U
#define LPDDR4__DENALI_CTL_49__TRP_F0_WIDTH                                   8U
#define LPDDR4__TRP_F0__REG DENALI_CTL_49
#define LPDDR4__TRP_F0__FLD LPDDR4__DENALI_CTL_49__TRP_F0

#define LPDDR4__DENALI_CTL_49__TFAW_F0_MASK                          0x0001FF00U
#define LPDDR4__DENALI_CTL_49__TFAW_F0_SHIFT                                  8U
#define LPDDR4__DENALI_CTL_49__TFAW_F0_WIDTH                                  9U
#define LPDDR4__TFAW_F0__REG DENALI_CTL_49
#define LPDDR4__TFAW_F0__FLD LPDDR4__DENALI_CTL_49__TFAW_F0

#define LPDDR4__DENALI_CTL_49__TCCD_L_F1_MASK                        0x1F000000U
#define LPDDR4__DENALI_CTL_49__TCCD_L_F1_SHIFT                               24U
#define LPDDR4__DENALI_CTL_49__TCCD_L_F1_WIDTH                                5U
#define LPDDR4__TCCD_L_F1__REG DENALI_CTL_49
#define LPDDR4__TCCD_L_F1__FLD LPDDR4__DENALI_CTL_49__TCCD_L_F1

#define LPDDR4__DENALI_CTL_50_READ_MASK                              0x01FFFFFFU
#define LPDDR4__DENALI_CTL_50_WRITE_MASK                             0x01FFFFFFU
#define LPDDR4__DENALI_CTL_50__TRRD_F1_MASK                          0x000000FFU
#define LPDDR4__DENALI_CTL_50__TRRD_F1_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_50__TRRD_F1_WIDTH                                  8U
#define LPDDR4__TRRD_F1__REG DENALI_CTL_50
#define LPDDR4__TRRD_F1__FLD LPDDR4__DENALI_CTL_50__TRRD_F1

#define LPDDR4__DENALI_CTL_50__TRRD_L_F1_MASK                        0x0000FF00U
#define LPDDR4__DENALI_CTL_50__TRRD_L_F1_SHIFT                                8U
#define LPDDR4__DENALI_CTL_50__TRRD_L_F1_WIDTH                                8U
#define LPDDR4__TRRD_L_F1__REG DENALI_CTL_50
#define LPDDR4__TRRD_L_F1__FLD LPDDR4__DENALI_CTL_50__TRRD_L_F1

#define LPDDR4__DENALI_CTL_50__TRC_F1_MASK                           0x01FF0000U
#define LPDDR4__DENALI_CTL_50__TRC_F1_SHIFT                                  16U
#define LPDDR4__DENALI_CTL_50__TRC_F1_WIDTH                                   9U
#define LPDDR4__TRC_F1__REG DENALI_CTL_50
#define LPDDR4__TRC_F1__FLD LPDDR4__DENALI_CTL_50__TRC_F1

#define LPDDR4__DENALI_CTL_51_READ_MASK                              0x3F3F01FFU
#define LPDDR4__DENALI_CTL_51_WRITE_MASK                             0x3F3F01FFU
#define LPDDR4__DENALI_CTL_51__TRAS_MIN_F1_MASK                      0x000001FFU
#define LPDDR4__DENALI_CTL_51__TRAS_MIN_F1_SHIFT                              0U
#define LPDDR4__DENALI_CTL_51__TRAS_MIN_F1_WIDTH                              9U
#define LPDDR4__TRAS_MIN_F1__REG DENALI_CTL_51
#define LPDDR4__TRAS_MIN_F1__FLD LPDDR4__DENALI_CTL_51__TRAS_MIN_F1

#define LPDDR4__DENALI_CTL_51__TWTR_F1_MASK                          0x003F0000U
#define LPDDR4__DENALI_CTL_51__TWTR_F1_SHIFT                                 16U
#define LPDDR4__DENALI_CTL_51__TWTR_F1_WIDTH                                  6U
#define LPDDR4__TWTR_F1__REG DENALI_CTL_51
#define LPDDR4__TWTR_F1__FLD LPDDR4__DENALI_CTL_51__TWTR_F1

#define LPDDR4__DENALI_CTL_51__TWTR_L_F1_MASK                        0x3F000000U
#define LPDDR4__DENALI_CTL_51__TWTR_L_F1_SHIFT                               24U
#define LPDDR4__DENALI_CTL_51__TWTR_L_F1_WIDTH                                6U
#define LPDDR4__TWTR_L_F1__REG DENALI_CTL_51
#define LPDDR4__TWTR_L_F1__FLD LPDDR4__DENALI_CTL_51__TWTR_L_F1

#define LPDDR4__DENALI_CTL_52_READ_MASK                              0x1F01FFFFU
#define LPDDR4__DENALI_CTL_52_WRITE_MASK                             0x1F01FFFFU
#define LPDDR4__DENALI_CTL_52__TRP_F1_MASK                           0x000000FFU
#define LPDDR4__DENALI_CTL_52__TRP_F1_SHIFT                                   0U
#define LPDDR4__DENALI_CTL_52__TRP_F1_WIDTH                                   8U
#define LPDDR4__TRP_F1__REG DENALI_CTL_52
#define LPDDR4__TRP_F1__FLD LPDDR4__DENALI_CTL_52__TRP_F1

#define LPDDR4__DENALI_CTL_52__TFAW_F1_MASK                          0x0001FF00U
#define LPDDR4__DENALI_CTL_52__TFAW_F1_SHIFT                                  8U
#define LPDDR4__DENALI_CTL_52__TFAW_F1_WIDTH                                  9U
#define LPDDR4__TFAW_F1__REG DENALI_CTL_52
#define LPDDR4__TFAW_F1__FLD LPDDR4__DENALI_CTL_52__TFAW_F1

#define LPDDR4__DENALI_CTL_52__TCCD_L_F2_MASK                        0x1F000000U
#define LPDDR4__DENALI_CTL_52__TCCD_L_F2_SHIFT                               24U
#define LPDDR4__DENALI_CTL_52__TCCD_L_F2_WIDTH                                5U
#define LPDDR4__TCCD_L_F2__REG DENALI_CTL_52
#define LPDDR4__TCCD_L_F2__FLD LPDDR4__DENALI_CTL_52__TCCD_L_F2

#define LPDDR4__DENALI_CTL_53_READ_MASK                              0x01FFFFFFU
#define LPDDR4__DENALI_CTL_53_WRITE_MASK                             0x01FFFFFFU
#define LPDDR4__DENALI_CTL_53__TRRD_F2_MASK                          0x000000FFU
#define LPDDR4__DENALI_CTL_53__TRRD_F2_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_53__TRRD_F2_WIDTH                                  8U
#define LPDDR4__TRRD_F2__REG DENALI_CTL_53
#define LPDDR4__TRRD_F2__FLD LPDDR4__DENALI_CTL_53__TRRD_F2

#define LPDDR4__DENALI_CTL_53__TRRD_L_F2_MASK                        0x0000FF00U
#define LPDDR4__DENALI_CTL_53__TRRD_L_F2_SHIFT                                8U
#define LPDDR4__DENALI_CTL_53__TRRD_L_F2_WIDTH                                8U
#define LPDDR4__TRRD_L_F2__REG DENALI_CTL_53
#define LPDDR4__TRRD_L_F2__FLD LPDDR4__DENALI_CTL_53__TRRD_L_F2

#define LPDDR4__DENALI_CTL_53__TRC_F2_MASK                           0x01FF0000U
#define LPDDR4__DENALI_CTL_53__TRC_F2_SHIFT                                  16U
#define LPDDR4__DENALI_CTL_53__TRC_F2_WIDTH                                   9U
#define LPDDR4__TRC_F2__REG DENALI_CTL_53
#define LPDDR4__TRC_F2__FLD LPDDR4__DENALI_CTL_53__TRC_F2

#define LPDDR4__DENALI_CTL_54_READ_MASK                              0x3F3F01FFU
#define LPDDR4__DENALI_CTL_54_WRITE_MASK                             0x3F3F01FFU
#define LPDDR4__DENALI_CTL_54__TRAS_MIN_F2_MASK                      0x000001FFU
#define LPDDR4__DENALI_CTL_54__TRAS_MIN_F2_SHIFT                              0U
#define LPDDR4__DENALI_CTL_54__TRAS_MIN_F2_WIDTH                              9U
#define LPDDR4__TRAS_MIN_F2__REG DENALI_CTL_54
#define LPDDR4__TRAS_MIN_F2__FLD LPDDR4__DENALI_CTL_54__TRAS_MIN_F2

#define LPDDR4__DENALI_CTL_54__TWTR_F2_MASK                          0x003F0000U
#define LPDDR4__DENALI_CTL_54__TWTR_F2_SHIFT                                 16U
#define LPDDR4__DENALI_CTL_54__TWTR_F2_WIDTH                                  6U
#define LPDDR4__TWTR_F2__REG DENALI_CTL_54
#define LPDDR4__TWTR_F2__FLD LPDDR4__DENALI_CTL_54__TWTR_F2

#define LPDDR4__DENALI_CTL_54__TWTR_L_F2_MASK                        0x3F000000U
#define LPDDR4__DENALI_CTL_54__TWTR_L_F2_SHIFT                               24U
#define LPDDR4__DENALI_CTL_54__TWTR_L_F2_WIDTH                                6U
#define LPDDR4__TWTR_L_F2__REG DENALI_CTL_54
#define LPDDR4__TWTR_L_F2__FLD LPDDR4__DENALI_CTL_54__TWTR_L_F2

#define LPDDR4__DENALI_CTL_55_READ_MASK                              0xFF01FFFFU
#define LPDDR4__DENALI_CTL_55_WRITE_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_CTL_55__TRP_F2_MASK                           0x000000FFU
#define LPDDR4__DENALI_CTL_55__TRP_F2_SHIFT                                   0U
#define LPDDR4__DENALI_CTL_55__TRP_F2_WIDTH                                   8U
#define LPDDR4__TRP_F2__REG DENALI_CTL_55
#define LPDDR4__TRP_F2__FLD LPDDR4__DENALI_CTL_55__TRP_F2

#define LPDDR4__DENALI_CTL_55__TFAW_F2_MASK                          0x0001FF00U
#define LPDDR4__DENALI_CTL_55__TFAW_F2_SHIFT                                  8U
#define LPDDR4__DENALI_CTL_55__TFAW_F2_WIDTH                                  9U
#define LPDDR4__TFAW_F2__REG DENALI_CTL_55
#define LPDDR4__TFAW_F2__FLD LPDDR4__DENALI_CTL_55__TFAW_F2

#define LPDDR4__DENALI_CTL_55__TRTP_F0_MASK                          0xFF000000U
#define LPDDR4__DENALI_CTL_55__TRTP_F0_SHIFT                                 24U
#define LPDDR4__DENALI_CTL_55__TRTP_F0_WIDTH                                  8U
#define LPDDR4__TRTP_F0__REG DENALI_CTL_55
#define LPDDR4__TRTP_F0__FLD LPDDR4__DENALI_CTL_55__TRTP_F0

#define LPDDR4__DENALI_CTL_56_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_CTL_56_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_56__TRTP_AP_F0_MASK                       0x000000FFU
#define LPDDR4__DENALI_CTL_56__TRTP_AP_F0_SHIFT                               0U
#define LPDDR4__DENALI_CTL_56__TRTP_AP_F0_WIDTH                               8U
#define LPDDR4__TRTP_AP_F0__REG DENALI_CTL_56
#define LPDDR4__TRTP_AP_F0__FLD LPDDR4__DENALI_CTL_56__TRTP_AP_F0

#define LPDDR4__DENALI_CTL_56__TMRD_F0_MASK                          0x0000FF00U
#define LPDDR4__DENALI_CTL_56__TMRD_F0_SHIFT                                  8U
#define LPDDR4__DENALI_CTL_56__TMRD_F0_WIDTH                                  8U
#define LPDDR4__TMRD_F0__REG DENALI_CTL_56
#define LPDDR4__TMRD_F0__FLD LPDDR4__DENALI_CTL_56__TMRD_F0

#define LPDDR4__DENALI_CTL_56__TMOD_F0_MASK                          0x00FF0000U
#define LPDDR4__DENALI_CTL_56__TMOD_F0_SHIFT                                 16U
#define LPDDR4__DENALI_CTL_56__TMOD_F0_WIDTH                                  8U
#define LPDDR4__TMOD_F0__REG DENALI_CTL_56
#define LPDDR4__TMOD_F0__FLD LPDDR4__DENALI_CTL_56__TMOD_F0

#define LPDDR4__DENALI_CTL_57_READ_MASK                              0x1F0FFFFFU
#define LPDDR4__DENALI_CTL_57_WRITE_MASK                             0x1F0FFFFFU
#define LPDDR4__DENALI_CTL_57__TRAS_MAX_F0_MASK                      0x000FFFFFU
#define LPDDR4__DENALI_CTL_57__TRAS_MAX_F0_SHIFT                              0U
#define LPDDR4__DENALI_CTL_57__TRAS_MAX_F0_WIDTH                             20U
#define LPDDR4__TRAS_MAX_F0__REG DENALI_CTL_57
#define LPDDR4__TRAS_MAX_F0__FLD LPDDR4__DENALI_CTL_57__TRAS_MAX_F0

#define LPDDR4__DENALI_CTL_57__TCKE_F0_MASK                          0x1F000000U
#define LPDDR4__DENALI_CTL_57__TCKE_F0_SHIFT                                 24U
#define LPDDR4__DENALI_CTL_57__TCKE_F0_WIDTH                                  5U
#define LPDDR4__TCKE_F0__REG DENALI_CTL_57
#define LPDDR4__TCKE_F0__FLD LPDDR4__DENALI_CTL_57__TCKE_F0

#define LPDDR4__DENALI_CTL_58_READ_MASK                              0xFFFF3FFFU
#define LPDDR4__DENALI_CTL_58_WRITE_MASK                             0xFFFF3FFFU
#define LPDDR4__DENALI_CTL_58__TCKESR_F0_MASK                        0x000000FFU
#define LPDDR4__DENALI_CTL_58__TCKESR_F0_SHIFT                                0U
#define LPDDR4__DENALI_CTL_58__TCKESR_F0_WIDTH                                8U
#define LPDDR4__TCKESR_F0__REG DENALI_CTL_58
#define LPDDR4__TCKESR_F0__FLD LPDDR4__DENALI_CTL_58__TCKESR_F0

#define LPDDR4__DENALI_CTL_58__TCCDMW_F0_MASK                        0x00003F00U
#define LPDDR4__DENALI_CTL_58__TCCDMW_F0_SHIFT                                8U
#define LPDDR4__DENALI_CTL_58__TCCDMW_F0_WIDTH                                6U
#define LPDDR4__TCCDMW_F0__REG DENALI_CTL_58
#define LPDDR4__TCCDMW_F0__FLD LPDDR4__DENALI_CTL_58__TCCDMW_F0

#define LPDDR4__DENALI_CTL_58__TRTP_F1_MASK                          0x00FF0000U
#define LPDDR4__DENALI_CTL_58__TRTP_F1_SHIFT                                 16U
#define LPDDR4__DENALI_CTL_58__TRTP_F1_WIDTH                                  8U
#define LPDDR4__TRTP_F1__REG DENALI_CTL_58
#define LPDDR4__TRTP_F1__FLD LPDDR4__DENALI_CTL_58__TRTP_F1

#define LPDDR4__DENALI_CTL_58__TRTP_AP_F1_MASK                       0xFF000000U
#define LPDDR4__DENALI_CTL_58__TRTP_AP_F1_SHIFT                              24U
#define LPDDR4__DENALI_CTL_58__TRTP_AP_F1_WIDTH                               8U
#define LPDDR4__TRTP_AP_F1__REG DENALI_CTL_58
#define LPDDR4__TRTP_AP_F1__FLD LPDDR4__DENALI_CTL_58__TRTP_AP_F1

#define LPDDR4__DENALI_CTL_59_READ_MASK                              0x0000FFFFU
#define LPDDR4__DENALI_CTL_59_WRITE_MASK                             0x0000FFFFU
#define LPDDR4__DENALI_CTL_59__TMRD_F1_MASK                          0x000000FFU
#define LPDDR4__DENALI_CTL_59__TMRD_F1_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_59__TMRD_F1_WIDTH                                  8U
#define LPDDR4__TMRD_F1__REG DENALI_CTL_59
#define LPDDR4__TMRD_F1__FLD LPDDR4__DENALI_CTL_59__TMRD_F1

#define LPDDR4__DENALI_CTL_59__TMOD_F1_MASK                          0x0000FF00U
#define LPDDR4__DENALI_CTL_59__TMOD_F1_SHIFT                                  8U
#define LPDDR4__DENALI_CTL_59__TMOD_F1_WIDTH                                  8U
#define LPDDR4__TMOD_F1__REG DENALI_CTL_59
#define LPDDR4__TMOD_F1__FLD LPDDR4__DENALI_CTL_59__TMOD_F1

#define LPDDR4__DENALI_CTL_60_READ_MASK                              0x1F0FFFFFU
#define LPDDR4__DENALI_CTL_60_WRITE_MASK                             0x1F0FFFFFU
#define LPDDR4__DENALI_CTL_60__TRAS_MAX_F1_MASK                      0x000FFFFFU
#define LPDDR4__DENALI_CTL_60__TRAS_MAX_F1_SHIFT                              0U
#define LPDDR4__DENALI_CTL_60__TRAS_MAX_F1_WIDTH                             20U
#define LPDDR4__TRAS_MAX_F1__REG DENALI_CTL_60
#define LPDDR4__TRAS_MAX_F1__FLD LPDDR4__DENALI_CTL_60__TRAS_MAX_F1

#define LPDDR4__DENALI_CTL_60__TCKE_F1_MASK                          0x1F000000U
#define LPDDR4__DENALI_CTL_60__TCKE_F1_SHIFT                                 24U
#define LPDDR4__DENALI_CTL_60__TCKE_F1_WIDTH                                  5U
#define LPDDR4__TCKE_F1__REG DENALI_CTL_60
#define LPDDR4__TCKE_F1__FLD LPDDR4__DENALI_CTL_60__TCKE_F1

#define LPDDR4__DENALI_CTL_61_READ_MASK                              0xFFFF3FFFU
#define LPDDR4__DENALI_CTL_61_WRITE_MASK                             0xFFFF3FFFU
#define LPDDR4__DENALI_CTL_61__TCKESR_F1_MASK                        0x000000FFU
#define LPDDR4__DENALI_CTL_61__TCKESR_F1_SHIFT                                0U
#define LPDDR4__DENALI_CTL_61__TCKESR_F1_WIDTH                                8U
#define LPDDR4__TCKESR_F1__REG DENALI_CTL_61
#define LPDDR4__TCKESR_F1__FLD LPDDR4__DENALI_CTL_61__TCKESR_F1

#define LPDDR4__DENALI_CTL_61__TCCDMW_F1_MASK                        0x00003F00U
#define LPDDR4__DENALI_CTL_61__TCCDMW_F1_SHIFT                                8U
#define LPDDR4__DENALI_CTL_61__TCCDMW_F1_WIDTH                                6U
#define LPDDR4__TCCDMW_F1__REG DENALI_CTL_61
#define LPDDR4__TCCDMW_F1__FLD LPDDR4__DENALI_CTL_61__TCCDMW_F1

#define LPDDR4__DENALI_CTL_61__TRTP_F2_MASK                          0x00FF0000U
#define LPDDR4__DENALI_CTL_61__TRTP_F2_SHIFT                                 16U
#define LPDDR4__DENALI_CTL_61__TRTP_F2_WIDTH                                  8U
#define LPDDR4__TRTP_F2__REG DENALI_CTL_61
#define LPDDR4__TRTP_F2__FLD LPDDR4__DENALI_CTL_61__TRTP_F2

#define LPDDR4__DENALI_CTL_61__TRTP_AP_F2_MASK                       0xFF000000U
#define LPDDR4__DENALI_CTL_61__TRTP_AP_F2_SHIFT                              24U
#define LPDDR4__DENALI_CTL_61__TRTP_AP_F2_WIDTH                               8U
#define LPDDR4__TRTP_AP_F2__REG DENALI_CTL_61
#define LPDDR4__TRTP_AP_F2__FLD LPDDR4__DENALI_CTL_61__TRTP_AP_F2

#define LPDDR4__DENALI_CTL_62_READ_MASK                              0x0000FFFFU
#define LPDDR4__DENALI_CTL_62_WRITE_MASK                             0x0000FFFFU
#define LPDDR4__DENALI_CTL_62__TMRD_F2_MASK                          0x000000FFU
#define LPDDR4__DENALI_CTL_62__TMRD_F2_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_62__TMRD_F2_WIDTH                                  8U
#define LPDDR4__TMRD_F2__REG DENALI_CTL_62
#define LPDDR4__TMRD_F2__FLD LPDDR4__DENALI_CTL_62__TMRD_F2

#define LPDDR4__DENALI_CTL_62__TMOD_F2_MASK                          0x0000FF00U
#define LPDDR4__DENALI_CTL_62__TMOD_F2_SHIFT                                  8U
#define LPDDR4__DENALI_CTL_62__TMOD_F2_WIDTH                                  8U
#define LPDDR4__TMOD_F2__REG DENALI_CTL_62
#define LPDDR4__TMOD_F2__FLD LPDDR4__DENALI_CTL_62__TMOD_F2

#define LPDDR4__DENALI_CTL_63_READ_MASK                              0x1F0FFFFFU
#define LPDDR4__DENALI_CTL_63_WRITE_MASK                             0x1F0FFFFFU
#define LPDDR4__DENALI_CTL_63__TRAS_MAX_F2_MASK                      0x000FFFFFU
#define LPDDR4__DENALI_CTL_63__TRAS_MAX_F2_SHIFT                              0U
#define LPDDR4__DENALI_CTL_63__TRAS_MAX_F2_WIDTH                             20U
#define LPDDR4__TRAS_MAX_F2__REG DENALI_CTL_63
#define LPDDR4__TRAS_MAX_F2__FLD LPDDR4__DENALI_CTL_63__TRAS_MAX_F2

#define LPDDR4__DENALI_CTL_63__TCKE_F2_MASK                          0x1F000000U
#define LPDDR4__DENALI_CTL_63__TCKE_F2_SHIFT                                 24U
#define LPDDR4__DENALI_CTL_63__TCKE_F2_WIDTH                                  5U
#define LPDDR4__TCKE_F2__REG DENALI_CTL_63
#define LPDDR4__TCKE_F2__FLD LPDDR4__DENALI_CTL_63__TCKE_F2

#define LPDDR4__DENALI_CTL_64_READ_MASK                              0x07073FFFU
#define LPDDR4__DENALI_CTL_64_WRITE_MASK                             0x07073FFFU
#define LPDDR4__DENALI_CTL_64__TCKESR_F2_MASK                        0x000000FFU
#define LPDDR4__DENALI_CTL_64__TCKESR_F2_SHIFT                                0U
#define LPDDR4__DENALI_CTL_64__TCKESR_F2_WIDTH                                8U
#define LPDDR4__TCKESR_F2__REG DENALI_CTL_64
#define LPDDR4__TCKESR_F2__FLD LPDDR4__DENALI_CTL_64__TCKESR_F2

#define LPDDR4__DENALI_CTL_64__TCCDMW_F2_MASK                        0x00003F00U
#define LPDDR4__DENALI_CTL_64__TCCDMW_F2_SHIFT                                8U
#define LPDDR4__DENALI_CTL_64__TCCDMW_F2_WIDTH                                6U
#define LPDDR4__TCCDMW_F2__REG DENALI_CTL_64
#define LPDDR4__TCCDMW_F2__FLD LPDDR4__DENALI_CTL_64__TCCDMW_F2

#define LPDDR4__DENALI_CTL_64__TPPD_MASK                             0x00070000U
#define LPDDR4__DENALI_CTL_64__TPPD_SHIFT                                    16U
#define LPDDR4__DENALI_CTL_64__TPPD_WIDTH                                     3U
#define LPDDR4__TPPD__REG DENALI_CTL_64
#define LPDDR4__TPPD__FLD LPDDR4__DENALI_CTL_64__TPPD

#define LPDDR4__DENALI_CTL_64__MC_RESERVED0_MASK                     0x07000000U
#define LPDDR4__DENALI_CTL_64__MC_RESERVED0_SHIFT                            24U
#define LPDDR4__DENALI_CTL_64__MC_RESERVED0_WIDTH                             3U
#define LPDDR4__MC_RESERVED0__REG DENALI_CTL_64
#define LPDDR4__MC_RESERVED0__FLD LPDDR4__DENALI_CTL_64__MC_RESERVED0

#define LPDDR4__DENALI_CTL_65_READ_MASK                              0xFFFF0107U
#define LPDDR4__DENALI_CTL_65_WRITE_MASK                             0xFFFF0107U
#define LPDDR4__DENALI_CTL_65__MC_RESERVED1_MASK                     0x00000007U
#define LPDDR4__DENALI_CTL_65__MC_RESERVED1_SHIFT                             0U
#define LPDDR4__DENALI_CTL_65__MC_RESERVED1_WIDTH                             3U
#define LPDDR4__MC_RESERVED1__REG DENALI_CTL_65
#define LPDDR4__MC_RESERVED1__FLD LPDDR4__DENALI_CTL_65__MC_RESERVED1

#define LPDDR4__DENALI_CTL_65__WRITEINTERP_MASK                      0x00000100U
#define LPDDR4__DENALI_CTL_65__WRITEINTERP_SHIFT                              8U
#define LPDDR4__DENALI_CTL_65__WRITEINTERP_WIDTH                              1U
#define LPDDR4__DENALI_CTL_65__WRITEINTERP_WOCLR                              0U
#define LPDDR4__DENALI_CTL_65__WRITEINTERP_WOSET                              0U
#define LPDDR4__WRITEINTERP__REG DENALI_CTL_65
#define LPDDR4__WRITEINTERP__FLD LPDDR4__DENALI_CTL_65__WRITEINTERP

#define LPDDR4__DENALI_CTL_65__TRCD_F0_MASK                          0x00FF0000U
#define LPDDR4__DENALI_CTL_65__TRCD_F0_SHIFT                                 16U
#define LPDDR4__DENALI_CTL_65__TRCD_F0_WIDTH                                  8U
#define LPDDR4__TRCD_F0__REG DENALI_CTL_65
#define LPDDR4__TRCD_F0__FLD LPDDR4__DENALI_CTL_65__TRCD_F0

#define LPDDR4__DENALI_CTL_65__TWR_F0_MASK                           0xFF000000U
#define LPDDR4__DENALI_CTL_65__TWR_F0_SHIFT                                  24U
#define LPDDR4__DENALI_CTL_65__TWR_F0_WIDTH                                   8U
#define LPDDR4__TWR_F0__REG DENALI_CTL_65
#define LPDDR4__TWR_F0__FLD LPDDR4__DENALI_CTL_65__TWR_F0

#define LPDDR4__DENALI_CTL_66_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_66_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_66__TRCD_F1_MASK                          0x000000FFU
#define LPDDR4__DENALI_CTL_66__TRCD_F1_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_66__TRCD_F1_WIDTH                                  8U
#define LPDDR4__TRCD_F1__REG DENALI_CTL_66
#define LPDDR4__TRCD_F1__FLD LPDDR4__DENALI_CTL_66__TRCD_F1

#define LPDDR4__DENALI_CTL_66__TWR_F1_MASK                           0x0000FF00U
#define LPDDR4__DENALI_CTL_66__TWR_F1_SHIFT                                   8U
#define LPDDR4__DENALI_CTL_66__TWR_F1_WIDTH                                   8U
#define LPDDR4__TWR_F1__REG DENALI_CTL_66
#define LPDDR4__TWR_F1__FLD LPDDR4__DENALI_CTL_66__TWR_F1

#define LPDDR4__DENALI_CTL_66__TRCD_F2_MASK                          0x00FF0000U
#define LPDDR4__DENALI_CTL_66__TRCD_F2_SHIFT                                 16U
#define LPDDR4__DENALI_CTL_66__TRCD_F2_WIDTH                                  8U
#define LPDDR4__TRCD_F2__REG DENALI_CTL_66
#define LPDDR4__TRCD_F2__FLD LPDDR4__DENALI_CTL_66__TRCD_F2

#define LPDDR4__DENALI_CTL_66__TWR_F2_MASK                           0xFF000000U
#define LPDDR4__DENALI_CTL_66__TWR_F2_SHIFT                                  24U
#define LPDDR4__DENALI_CTL_66__TWR_F2_WIDTH                                   8U
#define LPDDR4__TWR_F2__REG DENALI_CTL_66
#define LPDDR4__TWR_F2__FLD LPDDR4__DENALI_CTL_66__TWR_F2

#define LPDDR4__DENALI_CTL_67_READ_MASK                              0x0101010FU
#define LPDDR4__DENALI_CTL_67_WRITE_MASK                             0x0101010FU
#define LPDDR4__DENALI_CTL_67__TMRR_MASK                             0x0000000FU
#define LPDDR4__DENALI_CTL_67__TMRR_SHIFT                                     0U
#define LPDDR4__DENALI_CTL_67__TMRR_WIDTH                                     4U
#define LPDDR4__TMRR__REG DENALI_CTL_67
#define LPDDR4__TMRR__FLD LPDDR4__DENALI_CTL_67__TMRR

#define LPDDR4__DENALI_CTL_67__AP_MASK                               0x00000100U
#define LPDDR4__DENALI_CTL_67__AP_SHIFT                                       8U
#define LPDDR4__DENALI_CTL_67__AP_WIDTH                                       1U
#define LPDDR4__DENALI_CTL_67__AP_WOCLR                                       0U
#define LPDDR4__DENALI_CTL_67__AP_WOSET                                       0U
#define LPDDR4__AP__REG DENALI_CTL_67
#define LPDDR4__AP__FLD LPDDR4__DENALI_CTL_67__AP

#define LPDDR4__DENALI_CTL_67__CONCURRENTAP_MASK                     0x00010000U
#define LPDDR4__DENALI_CTL_67__CONCURRENTAP_SHIFT                            16U
#define LPDDR4__DENALI_CTL_67__CONCURRENTAP_WIDTH                             1U
#define LPDDR4__DENALI_CTL_67__CONCURRENTAP_WOCLR                             0U
#define LPDDR4__DENALI_CTL_67__CONCURRENTAP_WOSET                             0U
#define LPDDR4__CONCURRENTAP__REG DENALI_CTL_67
#define LPDDR4__CONCURRENTAP__FLD LPDDR4__DENALI_CTL_67__CONCURRENTAP

#define LPDDR4__DENALI_CTL_67__TRAS_LOCKOUT_MASK                     0x01000000U
#define LPDDR4__DENALI_CTL_67__TRAS_LOCKOUT_SHIFT                            24U
#define LPDDR4__DENALI_CTL_67__TRAS_LOCKOUT_WIDTH                             1U
#define LPDDR4__DENALI_CTL_67__TRAS_LOCKOUT_WOCLR                             0U
#define LPDDR4__DENALI_CTL_67__TRAS_LOCKOUT_WOSET                             0U
#define LPDDR4__TRAS_LOCKOUT__REG DENALI_CTL_67
#define LPDDR4__TRAS_LOCKOUT__FLD LPDDR4__DENALI_CTL_67__TRAS_LOCKOUT

#define LPDDR4__DENALI_CTL_68_READ_MASK                              0x3FFFFFFFU
#define LPDDR4__DENALI_CTL_68_WRITE_MASK                             0x3FFFFFFFU
#define LPDDR4__DENALI_CTL_68__TDAL_F0_MASK                          0x000000FFU
#define LPDDR4__DENALI_CTL_68__TDAL_F0_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_68__TDAL_F0_WIDTH                                  8U
#define LPDDR4__TDAL_F0__REG DENALI_CTL_68
#define LPDDR4__TDAL_F0__FLD LPDDR4__DENALI_CTL_68__TDAL_F0

#define LPDDR4__DENALI_CTL_68__TDAL_F1_MASK                          0x0000FF00U
#define LPDDR4__DENALI_CTL_68__TDAL_F1_SHIFT                                  8U
#define LPDDR4__DENALI_CTL_68__TDAL_F1_WIDTH                                  8U
#define LPDDR4__TDAL_F1__REG DENALI_CTL_68
#define LPDDR4__TDAL_F1__FLD LPDDR4__DENALI_CTL_68__TDAL_F1

#define LPDDR4__DENALI_CTL_68__TDAL_F2_MASK                          0x00FF0000U
#define LPDDR4__DENALI_CTL_68__TDAL_F2_SHIFT                                 16U
#define LPDDR4__DENALI_CTL_68__TDAL_F2_WIDTH                                  8U
#define LPDDR4__TDAL_F2__REG DENALI_CTL_68
#define LPDDR4__TDAL_F2__FLD LPDDR4__DENALI_CTL_68__TDAL_F2

#define LPDDR4__DENALI_CTL_68__BSTLEN_MASK                           0x3F000000U
#define LPDDR4__DENALI_CTL_68__BSTLEN_SHIFT                                  24U
#define LPDDR4__DENALI_CTL_68__BSTLEN_WIDTH                                   6U
#define LPDDR4__BSTLEN__REG DENALI_CTL_68
#define LPDDR4__BSTLEN__FLD LPDDR4__DENALI_CTL_68__BSTLEN

#define LPDDR4__DENALI_CTL_69_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_69_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_69__TRP_AB_F0_0_MASK                      0x000000FFU
#define LPDDR4__DENALI_CTL_69__TRP_AB_F0_0_SHIFT                              0U
#define LPDDR4__DENALI_CTL_69__TRP_AB_F0_0_WIDTH                              8U
#define LPDDR4__TRP_AB_F0_0__REG DENALI_CTL_69
#define LPDDR4__TRP_AB_F0_0__FLD LPDDR4__DENALI_CTL_69__TRP_AB_F0_0

#define LPDDR4__DENALI_CTL_69__TRP_AB_F1_0_MASK                      0x0000FF00U
#define LPDDR4__DENALI_CTL_69__TRP_AB_F1_0_SHIFT                              8U
#define LPDDR4__DENALI_CTL_69__TRP_AB_F1_0_WIDTH                              8U
#define LPDDR4__TRP_AB_F1_0__REG DENALI_CTL_69
#define LPDDR4__TRP_AB_F1_0__FLD LPDDR4__DENALI_CTL_69__TRP_AB_F1_0

#define LPDDR4__DENALI_CTL_69__TRP_AB_F2_0_MASK                      0x00FF0000U
#define LPDDR4__DENALI_CTL_69__TRP_AB_F2_0_SHIFT                             16U
#define LPDDR4__DENALI_CTL_69__TRP_AB_F2_0_WIDTH                              8U
#define LPDDR4__TRP_AB_F2_0__REG DENALI_CTL_69
#define LPDDR4__TRP_AB_F2_0__FLD LPDDR4__DENALI_CTL_69__TRP_AB_F2_0

#define LPDDR4__DENALI_CTL_69__TRP_AB_F0_1_MASK                      0xFF000000U
#define LPDDR4__DENALI_CTL_69__TRP_AB_F0_1_SHIFT                             24U
#define LPDDR4__DENALI_CTL_69__TRP_AB_F0_1_WIDTH                              8U
#define LPDDR4__TRP_AB_F0_1__REG DENALI_CTL_69
#define LPDDR4__TRP_AB_F0_1__FLD LPDDR4__DENALI_CTL_69__TRP_AB_F0_1

#define LPDDR4__DENALI_CTL_70_READ_MASK                              0x0301FFFFU
#define LPDDR4__DENALI_CTL_70_WRITE_MASK                             0x0301FFFFU
#define LPDDR4__DENALI_CTL_70__TRP_AB_F1_1_MASK                      0x000000FFU
#define LPDDR4__DENALI_CTL_70__TRP_AB_F1_1_SHIFT                              0U
#define LPDDR4__DENALI_CTL_70__TRP_AB_F1_1_WIDTH                              8U
#define LPDDR4__TRP_AB_F1_1__REG DENALI_CTL_70
#define LPDDR4__TRP_AB_F1_1__FLD LPDDR4__DENALI_CTL_70__TRP_AB_F1_1

#define LPDDR4__DENALI_CTL_70__TRP_AB_F2_1_MASK                      0x0000FF00U
#define LPDDR4__DENALI_CTL_70__TRP_AB_F2_1_SHIFT                              8U
#define LPDDR4__DENALI_CTL_70__TRP_AB_F2_1_WIDTH                              8U
#define LPDDR4__TRP_AB_F2_1__REG DENALI_CTL_70
#define LPDDR4__TRP_AB_F2_1__FLD LPDDR4__DENALI_CTL_70__TRP_AB_F2_1

#define LPDDR4__DENALI_CTL_70__REG_DIMM_ENABLE_MASK                  0x00010000U
#define LPDDR4__DENALI_CTL_70__REG_DIMM_ENABLE_SHIFT                         16U
#define LPDDR4__DENALI_CTL_70__REG_DIMM_ENABLE_WIDTH                          1U
#define LPDDR4__DENALI_CTL_70__REG_DIMM_ENABLE_WOCLR                          0U
#define LPDDR4__DENALI_CTL_70__REG_DIMM_ENABLE_WOSET                          0U
#define LPDDR4__REG_DIMM_ENABLE__REG DENALI_CTL_70
#define LPDDR4__REG_DIMM_ENABLE__FLD LPDDR4__DENALI_CTL_70__REG_DIMM_ENABLE

#define LPDDR4__DENALI_CTL_70__ADDRESS_MIRRORING_MASK                0x03000000U
#define LPDDR4__DENALI_CTL_70__ADDRESS_MIRRORING_SHIFT                       24U
#define LPDDR4__DENALI_CTL_70__ADDRESS_MIRRORING_WIDTH                        2U
#define LPDDR4__ADDRESS_MIRRORING__REG DENALI_CTL_70
#define LPDDR4__ADDRESS_MIRRORING__FLD LPDDR4__DENALI_CTL_70__ADDRESS_MIRRORING

#define LPDDR4__DENALI_CTL_71_READ_MASK                              0x00010101U
#define LPDDR4__DENALI_CTL_71_WRITE_MASK                             0x00010101U
#define LPDDR4__DENALI_CTL_71__OPTIMAL_RMODW_EN_MASK                 0x00000001U
#define LPDDR4__DENALI_CTL_71__OPTIMAL_RMODW_EN_SHIFT                         0U
#define LPDDR4__DENALI_CTL_71__OPTIMAL_RMODW_EN_WIDTH                         1U
#define LPDDR4__DENALI_CTL_71__OPTIMAL_RMODW_EN_WOCLR                         0U
#define LPDDR4__DENALI_CTL_71__OPTIMAL_RMODW_EN_WOSET                         0U
#define LPDDR4__OPTIMAL_RMODW_EN__REG DENALI_CTL_71
#define LPDDR4__OPTIMAL_RMODW_EN__FLD LPDDR4__DENALI_CTL_71__OPTIMAL_RMODW_EN

#define LPDDR4__DENALI_CTL_71__MC_RESERVED2_MASK                     0x00000100U
#define LPDDR4__DENALI_CTL_71__MC_RESERVED2_SHIFT                             8U
#define LPDDR4__DENALI_CTL_71__MC_RESERVED2_WIDTH                             1U
#define LPDDR4__DENALI_CTL_71__MC_RESERVED2_WOCLR                             0U
#define LPDDR4__DENALI_CTL_71__MC_RESERVED2_WOSET                             0U
#define LPDDR4__MC_RESERVED2__REG DENALI_CTL_71
#define LPDDR4__MC_RESERVED2__FLD LPDDR4__DENALI_CTL_71__MC_RESERVED2

#define LPDDR4__DENALI_CTL_71__NO_MEMORY_DM_MASK                     0x00010000U
#define LPDDR4__DENALI_CTL_71__NO_MEMORY_DM_SHIFT                            16U
#define LPDDR4__DENALI_CTL_71__NO_MEMORY_DM_WIDTH                             1U
#define LPDDR4__DENALI_CTL_71__NO_MEMORY_DM_WOCLR                             0U
#define LPDDR4__DENALI_CTL_71__NO_MEMORY_DM_WOSET                             0U
#define LPDDR4__NO_MEMORY_DM__REG DENALI_CTL_71
#define LPDDR4__NO_MEMORY_DM__FLD LPDDR4__DENALI_CTL_71__NO_MEMORY_DM

#define LPDDR4__DENALI_CTL_72_READ_MASK                              0x03FFFFFFU
#define LPDDR4__DENALI_CTL_72_WRITE_MASK                             0x03FFFFFFU
#define LPDDR4__DENALI_CTL_72__CA_PARITY_ERROR_INJECT_MASK           0x03FFFFFFU
#define LPDDR4__DENALI_CTL_72__CA_PARITY_ERROR_INJECT_SHIFT                   0U
#define LPDDR4__DENALI_CTL_72__CA_PARITY_ERROR_INJECT_WIDTH                  26U
#define LPDDR4__CA_PARITY_ERROR_INJECT__REG DENALI_CTL_72
#define LPDDR4__CA_PARITY_ERROR_INJECT__FLD LPDDR4__DENALI_CTL_72__CA_PARITY_ERROR_INJECT

#define LPDDR4__DENALI_CTL_73_READ_MASK                              0x01010001U
#define LPDDR4__DENALI_CTL_73_WRITE_MASK                             0x01010001U
#define LPDDR4__DENALI_CTL_73__CA_PARITY_ERROR_MASK                  0x00000001U
#define LPDDR4__DENALI_CTL_73__CA_PARITY_ERROR_SHIFT                          0U
#define LPDDR4__DENALI_CTL_73__CA_PARITY_ERROR_WIDTH                          1U
#define LPDDR4__DENALI_CTL_73__CA_PARITY_ERROR_WOCLR                          0U
#define LPDDR4__DENALI_CTL_73__CA_PARITY_ERROR_WOSET                          0U
#define LPDDR4__CA_PARITY_ERROR__REG DENALI_CTL_73
#define LPDDR4__CA_PARITY_ERROR__FLD LPDDR4__DENALI_CTL_73__CA_PARITY_ERROR

#define LPDDR4__DENALI_CTL_73__AREFRESH_MASK                         0x00000100U
#define LPDDR4__DENALI_CTL_73__AREFRESH_SHIFT                                 8U
#define LPDDR4__DENALI_CTL_73__AREFRESH_WIDTH                                 1U
#define LPDDR4__DENALI_CTL_73__AREFRESH_WOCLR                                 0U
#define LPDDR4__DENALI_CTL_73__AREFRESH_WOSET                                 0U
#define LPDDR4__AREFRESH__REG DENALI_CTL_73
#define LPDDR4__AREFRESH__FLD LPDDR4__DENALI_CTL_73__AREFRESH

#define LPDDR4__DENALI_CTL_73__AREF_STATUS_MASK                      0x00010000U
#define LPDDR4__DENALI_CTL_73__AREF_STATUS_SHIFT                             16U
#define LPDDR4__DENALI_CTL_73__AREF_STATUS_WIDTH                              1U
#define LPDDR4__DENALI_CTL_73__AREF_STATUS_WOCLR                              0U
#define LPDDR4__DENALI_CTL_73__AREF_STATUS_WOSET                              0U
#define LPDDR4__AREF_STATUS__REG DENALI_CTL_73
#define LPDDR4__AREF_STATUS__FLD LPDDR4__DENALI_CTL_73__AREF_STATUS

#define LPDDR4__DENALI_CTL_73__TREF_ENABLE_MASK                      0x01000000U
#define LPDDR4__DENALI_CTL_73__TREF_ENABLE_SHIFT                             24U
#define LPDDR4__DENALI_CTL_73__TREF_ENABLE_WIDTH                              1U
#define LPDDR4__DENALI_CTL_73__TREF_ENABLE_WOCLR                              0U
#define LPDDR4__DENALI_CTL_73__TREF_ENABLE_WOSET                              0U
#define LPDDR4__TREF_ENABLE__REG DENALI_CTL_73
#define LPDDR4__TREF_ENABLE__FLD LPDDR4__DENALI_CTL_73__TREF_ENABLE

#define LPDDR4__DENALI_CTL_74_READ_MASK                              0x03FF3F07U
#define LPDDR4__DENALI_CTL_74_WRITE_MASK                             0x03FF3F07U
#define LPDDR4__DENALI_CTL_74__TRFC_OPT_THRESHOLD_MASK               0x00000007U
#define LPDDR4__DENALI_CTL_74__TRFC_OPT_THRESHOLD_SHIFT                       0U
#define LPDDR4__DENALI_CTL_74__TRFC_OPT_THRESHOLD_WIDTH                       3U
#define LPDDR4__TRFC_OPT_THRESHOLD__REG DENALI_CTL_74
#define LPDDR4__TRFC_OPT_THRESHOLD__FLD LPDDR4__DENALI_CTL_74__TRFC_OPT_THRESHOLD

#define LPDDR4__DENALI_CTL_74__CS_COMPARISON_FOR_REFRESH_DEPTH_MASK  0x00003F00U
#define LPDDR4__DENALI_CTL_74__CS_COMPARISON_FOR_REFRESH_DEPTH_SHIFT          8U
#define LPDDR4__DENALI_CTL_74__CS_COMPARISON_FOR_REFRESH_DEPTH_WIDTH          6U
#define LPDDR4__CS_COMPARISON_FOR_REFRESH_DEPTH__REG DENALI_CTL_74
#define LPDDR4__CS_COMPARISON_FOR_REFRESH_DEPTH__FLD LPDDR4__DENALI_CTL_74__CS_COMPARISON_FOR_REFRESH_DEPTH

#define LPDDR4__DENALI_CTL_74__TRFC_F0_MASK                          0x03FF0000U
#define LPDDR4__DENALI_CTL_74__TRFC_F0_SHIFT                                 16U
#define LPDDR4__DENALI_CTL_74__TRFC_F0_WIDTH                                 10U
#define LPDDR4__TRFC_F0__REG DENALI_CTL_74
#define LPDDR4__TRFC_F0__FLD LPDDR4__DENALI_CTL_74__TRFC_F0

#define LPDDR4__DENALI_CTL_75_READ_MASK                              0x000FFFFFU
#define LPDDR4__DENALI_CTL_75_WRITE_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_CTL_75__TREF_F0_MASK                          0x000FFFFFU
#define LPDDR4__DENALI_CTL_75__TREF_F0_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_75__TREF_F0_WIDTH                                 20U
#define LPDDR4__TREF_F0__REG DENALI_CTL_75
#define LPDDR4__TREF_F0__FLD LPDDR4__DENALI_CTL_75__TREF_F0

#define LPDDR4__DENALI_CTL_76_READ_MASK                              0x000003FFU
#define LPDDR4__DENALI_CTL_76_WRITE_MASK                             0x000003FFU
#define LPDDR4__DENALI_CTL_76__TRFC_F1_MASK                          0x000003FFU
#define LPDDR4__DENALI_CTL_76__TRFC_F1_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_76__TRFC_F1_WIDTH                                 10U
#define LPDDR4__TRFC_F1__REG DENALI_CTL_76
#define LPDDR4__TRFC_F1__FLD LPDDR4__DENALI_CTL_76__TRFC_F1

#define LPDDR4__DENALI_CTL_77_READ_MASK                              0x000FFFFFU
#define LPDDR4__DENALI_CTL_77_WRITE_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_CTL_77__TREF_F1_MASK                          0x000FFFFFU
#define LPDDR4__DENALI_CTL_77__TREF_F1_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_77__TREF_F1_WIDTH                                 20U
#define LPDDR4__TREF_F1__REG DENALI_CTL_77
#define LPDDR4__TREF_F1__FLD LPDDR4__DENALI_CTL_77__TREF_F1

#define LPDDR4__DENALI_CTL_78_READ_MASK                              0x000003FFU
#define LPDDR4__DENALI_CTL_78_WRITE_MASK                             0x000003FFU
#define LPDDR4__DENALI_CTL_78__TRFC_F2_MASK                          0x000003FFU
#define LPDDR4__DENALI_CTL_78__TRFC_F2_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_78__TRFC_F2_WIDTH                                 10U
#define LPDDR4__TRFC_F2__REG DENALI_CTL_78
#define LPDDR4__TRFC_F2__FLD LPDDR4__DENALI_CTL_78__TRFC_F2

#define LPDDR4__DENALI_CTL_79_READ_MASK                              0x000FFFFFU
#define LPDDR4__DENALI_CTL_79_WRITE_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_CTL_79__TREF_F2_MASK                          0x000FFFFFU
#define LPDDR4__DENALI_CTL_79__TREF_F2_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_79__TREF_F2_WIDTH                                 20U
#define LPDDR4__TREF_F2__REG DENALI_CTL_79
#define LPDDR4__TREF_F2__FLD LPDDR4__DENALI_CTL_79__TREF_F2

#define LPDDR4__DENALI_CTL_80_READ_MASK                              0x000FFFFFU
#define LPDDR4__DENALI_CTL_80_WRITE_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_CTL_80__TREF_INTERVAL_MASK                    0x000FFFFFU
#define LPDDR4__DENALI_CTL_80__TREF_INTERVAL_SHIFT                            0U
#define LPDDR4__DENALI_CTL_80__TREF_INTERVAL_WIDTH                           20U
#define LPDDR4__TREF_INTERVAL__REG DENALI_CTL_80
#define LPDDR4__TREF_INTERVAL__FLD LPDDR4__DENALI_CTL_80__TREF_INTERVAL

#define LPDDR4__DENALI_CTL_81_READ_MASK                              0x000003FFU
#define LPDDR4__DENALI_CTL_81_WRITE_MASK                             0x000003FFU
#define LPDDR4__DENALI_CTL_81__TRFC_PB_F0_MASK                       0x000003FFU
#define LPDDR4__DENALI_CTL_81__TRFC_PB_F0_SHIFT                               0U
#define LPDDR4__DENALI_CTL_81__TRFC_PB_F0_WIDTH                              10U
#define LPDDR4__TRFC_PB_F0__REG DENALI_CTL_81
#define LPDDR4__TRFC_PB_F0__FLD LPDDR4__DENALI_CTL_81__TRFC_PB_F0

#define LPDDR4__DENALI_CTL_82_READ_MASK                              0x000FFFFFU
#define LPDDR4__DENALI_CTL_82_WRITE_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_CTL_82__TREFI_PB_F0_MASK                      0x000FFFFFU
#define LPDDR4__DENALI_CTL_82__TREFI_PB_F0_SHIFT                              0U
#define LPDDR4__DENALI_CTL_82__TREFI_PB_F0_WIDTH                             20U
#define LPDDR4__TREFI_PB_F0__REG DENALI_CTL_82
#define LPDDR4__TREFI_PB_F0__FLD LPDDR4__DENALI_CTL_82__TREFI_PB_F0

#define LPDDR4__DENALI_CTL_83_READ_MASK                              0x000003FFU
#define LPDDR4__DENALI_CTL_83_WRITE_MASK                             0x000003FFU
#define LPDDR4__DENALI_CTL_83__TRFC_PB_F1_MASK                       0x000003FFU
#define LPDDR4__DENALI_CTL_83__TRFC_PB_F1_SHIFT                               0U
#define LPDDR4__DENALI_CTL_83__TRFC_PB_F1_WIDTH                              10U
#define LPDDR4__TRFC_PB_F1__REG DENALI_CTL_83
#define LPDDR4__TRFC_PB_F1__FLD LPDDR4__DENALI_CTL_83__TRFC_PB_F1

#define LPDDR4__DENALI_CTL_84_READ_MASK                              0x000FFFFFU
#define LPDDR4__DENALI_CTL_84_WRITE_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_CTL_84__TREFI_PB_F1_MASK                      0x000FFFFFU
#define LPDDR4__DENALI_CTL_84__TREFI_PB_F1_SHIFT                              0U
#define LPDDR4__DENALI_CTL_84__TREFI_PB_F1_WIDTH                             20U
#define LPDDR4__TREFI_PB_F1__REG DENALI_CTL_84
#define LPDDR4__TREFI_PB_F1__FLD LPDDR4__DENALI_CTL_84__TREFI_PB_F1

#define LPDDR4__DENALI_CTL_85_READ_MASK                              0x000003FFU
#define LPDDR4__DENALI_CTL_85_WRITE_MASK                             0x000003FFU
#define LPDDR4__DENALI_CTL_85__TRFC_PB_F2_MASK                       0x000003FFU
#define LPDDR4__DENALI_CTL_85__TRFC_PB_F2_SHIFT                               0U
#define LPDDR4__DENALI_CTL_85__TRFC_PB_F2_WIDTH                              10U
#define LPDDR4__TRFC_PB_F2__REG DENALI_CTL_85
#define LPDDR4__TRFC_PB_F2__FLD LPDDR4__DENALI_CTL_85__TRFC_PB_F2

#define LPDDR4__DENALI_CTL_86_READ_MASK                              0x010FFFFFU
#define LPDDR4__DENALI_CTL_86_WRITE_MASK                             0x010FFFFFU
#define LPDDR4__DENALI_CTL_86__TREFI_PB_F2_MASK                      0x000FFFFFU
#define LPDDR4__DENALI_CTL_86__TREFI_PB_F2_SHIFT                              0U
#define LPDDR4__DENALI_CTL_86__TREFI_PB_F2_WIDTH                             20U
#define LPDDR4__TREFI_PB_F2__REG DENALI_CTL_86
#define LPDDR4__TREFI_PB_F2__FLD LPDDR4__DENALI_CTL_86__TREFI_PB_F2

#define LPDDR4__DENALI_CTL_86__PBR_EN_MASK                           0x01000000U
#define LPDDR4__DENALI_CTL_86__PBR_EN_SHIFT                                  24U
#define LPDDR4__DENALI_CTL_86__PBR_EN_WIDTH                                   1U
#define LPDDR4__DENALI_CTL_86__PBR_EN_WOCLR                                   0U
#define LPDDR4__DENALI_CTL_86__PBR_EN_WOSET                                   0U
#define LPDDR4__PBR_EN__REG DENALI_CTL_86
#define LPDDR4__PBR_EN__FLD LPDDR4__DENALI_CTL_86__PBR_EN

#define LPDDR4__DENALI_CTL_87_READ_MASK                              0x0FFFFF01U
#define LPDDR4__DENALI_CTL_87_WRITE_MASK                             0x0FFFFF01U
#define LPDDR4__DENALI_CTL_87__PBR_NUMERIC_ORDER_MASK                0x00000001U
#define LPDDR4__DENALI_CTL_87__PBR_NUMERIC_ORDER_SHIFT                        0U
#define LPDDR4__DENALI_CTL_87__PBR_NUMERIC_ORDER_WIDTH                        1U
#define LPDDR4__DENALI_CTL_87__PBR_NUMERIC_ORDER_WOCLR                        0U
#define LPDDR4__DENALI_CTL_87__PBR_NUMERIC_ORDER_WOSET                        0U
#define LPDDR4__PBR_NUMERIC_ORDER__REG DENALI_CTL_87
#define LPDDR4__PBR_NUMERIC_ORDER__FLD LPDDR4__DENALI_CTL_87__PBR_NUMERIC_ORDER

#define LPDDR4__DENALI_CTL_87__PBR_MAX_BANK_WAIT_MASK                0x00FFFF00U
#define LPDDR4__DENALI_CTL_87__PBR_MAX_BANK_WAIT_SHIFT                        8U
#define LPDDR4__DENALI_CTL_87__PBR_MAX_BANK_WAIT_WIDTH                       16U
#define LPDDR4__PBR_MAX_BANK_WAIT__REG DENALI_CTL_87
#define LPDDR4__PBR_MAX_BANK_WAIT__FLD LPDDR4__DENALI_CTL_87__PBR_MAX_BANK_WAIT

#define LPDDR4__DENALI_CTL_87__PBR_BANK_SELECT_DELAY_MASK            0x0F000000U
#define LPDDR4__DENALI_CTL_87__PBR_BANK_SELECT_DELAY_SHIFT                   24U
#define LPDDR4__DENALI_CTL_87__PBR_BANK_SELECT_DELAY_WIDTH                    4U
#define LPDDR4__PBR_BANK_SELECT_DELAY__REG DENALI_CTL_87
#define LPDDR4__PBR_BANK_SELECT_DELAY__FLD LPDDR4__DENALI_CTL_87__PBR_BANK_SELECT_DELAY

#define LPDDR4__DENALI_CTL_88_READ_MASK                              0x001F1F01U
#define LPDDR4__DENALI_CTL_88_WRITE_MASK                             0x001F1F01U
#define LPDDR4__DENALI_CTL_88__PBR_CONT_REQ_EN_MASK                  0x00000001U
#define LPDDR4__DENALI_CTL_88__PBR_CONT_REQ_EN_SHIFT                          0U
#define LPDDR4__DENALI_CTL_88__PBR_CONT_REQ_EN_WIDTH                          1U
#define LPDDR4__DENALI_CTL_88__PBR_CONT_REQ_EN_WOCLR                          0U
#define LPDDR4__DENALI_CTL_88__PBR_CONT_REQ_EN_WOSET                          0U
#define LPDDR4__PBR_CONT_REQ_EN__REG DENALI_CTL_88
#define LPDDR4__PBR_CONT_REQ_EN__FLD LPDDR4__DENALI_CTL_88__PBR_CONT_REQ_EN

#define LPDDR4__DENALI_CTL_88__AREF_PBR_CONT_EN_THRESHOLD_MASK       0x00001F00U
#define LPDDR4__DENALI_CTL_88__AREF_PBR_CONT_EN_THRESHOLD_SHIFT               8U
#define LPDDR4__DENALI_CTL_88__AREF_PBR_CONT_EN_THRESHOLD_WIDTH               5U
#define LPDDR4__AREF_PBR_CONT_EN_THRESHOLD__REG DENALI_CTL_88
#define LPDDR4__AREF_PBR_CONT_EN_THRESHOLD__FLD LPDDR4__DENALI_CTL_88__AREF_PBR_CONT_EN_THRESHOLD

#define LPDDR4__DENALI_CTL_88__AREF_PBR_CONT_DIS_THRESHOLD_MASK      0x001F0000U
#define LPDDR4__DENALI_CTL_88__AREF_PBR_CONT_DIS_THRESHOLD_SHIFT             16U
#define LPDDR4__DENALI_CTL_88__AREF_PBR_CONT_DIS_THRESHOLD_WIDTH              5U
#define LPDDR4__AREF_PBR_CONT_DIS_THRESHOLD__REG DENALI_CTL_88
#define LPDDR4__AREF_PBR_CONT_DIS_THRESHOLD__FLD LPDDR4__DENALI_CTL_88__AREF_PBR_CONT_DIS_THRESHOLD

#define LPDDR4__DENALI_CTL_89_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_89_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_89__TPDEX_F0_MASK                         0x0000FFFFU
#define LPDDR4__DENALI_CTL_89__TPDEX_F0_SHIFT                                 0U
#define LPDDR4__DENALI_CTL_89__TPDEX_F0_WIDTH                                16U
#define LPDDR4__TPDEX_F0__REG DENALI_CTL_89
#define LPDDR4__TPDEX_F0__FLD LPDDR4__DENALI_CTL_89__TPDEX_F0

#define LPDDR4__DENALI_CTL_89__TPDEX_F1_MASK                         0xFFFF0000U
#define LPDDR4__DENALI_CTL_89__TPDEX_F1_SHIFT                                16U
#define LPDDR4__DENALI_CTL_89__TPDEX_F1_WIDTH                                16U
#define LPDDR4__TPDEX_F1__REG DENALI_CTL_89
#define LPDDR4__TPDEX_F1__FLD LPDDR4__DENALI_CTL_89__TPDEX_F1

#define LPDDR4__DENALI_CTL_90_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_90_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_90__TPDEX_F2_MASK                         0x0000FFFFU
#define LPDDR4__DENALI_CTL_90__TPDEX_F2_SHIFT                                 0U
#define LPDDR4__DENALI_CTL_90__TPDEX_F2_WIDTH                                16U
#define LPDDR4__TPDEX_F2__REG DENALI_CTL_90
#define LPDDR4__TPDEX_F2__FLD LPDDR4__DENALI_CTL_90__TPDEX_F2

#define LPDDR4__DENALI_CTL_90__TMRRI_F0_MASK                         0x00FF0000U
#define LPDDR4__DENALI_CTL_90__TMRRI_F0_SHIFT                                16U
#define LPDDR4__DENALI_CTL_90__TMRRI_F0_WIDTH                                 8U
#define LPDDR4__TMRRI_F0__REG DENALI_CTL_90
#define LPDDR4__TMRRI_F0__FLD LPDDR4__DENALI_CTL_90__TMRRI_F0

#define LPDDR4__DENALI_CTL_90__TMRRI_F1_MASK                         0xFF000000U
#define LPDDR4__DENALI_CTL_90__TMRRI_F1_SHIFT                                24U
#define LPDDR4__DENALI_CTL_90__TMRRI_F1_WIDTH                                 8U
#define LPDDR4__TMRRI_F1__REG DENALI_CTL_90
#define LPDDR4__TMRRI_F1__FLD LPDDR4__DENALI_CTL_90__TMRRI_F1

#define LPDDR4__DENALI_CTL_91_READ_MASK                              0x1F1F1FFFU
#define LPDDR4__DENALI_CTL_91_WRITE_MASK                             0x1F1F1FFFU
#define LPDDR4__DENALI_CTL_91__TMRRI_F2_MASK                         0x000000FFU
#define LPDDR4__DENALI_CTL_91__TMRRI_F2_SHIFT                                 0U
#define LPDDR4__DENALI_CTL_91__TMRRI_F2_WIDTH                                 8U
#define LPDDR4__TMRRI_F2__REG DENALI_CTL_91
#define LPDDR4__TMRRI_F2__FLD LPDDR4__DENALI_CTL_91__TMRRI_F2

#define LPDDR4__DENALI_CTL_91__TCKELCS_F0_MASK                       0x00001F00U
#define LPDDR4__DENALI_CTL_91__TCKELCS_F0_SHIFT                               8U
#define LPDDR4__DENALI_CTL_91__TCKELCS_F0_WIDTH                               5U
#define LPDDR4__TCKELCS_F0__REG DENALI_CTL_91
#define LPDDR4__TCKELCS_F0__FLD LPDDR4__DENALI_CTL_91__TCKELCS_F0

#define LPDDR4__DENALI_CTL_91__TCKEHCS_F0_MASK                       0x001F0000U
#define LPDDR4__DENALI_CTL_91__TCKEHCS_F0_SHIFT                              16U
#define LPDDR4__DENALI_CTL_91__TCKEHCS_F0_WIDTH                               5U
#define LPDDR4__TCKEHCS_F0__REG DENALI_CTL_91
#define LPDDR4__TCKEHCS_F0__FLD LPDDR4__DENALI_CTL_91__TCKEHCS_F0

#define LPDDR4__DENALI_CTL_91__TMRWCKEL_F0_MASK                      0x1F000000U
#define LPDDR4__DENALI_CTL_91__TMRWCKEL_F0_SHIFT                             24U
#define LPDDR4__DENALI_CTL_91__TMRWCKEL_F0_WIDTH                              5U
#define LPDDR4__TMRWCKEL_F0__REG DENALI_CTL_91
#define LPDDR4__TMRWCKEL_F0__FLD LPDDR4__DENALI_CTL_91__TMRWCKEL_F0

#define LPDDR4__DENALI_CTL_92_READ_MASK                              0x1F1F1F0FU
#define LPDDR4__DENALI_CTL_92_WRITE_MASK                             0x1F1F1F0FU
#define LPDDR4__DENALI_CTL_92__TZQCKE_F0_MASK                        0x0000000FU
#define LPDDR4__DENALI_CTL_92__TZQCKE_F0_SHIFT                                0U
#define LPDDR4__DENALI_CTL_92__TZQCKE_F0_WIDTH                                4U
#define LPDDR4__TZQCKE_F0__REG DENALI_CTL_92
#define LPDDR4__TZQCKE_F0__FLD LPDDR4__DENALI_CTL_92__TZQCKE_F0

#define LPDDR4__DENALI_CTL_92__TCKELCS_F1_MASK                       0x00001F00U
#define LPDDR4__DENALI_CTL_92__TCKELCS_F1_SHIFT                               8U
#define LPDDR4__DENALI_CTL_92__TCKELCS_F1_WIDTH                               5U
#define LPDDR4__TCKELCS_F1__REG DENALI_CTL_92
#define LPDDR4__TCKELCS_F1__FLD LPDDR4__DENALI_CTL_92__TCKELCS_F1

#define LPDDR4__DENALI_CTL_92__TCKEHCS_F1_MASK                       0x001F0000U
#define LPDDR4__DENALI_CTL_92__TCKEHCS_F1_SHIFT                              16U
#define LPDDR4__DENALI_CTL_92__TCKEHCS_F1_WIDTH                               5U
#define LPDDR4__TCKEHCS_F1__REG DENALI_CTL_92
#define LPDDR4__TCKEHCS_F1__FLD LPDDR4__DENALI_CTL_92__TCKEHCS_F1

#define LPDDR4__DENALI_CTL_92__TMRWCKEL_F1_MASK                      0x1F000000U
#define LPDDR4__DENALI_CTL_92__TMRWCKEL_F1_SHIFT                             24U
#define LPDDR4__DENALI_CTL_92__TMRWCKEL_F1_WIDTH                              5U
#define LPDDR4__TMRWCKEL_F1__REG DENALI_CTL_92
#define LPDDR4__TMRWCKEL_F1__FLD LPDDR4__DENALI_CTL_92__TMRWCKEL_F1

#define LPDDR4__DENALI_CTL_93_READ_MASK                              0x1F1F1F0FU
#define LPDDR4__DENALI_CTL_93_WRITE_MASK                             0x1F1F1F0FU
#define LPDDR4__DENALI_CTL_93__TZQCKE_F1_MASK                        0x0000000FU
#define LPDDR4__DENALI_CTL_93__TZQCKE_F1_SHIFT                                0U
#define LPDDR4__DENALI_CTL_93__TZQCKE_F1_WIDTH                                4U
#define LPDDR4__TZQCKE_F1__REG DENALI_CTL_93
#define LPDDR4__TZQCKE_F1__FLD LPDDR4__DENALI_CTL_93__TZQCKE_F1

#define LPDDR4__DENALI_CTL_93__TCKELCS_F2_MASK                       0x00001F00U
#define LPDDR4__DENALI_CTL_93__TCKELCS_F2_SHIFT                               8U
#define LPDDR4__DENALI_CTL_93__TCKELCS_F2_WIDTH                               5U
#define LPDDR4__TCKELCS_F2__REG DENALI_CTL_93
#define LPDDR4__TCKELCS_F2__FLD LPDDR4__DENALI_CTL_93__TCKELCS_F2

#define LPDDR4__DENALI_CTL_93__TCKEHCS_F2_MASK                       0x001F0000U
#define LPDDR4__DENALI_CTL_93__TCKEHCS_F2_SHIFT                              16U
#define LPDDR4__DENALI_CTL_93__TCKEHCS_F2_WIDTH                               5U
#define LPDDR4__TCKEHCS_F2__REG DENALI_CTL_93
#define LPDDR4__TCKEHCS_F2__FLD LPDDR4__DENALI_CTL_93__TCKEHCS_F2

#define LPDDR4__DENALI_CTL_93__TMRWCKEL_F2_MASK                      0x1F000000U
#define LPDDR4__DENALI_CTL_93__TMRWCKEL_F2_SHIFT                             24U
#define LPDDR4__DENALI_CTL_93__TMRWCKEL_F2_WIDTH                              5U
#define LPDDR4__TMRWCKEL_F2__REG DENALI_CTL_93
#define LPDDR4__TMRWCKEL_F2__FLD LPDDR4__DENALI_CTL_93__TMRWCKEL_F2

#define LPDDR4__DENALI_CTL_94_READ_MASK                              0x1F011F0FU
#define LPDDR4__DENALI_CTL_94_WRITE_MASK                             0x1F011F0FU
#define LPDDR4__DENALI_CTL_94__TZQCKE_F2_MASK                        0x0000000FU
#define LPDDR4__DENALI_CTL_94__TZQCKE_F2_SHIFT                                0U
#define LPDDR4__DENALI_CTL_94__TZQCKE_F2_WIDTH                                4U
#define LPDDR4__TZQCKE_F2__REG DENALI_CTL_94
#define LPDDR4__TZQCKE_F2__FLD LPDDR4__DENALI_CTL_94__TZQCKE_F2

#define LPDDR4__DENALI_CTL_94__TCSCKE_F0_MASK                        0x00001F00U
#define LPDDR4__DENALI_CTL_94__TCSCKE_F0_SHIFT                                8U
#define LPDDR4__DENALI_CTL_94__TCSCKE_F0_WIDTH                                5U
#define LPDDR4__TCSCKE_F0__REG DENALI_CTL_94
#define LPDDR4__TCSCKE_F0__FLD LPDDR4__DENALI_CTL_94__TCSCKE_F0

#define LPDDR4__DENALI_CTL_94__CA_DEFAULT_VAL_F0_MASK                0x00010000U
#define LPDDR4__DENALI_CTL_94__CA_DEFAULT_VAL_F0_SHIFT                       16U
#define LPDDR4__DENALI_CTL_94__CA_DEFAULT_VAL_F0_WIDTH                        1U
#define LPDDR4__DENALI_CTL_94__CA_DEFAULT_VAL_F0_WOCLR                        0U
#define LPDDR4__DENALI_CTL_94__CA_DEFAULT_VAL_F0_WOSET                        0U
#define LPDDR4__CA_DEFAULT_VAL_F0__REG DENALI_CTL_94
#define LPDDR4__CA_DEFAULT_VAL_F0__FLD LPDDR4__DENALI_CTL_94__CA_DEFAULT_VAL_F0

#define LPDDR4__DENALI_CTL_94__TCSCKE_F1_MASK                        0x1F000000U
#define LPDDR4__DENALI_CTL_94__TCSCKE_F1_SHIFT                               24U
#define LPDDR4__DENALI_CTL_94__TCSCKE_F1_WIDTH                                5U
#define LPDDR4__TCSCKE_F1__REG DENALI_CTL_94
#define LPDDR4__TCSCKE_F1__FLD LPDDR4__DENALI_CTL_94__TCSCKE_F1

#define LPDDR4__DENALI_CTL_95_READ_MASK                              0x00011F01U
#define LPDDR4__DENALI_CTL_95_WRITE_MASK                             0x00011F01U
#define LPDDR4__DENALI_CTL_95__CA_DEFAULT_VAL_F1_MASK                0x00000001U
#define LPDDR4__DENALI_CTL_95__CA_DEFAULT_VAL_F1_SHIFT                        0U
#define LPDDR4__DENALI_CTL_95__CA_DEFAULT_VAL_F1_WIDTH                        1U
#define LPDDR4__DENALI_CTL_95__CA_DEFAULT_VAL_F1_WOCLR                        0U
#define LPDDR4__DENALI_CTL_95__CA_DEFAULT_VAL_F1_WOSET                        0U
#define LPDDR4__CA_DEFAULT_VAL_F1__REG DENALI_CTL_95
#define LPDDR4__CA_DEFAULT_VAL_F1__FLD LPDDR4__DENALI_CTL_95__CA_DEFAULT_VAL_F1

#define LPDDR4__DENALI_CTL_95__TCSCKE_F2_MASK                        0x00001F00U
#define LPDDR4__DENALI_CTL_95__TCSCKE_F2_SHIFT                                8U
#define LPDDR4__DENALI_CTL_95__TCSCKE_F2_WIDTH                                5U
#define LPDDR4__TCSCKE_F2__REG DENALI_CTL_95
#define LPDDR4__TCSCKE_F2__FLD LPDDR4__DENALI_CTL_95__TCSCKE_F2

#define LPDDR4__DENALI_CTL_95__CA_DEFAULT_VAL_F2_MASK                0x00010000U
#define LPDDR4__DENALI_CTL_95__CA_DEFAULT_VAL_F2_SHIFT                       16U
#define LPDDR4__DENALI_CTL_95__CA_DEFAULT_VAL_F2_WIDTH                        1U
#define LPDDR4__DENALI_CTL_95__CA_DEFAULT_VAL_F2_WOCLR                        0U
#define LPDDR4__DENALI_CTL_95__CA_DEFAULT_VAL_F2_WOSET                        0U
#define LPDDR4__CA_DEFAULT_VAL_F2__REG DENALI_CTL_95
#define LPDDR4__CA_DEFAULT_VAL_F2__FLD LPDDR4__DENALI_CTL_95__CA_DEFAULT_VAL_F2

#define LPDDR4__DENALI_CTL_96_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_96_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_96__TXSR_F0_MASK                          0x0000FFFFU
#define LPDDR4__DENALI_CTL_96__TXSR_F0_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_96__TXSR_F0_WIDTH                                 16U
#define LPDDR4__TXSR_F0__REG DENALI_CTL_96
#define LPDDR4__TXSR_F0__FLD LPDDR4__DENALI_CTL_96__TXSR_F0

#define LPDDR4__DENALI_CTL_96__TXSNR_F0_MASK                         0xFFFF0000U
#define LPDDR4__DENALI_CTL_96__TXSNR_F0_SHIFT                                16U
#define LPDDR4__DENALI_CTL_96__TXSNR_F0_WIDTH                                16U
#define LPDDR4__TXSNR_F0__REG DENALI_CTL_96
#define LPDDR4__TXSNR_F0__FLD LPDDR4__DENALI_CTL_96__TXSNR_F0

#define LPDDR4__DENALI_CTL_97_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_97_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_97__TXSR_F1_MASK                          0x0000FFFFU
#define LPDDR4__DENALI_CTL_97__TXSR_F1_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_97__TXSR_F1_WIDTH                                 16U
#define LPDDR4__TXSR_F1__REG DENALI_CTL_97
#define LPDDR4__TXSR_F1__FLD LPDDR4__DENALI_CTL_97__TXSR_F1

#define LPDDR4__DENALI_CTL_97__TXSNR_F1_MASK                         0xFFFF0000U
#define LPDDR4__DENALI_CTL_97__TXSNR_F1_SHIFT                                16U
#define LPDDR4__DENALI_CTL_97__TXSNR_F1_WIDTH                                16U
#define LPDDR4__TXSNR_F1__REG DENALI_CTL_97
#define LPDDR4__TXSNR_F1__FLD LPDDR4__DENALI_CTL_97__TXSNR_F1

#define LPDDR4__DENALI_CTL_98_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_98_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_98__TXSR_F2_MASK                          0x0000FFFFU
#define LPDDR4__DENALI_CTL_98__TXSR_F2_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_98__TXSR_F2_WIDTH                                 16U
#define LPDDR4__TXSR_F2__REG DENALI_CTL_98
#define LPDDR4__TXSR_F2__FLD LPDDR4__DENALI_CTL_98__TXSR_F2

#define LPDDR4__DENALI_CTL_98__TXSNR_F2_MASK                         0xFFFF0000U
#define LPDDR4__DENALI_CTL_98__TXSNR_F2_SHIFT                                16U
#define LPDDR4__DENALI_CTL_98__TXSNR_F2_WIDTH                                16U
#define LPDDR4__TXSNR_F2__REG DENALI_CTL_98
#define LPDDR4__TXSNR_F2__FLD LPDDR4__DENALI_CTL_98__TXSNR_F2

#define LPDDR4__DENALI_CTL_99_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_99_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_99__TXPR_F0_MASK                          0x0000FFFFU
#define LPDDR4__DENALI_CTL_99__TXPR_F0_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_99__TXPR_F0_WIDTH                                 16U
#define LPDDR4__TXPR_F0__REG DENALI_CTL_99
#define LPDDR4__TXPR_F0__FLD LPDDR4__DENALI_CTL_99__TXPR_F0

#define LPDDR4__DENALI_CTL_99__TXPR_F1_MASK                          0xFFFF0000U
#define LPDDR4__DENALI_CTL_99__TXPR_F1_SHIFT                                 16U
#define LPDDR4__DENALI_CTL_99__TXPR_F1_WIDTH                                 16U
#define LPDDR4__TXPR_F1__REG DENALI_CTL_99
#define LPDDR4__TXPR_F1__FLD LPDDR4__DENALI_CTL_99__TXPR_F1

#define LPDDR4__DENALI_CTL_100_READ_MASK                             0x07FFFFFFU
#define LPDDR4__DENALI_CTL_100_WRITE_MASK                            0x07FFFFFFU
#define LPDDR4__DENALI_CTL_100__TXPR_F2_MASK                         0x0000FFFFU
#define LPDDR4__DENALI_CTL_100__TXPR_F2_SHIFT                                 0U
#define LPDDR4__DENALI_CTL_100__TXPR_F2_WIDTH                                16U
#define LPDDR4__TXPR_F2__REG DENALI_CTL_100
#define LPDDR4__TXPR_F2__FLD LPDDR4__DENALI_CTL_100__TXPR_F2

#define LPDDR4__DENALI_CTL_100__TSR_F0_MASK                          0x00FF0000U
#define LPDDR4__DENALI_CTL_100__TSR_F0_SHIFT                                 16U
#define LPDDR4__DENALI_CTL_100__TSR_F0_WIDTH                                  8U
#define LPDDR4__TSR_F0__REG DENALI_CTL_100
#define LPDDR4__TSR_F0__FLD LPDDR4__DENALI_CTL_100__TSR_F0

#define LPDDR4__DENALI_CTL_100__TESCKE_F0_MASK                       0x07000000U
#define LPDDR4__DENALI_CTL_100__TESCKE_F0_SHIFT                              24U
#define LPDDR4__DENALI_CTL_100__TESCKE_F0_WIDTH                               3U
#define LPDDR4__TESCKE_F0__REG DENALI_CTL_100
#define LPDDR4__TESCKE_F0__FLD LPDDR4__DENALI_CTL_100__TESCKE_F0

#define LPDDR4__DENALI_CTL_101_READ_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_CTL_101_WRITE_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_CTL_101__TCSCKEH_F0_MASK                      0x0000001FU
#define LPDDR4__DENALI_CTL_101__TCSCKEH_F0_SHIFT                              0U
#define LPDDR4__DENALI_CTL_101__TCSCKEH_F0_WIDTH                              5U
#define LPDDR4__TCSCKEH_F0__REG DENALI_CTL_101
#define LPDDR4__TCSCKEH_F0__FLD LPDDR4__DENALI_CTL_101__TCSCKEH_F0

#define LPDDR4__DENALI_CTL_101__TCKELCMD_F0_MASK                     0x00001F00U
#define LPDDR4__DENALI_CTL_101__TCKELCMD_F0_SHIFT                             8U
#define LPDDR4__DENALI_CTL_101__TCKELCMD_F0_WIDTH                             5U
#define LPDDR4__TCKELCMD_F0__REG DENALI_CTL_101
#define LPDDR4__TCKELCMD_F0__FLD LPDDR4__DENALI_CTL_101__TCKELCMD_F0

#define LPDDR4__DENALI_CTL_101__TCKEHCMD_F0_MASK                     0x001F0000U
#define LPDDR4__DENALI_CTL_101__TCKEHCMD_F0_SHIFT                            16U
#define LPDDR4__DENALI_CTL_101__TCKEHCMD_F0_WIDTH                             5U
#define LPDDR4__TCKEHCMD_F0__REG DENALI_CTL_101
#define LPDDR4__TCKEHCMD_F0__FLD LPDDR4__DENALI_CTL_101__TCKEHCMD_F0

#define LPDDR4__DENALI_CTL_101__TCKCKEL_F0_MASK                      0x1F000000U
#define LPDDR4__DENALI_CTL_101__TCKCKEL_F0_SHIFT                             24U
#define LPDDR4__DENALI_CTL_101__TCKCKEL_F0_WIDTH                              5U
#define LPDDR4__TCKCKEL_F0__REG DENALI_CTL_101
#define LPDDR4__TCKCKEL_F0__FLD LPDDR4__DENALI_CTL_101__TCKCKEL_F0

#define LPDDR4__DENALI_CTL_102_READ_MASK                             0x1F07FF1FU
#define LPDDR4__DENALI_CTL_102_WRITE_MASK                            0x1F07FF1FU
#define LPDDR4__DENALI_CTL_102__TCKELPD_F0_MASK                      0x0000001FU
#define LPDDR4__DENALI_CTL_102__TCKELPD_F0_SHIFT                              0U
#define LPDDR4__DENALI_CTL_102__TCKELPD_F0_WIDTH                              5U
#define LPDDR4__TCKELPD_F0__REG DENALI_CTL_102
#define LPDDR4__TCKELPD_F0__FLD LPDDR4__DENALI_CTL_102__TCKELPD_F0

#define LPDDR4__DENALI_CTL_102__TSR_F1_MASK                          0x0000FF00U
#define LPDDR4__DENALI_CTL_102__TSR_F1_SHIFT                                  8U
#define LPDDR4__DENALI_CTL_102__TSR_F1_WIDTH                                  8U
#define LPDDR4__TSR_F1__REG DENALI_CTL_102
#define LPDDR4__TSR_F1__FLD LPDDR4__DENALI_CTL_102__TSR_F1

#define LPDDR4__DENALI_CTL_102__TESCKE_F1_MASK                       0x00070000U
#define LPDDR4__DENALI_CTL_102__TESCKE_F1_SHIFT                              16U
#define LPDDR4__DENALI_CTL_102__TESCKE_F1_WIDTH                               3U
#define LPDDR4__TESCKE_F1__REG DENALI_CTL_102
#define LPDDR4__TESCKE_F1__FLD LPDDR4__DENALI_CTL_102__TESCKE_F1

#define LPDDR4__DENALI_CTL_102__TCSCKEH_F1_MASK                      0x1F000000U
#define LPDDR4__DENALI_CTL_102__TCSCKEH_F1_SHIFT                             24U
#define LPDDR4__DENALI_CTL_102__TCSCKEH_F1_WIDTH                              5U
#define LPDDR4__TCSCKEH_F1__REG DENALI_CTL_102
#define LPDDR4__TCSCKEH_F1__FLD LPDDR4__DENALI_CTL_102__TCSCKEH_F1

#define LPDDR4__DENALI_CTL_103_READ_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_CTL_103_WRITE_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_CTL_103__TCKELCMD_F1_MASK                     0x0000001FU
#define LPDDR4__DENALI_CTL_103__TCKELCMD_F1_SHIFT                             0U
#define LPDDR4__DENALI_CTL_103__TCKELCMD_F1_WIDTH                             5U
#define LPDDR4__TCKELCMD_F1__REG DENALI_CTL_103
#define LPDDR4__TCKELCMD_F1__FLD LPDDR4__DENALI_CTL_103__TCKELCMD_F1

#define LPDDR4__DENALI_CTL_103__TCKEHCMD_F1_MASK                     0x00001F00U
#define LPDDR4__DENALI_CTL_103__TCKEHCMD_F1_SHIFT                             8U
#define LPDDR4__DENALI_CTL_103__TCKEHCMD_F1_WIDTH                             5U
#define LPDDR4__TCKEHCMD_F1__REG DENALI_CTL_103
#define LPDDR4__TCKEHCMD_F1__FLD LPDDR4__DENALI_CTL_103__TCKEHCMD_F1

#define LPDDR4__DENALI_CTL_103__TCKCKEL_F1_MASK                      0x001F0000U
#define LPDDR4__DENALI_CTL_103__TCKCKEL_F1_SHIFT                             16U
#define LPDDR4__DENALI_CTL_103__TCKCKEL_F1_WIDTH                              5U
#define LPDDR4__TCKCKEL_F1__REG DENALI_CTL_103
#define LPDDR4__TCKCKEL_F1__FLD LPDDR4__DENALI_CTL_103__TCKCKEL_F1

#define LPDDR4__DENALI_CTL_103__TCKELPD_F1_MASK                      0x1F000000U
#define LPDDR4__DENALI_CTL_103__TCKELPD_F1_SHIFT                             24U
#define LPDDR4__DENALI_CTL_103__TCKELPD_F1_WIDTH                              5U
#define LPDDR4__TCKELPD_F1__REG DENALI_CTL_103
#define LPDDR4__TCKELPD_F1__FLD LPDDR4__DENALI_CTL_103__TCKELPD_F1

#define LPDDR4__DENALI_CTL_104_READ_MASK                             0x1F1F07FFU
#define LPDDR4__DENALI_CTL_104_WRITE_MASK                            0x1F1F07FFU
#define LPDDR4__DENALI_CTL_104__TSR_F2_MASK                          0x000000FFU
#define LPDDR4__DENALI_CTL_104__TSR_F2_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_104__TSR_F2_WIDTH                                  8U
#define LPDDR4__TSR_F2__REG DENALI_CTL_104
#define LPDDR4__TSR_F2__FLD LPDDR4__DENALI_CTL_104__TSR_F2

#define LPDDR4__DENALI_CTL_104__TESCKE_F2_MASK                       0x00000700U
#define LPDDR4__DENALI_CTL_104__TESCKE_F2_SHIFT                               8U
#define LPDDR4__DENALI_CTL_104__TESCKE_F2_WIDTH                               3U
#define LPDDR4__TESCKE_F2__REG DENALI_CTL_104
#define LPDDR4__TESCKE_F2__FLD LPDDR4__DENALI_CTL_104__TESCKE_F2

#define LPDDR4__DENALI_CTL_104__TCSCKEH_F2_MASK                      0x001F0000U
#define LPDDR4__DENALI_CTL_104__TCSCKEH_F2_SHIFT                             16U
#define LPDDR4__DENALI_CTL_104__TCSCKEH_F2_WIDTH                              5U
#define LPDDR4__TCSCKEH_F2__REG DENALI_CTL_104
#define LPDDR4__TCSCKEH_F2__FLD LPDDR4__DENALI_CTL_104__TCSCKEH_F2

#define LPDDR4__DENALI_CTL_104__TCKELCMD_F2_MASK                     0x1F000000U
#define LPDDR4__DENALI_CTL_104__TCKELCMD_F2_SHIFT                            24U
#define LPDDR4__DENALI_CTL_104__TCKELCMD_F2_WIDTH                             5U
#define LPDDR4__TCKELCMD_F2__REG DENALI_CTL_104
#define LPDDR4__TCKELCMD_F2__FLD LPDDR4__DENALI_CTL_104__TCKELCMD_F2

#define LPDDR4__DENALI_CTL_105_READ_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_CTL_105_WRITE_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_CTL_105__TCKEHCMD_F2_MASK                     0x0000001FU
#define LPDDR4__DENALI_CTL_105__TCKEHCMD_F2_SHIFT                             0U
#define LPDDR4__DENALI_CTL_105__TCKEHCMD_F2_WIDTH                             5U
#define LPDDR4__TCKEHCMD_F2__REG DENALI_CTL_105
#define LPDDR4__TCKEHCMD_F2__FLD LPDDR4__DENALI_CTL_105__TCKEHCMD_F2

#define LPDDR4__DENALI_CTL_105__TCKCKEL_F2_MASK                      0x00001F00U
#define LPDDR4__DENALI_CTL_105__TCKCKEL_F2_SHIFT                              8U
#define LPDDR4__DENALI_CTL_105__TCKCKEL_F2_WIDTH                              5U
#define LPDDR4__TCKCKEL_F2__REG DENALI_CTL_105
#define LPDDR4__TCKCKEL_F2__FLD LPDDR4__DENALI_CTL_105__TCKCKEL_F2

#define LPDDR4__DENALI_CTL_105__TCKELPD_F2_MASK                      0x001F0000U
#define LPDDR4__DENALI_CTL_105__TCKELPD_F2_SHIFT                             16U
#define LPDDR4__DENALI_CTL_105__TCKELPD_F2_WIDTH                              5U
#define LPDDR4__TCKELPD_F2__REG DENALI_CTL_105
#define LPDDR4__TCKELPD_F2__FLD LPDDR4__DENALI_CTL_105__TCKELPD_F2

#define LPDDR4__DENALI_CTL_105__TCMDCKE_F0_MASK                      0x1F000000U
#define LPDDR4__DENALI_CTL_105__TCMDCKE_F0_SHIFT                             24U
#define LPDDR4__DENALI_CTL_105__TCMDCKE_F0_WIDTH                              5U
#define LPDDR4__TCMDCKE_F0__REG DENALI_CTL_105
#define LPDDR4__TCMDCKE_F0__FLD LPDDR4__DENALI_CTL_105__TCMDCKE_F0

#define LPDDR4__DENALI_CTL_106_READ_MASK                             0x01011F1FU
#define LPDDR4__DENALI_CTL_106_WRITE_MASK                            0x01011F1FU
#define LPDDR4__DENALI_CTL_106__TCMDCKE_F1_MASK                      0x0000001FU
#define LPDDR4__DENALI_CTL_106__TCMDCKE_F1_SHIFT                              0U
#define LPDDR4__DENALI_CTL_106__TCMDCKE_F1_WIDTH                              5U
#define LPDDR4__TCMDCKE_F1__REG DENALI_CTL_106
#define LPDDR4__TCMDCKE_F1__FLD LPDDR4__DENALI_CTL_106__TCMDCKE_F1

#define LPDDR4__DENALI_CTL_106__TCMDCKE_F2_MASK                      0x00001F00U
#define LPDDR4__DENALI_CTL_106__TCMDCKE_F2_SHIFT                              8U
#define LPDDR4__DENALI_CTL_106__TCMDCKE_F2_WIDTH                              5U
#define LPDDR4__TCMDCKE_F2__REG DENALI_CTL_106
#define LPDDR4__TCMDCKE_F2__FLD LPDDR4__DENALI_CTL_106__TCMDCKE_F2

#define LPDDR4__DENALI_CTL_106__PWRUP_SREFRESH_EXIT_MASK             0x00010000U
#define LPDDR4__DENALI_CTL_106__PWRUP_SREFRESH_EXIT_SHIFT                    16U
#define LPDDR4__DENALI_CTL_106__PWRUP_SREFRESH_EXIT_WIDTH                     1U
#define LPDDR4__DENALI_CTL_106__PWRUP_SREFRESH_EXIT_WOCLR                     0U
#define LPDDR4__DENALI_CTL_106__PWRUP_SREFRESH_EXIT_WOSET                     0U
#define LPDDR4__PWRUP_SREFRESH_EXIT__REG DENALI_CTL_106
#define LPDDR4__PWRUP_SREFRESH_EXIT__FLD LPDDR4__DENALI_CTL_106__PWRUP_SREFRESH_EXIT

#define LPDDR4__DENALI_CTL_106__SREFRESH_EXIT_NO_REFRESH_MASK        0x01000000U
#define LPDDR4__DENALI_CTL_106__SREFRESH_EXIT_NO_REFRESH_SHIFT               24U
#define LPDDR4__DENALI_CTL_106__SREFRESH_EXIT_NO_REFRESH_WIDTH                1U
#define LPDDR4__DENALI_CTL_106__SREFRESH_EXIT_NO_REFRESH_WOCLR                0U
#define LPDDR4__DENALI_CTL_106__SREFRESH_EXIT_NO_REFRESH_WOSET                0U
#define LPDDR4__SREFRESH_EXIT_NO_REFRESH__REG DENALI_CTL_106
#define LPDDR4__SREFRESH_EXIT_NO_REFRESH__FLD LPDDR4__DENALI_CTL_106__SREFRESH_EXIT_NO_REFRESH

#define LPDDR4__DENALI_CTL_107_READ_MASK                             0x7F000701U
#define LPDDR4__DENALI_CTL_107_WRITE_MASK                            0x7F000701U
#define LPDDR4__DENALI_CTL_107__ENABLE_QUICK_SREFRESH_MASK           0x00000001U
#define LPDDR4__DENALI_CTL_107__ENABLE_QUICK_SREFRESH_SHIFT                   0U
#define LPDDR4__DENALI_CTL_107__ENABLE_QUICK_SREFRESH_WIDTH                   1U
#define LPDDR4__DENALI_CTL_107__ENABLE_QUICK_SREFRESH_WOCLR                   0U
#define LPDDR4__DENALI_CTL_107__ENABLE_QUICK_SREFRESH_WOSET                   0U
#define LPDDR4__ENABLE_QUICK_SREFRESH__REG DENALI_CTL_107
#define LPDDR4__ENABLE_QUICK_SREFRESH__FLD LPDDR4__DENALI_CTL_107__ENABLE_QUICK_SREFRESH

#define LPDDR4__DENALI_CTL_107__CKE_DELAY_MASK                       0x00000700U
#define LPDDR4__DENALI_CTL_107__CKE_DELAY_SHIFT                               8U
#define LPDDR4__DENALI_CTL_107__CKE_DELAY_WIDTH                               3U
#define LPDDR4__CKE_DELAY__REG DENALI_CTL_107
#define LPDDR4__CKE_DELAY__FLD LPDDR4__DENALI_CTL_107__CKE_DELAY

#define LPDDR4__DENALI_CTL_107__DFS_CMD_MASK                         0x001F0000U
#define LPDDR4__DENALI_CTL_107__DFS_CMD_SHIFT                                16U
#define LPDDR4__DENALI_CTL_107__DFS_CMD_WIDTH                                 5U
#define LPDDR4__DFS_CMD__REG DENALI_CTL_107
#define LPDDR4__DFS_CMD__FLD LPDDR4__DENALI_CTL_107__DFS_CMD

#define LPDDR4__DENALI_CTL_107__DFS_STATUS_MASK                      0x7F000000U
#define LPDDR4__DENALI_CTL_107__DFS_STATUS_SHIFT                             24U
#define LPDDR4__DENALI_CTL_107__DFS_STATUS_WIDTH                              7U
#define LPDDR4__DFS_STATUS__REG DENALI_CTL_107
#define LPDDR4__DFS_STATUS__FLD LPDDR4__DENALI_CTL_107__DFS_STATUS

#define LPDDR4__DENALI_CTL_108_READ_MASK                             0x00FFFF01U
#define LPDDR4__DENALI_CTL_108_WRITE_MASK                            0x00FFFF01U
#define LPDDR4__DENALI_CTL_108__DFS_ZQ_EN_MASK                       0x00000001U
#define LPDDR4__DENALI_CTL_108__DFS_ZQ_EN_SHIFT                               0U
#define LPDDR4__DENALI_CTL_108__DFS_ZQ_EN_WIDTH                               1U
#define LPDDR4__DENALI_CTL_108__DFS_ZQ_EN_WOCLR                               0U
#define LPDDR4__DENALI_CTL_108__DFS_ZQ_EN_WOSET                               0U
#define LPDDR4__DFS_ZQ_EN__REG DENALI_CTL_108
#define LPDDR4__DFS_ZQ_EN__FLD LPDDR4__DENALI_CTL_108__DFS_ZQ_EN

#define LPDDR4__DENALI_CTL_108__DFS_PROMOTE_THRESHOLD_F0_MASK        0x00FFFF00U
#define LPDDR4__DENALI_CTL_108__DFS_PROMOTE_THRESHOLD_F0_SHIFT                8U
#define LPDDR4__DENALI_CTL_108__DFS_PROMOTE_THRESHOLD_F0_WIDTH               16U
#define LPDDR4__DFS_PROMOTE_THRESHOLD_F0__REG DENALI_CTL_108
#define LPDDR4__DFS_PROMOTE_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_108__DFS_PROMOTE_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_109_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_109_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_109__DFS_PROMOTE_THRESHOLD_F1_MASK        0x0000FFFFU
#define LPDDR4__DENALI_CTL_109__DFS_PROMOTE_THRESHOLD_F1_SHIFT                0U
#define LPDDR4__DENALI_CTL_109__DFS_PROMOTE_THRESHOLD_F1_WIDTH               16U
#define LPDDR4__DFS_PROMOTE_THRESHOLD_F1__REG DENALI_CTL_109
#define LPDDR4__DFS_PROMOTE_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_109__DFS_PROMOTE_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_109__DFS_PROMOTE_THRESHOLD_F2_MASK        0xFFFF0000U
#define LPDDR4__DENALI_CTL_109__DFS_PROMOTE_THRESHOLD_F2_SHIFT               16U
#define LPDDR4__DENALI_CTL_109__DFS_PROMOTE_THRESHOLD_F2_WIDTH               16U
#define LPDDR4__DFS_PROMOTE_THRESHOLD_F2__REG DENALI_CTL_109
#define LPDDR4__DFS_PROMOTE_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_109__DFS_PROMOTE_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_110_READ_MASK                             0xFF070707U
#define LPDDR4__DENALI_CTL_110_WRITE_MASK                            0xFF070707U
#define LPDDR4__DENALI_CTL_110__ZQ_STATUS_LOG_MASK                   0x00000007U
#define LPDDR4__DENALI_CTL_110__ZQ_STATUS_LOG_SHIFT                           0U
#define LPDDR4__DENALI_CTL_110__ZQ_STATUS_LOG_WIDTH                           3U
#define LPDDR4__ZQ_STATUS_LOG__REG DENALI_CTL_110
#define LPDDR4__ZQ_STATUS_LOG__FLD LPDDR4__DENALI_CTL_110__ZQ_STATUS_LOG

#define LPDDR4__DENALI_CTL_110__MC_RESERVED3_MASK                    0x00000700U
#define LPDDR4__DENALI_CTL_110__MC_RESERVED3_SHIFT                            8U
#define LPDDR4__DENALI_CTL_110__MC_RESERVED3_WIDTH                            3U
#define LPDDR4__MC_RESERVED3__REG DENALI_CTL_110
#define LPDDR4__MC_RESERVED3__FLD LPDDR4__DENALI_CTL_110__MC_RESERVED3

#define LPDDR4__DENALI_CTL_110__MC_RESERVED4_MASK                    0x00070000U
#define LPDDR4__DENALI_CTL_110__MC_RESERVED4_SHIFT                           16U
#define LPDDR4__DENALI_CTL_110__MC_RESERVED4_WIDTH                            3U
#define LPDDR4__MC_RESERVED4__REG DENALI_CTL_110
#define LPDDR4__MC_RESERVED4__FLD LPDDR4__DENALI_CTL_110__MC_RESERVED4

#define LPDDR4__DENALI_CTL_110__MC_RESERVED5_MASK                    0xFF000000U
#define LPDDR4__DENALI_CTL_110__MC_RESERVED5_SHIFT                           24U
#define LPDDR4__DENALI_CTL_110__MC_RESERVED5_WIDTH                            8U
#define LPDDR4__MC_RESERVED5__REG DENALI_CTL_110
#define LPDDR4__MC_RESERVED5__FLD LPDDR4__DENALI_CTL_110__MC_RESERVED5

#define LPDDR4__DENALI_CTL_111_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_111_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_111__MC_RESERVED6_MASK                    0x000000FFU
#define LPDDR4__DENALI_CTL_111__MC_RESERVED6_SHIFT                            0U
#define LPDDR4__DENALI_CTL_111__MC_RESERVED6_WIDTH                            8U
#define LPDDR4__MC_RESERVED6__REG DENALI_CTL_111
#define LPDDR4__MC_RESERVED6__FLD LPDDR4__DENALI_CTL_111__MC_RESERVED6

#define LPDDR4__DENALI_CTL_111__MC_RESERVED7_MASK                    0x0000FF00U
#define LPDDR4__DENALI_CTL_111__MC_RESERVED7_SHIFT                            8U
#define LPDDR4__DENALI_CTL_111__MC_RESERVED7_WIDTH                            8U
#define LPDDR4__MC_RESERVED7__REG DENALI_CTL_111
#define LPDDR4__MC_RESERVED7__FLD LPDDR4__DENALI_CTL_111__MC_RESERVED7

#define LPDDR4__DENALI_CTL_111__UPD_CTRLUPD_NORM_THRESHOLD_F0_MASK   0xFFFF0000U
#define LPDDR4__DENALI_CTL_111__UPD_CTRLUPD_NORM_THRESHOLD_F0_SHIFT          16U
#define LPDDR4__DENALI_CTL_111__UPD_CTRLUPD_NORM_THRESHOLD_F0_WIDTH          16U
#define LPDDR4__UPD_CTRLUPD_NORM_THRESHOLD_F0__REG DENALI_CTL_111
#define LPDDR4__UPD_CTRLUPD_NORM_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_111__UPD_CTRLUPD_NORM_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_112_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_112_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_112__UPD_CTRLUPD_HIGH_THRESHOLD_F0_MASK   0x0000FFFFU
#define LPDDR4__DENALI_CTL_112__UPD_CTRLUPD_HIGH_THRESHOLD_F0_SHIFT           0U
#define LPDDR4__DENALI_CTL_112__UPD_CTRLUPD_HIGH_THRESHOLD_F0_WIDTH          16U
#define LPDDR4__UPD_CTRLUPD_HIGH_THRESHOLD_F0__REG DENALI_CTL_112
#define LPDDR4__UPD_CTRLUPD_HIGH_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_112__UPD_CTRLUPD_HIGH_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_112__UPD_CTRLUPD_TIMEOUT_F0_MASK          0xFFFF0000U
#define LPDDR4__DENALI_CTL_112__UPD_CTRLUPD_TIMEOUT_F0_SHIFT                 16U
#define LPDDR4__DENALI_CTL_112__UPD_CTRLUPD_TIMEOUT_F0_WIDTH                 16U
#define LPDDR4__UPD_CTRLUPD_TIMEOUT_F0__REG DENALI_CTL_112
#define LPDDR4__UPD_CTRLUPD_TIMEOUT_F0__FLD LPDDR4__DENALI_CTL_112__UPD_CTRLUPD_TIMEOUT_F0

#define LPDDR4__DENALI_CTL_113_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_113_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_113__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F0_MASK 0x0000FFFFU
#define LPDDR4__DENALI_CTL_113__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F0_SHIFT     0U
#define LPDDR4__DENALI_CTL_113__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F0_WIDTH    16U
#define LPDDR4__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F0__REG DENALI_CTL_113
#define LPDDR4__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_113__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_113__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F0_MASK 0xFFFF0000U
#define LPDDR4__DENALI_CTL_113__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F0_SHIFT    16U
#define LPDDR4__DENALI_CTL_113__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F0_WIDTH    16U
#define LPDDR4__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F0__REG DENALI_CTL_113
#define LPDDR4__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_113__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_114_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_114_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_114__UPD_CTRLUPD_NORM_THRESHOLD_F1_MASK   0x0000FFFFU
#define LPDDR4__DENALI_CTL_114__UPD_CTRLUPD_NORM_THRESHOLD_F1_SHIFT           0U
#define LPDDR4__DENALI_CTL_114__UPD_CTRLUPD_NORM_THRESHOLD_F1_WIDTH          16U
#define LPDDR4__UPD_CTRLUPD_NORM_THRESHOLD_F1__REG DENALI_CTL_114
#define LPDDR4__UPD_CTRLUPD_NORM_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_114__UPD_CTRLUPD_NORM_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_114__UPD_CTRLUPD_HIGH_THRESHOLD_F1_MASK   0xFFFF0000U
#define LPDDR4__DENALI_CTL_114__UPD_CTRLUPD_HIGH_THRESHOLD_F1_SHIFT          16U
#define LPDDR4__DENALI_CTL_114__UPD_CTRLUPD_HIGH_THRESHOLD_F1_WIDTH          16U
#define LPDDR4__UPD_CTRLUPD_HIGH_THRESHOLD_F1__REG DENALI_CTL_114
#define LPDDR4__UPD_CTRLUPD_HIGH_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_114__UPD_CTRLUPD_HIGH_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_115_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_115_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_115__UPD_CTRLUPD_TIMEOUT_F1_MASK          0x0000FFFFU
#define LPDDR4__DENALI_CTL_115__UPD_CTRLUPD_TIMEOUT_F1_SHIFT                  0U
#define LPDDR4__DENALI_CTL_115__UPD_CTRLUPD_TIMEOUT_F1_WIDTH                 16U
#define LPDDR4__UPD_CTRLUPD_TIMEOUT_F1__REG DENALI_CTL_115
#define LPDDR4__UPD_CTRLUPD_TIMEOUT_F1__FLD LPDDR4__DENALI_CTL_115__UPD_CTRLUPD_TIMEOUT_F1

#define LPDDR4__DENALI_CTL_115__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F1_MASK 0xFFFF0000U
#define LPDDR4__DENALI_CTL_115__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F1_SHIFT    16U
#define LPDDR4__DENALI_CTL_115__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F1_WIDTH    16U
#define LPDDR4__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F1__REG DENALI_CTL_115
#define LPDDR4__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_115__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_116_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_116_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_116__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F1_MASK 0x0000FFFFU
#define LPDDR4__DENALI_CTL_116__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F1_SHIFT     0U
#define LPDDR4__DENALI_CTL_116__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F1_WIDTH    16U
#define LPDDR4__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F1__REG DENALI_CTL_116
#define LPDDR4__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_116__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_116__UPD_CTRLUPD_NORM_THRESHOLD_F2_MASK   0xFFFF0000U
#define LPDDR4__DENALI_CTL_116__UPD_CTRLUPD_NORM_THRESHOLD_F2_SHIFT          16U
#define LPDDR4__DENALI_CTL_116__UPD_CTRLUPD_NORM_THRESHOLD_F2_WIDTH          16U
#define LPDDR4__UPD_CTRLUPD_NORM_THRESHOLD_F2__REG DENALI_CTL_116
#define LPDDR4__UPD_CTRLUPD_NORM_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_116__UPD_CTRLUPD_NORM_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_117_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_117_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_117__UPD_CTRLUPD_HIGH_THRESHOLD_F2_MASK   0x0000FFFFU
#define LPDDR4__DENALI_CTL_117__UPD_CTRLUPD_HIGH_THRESHOLD_F2_SHIFT           0U
#define LPDDR4__DENALI_CTL_117__UPD_CTRLUPD_HIGH_THRESHOLD_F2_WIDTH          16U
#define LPDDR4__UPD_CTRLUPD_HIGH_THRESHOLD_F2__REG DENALI_CTL_117
#define LPDDR4__UPD_CTRLUPD_HIGH_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_117__UPD_CTRLUPD_HIGH_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_117__UPD_CTRLUPD_TIMEOUT_F2_MASK          0xFFFF0000U
#define LPDDR4__DENALI_CTL_117__UPD_CTRLUPD_TIMEOUT_F2_SHIFT                 16U
#define LPDDR4__DENALI_CTL_117__UPD_CTRLUPD_TIMEOUT_F2_WIDTH                 16U
#define LPDDR4__UPD_CTRLUPD_TIMEOUT_F2__REG DENALI_CTL_117
#define LPDDR4__UPD_CTRLUPD_TIMEOUT_F2__FLD LPDDR4__DENALI_CTL_117__UPD_CTRLUPD_TIMEOUT_F2

#define LPDDR4__DENALI_CTL_118_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_118_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_118__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F2_MASK 0x0000FFFFU
#define LPDDR4__DENALI_CTL_118__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F2_SHIFT     0U
#define LPDDR4__DENALI_CTL_118__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F2_WIDTH    16U
#define LPDDR4__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F2__REG DENALI_CTL_118
#define LPDDR4__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_118__UPD_CTRLUPD_SW_PROMOTE_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_118__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F2_MASK 0xFFFF0000U
#define LPDDR4__DENALI_CTL_118__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F2_SHIFT    16U
#define LPDDR4__DENALI_CTL_118__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F2_WIDTH    16U
#define LPDDR4__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F2__REG DENALI_CTL_118
#define LPDDR4__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_118__UPD_PHYUPD_DFI_PROMOTE_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_119_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_119_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_119__TDFI_PHYMSTR_MAX_F0_MASK             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_119__TDFI_PHYMSTR_MAX_F0_SHIFT                     0U
#define LPDDR4__DENALI_CTL_119__TDFI_PHYMSTR_MAX_F0_WIDTH                    32U
#define LPDDR4__TDFI_PHYMSTR_MAX_F0__REG DENALI_CTL_119
#define LPDDR4__TDFI_PHYMSTR_MAX_F0__FLD LPDDR4__DENALI_CTL_119__TDFI_PHYMSTR_MAX_F0

#define LPDDR4__DENALI_CTL_120_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_120_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_120__TDFI_PHYMSTR_MAX_TYPE0_F0_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_120__TDFI_PHYMSTR_MAX_TYPE0_F0_SHIFT               0U
#define LPDDR4__DENALI_CTL_120__TDFI_PHYMSTR_MAX_TYPE0_F0_WIDTH              32U
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE0_F0__REG DENALI_CTL_120
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE0_F0__FLD LPDDR4__DENALI_CTL_120__TDFI_PHYMSTR_MAX_TYPE0_F0

#define LPDDR4__DENALI_CTL_121_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_121_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_121__TDFI_PHYMSTR_MAX_TYPE1_F0_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_121__TDFI_PHYMSTR_MAX_TYPE1_F0_SHIFT               0U
#define LPDDR4__DENALI_CTL_121__TDFI_PHYMSTR_MAX_TYPE1_F0_WIDTH              32U
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE1_F0__REG DENALI_CTL_121
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE1_F0__FLD LPDDR4__DENALI_CTL_121__TDFI_PHYMSTR_MAX_TYPE1_F0

#define LPDDR4__DENALI_CTL_122_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_122_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_122__TDFI_PHYMSTR_MAX_TYPE2_F0_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_122__TDFI_PHYMSTR_MAX_TYPE2_F0_SHIFT               0U
#define LPDDR4__DENALI_CTL_122__TDFI_PHYMSTR_MAX_TYPE2_F0_WIDTH              32U
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE2_F0__REG DENALI_CTL_122
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE2_F0__FLD LPDDR4__DENALI_CTL_122__TDFI_PHYMSTR_MAX_TYPE2_F0

#define LPDDR4__DENALI_CTL_123_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_123_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_123__TDFI_PHYMSTR_MAX_TYPE3_F0_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_123__TDFI_PHYMSTR_MAX_TYPE3_F0_SHIFT               0U
#define LPDDR4__DENALI_CTL_123__TDFI_PHYMSTR_MAX_TYPE3_F0_WIDTH              32U
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE3_F0__REG DENALI_CTL_123
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE3_F0__FLD LPDDR4__DENALI_CTL_123__TDFI_PHYMSTR_MAX_TYPE3_F0

#define LPDDR4__DENALI_CTL_124_READ_MASK                             0x0000FFFFU
#define LPDDR4__DENALI_CTL_124_WRITE_MASK                            0x0000FFFFU
#define LPDDR4__DENALI_CTL_124__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F0_MASK 0x0000FFFFU
#define LPDDR4__DENALI_CTL_124__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F0_SHIFT       0U
#define LPDDR4__DENALI_CTL_124__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F0_WIDTH      16U
#define LPDDR4__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F0__REG DENALI_CTL_124
#define LPDDR4__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_124__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_125_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_CTL_125_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_CTL_125__TDFI_PHYMSTR_RESP_F0_MASK            0x000FFFFFU
#define LPDDR4__DENALI_CTL_125__TDFI_PHYMSTR_RESP_F0_SHIFT                    0U
#define LPDDR4__DENALI_CTL_125__TDFI_PHYMSTR_RESP_F0_WIDTH                   20U
#define LPDDR4__TDFI_PHYMSTR_RESP_F0__REG DENALI_CTL_125
#define LPDDR4__TDFI_PHYMSTR_RESP_F0__FLD LPDDR4__DENALI_CTL_125__TDFI_PHYMSTR_RESP_F0

#define LPDDR4__DENALI_CTL_126_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_126_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_126__TDFI_PHYMSTR_MAX_F1_MASK             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_126__TDFI_PHYMSTR_MAX_F1_SHIFT                     0U
#define LPDDR4__DENALI_CTL_126__TDFI_PHYMSTR_MAX_F1_WIDTH                    32U
#define LPDDR4__TDFI_PHYMSTR_MAX_F1__REG DENALI_CTL_126
#define LPDDR4__TDFI_PHYMSTR_MAX_F1__FLD LPDDR4__DENALI_CTL_126__TDFI_PHYMSTR_MAX_F1

#define LPDDR4__DENALI_CTL_127_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_127_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_127__TDFI_PHYMSTR_MAX_TYPE0_F1_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_127__TDFI_PHYMSTR_MAX_TYPE0_F1_SHIFT               0U
#define LPDDR4__DENALI_CTL_127__TDFI_PHYMSTR_MAX_TYPE0_F1_WIDTH              32U
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE0_F1__REG DENALI_CTL_127
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE0_F1__FLD LPDDR4__DENALI_CTL_127__TDFI_PHYMSTR_MAX_TYPE0_F1

#define LPDDR4__DENALI_CTL_128_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_128_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_128__TDFI_PHYMSTR_MAX_TYPE1_F1_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_128__TDFI_PHYMSTR_MAX_TYPE1_F1_SHIFT               0U
#define LPDDR4__DENALI_CTL_128__TDFI_PHYMSTR_MAX_TYPE1_F1_WIDTH              32U
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE1_F1__REG DENALI_CTL_128
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE1_F1__FLD LPDDR4__DENALI_CTL_128__TDFI_PHYMSTR_MAX_TYPE1_F1

#define LPDDR4__DENALI_CTL_129_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_129_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_129__TDFI_PHYMSTR_MAX_TYPE2_F1_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_129__TDFI_PHYMSTR_MAX_TYPE2_F1_SHIFT               0U
#define LPDDR4__DENALI_CTL_129__TDFI_PHYMSTR_MAX_TYPE2_F1_WIDTH              32U
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE2_F1__REG DENALI_CTL_129
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE2_F1__FLD LPDDR4__DENALI_CTL_129__TDFI_PHYMSTR_MAX_TYPE2_F1

#define LPDDR4__DENALI_CTL_130_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_130_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_130__TDFI_PHYMSTR_MAX_TYPE3_F1_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_130__TDFI_PHYMSTR_MAX_TYPE3_F1_SHIFT               0U
#define LPDDR4__DENALI_CTL_130__TDFI_PHYMSTR_MAX_TYPE3_F1_WIDTH              32U
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE3_F1__REG DENALI_CTL_130
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE3_F1__FLD LPDDR4__DENALI_CTL_130__TDFI_PHYMSTR_MAX_TYPE3_F1

#define LPDDR4__DENALI_CTL_131_READ_MASK                             0x0000FFFFU
#define LPDDR4__DENALI_CTL_131_WRITE_MASK                            0x0000FFFFU
#define LPDDR4__DENALI_CTL_131__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F1_MASK 0x0000FFFFU
#define LPDDR4__DENALI_CTL_131__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F1_SHIFT       0U
#define LPDDR4__DENALI_CTL_131__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F1_WIDTH      16U
#define LPDDR4__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F1__REG DENALI_CTL_131
#define LPDDR4__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_131__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_132_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_CTL_132_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_CTL_132__TDFI_PHYMSTR_RESP_F1_MASK            0x000FFFFFU
#define LPDDR4__DENALI_CTL_132__TDFI_PHYMSTR_RESP_F1_SHIFT                    0U
#define LPDDR4__DENALI_CTL_132__TDFI_PHYMSTR_RESP_F1_WIDTH                   20U
#define LPDDR4__TDFI_PHYMSTR_RESP_F1__REG DENALI_CTL_132
#define LPDDR4__TDFI_PHYMSTR_RESP_F1__FLD LPDDR4__DENALI_CTL_132__TDFI_PHYMSTR_RESP_F1

#define LPDDR4__DENALI_CTL_133_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_133_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_133__TDFI_PHYMSTR_MAX_F2_MASK             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_133__TDFI_PHYMSTR_MAX_F2_SHIFT                     0U
#define LPDDR4__DENALI_CTL_133__TDFI_PHYMSTR_MAX_F2_WIDTH                    32U
#define LPDDR4__TDFI_PHYMSTR_MAX_F2__REG DENALI_CTL_133
#define LPDDR4__TDFI_PHYMSTR_MAX_F2__FLD LPDDR4__DENALI_CTL_133__TDFI_PHYMSTR_MAX_F2

#define LPDDR4__DENALI_CTL_134_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_134_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_134__TDFI_PHYMSTR_MAX_TYPE0_F2_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_134__TDFI_PHYMSTR_MAX_TYPE0_F2_SHIFT               0U
#define LPDDR4__DENALI_CTL_134__TDFI_PHYMSTR_MAX_TYPE0_F2_WIDTH              32U
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE0_F2__REG DENALI_CTL_134
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE0_F2__FLD LPDDR4__DENALI_CTL_134__TDFI_PHYMSTR_MAX_TYPE0_F2

#define LPDDR4__DENALI_CTL_135_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_135_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_135__TDFI_PHYMSTR_MAX_TYPE1_F2_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_135__TDFI_PHYMSTR_MAX_TYPE1_F2_SHIFT               0U
#define LPDDR4__DENALI_CTL_135__TDFI_PHYMSTR_MAX_TYPE1_F2_WIDTH              32U
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE1_F2__REG DENALI_CTL_135
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE1_F2__FLD LPDDR4__DENALI_CTL_135__TDFI_PHYMSTR_MAX_TYPE1_F2

#define LPDDR4__DENALI_CTL_136_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_136_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_136__TDFI_PHYMSTR_MAX_TYPE2_F2_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_136__TDFI_PHYMSTR_MAX_TYPE2_F2_SHIFT               0U
#define LPDDR4__DENALI_CTL_136__TDFI_PHYMSTR_MAX_TYPE2_F2_WIDTH              32U
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE2_F2__REG DENALI_CTL_136
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE2_F2__FLD LPDDR4__DENALI_CTL_136__TDFI_PHYMSTR_MAX_TYPE2_F2

#define LPDDR4__DENALI_CTL_137_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_137_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_137__TDFI_PHYMSTR_MAX_TYPE3_F2_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_137__TDFI_PHYMSTR_MAX_TYPE3_F2_SHIFT               0U
#define LPDDR4__DENALI_CTL_137__TDFI_PHYMSTR_MAX_TYPE3_F2_WIDTH              32U
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE3_F2__REG DENALI_CTL_137
#define LPDDR4__TDFI_PHYMSTR_MAX_TYPE3_F2__FLD LPDDR4__DENALI_CTL_137__TDFI_PHYMSTR_MAX_TYPE3_F2

#define LPDDR4__DENALI_CTL_138_READ_MASK                             0x0000FFFFU
#define LPDDR4__DENALI_CTL_138_WRITE_MASK                            0x0000FFFFU
#define LPDDR4__DENALI_CTL_138__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F2_MASK 0x0000FFFFU
#define LPDDR4__DENALI_CTL_138__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F2_SHIFT       0U
#define LPDDR4__DENALI_CTL_138__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F2_WIDTH      16U
#define LPDDR4__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F2__REG DENALI_CTL_138
#define LPDDR4__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_138__PHYMSTR_DFI4_PROMOTE_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_139_READ_MASK                             0x010FFFFFU
#define LPDDR4__DENALI_CTL_139_WRITE_MASK                            0x010FFFFFU
#define LPDDR4__DENALI_CTL_139__TDFI_PHYMSTR_RESP_F2_MASK            0x000FFFFFU
#define LPDDR4__DENALI_CTL_139__TDFI_PHYMSTR_RESP_F2_SHIFT                    0U
#define LPDDR4__DENALI_CTL_139__TDFI_PHYMSTR_RESP_F2_WIDTH                   20U
#define LPDDR4__TDFI_PHYMSTR_RESP_F2__REG DENALI_CTL_139
#define LPDDR4__TDFI_PHYMSTR_RESP_F2__FLD LPDDR4__DENALI_CTL_139__TDFI_PHYMSTR_RESP_F2

#define LPDDR4__DENALI_CTL_139__PHYMSTR_NO_AREF_MASK                 0x01000000U
#define LPDDR4__DENALI_CTL_139__PHYMSTR_NO_AREF_SHIFT                        24U
#define LPDDR4__DENALI_CTL_139__PHYMSTR_NO_AREF_WIDTH                         1U
#define LPDDR4__DENALI_CTL_139__PHYMSTR_NO_AREF_WOCLR                         0U
#define LPDDR4__DENALI_CTL_139__PHYMSTR_NO_AREF_WOSET                         0U
#define LPDDR4__PHYMSTR_NO_AREF__REG DENALI_CTL_139
#define LPDDR4__PHYMSTR_NO_AREF__FLD LPDDR4__DENALI_CTL_139__PHYMSTR_NO_AREF

#define LPDDR4__DENALI_CTL_140_READ_MASK                             0x00010103U
#define LPDDR4__DENALI_CTL_140_WRITE_MASK                            0x00010103U
#define LPDDR4__DENALI_CTL_140__PHYMSTR_ERROR_STATUS_MASK            0x00000003U
#define LPDDR4__DENALI_CTL_140__PHYMSTR_ERROR_STATUS_SHIFT                    0U
#define LPDDR4__DENALI_CTL_140__PHYMSTR_ERROR_STATUS_WIDTH                    2U
#define LPDDR4__PHYMSTR_ERROR_STATUS__REG DENALI_CTL_140
#define LPDDR4__PHYMSTR_ERROR_STATUS__FLD LPDDR4__DENALI_CTL_140__PHYMSTR_ERROR_STATUS

#define LPDDR4__DENALI_CTL_140__PHYMSTR_DFI_VERSION_4P0V1_MASK       0x00000100U
#define LPDDR4__DENALI_CTL_140__PHYMSTR_DFI_VERSION_4P0V1_SHIFT               8U
#define LPDDR4__DENALI_CTL_140__PHYMSTR_DFI_VERSION_4P0V1_WIDTH               1U
#define LPDDR4__DENALI_CTL_140__PHYMSTR_DFI_VERSION_4P0V1_WOCLR               0U
#define LPDDR4__DENALI_CTL_140__PHYMSTR_DFI_VERSION_4P0V1_WOSET               0U
#define LPDDR4__PHYMSTR_DFI_VERSION_4P0V1__REG DENALI_CTL_140
#define LPDDR4__PHYMSTR_DFI_VERSION_4P0V1__FLD LPDDR4__DENALI_CTL_140__PHYMSTR_DFI_VERSION_4P0V1

#define LPDDR4__DENALI_CTL_140__PHYMSTR_TRAIN_AFTER_INIT_COMPLETE_MASK 0x00010000U
#define LPDDR4__DENALI_CTL_140__PHYMSTR_TRAIN_AFTER_INIT_COMPLETE_SHIFT      16U
#define LPDDR4__DENALI_CTL_140__PHYMSTR_TRAIN_AFTER_INIT_COMPLETE_WIDTH       1U
#define LPDDR4__DENALI_CTL_140__PHYMSTR_TRAIN_AFTER_INIT_COMPLETE_WOCLR       0U
#define LPDDR4__DENALI_CTL_140__PHYMSTR_TRAIN_AFTER_INIT_COMPLETE_WOSET       0U
#define LPDDR4__PHYMSTR_TRAIN_AFTER_INIT_COMPLETE__REG DENALI_CTL_140
#define LPDDR4__PHYMSTR_TRAIN_AFTER_INIT_COMPLETE__FLD LPDDR4__DENALI_CTL_140__PHYMSTR_TRAIN_AFTER_INIT_COMPLETE

#define LPDDR4__DENALI_CTL_141_READ_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_141_WRITE_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_CTL_141__MRR_TEMPCHK_NORM_THRESHOLD_F0_MASK   0x00FFFFFFU
#define LPDDR4__DENALI_CTL_141__MRR_TEMPCHK_NORM_THRESHOLD_F0_SHIFT           0U
#define LPDDR4__DENALI_CTL_141__MRR_TEMPCHK_NORM_THRESHOLD_F0_WIDTH          24U
#define LPDDR4__MRR_TEMPCHK_NORM_THRESHOLD_F0__REG DENALI_CTL_141
#define LPDDR4__MRR_TEMPCHK_NORM_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_141__MRR_TEMPCHK_NORM_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_142_READ_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_142_WRITE_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_CTL_142__MRR_TEMPCHK_HIGH_THRESHOLD_F0_MASK   0x00FFFFFFU
#define LPDDR4__DENALI_CTL_142__MRR_TEMPCHK_HIGH_THRESHOLD_F0_SHIFT           0U
#define LPDDR4__DENALI_CTL_142__MRR_TEMPCHK_HIGH_THRESHOLD_F0_WIDTH          24U
#define LPDDR4__MRR_TEMPCHK_HIGH_THRESHOLD_F0__REG DENALI_CTL_142
#define LPDDR4__MRR_TEMPCHK_HIGH_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_142__MRR_TEMPCHK_HIGH_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_143_READ_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_143_WRITE_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_CTL_143__MRR_TEMPCHK_TIMEOUT_F0_MASK          0x00FFFFFFU
#define LPDDR4__DENALI_CTL_143__MRR_TEMPCHK_TIMEOUT_F0_SHIFT                  0U
#define LPDDR4__DENALI_CTL_143__MRR_TEMPCHK_TIMEOUT_F0_WIDTH                 24U
#define LPDDR4__MRR_TEMPCHK_TIMEOUT_F0__REG DENALI_CTL_143
#define LPDDR4__MRR_TEMPCHK_TIMEOUT_F0__FLD LPDDR4__DENALI_CTL_143__MRR_TEMPCHK_TIMEOUT_F0

#define LPDDR4__DENALI_CTL_144_READ_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_144_WRITE_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_CTL_144__MRR_TEMPCHK_NORM_THRESHOLD_F1_MASK   0x00FFFFFFU
#define LPDDR4__DENALI_CTL_144__MRR_TEMPCHK_NORM_THRESHOLD_F1_SHIFT           0U
#define LPDDR4__DENALI_CTL_144__MRR_TEMPCHK_NORM_THRESHOLD_F1_WIDTH          24U
#define LPDDR4__MRR_TEMPCHK_NORM_THRESHOLD_F1__REG DENALI_CTL_144
#define LPDDR4__MRR_TEMPCHK_NORM_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_144__MRR_TEMPCHK_NORM_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_145_READ_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_145_WRITE_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_CTL_145__MRR_TEMPCHK_HIGH_THRESHOLD_F1_MASK   0x00FFFFFFU
#define LPDDR4__DENALI_CTL_145__MRR_TEMPCHK_HIGH_THRESHOLD_F1_SHIFT           0U
#define LPDDR4__DENALI_CTL_145__MRR_TEMPCHK_HIGH_THRESHOLD_F1_WIDTH          24U
#define LPDDR4__MRR_TEMPCHK_HIGH_THRESHOLD_F1__REG DENALI_CTL_145
#define LPDDR4__MRR_TEMPCHK_HIGH_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_145__MRR_TEMPCHK_HIGH_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_146_READ_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_146_WRITE_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_CTL_146__MRR_TEMPCHK_TIMEOUT_F1_MASK          0x00FFFFFFU
#define LPDDR4__DENALI_CTL_146__MRR_TEMPCHK_TIMEOUT_F1_SHIFT                  0U
#define LPDDR4__DENALI_CTL_146__MRR_TEMPCHK_TIMEOUT_F1_WIDTH                 24U
#define LPDDR4__MRR_TEMPCHK_TIMEOUT_F1__REG DENALI_CTL_146
#define LPDDR4__MRR_TEMPCHK_TIMEOUT_F1__FLD LPDDR4__DENALI_CTL_146__MRR_TEMPCHK_TIMEOUT_F1

#define LPDDR4__DENALI_CTL_147_READ_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_147_WRITE_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_CTL_147__MRR_TEMPCHK_NORM_THRESHOLD_F2_MASK   0x00FFFFFFU
#define LPDDR4__DENALI_CTL_147__MRR_TEMPCHK_NORM_THRESHOLD_F2_SHIFT           0U
#define LPDDR4__DENALI_CTL_147__MRR_TEMPCHK_NORM_THRESHOLD_F2_WIDTH          24U
#define LPDDR4__MRR_TEMPCHK_NORM_THRESHOLD_F2__REG DENALI_CTL_147
#define LPDDR4__MRR_TEMPCHK_NORM_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_147__MRR_TEMPCHK_NORM_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_148_READ_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_148_WRITE_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_CTL_148__MRR_TEMPCHK_HIGH_THRESHOLD_F2_MASK   0x00FFFFFFU
#define LPDDR4__DENALI_CTL_148__MRR_TEMPCHK_HIGH_THRESHOLD_F2_SHIFT           0U
#define LPDDR4__DENALI_CTL_148__MRR_TEMPCHK_HIGH_THRESHOLD_F2_WIDTH          24U
#define LPDDR4__MRR_TEMPCHK_HIGH_THRESHOLD_F2__REG DENALI_CTL_148
#define LPDDR4__MRR_TEMPCHK_HIGH_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_148__MRR_TEMPCHK_HIGH_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_149_READ_MASK                             0x01FFFFFFU
#define LPDDR4__DENALI_CTL_149_WRITE_MASK                            0x01FFFFFFU
#define LPDDR4__DENALI_CTL_149__MRR_TEMPCHK_TIMEOUT_F2_MASK          0x00FFFFFFU
#define LPDDR4__DENALI_CTL_149__MRR_TEMPCHK_TIMEOUT_F2_SHIFT                  0U
#define LPDDR4__DENALI_CTL_149__MRR_TEMPCHK_TIMEOUT_F2_WIDTH                 24U
#define LPDDR4__MRR_TEMPCHK_TIMEOUT_F2__REG DENALI_CTL_149
#define LPDDR4__MRR_TEMPCHK_TIMEOUT_F2__FLD LPDDR4__DENALI_CTL_149__MRR_TEMPCHK_TIMEOUT_F2

#define LPDDR4__DENALI_CTL_149__PPR_CONTROL_MASK                     0x01000000U
#define LPDDR4__DENALI_CTL_149__PPR_CONTROL_SHIFT                            24U
#define LPDDR4__DENALI_CTL_149__PPR_CONTROL_WIDTH                             1U
#define LPDDR4__DENALI_CTL_149__PPR_CONTROL_WOCLR                             0U
#define LPDDR4__DENALI_CTL_149__PPR_CONTROL_WOSET                             0U
#define LPDDR4__PPR_CONTROL__REG DENALI_CTL_149
#define LPDDR4__PPR_CONTROL__FLD LPDDR4__DENALI_CTL_149__PPR_CONTROL

#define LPDDR4__DENALI_CTL_150_READ_MASK                             0x0000FF00U
#define LPDDR4__DENALI_CTL_150_WRITE_MASK                            0x0000FF00U
#define LPDDR4__DENALI_CTL_150__PPR_COMMAND_MASK                     0x00000007U
#define LPDDR4__DENALI_CTL_150__PPR_COMMAND_SHIFT                             0U
#define LPDDR4__DENALI_CTL_150__PPR_COMMAND_WIDTH                             3U
#define LPDDR4__PPR_COMMAND__REG DENALI_CTL_150
#define LPDDR4__PPR_COMMAND__FLD LPDDR4__DENALI_CTL_150__PPR_COMMAND

#define LPDDR4__DENALI_CTL_150__PPR_COMMAND_MRW_REGNUM_MASK          0x0000FF00U
#define LPDDR4__DENALI_CTL_150__PPR_COMMAND_MRW_REGNUM_SHIFT                  8U
#define LPDDR4__DENALI_CTL_150__PPR_COMMAND_MRW_REGNUM_WIDTH                  8U
#define LPDDR4__PPR_COMMAND_MRW_REGNUM__REG DENALI_CTL_150
#define LPDDR4__PPR_COMMAND_MRW_REGNUM__FLD LPDDR4__DENALI_CTL_150__PPR_COMMAND_MRW_REGNUM

#define LPDDR4__DENALI_CTL_151_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_151_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_151__PPR_COMMAND_MRW_DATA_MASK            0x0001FFFFU
#define LPDDR4__DENALI_CTL_151__PPR_COMMAND_MRW_DATA_SHIFT                    0U
#define LPDDR4__DENALI_CTL_151__PPR_COMMAND_MRW_DATA_WIDTH                   17U
#define LPDDR4__PPR_COMMAND_MRW_DATA__REG DENALI_CTL_151
#define LPDDR4__PPR_COMMAND_MRW_DATA__FLD LPDDR4__DENALI_CTL_151__PPR_COMMAND_MRW_DATA

#define LPDDR4__DENALI_CTL_152_READ_MASK                             0x0F03FFFFU
#define LPDDR4__DENALI_CTL_152_WRITE_MASK                            0x0F03FFFFU
#define LPDDR4__DENALI_CTL_152__PPR_ROW_ADDRESS_MASK                 0x0003FFFFU
#define LPDDR4__DENALI_CTL_152__PPR_ROW_ADDRESS_SHIFT                         0U
#define LPDDR4__DENALI_CTL_152__PPR_ROW_ADDRESS_WIDTH                        18U
#define LPDDR4__PPR_ROW_ADDRESS__REG DENALI_CTL_152
#define LPDDR4__PPR_ROW_ADDRESS__FLD LPDDR4__DENALI_CTL_152__PPR_ROW_ADDRESS

#define LPDDR4__DENALI_CTL_152__PPR_BANK_ADDRESS_MASK                0x0F000000U
#define LPDDR4__DENALI_CTL_152__PPR_BANK_ADDRESS_SHIFT                       24U
#define LPDDR4__DENALI_CTL_152__PPR_BANK_ADDRESS_WIDTH                        4U
#define LPDDR4__PPR_BANK_ADDRESS__REG DENALI_CTL_152
#define LPDDR4__PPR_BANK_ADDRESS__FLD LPDDR4__DENALI_CTL_152__PPR_BANK_ADDRESS

#define LPDDR4__DENALI_CTL_153_READ_MASK                             0x00000001U
#define LPDDR4__DENALI_CTL_153_WRITE_MASK                            0x00000001U
#define LPDDR4__DENALI_CTL_153__PPR_CS_ADDRESS_MASK                  0x00000001U
#define LPDDR4__DENALI_CTL_153__PPR_CS_ADDRESS_SHIFT                          0U
#define LPDDR4__DENALI_CTL_153__PPR_CS_ADDRESS_WIDTH                          1U
#define LPDDR4__DENALI_CTL_153__PPR_CS_ADDRESS_WOCLR                          0U
#define LPDDR4__DENALI_CTL_153__PPR_CS_ADDRESS_WOSET                          0U
#define LPDDR4__PPR_CS_ADDRESS__REG DENALI_CTL_153
#define LPDDR4__PPR_CS_ADDRESS__FLD LPDDR4__DENALI_CTL_153__PPR_CS_ADDRESS

#define LPDDR4__DENALI_CTL_154_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_154_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_154__PPR_DATA_0_MASK                      0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_154__PPR_DATA_0_SHIFT                              0U
#define LPDDR4__DENALI_CTL_154__PPR_DATA_0_WIDTH                             32U
#define LPDDR4__PPR_DATA_0__REG DENALI_CTL_154
#define LPDDR4__PPR_DATA_0__FLD LPDDR4__DENALI_CTL_154__PPR_DATA_0

#define LPDDR4__DENALI_CTL_155_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_155_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_155__PPR_DATA_1_MASK                      0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_155__PPR_DATA_1_SHIFT                              0U
#define LPDDR4__DENALI_CTL_155__PPR_DATA_1_WIDTH                             32U
#define LPDDR4__PPR_DATA_1__REG DENALI_CTL_155
#define LPDDR4__PPR_DATA_1__FLD LPDDR4__DENALI_CTL_155__PPR_DATA_1

#define LPDDR4__DENALI_CTL_156_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_156_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_156__PPR_DATA_2_MASK                      0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_156__PPR_DATA_2_SHIFT                              0U
#define LPDDR4__DENALI_CTL_156__PPR_DATA_2_WIDTH                             32U
#define LPDDR4__PPR_DATA_2__REG DENALI_CTL_156
#define LPDDR4__PPR_DATA_2__FLD LPDDR4__DENALI_CTL_156__PPR_DATA_2

#define LPDDR4__DENALI_CTL_157_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_157_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_157__PPR_DATA_3_MASK                      0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_157__PPR_DATA_3_SHIFT                              0U
#define LPDDR4__DENALI_CTL_157__PPR_DATA_3_WIDTH                             32U
#define LPDDR4__PPR_DATA_3__REG DENALI_CTL_157
#define LPDDR4__PPR_DATA_3__FLD LPDDR4__DENALI_CTL_157__PPR_DATA_3

#define LPDDR4__DENALI_CTL_158_READ_MASK                             0xFFFF0103U
#define LPDDR4__DENALI_CTL_158_WRITE_MASK                            0xFFFF0103U
#define LPDDR4__DENALI_CTL_158__PPR_STATUS_MASK                      0x00000003U
#define LPDDR4__DENALI_CTL_158__PPR_STATUS_SHIFT                              0U
#define LPDDR4__DENALI_CTL_158__PPR_STATUS_WIDTH                              2U
#define LPDDR4__PPR_STATUS__REG DENALI_CTL_158
#define LPDDR4__PPR_STATUS__FLD LPDDR4__DENALI_CTL_158__PPR_STATUS

#define LPDDR4__DENALI_CTL_158__FM_OVRIDE_CONTROL_MASK               0x00000100U
#define LPDDR4__DENALI_CTL_158__FM_OVRIDE_CONTROL_SHIFT                       8U
#define LPDDR4__DENALI_CTL_158__FM_OVRIDE_CONTROL_WIDTH                       1U
#define LPDDR4__DENALI_CTL_158__FM_OVRIDE_CONTROL_WOCLR                       0U
#define LPDDR4__DENALI_CTL_158__FM_OVRIDE_CONTROL_WOSET                       0U
#define LPDDR4__FM_OVRIDE_CONTROL__REG DENALI_CTL_158
#define LPDDR4__FM_OVRIDE_CONTROL__FLD LPDDR4__DENALI_CTL_158__FM_OVRIDE_CONTROL

#define LPDDR4__DENALI_CTL_158__CKSRE_F0_MASK                        0x00FF0000U
#define LPDDR4__DENALI_CTL_158__CKSRE_F0_SHIFT                               16U
#define LPDDR4__DENALI_CTL_158__CKSRE_F0_WIDTH                                8U
#define LPDDR4__CKSRE_F0__REG DENALI_CTL_158
#define LPDDR4__CKSRE_F0__FLD LPDDR4__DENALI_CTL_158__CKSRE_F0

#define LPDDR4__DENALI_CTL_158__CKSRX_F0_MASK                        0xFF000000U
#define LPDDR4__DENALI_CTL_158__CKSRX_F0_SHIFT                               24U
#define LPDDR4__DENALI_CTL_158__CKSRX_F0_WIDTH                                8U
#define LPDDR4__CKSRX_F0__REG DENALI_CTL_158
#define LPDDR4__CKSRX_F0__FLD LPDDR4__DENALI_CTL_158__CKSRX_F0

#define LPDDR4__DENALI_CTL_159_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_159_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_159__CKSRE_F1_MASK                        0x000000FFU
#define LPDDR4__DENALI_CTL_159__CKSRE_F1_SHIFT                                0U
#define LPDDR4__DENALI_CTL_159__CKSRE_F1_WIDTH                                8U
#define LPDDR4__CKSRE_F1__REG DENALI_CTL_159
#define LPDDR4__CKSRE_F1__FLD LPDDR4__DENALI_CTL_159__CKSRE_F1

#define LPDDR4__DENALI_CTL_159__CKSRX_F1_MASK                        0x0000FF00U
#define LPDDR4__DENALI_CTL_159__CKSRX_F1_SHIFT                                8U
#define LPDDR4__DENALI_CTL_159__CKSRX_F1_WIDTH                                8U
#define LPDDR4__CKSRX_F1__REG DENALI_CTL_159
#define LPDDR4__CKSRX_F1__FLD LPDDR4__DENALI_CTL_159__CKSRX_F1

#define LPDDR4__DENALI_CTL_159__CKSRE_F2_MASK                        0x00FF0000U
#define LPDDR4__DENALI_CTL_159__CKSRE_F2_SHIFT                               16U
#define LPDDR4__DENALI_CTL_159__CKSRE_F2_WIDTH                                8U
#define LPDDR4__CKSRE_F2__REG DENALI_CTL_159
#define LPDDR4__CKSRE_F2__FLD LPDDR4__DENALI_CTL_159__CKSRE_F2

#define LPDDR4__DENALI_CTL_159__CKSRX_F2_MASK                        0xFF000000U
#define LPDDR4__DENALI_CTL_159__CKSRX_F2_SHIFT                               24U
#define LPDDR4__DENALI_CTL_159__CKSRX_F2_WIDTH                                8U
#define LPDDR4__CKSRX_F2__REG DENALI_CTL_159
#define LPDDR4__CKSRX_F2__FLD LPDDR4__DENALI_CTL_159__CKSRX_F2

#define LPDDR4__DENALI_CTL_160_READ_MASK                             0x0F0F0003U
#define LPDDR4__DENALI_CTL_160_WRITE_MASK                            0x0F0F0003U
#define LPDDR4__DENALI_CTL_160__LOWPOWER_REFRESH_ENABLE_MASK         0x00000003U
#define LPDDR4__DENALI_CTL_160__LOWPOWER_REFRESH_ENABLE_SHIFT                 0U
#define LPDDR4__DENALI_CTL_160__LOWPOWER_REFRESH_ENABLE_WIDTH                 2U
#define LPDDR4__LOWPOWER_REFRESH_ENABLE__REG DENALI_CTL_160
#define LPDDR4__LOWPOWER_REFRESH_ENABLE__FLD LPDDR4__DENALI_CTL_160__LOWPOWER_REFRESH_ENABLE

#define LPDDR4__DENALI_CTL_160__LP_CMD_MASK                          0x00007F00U
#define LPDDR4__DENALI_CTL_160__LP_CMD_SHIFT                                  8U
#define LPDDR4__DENALI_CTL_160__LP_CMD_WIDTH                                  7U
#define LPDDR4__LP_CMD__REG DENALI_CTL_160
#define LPDDR4__LP_CMD__FLD LPDDR4__DENALI_CTL_160__LP_CMD

#define LPDDR4__DENALI_CTL_160__LPI_IDLE_WAKEUP_F0_MASK              0x000F0000U
#define LPDDR4__DENALI_CTL_160__LPI_IDLE_WAKEUP_F0_SHIFT                     16U
#define LPDDR4__DENALI_CTL_160__LPI_IDLE_WAKEUP_F0_WIDTH                      4U
#define LPDDR4__LPI_IDLE_WAKEUP_F0__REG DENALI_CTL_160
#define LPDDR4__LPI_IDLE_WAKEUP_F0__FLD LPDDR4__DENALI_CTL_160__LPI_IDLE_WAKEUP_F0

#define LPDDR4__DENALI_CTL_160__LPI_SR_SHORT_WAKEUP_F0_MASK          0x0F000000U
#define LPDDR4__DENALI_CTL_160__LPI_SR_SHORT_WAKEUP_F0_SHIFT                 24U
#define LPDDR4__DENALI_CTL_160__LPI_SR_SHORT_WAKEUP_F0_WIDTH                  4U
#define LPDDR4__LPI_SR_SHORT_WAKEUP_F0__REG DENALI_CTL_160
#define LPDDR4__LPI_SR_SHORT_WAKEUP_F0__FLD LPDDR4__DENALI_CTL_160__LPI_SR_SHORT_WAKEUP_F0

#define LPDDR4__DENALI_CTL_161_READ_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_161_WRITE_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_161__LPI_SR_LONG_WAKEUP_F0_MASK           0x0000000FU
#define LPDDR4__DENALI_CTL_161__LPI_SR_LONG_WAKEUP_F0_SHIFT                   0U
#define LPDDR4__DENALI_CTL_161__LPI_SR_LONG_WAKEUP_F0_WIDTH                   4U
#define LPDDR4__LPI_SR_LONG_WAKEUP_F0__REG DENALI_CTL_161
#define LPDDR4__LPI_SR_LONG_WAKEUP_F0__FLD LPDDR4__DENALI_CTL_161__LPI_SR_LONG_WAKEUP_F0

#define LPDDR4__DENALI_CTL_161__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F0_MASK 0x00000F00U
#define LPDDR4__DENALI_CTL_161__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F0_SHIFT        8U
#define LPDDR4__DENALI_CTL_161__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F0_WIDTH        4U
#define LPDDR4__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F0__REG DENALI_CTL_161
#define LPDDR4__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F0__FLD LPDDR4__DENALI_CTL_161__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F0

#define LPDDR4__DENALI_CTL_161__LPI_PD_WAKEUP_F0_MASK                0x000F0000U
#define LPDDR4__DENALI_CTL_161__LPI_PD_WAKEUP_F0_SHIFT                       16U
#define LPDDR4__DENALI_CTL_161__LPI_PD_WAKEUP_F0_WIDTH                        4U
#define LPDDR4__LPI_PD_WAKEUP_F0__REG DENALI_CTL_161
#define LPDDR4__LPI_PD_WAKEUP_F0__FLD LPDDR4__DENALI_CTL_161__LPI_PD_WAKEUP_F0

#define LPDDR4__DENALI_CTL_161__LPI_SRPD_SHORT_WAKEUP_F0_MASK        0x0F000000U
#define LPDDR4__DENALI_CTL_161__LPI_SRPD_SHORT_WAKEUP_F0_SHIFT               24U
#define LPDDR4__DENALI_CTL_161__LPI_SRPD_SHORT_WAKEUP_F0_WIDTH                4U
#define LPDDR4__LPI_SRPD_SHORT_WAKEUP_F0__REG DENALI_CTL_161
#define LPDDR4__LPI_SRPD_SHORT_WAKEUP_F0__FLD LPDDR4__DENALI_CTL_161__LPI_SRPD_SHORT_WAKEUP_F0

#define LPDDR4__DENALI_CTL_162_READ_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_162_WRITE_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_162__LPI_SRPD_LONG_WAKEUP_F0_MASK         0x0000000FU
#define LPDDR4__DENALI_CTL_162__LPI_SRPD_LONG_WAKEUP_F0_SHIFT                 0U
#define LPDDR4__DENALI_CTL_162__LPI_SRPD_LONG_WAKEUP_F0_WIDTH                 4U
#define LPDDR4__LPI_SRPD_LONG_WAKEUP_F0__REG DENALI_CTL_162
#define LPDDR4__LPI_SRPD_LONG_WAKEUP_F0__FLD LPDDR4__DENALI_CTL_162__LPI_SRPD_LONG_WAKEUP_F0

#define LPDDR4__DENALI_CTL_162__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F0_MASK 0x00000F00U
#define LPDDR4__DENALI_CTL_162__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F0_SHIFT      8U
#define LPDDR4__DENALI_CTL_162__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F0_WIDTH      4U
#define LPDDR4__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F0__REG DENALI_CTL_162
#define LPDDR4__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F0__FLD LPDDR4__DENALI_CTL_162__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F0

#define LPDDR4__DENALI_CTL_162__LPI_TIMER_WAKEUP_F0_MASK             0x000F0000U
#define LPDDR4__DENALI_CTL_162__LPI_TIMER_WAKEUP_F0_SHIFT                    16U
#define LPDDR4__DENALI_CTL_162__LPI_TIMER_WAKEUP_F0_WIDTH                     4U
#define LPDDR4__LPI_TIMER_WAKEUP_F0__REG DENALI_CTL_162
#define LPDDR4__LPI_TIMER_WAKEUP_F0__FLD LPDDR4__DENALI_CTL_162__LPI_TIMER_WAKEUP_F0

#define LPDDR4__DENALI_CTL_162__LPI_IDLE_WAKEUP_F1_MASK              0x0F000000U
#define LPDDR4__DENALI_CTL_162__LPI_IDLE_WAKEUP_F1_SHIFT                     24U
#define LPDDR4__DENALI_CTL_162__LPI_IDLE_WAKEUP_F1_WIDTH                      4U
#define LPDDR4__LPI_IDLE_WAKEUP_F1__REG DENALI_CTL_162
#define LPDDR4__LPI_IDLE_WAKEUP_F1__FLD LPDDR4__DENALI_CTL_162__LPI_IDLE_WAKEUP_F1

#define LPDDR4__DENALI_CTL_163_READ_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_163_WRITE_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_163__LPI_SR_SHORT_WAKEUP_F1_MASK          0x0000000FU
#define LPDDR4__DENALI_CTL_163__LPI_SR_SHORT_WAKEUP_F1_SHIFT                  0U
#define LPDDR4__DENALI_CTL_163__LPI_SR_SHORT_WAKEUP_F1_WIDTH                  4U
#define LPDDR4__LPI_SR_SHORT_WAKEUP_F1__REG DENALI_CTL_163
#define LPDDR4__LPI_SR_SHORT_WAKEUP_F1__FLD LPDDR4__DENALI_CTL_163__LPI_SR_SHORT_WAKEUP_F1

#define LPDDR4__DENALI_CTL_163__LPI_SR_LONG_WAKEUP_F1_MASK           0x00000F00U
#define LPDDR4__DENALI_CTL_163__LPI_SR_LONG_WAKEUP_F1_SHIFT                   8U
#define LPDDR4__DENALI_CTL_163__LPI_SR_LONG_WAKEUP_F1_WIDTH                   4U
#define LPDDR4__LPI_SR_LONG_WAKEUP_F1__REG DENALI_CTL_163
#define LPDDR4__LPI_SR_LONG_WAKEUP_F1__FLD LPDDR4__DENALI_CTL_163__LPI_SR_LONG_WAKEUP_F1

#define LPDDR4__DENALI_CTL_163__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F1_MASK 0x000F0000U
#define LPDDR4__DENALI_CTL_163__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F1_SHIFT       16U
#define LPDDR4__DENALI_CTL_163__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F1_WIDTH        4U
#define LPDDR4__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F1__REG DENALI_CTL_163
#define LPDDR4__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F1__FLD LPDDR4__DENALI_CTL_163__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F1

#define LPDDR4__DENALI_CTL_163__LPI_PD_WAKEUP_F1_MASK                0x0F000000U
#define LPDDR4__DENALI_CTL_163__LPI_PD_WAKEUP_F1_SHIFT                       24U
#define LPDDR4__DENALI_CTL_163__LPI_PD_WAKEUP_F1_WIDTH                        4U
#define LPDDR4__LPI_PD_WAKEUP_F1__REG DENALI_CTL_163
#define LPDDR4__LPI_PD_WAKEUP_F1__FLD LPDDR4__DENALI_CTL_163__LPI_PD_WAKEUP_F1

#define LPDDR4__DENALI_CTL_164_READ_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_164_WRITE_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_164__LPI_SRPD_SHORT_WAKEUP_F1_MASK        0x0000000FU
#define LPDDR4__DENALI_CTL_164__LPI_SRPD_SHORT_WAKEUP_F1_SHIFT                0U
#define LPDDR4__DENALI_CTL_164__LPI_SRPD_SHORT_WAKEUP_F1_WIDTH                4U
#define LPDDR4__LPI_SRPD_SHORT_WAKEUP_F1__REG DENALI_CTL_164
#define LPDDR4__LPI_SRPD_SHORT_WAKEUP_F1__FLD LPDDR4__DENALI_CTL_164__LPI_SRPD_SHORT_WAKEUP_F1

#define LPDDR4__DENALI_CTL_164__LPI_SRPD_LONG_WAKEUP_F1_MASK         0x00000F00U
#define LPDDR4__DENALI_CTL_164__LPI_SRPD_LONG_WAKEUP_F1_SHIFT                 8U
#define LPDDR4__DENALI_CTL_164__LPI_SRPD_LONG_WAKEUP_F1_WIDTH                 4U
#define LPDDR4__LPI_SRPD_LONG_WAKEUP_F1__REG DENALI_CTL_164
#define LPDDR4__LPI_SRPD_LONG_WAKEUP_F1__FLD LPDDR4__DENALI_CTL_164__LPI_SRPD_LONG_WAKEUP_F1

#define LPDDR4__DENALI_CTL_164__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F1_MASK 0x000F0000U
#define LPDDR4__DENALI_CTL_164__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F1_SHIFT     16U
#define LPDDR4__DENALI_CTL_164__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F1_WIDTH      4U
#define LPDDR4__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F1__REG DENALI_CTL_164
#define LPDDR4__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F1__FLD LPDDR4__DENALI_CTL_164__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F1

#define LPDDR4__DENALI_CTL_164__LPI_TIMER_WAKEUP_F1_MASK             0x0F000000U
#define LPDDR4__DENALI_CTL_164__LPI_TIMER_WAKEUP_F1_SHIFT                    24U
#define LPDDR4__DENALI_CTL_164__LPI_TIMER_WAKEUP_F1_WIDTH                     4U
#define LPDDR4__LPI_TIMER_WAKEUP_F1__REG DENALI_CTL_164
#define LPDDR4__LPI_TIMER_WAKEUP_F1__FLD LPDDR4__DENALI_CTL_164__LPI_TIMER_WAKEUP_F1

#define LPDDR4__DENALI_CTL_165_READ_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_165_WRITE_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_165__LPI_IDLE_WAKEUP_F2_MASK              0x0000000FU
#define LPDDR4__DENALI_CTL_165__LPI_IDLE_WAKEUP_F2_SHIFT                      0U
#define LPDDR4__DENALI_CTL_165__LPI_IDLE_WAKEUP_F2_WIDTH                      4U
#define LPDDR4__LPI_IDLE_WAKEUP_F2__REG DENALI_CTL_165
#define LPDDR4__LPI_IDLE_WAKEUP_F2__FLD LPDDR4__DENALI_CTL_165__LPI_IDLE_WAKEUP_F2

#define LPDDR4__DENALI_CTL_165__LPI_SR_SHORT_WAKEUP_F2_MASK          0x00000F00U
#define LPDDR4__DENALI_CTL_165__LPI_SR_SHORT_WAKEUP_F2_SHIFT                  8U
#define LPDDR4__DENALI_CTL_165__LPI_SR_SHORT_WAKEUP_F2_WIDTH                  4U
#define LPDDR4__LPI_SR_SHORT_WAKEUP_F2__REG DENALI_CTL_165
#define LPDDR4__LPI_SR_SHORT_WAKEUP_F2__FLD LPDDR4__DENALI_CTL_165__LPI_SR_SHORT_WAKEUP_F2

#define LPDDR4__DENALI_CTL_165__LPI_SR_LONG_WAKEUP_F2_MASK           0x000F0000U
#define LPDDR4__DENALI_CTL_165__LPI_SR_LONG_WAKEUP_F2_SHIFT                  16U
#define LPDDR4__DENALI_CTL_165__LPI_SR_LONG_WAKEUP_F2_WIDTH                   4U
#define LPDDR4__LPI_SR_LONG_WAKEUP_F2__REG DENALI_CTL_165
#define LPDDR4__LPI_SR_LONG_WAKEUP_F2__FLD LPDDR4__DENALI_CTL_165__LPI_SR_LONG_WAKEUP_F2

#define LPDDR4__DENALI_CTL_165__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F2_MASK 0x0F000000U
#define LPDDR4__DENALI_CTL_165__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F2_SHIFT       24U
#define LPDDR4__DENALI_CTL_165__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F2_WIDTH        4U
#define LPDDR4__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F2__REG DENALI_CTL_165
#define LPDDR4__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F2__FLD LPDDR4__DENALI_CTL_165__LPI_SR_LONG_MCCLK_GATE_WAKEUP_F2

#define LPDDR4__DENALI_CTL_166_READ_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_166_WRITE_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_166__LPI_PD_WAKEUP_F2_MASK                0x0000000FU
#define LPDDR4__DENALI_CTL_166__LPI_PD_WAKEUP_F2_SHIFT                        0U
#define LPDDR4__DENALI_CTL_166__LPI_PD_WAKEUP_F2_WIDTH                        4U
#define LPDDR4__LPI_PD_WAKEUP_F2__REG DENALI_CTL_166
#define LPDDR4__LPI_PD_WAKEUP_F2__FLD LPDDR4__DENALI_CTL_166__LPI_PD_WAKEUP_F2

#define LPDDR4__DENALI_CTL_166__LPI_SRPD_SHORT_WAKEUP_F2_MASK        0x00000F00U
#define LPDDR4__DENALI_CTL_166__LPI_SRPD_SHORT_WAKEUP_F2_SHIFT                8U
#define LPDDR4__DENALI_CTL_166__LPI_SRPD_SHORT_WAKEUP_F2_WIDTH                4U
#define LPDDR4__LPI_SRPD_SHORT_WAKEUP_F2__REG DENALI_CTL_166
#define LPDDR4__LPI_SRPD_SHORT_WAKEUP_F2__FLD LPDDR4__DENALI_CTL_166__LPI_SRPD_SHORT_WAKEUP_F2

#define LPDDR4__DENALI_CTL_166__LPI_SRPD_LONG_WAKEUP_F2_MASK         0x000F0000U
#define LPDDR4__DENALI_CTL_166__LPI_SRPD_LONG_WAKEUP_F2_SHIFT                16U
#define LPDDR4__DENALI_CTL_166__LPI_SRPD_LONG_WAKEUP_F2_WIDTH                 4U
#define LPDDR4__LPI_SRPD_LONG_WAKEUP_F2__REG DENALI_CTL_166
#define LPDDR4__LPI_SRPD_LONG_WAKEUP_F2__FLD LPDDR4__DENALI_CTL_166__LPI_SRPD_LONG_WAKEUP_F2

#define LPDDR4__DENALI_CTL_166__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F2_MASK 0x0F000000U
#define LPDDR4__DENALI_CTL_166__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F2_SHIFT     24U
#define LPDDR4__DENALI_CTL_166__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F2_WIDTH      4U
#define LPDDR4__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F2__REG DENALI_CTL_166
#define LPDDR4__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F2__FLD LPDDR4__DENALI_CTL_166__LPI_SRPD_LONG_MCCLK_GATE_WAKEUP_F2

#define LPDDR4__DENALI_CTL_167_READ_MASK                             0x00013F0FU
#define LPDDR4__DENALI_CTL_167_WRITE_MASK                            0x00013F0FU
#define LPDDR4__DENALI_CTL_167__LPI_TIMER_WAKEUP_F2_MASK             0x0000000FU
#define LPDDR4__DENALI_CTL_167__LPI_TIMER_WAKEUP_F2_SHIFT                     0U
#define LPDDR4__DENALI_CTL_167__LPI_TIMER_WAKEUP_F2_WIDTH                     4U
#define LPDDR4__LPI_TIMER_WAKEUP_F2__REG DENALI_CTL_167
#define LPDDR4__LPI_TIMER_WAKEUP_F2__FLD LPDDR4__DENALI_CTL_167__LPI_TIMER_WAKEUP_F2

#define LPDDR4__DENALI_CTL_167__LPI_WAKEUP_EN_MASK                   0x00003F00U
#define LPDDR4__DENALI_CTL_167__LPI_WAKEUP_EN_SHIFT                           8U
#define LPDDR4__DENALI_CTL_167__LPI_WAKEUP_EN_WIDTH                           6U
#define LPDDR4__LPI_WAKEUP_EN__REG DENALI_CTL_167
#define LPDDR4__LPI_WAKEUP_EN__FLD LPDDR4__DENALI_CTL_167__LPI_WAKEUP_EN

#define LPDDR4__DENALI_CTL_167__LPI_CTRL_REQ_EN_MASK                 0x00010000U
#define LPDDR4__DENALI_CTL_167__LPI_CTRL_REQ_EN_SHIFT                        16U
#define LPDDR4__DENALI_CTL_167__LPI_CTRL_REQ_EN_WIDTH                         1U
#define LPDDR4__DENALI_CTL_167__LPI_CTRL_REQ_EN_WOCLR                         0U
#define LPDDR4__DENALI_CTL_167__LPI_CTRL_REQ_EN_WOSET                         0U
#define LPDDR4__LPI_CTRL_REQ_EN__REG DENALI_CTL_167
#define LPDDR4__LPI_CTRL_REQ_EN__FLD LPDDR4__DENALI_CTL_167__LPI_CTRL_REQ_EN

#define LPDDR4__DENALI_CTL_168_READ_MASK                             0x0FFF0FFFU
#define LPDDR4__DENALI_CTL_168_WRITE_MASK                            0x0FFF0FFFU
#define LPDDR4__DENALI_CTL_168__LPI_TIMER_COUNT_MASK                 0x00000FFFU
#define LPDDR4__DENALI_CTL_168__LPI_TIMER_COUNT_SHIFT                         0U
#define LPDDR4__DENALI_CTL_168__LPI_TIMER_COUNT_WIDTH                        12U
#define LPDDR4__LPI_TIMER_COUNT__REG DENALI_CTL_168
#define LPDDR4__LPI_TIMER_COUNT__FLD LPDDR4__DENALI_CTL_168__LPI_TIMER_COUNT

#define LPDDR4__DENALI_CTL_168__LPI_WAKEUP_TIMEOUT_MASK              0x0FFF0000U
#define LPDDR4__DENALI_CTL_168__LPI_WAKEUP_TIMEOUT_SHIFT                     16U
#define LPDDR4__DENALI_CTL_168__LPI_WAKEUP_TIMEOUT_WIDTH                     12U
#define LPDDR4__LPI_WAKEUP_TIMEOUT__REG DENALI_CTL_168
#define LPDDR4__LPI_WAKEUP_TIMEOUT__FLD LPDDR4__DENALI_CTL_168__LPI_WAKEUP_TIMEOUT

#define LPDDR4__DENALI_CTL_169_READ_MASK                             0x0F0F7F07U
#define LPDDR4__DENALI_CTL_169_WRITE_MASK                            0x0F0F7F07U
#define LPDDR4__DENALI_CTL_169__TDFI_LP_RESP_MASK                    0x00000007U
#define LPDDR4__DENALI_CTL_169__TDFI_LP_RESP_SHIFT                            0U
#define LPDDR4__DENALI_CTL_169__TDFI_LP_RESP_WIDTH                            3U
#define LPDDR4__TDFI_LP_RESP__REG DENALI_CTL_169
#define LPDDR4__TDFI_LP_RESP__FLD LPDDR4__DENALI_CTL_169__TDFI_LP_RESP

#define LPDDR4__DENALI_CTL_169__LP_STATE_MASK                        0x00007F00U
#define LPDDR4__DENALI_CTL_169__LP_STATE_SHIFT                                8U
#define LPDDR4__DENALI_CTL_169__LP_STATE_WIDTH                                7U
#define LPDDR4__LP_STATE__REG DENALI_CTL_169
#define LPDDR4__LP_STATE__FLD LPDDR4__DENALI_CTL_169__LP_STATE

#define LPDDR4__DENALI_CTL_169__LP_AUTO_ENTRY_EN_MASK                0x000F0000U
#define LPDDR4__DENALI_CTL_169__LP_AUTO_ENTRY_EN_SHIFT                       16U
#define LPDDR4__DENALI_CTL_169__LP_AUTO_ENTRY_EN_WIDTH                        4U
#define LPDDR4__LP_AUTO_ENTRY_EN__REG DENALI_CTL_169
#define LPDDR4__LP_AUTO_ENTRY_EN__FLD LPDDR4__DENALI_CTL_169__LP_AUTO_ENTRY_EN

#define LPDDR4__DENALI_CTL_169__LP_AUTO_EXIT_EN_MASK                 0x0F000000U
#define LPDDR4__DENALI_CTL_169__LP_AUTO_EXIT_EN_SHIFT                        24U
#define LPDDR4__DENALI_CTL_169__LP_AUTO_EXIT_EN_WIDTH                         4U
#define LPDDR4__LP_AUTO_EXIT_EN__REG DENALI_CTL_169
#define LPDDR4__LP_AUTO_EXIT_EN__FLD LPDDR4__DENALI_CTL_169__LP_AUTO_EXIT_EN

#define LPDDR4__DENALI_CTL_170_READ_MASK                             0x000FFF07U
#define LPDDR4__DENALI_CTL_170_WRITE_MASK                            0x000FFF07U
#define LPDDR4__DENALI_CTL_170__LP_AUTO_MEM_GATE_EN_MASK             0x00000007U
#define LPDDR4__DENALI_CTL_170__LP_AUTO_MEM_GATE_EN_SHIFT                     0U
#define LPDDR4__DENALI_CTL_170__LP_AUTO_MEM_GATE_EN_WIDTH                     3U
#define LPDDR4__LP_AUTO_MEM_GATE_EN__REG DENALI_CTL_170
#define LPDDR4__LP_AUTO_MEM_GATE_EN__FLD LPDDR4__DENALI_CTL_170__LP_AUTO_MEM_GATE_EN

#define LPDDR4__DENALI_CTL_170__LP_AUTO_PD_IDLE_MASK                 0x000FFF00U
#define LPDDR4__DENALI_CTL_170__LP_AUTO_PD_IDLE_SHIFT                         8U
#define LPDDR4__DENALI_CTL_170__LP_AUTO_PD_IDLE_WIDTH                        12U
#define LPDDR4__LP_AUTO_PD_IDLE__REG DENALI_CTL_170
#define LPDDR4__LP_AUTO_PD_IDLE__FLD LPDDR4__DENALI_CTL_170__LP_AUTO_PD_IDLE

#define LPDDR4__DENALI_CTL_171_READ_MASK                             0xFFFF0FFFU
#define LPDDR4__DENALI_CTL_171_WRITE_MASK                            0xFFFF0FFFU
#define LPDDR4__DENALI_CTL_171__LP_AUTO_SR_SHORT_IDLE_MASK           0x00000FFFU
#define LPDDR4__DENALI_CTL_171__LP_AUTO_SR_SHORT_IDLE_SHIFT                   0U
#define LPDDR4__DENALI_CTL_171__LP_AUTO_SR_SHORT_IDLE_WIDTH                  12U
#define LPDDR4__LP_AUTO_SR_SHORT_IDLE__REG DENALI_CTL_171
#define LPDDR4__LP_AUTO_SR_SHORT_IDLE__FLD LPDDR4__DENALI_CTL_171__LP_AUTO_SR_SHORT_IDLE

#define LPDDR4__DENALI_CTL_171__LP_AUTO_SR_LONG_IDLE_MASK            0x00FF0000U
#define LPDDR4__DENALI_CTL_171__LP_AUTO_SR_LONG_IDLE_SHIFT                   16U
#define LPDDR4__DENALI_CTL_171__LP_AUTO_SR_LONG_IDLE_WIDTH                    8U
#define LPDDR4__LP_AUTO_SR_LONG_IDLE__REG DENALI_CTL_171
#define LPDDR4__LP_AUTO_SR_LONG_IDLE__FLD LPDDR4__DENALI_CTL_171__LP_AUTO_SR_LONG_IDLE

#define LPDDR4__DENALI_CTL_171__LP_AUTO_SR_LONG_MC_GATE_IDLE_MASK    0xFF000000U
#define LPDDR4__DENALI_CTL_171__LP_AUTO_SR_LONG_MC_GATE_IDLE_SHIFT           24U
#define LPDDR4__DENALI_CTL_171__LP_AUTO_SR_LONG_MC_GATE_IDLE_WIDTH            8U
#define LPDDR4__LP_AUTO_SR_LONG_MC_GATE_IDLE__REG DENALI_CTL_171
#define LPDDR4__LP_AUTO_SR_LONG_MC_GATE_IDLE__FLD LPDDR4__DENALI_CTL_171__LP_AUTO_SR_LONG_MC_GATE_IDLE

#define LPDDR4__DENALI_CTL_172_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_172_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_172__HW_PROMOTE_THRESHOLD_F0_MASK         0x0000FFFFU
#define LPDDR4__DENALI_CTL_172__HW_PROMOTE_THRESHOLD_F0_SHIFT                 0U
#define LPDDR4__DENALI_CTL_172__HW_PROMOTE_THRESHOLD_F0_WIDTH                16U
#define LPDDR4__HW_PROMOTE_THRESHOLD_F0__REG DENALI_CTL_172
#define LPDDR4__HW_PROMOTE_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_172__HW_PROMOTE_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_172__HW_PROMOTE_THRESHOLD_F1_MASK         0xFFFF0000U
#define LPDDR4__DENALI_CTL_172__HW_PROMOTE_THRESHOLD_F1_SHIFT                16U
#define LPDDR4__DENALI_CTL_172__HW_PROMOTE_THRESHOLD_F1_WIDTH                16U
#define LPDDR4__HW_PROMOTE_THRESHOLD_F1__REG DENALI_CTL_172
#define LPDDR4__HW_PROMOTE_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_172__HW_PROMOTE_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_173_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_173_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_173__HW_PROMOTE_THRESHOLD_F2_MASK         0x0000FFFFU
#define LPDDR4__DENALI_CTL_173__HW_PROMOTE_THRESHOLD_F2_SHIFT                 0U
#define LPDDR4__DENALI_CTL_173__HW_PROMOTE_THRESHOLD_F2_WIDTH                16U
#define LPDDR4__HW_PROMOTE_THRESHOLD_F2__REG DENALI_CTL_173
#define LPDDR4__HW_PROMOTE_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_173__HW_PROMOTE_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_173__LPC_PROMOTE_THRESHOLD_F0_MASK        0xFFFF0000U
#define LPDDR4__DENALI_CTL_173__LPC_PROMOTE_THRESHOLD_F0_SHIFT               16U
#define LPDDR4__DENALI_CTL_173__LPC_PROMOTE_THRESHOLD_F0_WIDTH               16U
#define LPDDR4__LPC_PROMOTE_THRESHOLD_F0__REG DENALI_CTL_173
#define LPDDR4__LPC_PROMOTE_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_173__LPC_PROMOTE_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_174_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_174_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_174__LPC_PROMOTE_THRESHOLD_F1_MASK        0x0000FFFFU
#define LPDDR4__DENALI_CTL_174__LPC_PROMOTE_THRESHOLD_F1_SHIFT                0U
#define LPDDR4__DENALI_CTL_174__LPC_PROMOTE_THRESHOLD_F1_WIDTH               16U
#define LPDDR4__LPC_PROMOTE_THRESHOLD_F1__REG DENALI_CTL_174
#define LPDDR4__LPC_PROMOTE_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_174__LPC_PROMOTE_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_174__LPC_PROMOTE_THRESHOLD_F2_MASK        0xFFFF0000U
#define LPDDR4__DENALI_CTL_174__LPC_PROMOTE_THRESHOLD_F2_SHIFT               16U
#define LPDDR4__DENALI_CTL_174__LPC_PROMOTE_THRESHOLD_F2_WIDTH               16U
#define LPDDR4__LPC_PROMOTE_THRESHOLD_F2__REG DENALI_CTL_174
#define LPDDR4__LPC_PROMOTE_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_174__LPC_PROMOTE_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_175_READ_MASK                             0x01010101U
#define LPDDR4__DENALI_CTL_175_WRITE_MASK                            0x01010101U
#define LPDDR4__DENALI_CTL_175__LPC_SR_CTRLUPD_EN_MASK               0x00000001U
#define LPDDR4__DENALI_CTL_175__LPC_SR_CTRLUPD_EN_SHIFT                       0U
#define LPDDR4__DENALI_CTL_175__LPC_SR_CTRLUPD_EN_WIDTH                       1U
#define LPDDR4__DENALI_CTL_175__LPC_SR_CTRLUPD_EN_WOCLR                       0U
#define LPDDR4__DENALI_CTL_175__LPC_SR_CTRLUPD_EN_WOSET                       0U
#define LPDDR4__LPC_SR_CTRLUPD_EN__REG DENALI_CTL_175
#define LPDDR4__LPC_SR_CTRLUPD_EN__FLD LPDDR4__DENALI_CTL_175__LPC_SR_CTRLUPD_EN

#define LPDDR4__DENALI_CTL_175__LPC_SR_PHYUPD_EN_MASK                0x00000100U
#define LPDDR4__DENALI_CTL_175__LPC_SR_PHYUPD_EN_SHIFT                        8U
#define LPDDR4__DENALI_CTL_175__LPC_SR_PHYUPD_EN_WIDTH                        1U
#define LPDDR4__DENALI_CTL_175__LPC_SR_PHYUPD_EN_WOCLR                        0U
#define LPDDR4__DENALI_CTL_175__LPC_SR_PHYUPD_EN_WOSET                        0U
#define LPDDR4__LPC_SR_PHYUPD_EN__REG DENALI_CTL_175
#define LPDDR4__LPC_SR_PHYUPD_EN__FLD LPDDR4__DENALI_CTL_175__LPC_SR_PHYUPD_EN

#define LPDDR4__DENALI_CTL_175__LPC_SR_PHYMSTR_EN_MASK               0x00010000U
#define LPDDR4__DENALI_CTL_175__LPC_SR_PHYMSTR_EN_SHIFT                      16U
#define LPDDR4__DENALI_CTL_175__LPC_SR_PHYMSTR_EN_WIDTH                       1U
#define LPDDR4__DENALI_CTL_175__LPC_SR_PHYMSTR_EN_WOCLR                       0U
#define LPDDR4__DENALI_CTL_175__LPC_SR_PHYMSTR_EN_WOSET                       0U
#define LPDDR4__LPC_SR_PHYMSTR_EN__REG DENALI_CTL_175
#define LPDDR4__LPC_SR_PHYMSTR_EN__FLD LPDDR4__DENALI_CTL_175__LPC_SR_PHYMSTR_EN

#define LPDDR4__DENALI_CTL_175__LPC_SR_EXIT_CMD_EN_MASK              0x01000000U
#define LPDDR4__DENALI_CTL_175__LPC_SR_EXIT_CMD_EN_SHIFT                     24U
#define LPDDR4__DENALI_CTL_175__LPC_SR_EXIT_CMD_EN_WIDTH                      1U
#define LPDDR4__DENALI_CTL_175__LPC_SR_EXIT_CMD_EN_WOCLR                      0U
#define LPDDR4__DENALI_CTL_175__LPC_SR_EXIT_CMD_EN_WOSET                      0U
#define LPDDR4__LPC_SR_EXIT_CMD_EN__REG DENALI_CTL_175
#define LPDDR4__LPC_SR_EXIT_CMD_EN__FLD LPDDR4__DENALI_CTL_175__LPC_SR_EXIT_CMD_EN

#define LPDDR4__DENALI_CTL_176_READ_MASK                             0x0101FF01U
#define LPDDR4__DENALI_CTL_176_WRITE_MASK                            0x0101FF01U
#define LPDDR4__DENALI_CTL_176__LPC_SR_ZQ_EN_MASK                    0x00000001U
#define LPDDR4__DENALI_CTL_176__LPC_SR_ZQ_EN_SHIFT                            0U
#define LPDDR4__DENALI_CTL_176__LPC_SR_ZQ_EN_WIDTH                            1U
#define LPDDR4__DENALI_CTL_176__LPC_SR_ZQ_EN_WOCLR                            0U
#define LPDDR4__DENALI_CTL_176__LPC_SR_ZQ_EN_WOSET                            0U
#define LPDDR4__LPC_SR_ZQ_EN__REG DENALI_CTL_176
#define LPDDR4__LPC_SR_ZQ_EN__FLD LPDDR4__DENALI_CTL_176__LPC_SR_ZQ_EN

#define LPDDR4__DENALI_CTL_176__PWRDN_SHIFT_DELAY_MASK               0x0001FF00U
#define LPDDR4__DENALI_CTL_176__PWRDN_SHIFT_DELAY_SHIFT                       8U
#define LPDDR4__DENALI_CTL_176__PWRDN_SHIFT_DELAY_WIDTH                       9U
#define LPDDR4__PWRDN_SHIFT_DELAY__REG DENALI_CTL_176
#define LPDDR4__PWRDN_SHIFT_DELAY__FLD LPDDR4__DENALI_CTL_176__PWRDN_SHIFT_DELAY

#define LPDDR4__DENALI_CTL_176__DFS_ENABLE_MASK                      0x01000000U
#define LPDDR4__DENALI_CTL_176__DFS_ENABLE_SHIFT                             24U
#define LPDDR4__DENALI_CTL_176__DFS_ENABLE_WIDTH                              1U
#define LPDDR4__DENALI_CTL_176__DFS_ENABLE_WOCLR                              0U
#define LPDDR4__DENALI_CTL_176__DFS_ENABLE_WOSET                              0U
#define LPDDR4__DFS_ENABLE__REG DENALI_CTL_176
#define LPDDR4__DFS_ENABLE__FLD LPDDR4__DENALI_CTL_176__DFS_ENABLE

#define LPDDR4__DENALI_CTL_177_READ_MASK                             0x00000107U
#define LPDDR4__DENALI_CTL_177_WRITE_MASK                            0x00000107U
#define LPDDR4__DENALI_CTL_177__DFS_DLL_OFF_MASK                     0x00000007U
#define LPDDR4__DENALI_CTL_177__DFS_DLL_OFF_SHIFT                             0U
#define LPDDR4__DENALI_CTL_177__DFS_DLL_OFF_WIDTH                             3U
#define LPDDR4__DFS_DLL_OFF__REG DENALI_CTL_177
#define LPDDR4__DFS_DLL_OFF__FLD LPDDR4__DENALI_CTL_177__DFS_DLL_OFF

#define LPDDR4__DENALI_CTL_177__DFS_PHY_REG_WRITE_EN_MASK            0x00000100U
#define LPDDR4__DENALI_CTL_177__DFS_PHY_REG_WRITE_EN_SHIFT                    8U
#define LPDDR4__DENALI_CTL_177__DFS_PHY_REG_WRITE_EN_WIDTH                    1U
#define LPDDR4__DENALI_CTL_177__DFS_PHY_REG_WRITE_EN_WOCLR                    0U
#define LPDDR4__DENALI_CTL_177__DFS_PHY_REG_WRITE_EN_WOSET                    0U
#define LPDDR4__DFS_PHY_REG_WRITE_EN__REG DENALI_CTL_177
#define LPDDR4__DFS_PHY_REG_WRITE_EN__FLD LPDDR4__DENALI_CTL_177__DFS_PHY_REG_WRITE_EN

#define LPDDR4__DENALI_CTL_178_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_178_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_178__DFS_PHY_REG_WRITE_ADDR_MASK          0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_178__DFS_PHY_REG_WRITE_ADDR_SHIFT                  0U
#define LPDDR4__DENALI_CTL_178__DFS_PHY_REG_WRITE_ADDR_WIDTH                 32U
#define LPDDR4__DFS_PHY_REG_WRITE_ADDR__REG DENALI_CTL_178
#define LPDDR4__DFS_PHY_REG_WRITE_ADDR__FLD LPDDR4__DENALI_CTL_178__DFS_PHY_REG_WRITE_ADDR

#define LPDDR4__DENALI_CTL_179_READ_MASK                             0x03FFFF0FU
#define LPDDR4__DENALI_CTL_179_WRITE_MASK                            0x03FFFF0FU
#define LPDDR4__DENALI_CTL_179__DFS_PHY_REG_WRITE_MASK_MASK          0x0000000FU
#define LPDDR4__DENALI_CTL_179__DFS_PHY_REG_WRITE_MASK_SHIFT                  0U
#define LPDDR4__DENALI_CTL_179__DFS_PHY_REG_WRITE_MASK_WIDTH                  4U
#define LPDDR4__DFS_PHY_REG_WRITE_MASK__REG DENALI_CTL_179
#define LPDDR4__DFS_PHY_REG_WRITE_MASK__FLD LPDDR4__DENALI_CTL_179__DFS_PHY_REG_WRITE_MASK

#define LPDDR4__DENALI_CTL_179__DFS_PHY_REG_WRITE_WAIT_MASK          0x00FFFF00U
#define LPDDR4__DENALI_CTL_179__DFS_PHY_REG_WRITE_WAIT_SHIFT                  8U
#define LPDDR4__DENALI_CTL_179__DFS_PHY_REG_WRITE_WAIT_WIDTH                 16U
#define LPDDR4__DFS_PHY_REG_WRITE_WAIT__REG DENALI_CTL_179
#define LPDDR4__DFS_PHY_REG_WRITE_WAIT__FLD LPDDR4__DENALI_CTL_179__DFS_PHY_REG_WRITE_WAIT

#define LPDDR4__DENALI_CTL_179__CURRENT_REG_COPY_MASK                0x03000000U
#define LPDDR4__DENALI_CTL_179__CURRENT_REG_COPY_SHIFT                       24U
#define LPDDR4__DENALI_CTL_179__CURRENT_REG_COPY_WIDTH                        2U
#define LPDDR4__CURRENT_REG_COPY__REG DENALI_CTL_179
#define LPDDR4__CURRENT_REG_COPY__FLD LPDDR4__DENALI_CTL_179__CURRENT_REG_COPY

#define LPDDR4__DENALI_CTL_180_READ_MASK                             0x00000303U
#define LPDDR4__DENALI_CTL_180_WRITE_MASK                            0x00000303U
#define LPDDR4__DENALI_CTL_180__INIT_FREQ_MASK                       0x00000003U
#define LPDDR4__DENALI_CTL_180__INIT_FREQ_SHIFT                               0U
#define LPDDR4__DENALI_CTL_180__INIT_FREQ_WIDTH                               2U
#define LPDDR4__INIT_FREQ__REG DENALI_CTL_180
#define LPDDR4__INIT_FREQ__FLD LPDDR4__DENALI_CTL_180__INIT_FREQ

#define LPDDR4__DENALI_CTL_180__DFIBUS_BOOT_FREQ_MASK                0x00000300U
#define LPDDR4__DENALI_CTL_180__DFIBUS_BOOT_FREQ_SHIFT                        8U
#define LPDDR4__DENALI_CTL_180__DFIBUS_BOOT_FREQ_WIDTH                        2U
#define LPDDR4__DFIBUS_BOOT_FREQ__REG DENALI_CTL_180
#define LPDDR4__DFIBUS_BOOT_FREQ__FLD LPDDR4__DENALI_CTL_180__DFIBUS_BOOT_FREQ

#define LPDDR4__DENALI_CTL_181_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_181_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_181__DFS_PHY_REG_WRITE_DATA_F0_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_181__DFS_PHY_REG_WRITE_DATA_F0_SHIFT               0U
#define LPDDR4__DENALI_CTL_181__DFS_PHY_REG_WRITE_DATA_F0_WIDTH              32U
#define LPDDR4__DFS_PHY_REG_WRITE_DATA_F0__REG DENALI_CTL_181
#define LPDDR4__DFS_PHY_REG_WRITE_DATA_F0__FLD LPDDR4__DENALI_CTL_181__DFS_PHY_REG_WRITE_DATA_F0

#define LPDDR4__DENALI_CTL_182_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_182_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_182__DFS_PHY_REG_WRITE_DATA_F1_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_182__DFS_PHY_REG_WRITE_DATA_F1_SHIFT               0U
#define LPDDR4__DENALI_CTL_182__DFS_PHY_REG_WRITE_DATA_F1_WIDTH              32U
#define LPDDR4__DFS_PHY_REG_WRITE_DATA_F1__REG DENALI_CTL_182
#define LPDDR4__DFS_PHY_REG_WRITE_DATA_F1__FLD LPDDR4__DENALI_CTL_182__DFS_PHY_REG_WRITE_DATA_F1

#define LPDDR4__DENALI_CTL_183_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_183_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_183__DFS_PHY_REG_WRITE_DATA_F2_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_183__DFS_PHY_REG_WRITE_DATA_F2_SHIFT               0U
#define LPDDR4__DENALI_CTL_183__DFS_PHY_REG_WRITE_DATA_F2_WIDTH              32U
#define LPDDR4__DFS_PHY_REG_WRITE_DATA_F2__REG DENALI_CTL_183
#define LPDDR4__DFS_PHY_REG_WRITE_DATA_F2__FLD LPDDR4__DENALI_CTL_183__DFS_PHY_REG_WRITE_DATA_F2

#define LPDDR4__DENALI_CTL_184_READ_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_184_WRITE_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_CTL_184__TDFI_INIT_START_F0_MASK              0x00FFFFFFU
#define LPDDR4__DENALI_CTL_184__TDFI_INIT_START_F0_SHIFT                      0U
#define LPDDR4__DENALI_CTL_184__TDFI_INIT_START_F0_WIDTH                     24U
#define LPDDR4__TDFI_INIT_START_F0__REG DENALI_CTL_184
#define LPDDR4__TDFI_INIT_START_F0__FLD LPDDR4__DENALI_CTL_184__TDFI_INIT_START_F0

#define LPDDR4__DENALI_CTL_185_READ_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_185_WRITE_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_CTL_185__TDFI_INIT_COMPLETE_F0_MASK           0x00FFFFFFU
#define LPDDR4__DENALI_CTL_185__TDFI_INIT_COMPLETE_F0_SHIFT                   0U
#define LPDDR4__DENALI_CTL_185__TDFI_INIT_COMPLETE_F0_WIDTH                  24U
#define LPDDR4__TDFI_INIT_COMPLETE_F0__REG DENALI_CTL_185
#define LPDDR4__TDFI_INIT_COMPLETE_F0__FLD LPDDR4__DENALI_CTL_185__TDFI_INIT_COMPLETE_F0

#define LPDDR4__DENALI_CTL_186_READ_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_186_WRITE_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_CTL_186__TDFI_INIT_START_F1_MASK              0x00FFFFFFU
#define LPDDR4__DENALI_CTL_186__TDFI_INIT_START_F1_SHIFT                      0U
#define LPDDR4__DENALI_CTL_186__TDFI_INIT_START_F1_WIDTH                     24U
#define LPDDR4__TDFI_INIT_START_F1__REG DENALI_CTL_186
#define LPDDR4__TDFI_INIT_START_F1__FLD LPDDR4__DENALI_CTL_186__TDFI_INIT_START_F1

#define LPDDR4__DENALI_CTL_187_READ_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_187_WRITE_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_CTL_187__TDFI_INIT_COMPLETE_F1_MASK           0x00FFFFFFU
#define LPDDR4__DENALI_CTL_187__TDFI_INIT_COMPLETE_F1_SHIFT                   0U
#define LPDDR4__DENALI_CTL_187__TDFI_INIT_COMPLETE_F1_WIDTH                  24U
#define LPDDR4__TDFI_INIT_COMPLETE_F1__REG DENALI_CTL_187
#define LPDDR4__TDFI_INIT_COMPLETE_F1__FLD LPDDR4__DENALI_CTL_187__TDFI_INIT_COMPLETE_F1

#define LPDDR4__DENALI_CTL_188_READ_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_188_WRITE_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_CTL_188__TDFI_INIT_START_F2_MASK              0x00FFFFFFU
#define LPDDR4__DENALI_CTL_188__TDFI_INIT_START_F2_SHIFT                      0U
#define LPDDR4__DENALI_CTL_188__TDFI_INIT_START_F2_WIDTH                     24U
#define LPDDR4__TDFI_INIT_START_F2__REG DENALI_CTL_188
#define LPDDR4__TDFI_INIT_START_F2__FLD LPDDR4__DENALI_CTL_188__TDFI_INIT_START_F2

#define LPDDR4__DENALI_CTL_189_READ_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_189_WRITE_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_CTL_189__TDFI_INIT_COMPLETE_F2_MASK           0x00FFFFFFU
#define LPDDR4__DENALI_CTL_189__TDFI_INIT_COMPLETE_F2_SHIFT                   0U
#define LPDDR4__DENALI_CTL_189__TDFI_INIT_COMPLETE_F2_WIDTH                  24U
#define LPDDR4__TDFI_INIT_COMPLETE_F2__REG DENALI_CTL_189
#define LPDDR4__TDFI_INIT_COMPLETE_F2__FLD LPDDR4__DENALI_CTL_189__TDFI_INIT_COMPLETE_F2

#define LPDDR4__DENALI_CTL_190_READ_MASK                             0x07FFFFFFU
#define LPDDR4__DENALI_CTL_190_WRITE_MASK                            0x07FFFFFFU
#define LPDDR4__DENALI_CTL_190__WRITE_MODEREG_MASK                   0x07FFFFFFU
#define LPDDR4__DENALI_CTL_190__WRITE_MODEREG_SHIFT                           0U
#define LPDDR4__DENALI_CTL_190__WRITE_MODEREG_WIDTH                          27U
#define LPDDR4__WRITE_MODEREG__REG DENALI_CTL_190
#define LPDDR4__WRITE_MODEREG__FLD LPDDR4__DENALI_CTL_190__WRITE_MODEREG

#define LPDDR4__DENALI_CTL_191_READ_MASK                             0x01FFFFFFU
#define LPDDR4__DENALI_CTL_191_WRITE_MASK                            0x01FFFFFFU
#define LPDDR4__DENALI_CTL_191__MRW_STATUS_MASK                      0x000000FFU
#define LPDDR4__DENALI_CTL_191__MRW_STATUS_SHIFT                              0U
#define LPDDR4__DENALI_CTL_191__MRW_STATUS_WIDTH                              8U
#define LPDDR4__MRW_STATUS__REG DENALI_CTL_191
#define LPDDR4__MRW_STATUS__FLD LPDDR4__DENALI_CTL_191__MRW_STATUS

#define LPDDR4__DENALI_CTL_191__READ_MODEREG_MASK                    0x01FFFF00U
#define LPDDR4__DENALI_CTL_191__READ_MODEREG_SHIFT                            8U
#define LPDDR4__DENALI_CTL_191__READ_MODEREG_WIDTH                           17U
#define LPDDR4__READ_MODEREG__REG DENALI_CTL_191
#define LPDDR4__READ_MODEREG__FLD LPDDR4__DENALI_CTL_191__READ_MODEREG

#define LPDDR4__DENALI_CTL_192_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_192_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_192__PERIPHERAL_MRR_DATA_0_MASK           0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_192__PERIPHERAL_MRR_DATA_0_SHIFT                   0U
#define LPDDR4__DENALI_CTL_192__PERIPHERAL_MRR_DATA_0_WIDTH                  32U
#define LPDDR4__PERIPHERAL_MRR_DATA_0__REG DENALI_CTL_192
#define LPDDR4__PERIPHERAL_MRR_DATA_0__FLD LPDDR4__DENALI_CTL_192__PERIPHERAL_MRR_DATA_0

#define LPDDR4__DENALI_CTL_193_READ_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_CTL_193_WRITE_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_CTL_193__PERIPHERAL_MRR_DATA_1_MASK           0x000000FFU
#define LPDDR4__DENALI_CTL_193__PERIPHERAL_MRR_DATA_1_SHIFT                   0U
#define LPDDR4__DENALI_CTL_193__PERIPHERAL_MRR_DATA_1_WIDTH                   8U
#define LPDDR4__PERIPHERAL_MRR_DATA_1__REG DENALI_CTL_193
#define LPDDR4__PERIPHERAL_MRR_DATA_1__FLD LPDDR4__DENALI_CTL_193__PERIPHERAL_MRR_DATA_1

#define LPDDR4__DENALI_CTL_193__AUTO_TEMPCHK_VAL_0_MASK              0x00FFFF00U
#define LPDDR4__DENALI_CTL_193__AUTO_TEMPCHK_VAL_0_SHIFT                      8U
#define LPDDR4__DENALI_CTL_193__AUTO_TEMPCHK_VAL_0_WIDTH                     16U
#define LPDDR4__AUTO_TEMPCHK_VAL_0__REG DENALI_CTL_193
#define LPDDR4__AUTO_TEMPCHK_VAL_0__FLD LPDDR4__DENALI_CTL_193__AUTO_TEMPCHK_VAL_0

#define LPDDR4__DENALI_CTL_194_READ_MASK                             0x0301FFFFU
#define LPDDR4__DENALI_CTL_194_WRITE_MASK                            0x0301FFFFU
#define LPDDR4__DENALI_CTL_194__AUTO_TEMPCHK_VAL_1_MASK              0x0000FFFFU
#define LPDDR4__DENALI_CTL_194__AUTO_TEMPCHK_VAL_1_SHIFT                      0U
#define LPDDR4__DENALI_CTL_194__AUTO_TEMPCHK_VAL_1_WIDTH                     16U
#define LPDDR4__AUTO_TEMPCHK_VAL_1__REG DENALI_CTL_194
#define LPDDR4__AUTO_TEMPCHK_VAL_1__FLD LPDDR4__DENALI_CTL_194__AUTO_TEMPCHK_VAL_1

#define LPDDR4__DENALI_CTL_194__DISABLE_UPDATE_TVRCG_MASK            0x00010000U
#define LPDDR4__DENALI_CTL_194__DISABLE_UPDATE_TVRCG_SHIFT                   16U
#define LPDDR4__DENALI_CTL_194__DISABLE_UPDATE_TVRCG_WIDTH                    1U
#define LPDDR4__DENALI_CTL_194__DISABLE_UPDATE_TVRCG_WOCLR                    0U
#define LPDDR4__DENALI_CTL_194__DISABLE_UPDATE_TVRCG_WOSET                    0U
#define LPDDR4__DISABLE_UPDATE_TVRCG__REG DENALI_CTL_194
#define LPDDR4__DISABLE_UPDATE_TVRCG__FLD LPDDR4__DENALI_CTL_194__DISABLE_UPDATE_TVRCG

#define LPDDR4__DENALI_CTL_194__MRW_DFS_UPDATE_FRC_MASK              0x03000000U
#define LPDDR4__DENALI_CTL_194__MRW_DFS_UPDATE_FRC_SHIFT                     24U
#define LPDDR4__DENALI_CTL_194__MRW_DFS_UPDATE_FRC_WIDTH                      2U
#define LPDDR4__MRW_DFS_UPDATE_FRC__REG DENALI_CTL_194
#define LPDDR4__MRW_DFS_UPDATE_FRC__FLD LPDDR4__DENALI_CTL_194__MRW_DFS_UPDATE_FRC

#define LPDDR4__DENALI_CTL_195_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_CTL_195_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_CTL_195__TVRCG_ENABLE_F0_MASK                 0x000003FFU
#define LPDDR4__DENALI_CTL_195__TVRCG_ENABLE_F0_SHIFT                         0U
#define LPDDR4__DENALI_CTL_195__TVRCG_ENABLE_F0_WIDTH                        10U
#define LPDDR4__TVRCG_ENABLE_F0__REG DENALI_CTL_195
#define LPDDR4__TVRCG_ENABLE_F0__FLD LPDDR4__DENALI_CTL_195__TVRCG_ENABLE_F0

#define LPDDR4__DENALI_CTL_195__TVRCG_DISABLE_F0_MASK                0x03FF0000U
#define LPDDR4__DENALI_CTL_195__TVRCG_DISABLE_F0_SHIFT                       16U
#define LPDDR4__DENALI_CTL_195__TVRCG_DISABLE_F0_WIDTH                       10U
#define LPDDR4__TVRCG_DISABLE_F0__REG DENALI_CTL_195
#define LPDDR4__TVRCG_DISABLE_F0__FLD LPDDR4__DENALI_CTL_195__TVRCG_DISABLE_F0

#define LPDDR4__DENALI_CTL_196_READ_MASK                             0x1F1F03FFU
#define LPDDR4__DENALI_CTL_196_WRITE_MASK                            0x1F1F03FFU
#define LPDDR4__DENALI_CTL_196__TFC_F0_MASK                          0x000003FFU
#define LPDDR4__DENALI_CTL_196__TFC_F0_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_196__TFC_F0_WIDTH                                 10U
#define LPDDR4__TFC_F0__REG DENALI_CTL_196
#define LPDDR4__TFC_F0__FLD LPDDR4__DENALI_CTL_196__TFC_F0

#define LPDDR4__DENALI_CTL_196__TCKFSPE_F0_MASK                      0x001F0000U
#define LPDDR4__DENALI_CTL_196__TCKFSPE_F0_SHIFT                             16U
#define LPDDR4__DENALI_CTL_196__TCKFSPE_F0_WIDTH                              5U
#define LPDDR4__TCKFSPE_F0__REG DENALI_CTL_196
#define LPDDR4__TCKFSPE_F0__FLD LPDDR4__DENALI_CTL_196__TCKFSPE_F0

#define LPDDR4__DENALI_CTL_196__TCKFSPX_F0_MASK                      0x1F000000U
#define LPDDR4__DENALI_CTL_196__TCKFSPX_F0_SHIFT                             24U
#define LPDDR4__DENALI_CTL_196__TCKFSPX_F0_WIDTH                              5U
#define LPDDR4__TCKFSPX_F0__REG DENALI_CTL_196
#define LPDDR4__TCKFSPX_F0__FLD LPDDR4__DENALI_CTL_196__TCKFSPX_F0

#define LPDDR4__DENALI_CTL_197_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_CTL_197_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_CTL_197__TVREF_LONG_F0_MASK                   0x000FFFFFU
#define LPDDR4__DENALI_CTL_197__TVREF_LONG_F0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_197__TVREF_LONG_F0_WIDTH                          20U
#define LPDDR4__TVREF_LONG_F0__REG DENALI_CTL_197
#define LPDDR4__TVREF_LONG_F0__FLD LPDDR4__DENALI_CTL_197__TVREF_LONG_F0

#define LPDDR4__DENALI_CTL_198_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_CTL_198_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_CTL_198__TVRCG_ENABLE_F1_MASK                 0x000003FFU
#define LPDDR4__DENALI_CTL_198__TVRCG_ENABLE_F1_SHIFT                         0U
#define LPDDR4__DENALI_CTL_198__TVRCG_ENABLE_F1_WIDTH                        10U
#define LPDDR4__TVRCG_ENABLE_F1__REG DENALI_CTL_198
#define LPDDR4__TVRCG_ENABLE_F1__FLD LPDDR4__DENALI_CTL_198__TVRCG_ENABLE_F1

#define LPDDR4__DENALI_CTL_198__TVRCG_DISABLE_F1_MASK                0x03FF0000U
#define LPDDR4__DENALI_CTL_198__TVRCG_DISABLE_F1_SHIFT                       16U
#define LPDDR4__DENALI_CTL_198__TVRCG_DISABLE_F1_WIDTH                       10U
#define LPDDR4__TVRCG_DISABLE_F1__REG DENALI_CTL_198
#define LPDDR4__TVRCG_DISABLE_F1__FLD LPDDR4__DENALI_CTL_198__TVRCG_DISABLE_F1

#define LPDDR4__DENALI_CTL_199_READ_MASK                             0x1F1F03FFU
#define LPDDR4__DENALI_CTL_199_WRITE_MASK                            0x1F1F03FFU
#define LPDDR4__DENALI_CTL_199__TFC_F1_MASK                          0x000003FFU
#define LPDDR4__DENALI_CTL_199__TFC_F1_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_199__TFC_F1_WIDTH                                 10U
#define LPDDR4__TFC_F1__REG DENALI_CTL_199
#define LPDDR4__TFC_F1__FLD LPDDR4__DENALI_CTL_199__TFC_F1

#define LPDDR4__DENALI_CTL_199__TCKFSPE_F1_MASK                      0x001F0000U
#define LPDDR4__DENALI_CTL_199__TCKFSPE_F1_SHIFT                             16U
#define LPDDR4__DENALI_CTL_199__TCKFSPE_F1_WIDTH                              5U
#define LPDDR4__TCKFSPE_F1__REG DENALI_CTL_199
#define LPDDR4__TCKFSPE_F1__FLD LPDDR4__DENALI_CTL_199__TCKFSPE_F1

#define LPDDR4__DENALI_CTL_199__TCKFSPX_F1_MASK                      0x1F000000U
#define LPDDR4__DENALI_CTL_199__TCKFSPX_F1_SHIFT                             24U
#define LPDDR4__DENALI_CTL_199__TCKFSPX_F1_WIDTH                              5U
#define LPDDR4__TCKFSPX_F1__REG DENALI_CTL_199
#define LPDDR4__TCKFSPX_F1__FLD LPDDR4__DENALI_CTL_199__TCKFSPX_F1

#define LPDDR4__DENALI_CTL_200_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_CTL_200_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_CTL_200__TVREF_LONG_F1_MASK                   0x000FFFFFU
#define LPDDR4__DENALI_CTL_200__TVREF_LONG_F1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_200__TVREF_LONG_F1_WIDTH                          20U
#define LPDDR4__TVREF_LONG_F1__REG DENALI_CTL_200
#define LPDDR4__TVREF_LONG_F1__FLD LPDDR4__DENALI_CTL_200__TVREF_LONG_F1

#define LPDDR4__DENALI_CTL_201_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_CTL_201_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_CTL_201__TVRCG_ENABLE_F2_MASK                 0x000003FFU
#define LPDDR4__DENALI_CTL_201__TVRCG_ENABLE_F2_SHIFT                         0U
#define LPDDR4__DENALI_CTL_201__TVRCG_ENABLE_F2_WIDTH                        10U
#define LPDDR4__TVRCG_ENABLE_F2__REG DENALI_CTL_201
#define LPDDR4__TVRCG_ENABLE_F2__FLD LPDDR4__DENALI_CTL_201__TVRCG_ENABLE_F2

#define LPDDR4__DENALI_CTL_201__TVRCG_DISABLE_F2_MASK                0x03FF0000U
#define LPDDR4__DENALI_CTL_201__TVRCG_DISABLE_F2_SHIFT                       16U
#define LPDDR4__DENALI_CTL_201__TVRCG_DISABLE_F2_WIDTH                       10U
#define LPDDR4__TVRCG_DISABLE_F2__REG DENALI_CTL_201
#define LPDDR4__TVRCG_DISABLE_F2__FLD LPDDR4__DENALI_CTL_201__TVRCG_DISABLE_F2

#define LPDDR4__DENALI_CTL_202_READ_MASK                             0x1F1F03FFU
#define LPDDR4__DENALI_CTL_202_WRITE_MASK                            0x1F1F03FFU
#define LPDDR4__DENALI_CTL_202__TFC_F2_MASK                          0x000003FFU
#define LPDDR4__DENALI_CTL_202__TFC_F2_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_202__TFC_F2_WIDTH                                 10U
#define LPDDR4__TFC_F2__REG DENALI_CTL_202
#define LPDDR4__TFC_F2__FLD LPDDR4__DENALI_CTL_202__TFC_F2

#define LPDDR4__DENALI_CTL_202__TCKFSPE_F2_MASK                      0x001F0000U
#define LPDDR4__DENALI_CTL_202__TCKFSPE_F2_SHIFT                             16U
#define LPDDR4__DENALI_CTL_202__TCKFSPE_F2_WIDTH                              5U
#define LPDDR4__TCKFSPE_F2__REG DENALI_CTL_202
#define LPDDR4__TCKFSPE_F2__FLD LPDDR4__DENALI_CTL_202__TCKFSPE_F2

#define LPDDR4__DENALI_CTL_202__TCKFSPX_F2_MASK                      0x1F000000U
#define LPDDR4__DENALI_CTL_202__TCKFSPX_F2_SHIFT                             24U
#define LPDDR4__DENALI_CTL_202__TCKFSPX_F2_WIDTH                              5U
#define LPDDR4__TCKFSPX_F2__REG DENALI_CTL_202
#define LPDDR4__TCKFSPX_F2__FLD LPDDR4__DENALI_CTL_202__TCKFSPX_F2

#define LPDDR4__DENALI_CTL_203_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_CTL_203_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_CTL_203__TVREF_LONG_F2_MASK                   0x000FFFFFU
#define LPDDR4__DENALI_CTL_203__TVREF_LONG_F2_SHIFT                           0U
#define LPDDR4__DENALI_CTL_203__TVREF_LONG_F2_WIDTH                          20U
#define LPDDR4__TVREF_LONG_F2__REG DENALI_CTL_203
#define LPDDR4__TVREF_LONG_F2__FLD LPDDR4__DENALI_CTL_203__TVREF_LONG_F2

#define LPDDR4__DENALI_CTL_204_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_204_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_204__MRR_PROMOTE_THRESHOLD_F0_MASK        0x0000FFFFU
#define LPDDR4__DENALI_CTL_204__MRR_PROMOTE_THRESHOLD_F0_SHIFT                0U
#define LPDDR4__DENALI_CTL_204__MRR_PROMOTE_THRESHOLD_F0_WIDTH               16U
#define LPDDR4__MRR_PROMOTE_THRESHOLD_F0__REG DENALI_CTL_204
#define LPDDR4__MRR_PROMOTE_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_204__MRR_PROMOTE_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_204__MRR_PROMOTE_THRESHOLD_F1_MASK        0xFFFF0000U
#define LPDDR4__DENALI_CTL_204__MRR_PROMOTE_THRESHOLD_F1_SHIFT               16U
#define LPDDR4__DENALI_CTL_204__MRR_PROMOTE_THRESHOLD_F1_WIDTH               16U
#define LPDDR4__MRR_PROMOTE_THRESHOLD_F1__REG DENALI_CTL_204
#define LPDDR4__MRR_PROMOTE_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_204__MRR_PROMOTE_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_205_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_205_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_205__MRR_PROMOTE_THRESHOLD_F2_MASK        0x0000FFFFU
#define LPDDR4__DENALI_CTL_205__MRR_PROMOTE_THRESHOLD_F2_SHIFT                0U
#define LPDDR4__DENALI_CTL_205__MRR_PROMOTE_THRESHOLD_F2_WIDTH               16U
#define LPDDR4__MRR_PROMOTE_THRESHOLD_F2__REG DENALI_CTL_205
#define LPDDR4__MRR_PROMOTE_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_205__MRR_PROMOTE_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_205__MRW_PROMOTE_THRESHOLD_F0_MASK        0xFFFF0000U
#define LPDDR4__DENALI_CTL_205__MRW_PROMOTE_THRESHOLD_F0_SHIFT               16U
#define LPDDR4__DENALI_CTL_205__MRW_PROMOTE_THRESHOLD_F0_WIDTH               16U
#define LPDDR4__MRW_PROMOTE_THRESHOLD_F0__REG DENALI_CTL_205
#define LPDDR4__MRW_PROMOTE_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_205__MRW_PROMOTE_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_206_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_206_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_206__MRW_PROMOTE_THRESHOLD_F1_MASK        0x0000FFFFU
#define LPDDR4__DENALI_CTL_206__MRW_PROMOTE_THRESHOLD_F1_SHIFT                0U
#define LPDDR4__DENALI_CTL_206__MRW_PROMOTE_THRESHOLD_F1_WIDTH               16U
#define LPDDR4__MRW_PROMOTE_THRESHOLD_F1__REG DENALI_CTL_206
#define LPDDR4__MRW_PROMOTE_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_206__MRW_PROMOTE_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_206__MRW_PROMOTE_THRESHOLD_F2_MASK        0xFFFF0000U
#define LPDDR4__DENALI_CTL_206__MRW_PROMOTE_THRESHOLD_F2_SHIFT               16U
#define LPDDR4__DENALI_CTL_206__MRW_PROMOTE_THRESHOLD_F2_WIDTH               16U
#define LPDDR4__MRW_PROMOTE_THRESHOLD_F2__REG DENALI_CTL_206
#define LPDDR4__MRW_PROMOTE_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_206__MRW_PROMOTE_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_207_READ_MASK                             0x01FFFF01U
#define LPDDR4__DENALI_CTL_207_WRITE_MASK                            0x01FFFF01U
#define LPDDR4__DENALI_CTL_207__MR4_DLL_RST_MASK                     0x00000001U
#define LPDDR4__DENALI_CTL_207__MR4_DLL_RST_SHIFT                             0U
#define LPDDR4__DENALI_CTL_207__MR4_DLL_RST_WIDTH                             1U
#define LPDDR4__DENALI_CTL_207__MR4_DLL_RST_WOCLR                             0U
#define LPDDR4__DENALI_CTL_207__MR4_DLL_RST_WOSET                             0U
#define LPDDR4__MR4_DLL_RST__REG DENALI_CTL_207
#define LPDDR4__MR4_DLL_RST__FLD LPDDR4__DENALI_CTL_207__MR4_DLL_RST

#define LPDDR4__DENALI_CTL_207__MR0_DATA_F0_0_MASK                   0x01FFFF00U
#define LPDDR4__DENALI_CTL_207__MR0_DATA_F0_0_SHIFT                           8U
#define LPDDR4__DENALI_CTL_207__MR0_DATA_F0_0_WIDTH                          17U
#define LPDDR4__MR0_DATA_F0_0__REG DENALI_CTL_207
#define LPDDR4__MR0_DATA_F0_0__FLD LPDDR4__DENALI_CTL_207__MR0_DATA_F0_0

#define LPDDR4__DENALI_CTL_208_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_208_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_208__MR1_DATA_F0_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_208__MR1_DATA_F0_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_208__MR1_DATA_F0_0_WIDTH                          17U
#define LPDDR4__MR1_DATA_F0_0__REG DENALI_CTL_208
#define LPDDR4__MR1_DATA_F0_0__FLD LPDDR4__DENALI_CTL_208__MR1_DATA_F0_0

#define LPDDR4__DENALI_CTL_209_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_209_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_209__MR2_DATA_F0_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_209__MR2_DATA_F0_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_209__MR2_DATA_F0_0_WIDTH                          17U
#define LPDDR4__MR2_DATA_F0_0__REG DENALI_CTL_209
#define LPDDR4__MR2_DATA_F0_0__FLD LPDDR4__DENALI_CTL_209__MR2_DATA_F0_0

#define LPDDR4__DENALI_CTL_210_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_210_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_210__MR0_DATA_F1_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_210__MR0_DATA_F1_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_210__MR0_DATA_F1_0_WIDTH                          17U
#define LPDDR4__MR0_DATA_F1_0__REG DENALI_CTL_210
#define LPDDR4__MR0_DATA_F1_0__FLD LPDDR4__DENALI_CTL_210__MR0_DATA_F1_0

#define LPDDR4__DENALI_CTL_211_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_211_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_211__MR1_DATA_F1_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_211__MR1_DATA_F1_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_211__MR1_DATA_F1_0_WIDTH                          17U
#define LPDDR4__MR1_DATA_F1_0__REG DENALI_CTL_211
#define LPDDR4__MR1_DATA_F1_0__FLD LPDDR4__DENALI_CTL_211__MR1_DATA_F1_0

#define LPDDR4__DENALI_CTL_212_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_212_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_212__MR2_DATA_F1_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_212__MR2_DATA_F1_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_212__MR2_DATA_F1_0_WIDTH                          17U
#define LPDDR4__MR2_DATA_F1_0__REG DENALI_CTL_212
#define LPDDR4__MR2_DATA_F1_0__FLD LPDDR4__DENALI_CTL_212__MR2_DATA_F1_0

#define LPDDR4__DENALI_CTL_213_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_213_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_213__MR0_DATA_F2_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_213__MR0_DATA_F2_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_213__MR0_DATA_F2_0_WIDTH                          17U
#define LPDDR4__MR0_DATA_F2_0__REG DENALI_CTL_213
#define LPDDR4__MR0_DATA_F2_0__FLD LPDDR4__DENALI_CTL_213__MR0_DATA_F2_0

#define LPDDR4__DENALI_CTL_214_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_214_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_214__MR1_DATA_F2_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_214__MR1_DATA_F2_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_214__MR1_DATA_F2_0_WIDTH                          17U
#define LPDDR4__MR1_DATA_F2_0__REG DENALI_CTL_214
#define LPDDR4__MR1_DATA_F2_0__FLD LPDDR4__DENALI_CTL_214__MR1_DATA_F2_0

#define LPDDR4__DENALI_CTL_215_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_215_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_215__MR2_DATA_F2_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_215__MR2_DATA_F2_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_215__MR2_DATA_F2_0_WIDTH                          17U
#define LPDDR4__MR2_DATA_F2_0__REG DENALI_CTL_215
#define LPDDR4__MR2_DATA_F2_0__FLD LPDDR4__DENALI_CTL_215__MR2_DATA_F2_0

#define LPDDR4__DENALI_CTL_216_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_216_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_216__MR0_DATA_F0_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_216__MR0_DATA_F0_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_216__MR0_DATA_F0_1_WIDTH                          17U
#define LPDDR4__MR0_DATA_F0_1__REG DENALI_CTL_216
#define LPDDR4__MR0_DATA_F0_1__FLD LPDDR4__DENALI_CTL_216__MR0_DATA_F0_1

#define LPDDR4__DENALI_CTL_217_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_217_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_217__MR1_DATA_F0_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_217__MR1_DATA_F0_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_217__MR1_DATA_F0_1_WIDTH                          17U
#define LPDDR4__MR1_DATA_F0_1__REG DENALI_CTL_217
#define LPDDR4__MR1_DATA_F0_1__FLD LPDDR4__DENALI_CTL_217__MR1_DATA_F0_1

#define LPDDR4__DENALI_CTL_218_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_218_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_218__MR2_DATA_F0_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_218__MR2_DATA_F0_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_218__MR2_DATA_F0_1_WIDTH                          17U
#define LPDDR4__MR2_DATA_F0_1__REG DENALI_CTL_218
#define LPDDR4__MR2_DATA_F0_1__FLD LPDDR4__DENALI_CTL_218__MR2_DATA_F0_1

#define LPDDR4__DENALI_CTL_219_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_219_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_219__MR0_DATA_F1_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_219__MR0_DATA_F1_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_219__MR0_DATA_F1_1_WIDTH                          17U
#define LPDDR4__MR0_DATA_F1_1__REG DENALI_CTL_219
#define LPDDR4__MR0_DATA_F1_1__FLD LPDDR4__DENALI_CTL_219__MR0_DATA_F1_1

#define LPDDR4__DENALI_CTL_220_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_220_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_220__MR1_DATA_F1_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_220__MR1_DATA_F1_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_220__MR1_DATA_F1_1_WIDTH                          17U
#define LPDDR4__MR1_DATA_F1_1__REG DENALI_CTL_220
#define LPDDR4__MR1_DATA_F1_1__FLD LPDDR4__DENALI_CTL_220__MR1_DATA_F1_1

#define LPDDR4__DENALI_CTL_221_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_221_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_221__MR2_DATA_F1_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_221__MR2_DATA_F1_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_221__MR2_DATA_F1_1_WIDTH                          17U
#define LPDDR4__MR2_DATA_F1_1__REG DENALI_CTL_221
#define LPDDR4__MR2_DATA_F1_1__FLD LPDDR4__DENALI_CTL_221__MR2_DATA_F1_1

#define LPDDR4__DENALI_CTL_222_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_222_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_222__MR0_DATA_F2_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_222__MR0_DATA_F2_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_222__MR0_DATA_F2_1_WIDTH                          17U
#define LPDDR4__MR0_DATA_F2_1__REG DENALI_CTL_222
#define LPDDR4__MR0_DATA_F2_1__FLD LPDDR4__DENALI_CTL_222__MR0_DATA_F2_1

#define LPDDR4__DENALI_CTL_223_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_223_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_223__MR1_DATA_F2_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_223__MR1_DATA_F2_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_223__MR1_DATA_F2_1_WIDTH                          17U
#define LPDDR4__MR1_DATA_F2_1__REG DENALI_CTL_223
#define LPDDR4__MR1_DATA_F2_1__FLD LPDDR4__DENALI_CTL_223__MR1_DATA_F2_1

#define LPDDR4__DENALI_CTL_224_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_224_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_224__MR2_DATA_F2_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_224__MR2_DATA_F2_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_224__MR2_DATA_F2_1_WIDTH                          17U
#define LPDDR4__MR2_DATA_F2_1__REG DENALI_CTL_224
#define LPDDR4__MR2_DATA_F2_1__FLD LPDDR4__DENALI_CTL_224__MR2_DATA_F2_1

#define LPDDR4__DENALI_CTL_225_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_225_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_225__MRSINGLE_DATA_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_CTL_225__MRSINGLE_DATA_0_SHIFT                         0U
#define LPDDR4__DENALI_CTL_225__MRSINGLE_DATA_0_WIDTH                        17U
#define LPDDR4__MRSINGLE_DATA_0__REG DENALI_CTL_225
#define LPDDR4__MRSINGLE_DATA_0__FLD LPDDR4__DENALI_CTL_225__MRSINGLE_DATA_0

#define LPDDR4__DENALI_CTL_226_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_226_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_226__MRSINGLE_DATA_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_CTL_226__MRSINGLE_DATA_1_SHIFT                         0U
#define LPDDR4__DENALI_CTL_226__MRSINGLE_DATA_1_WIDTH                        17U
#define LPDDR4__MRSINGLE_DATA_1__REG DENALI_CTL_226
#define LPDDR4__MRSINGLE_DATA_1__FLD LPDDR4__DENALI_CTL_226__MRSINGLE_DATA_1

#define LPDDR4__DENALI_CTL_227_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_227_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_227__MR3_DATA_F0_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_227__MR3_DATA_F0_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_227__MR3_DATA_F0_0_WIDTH                          17U
#define LPDDR4__MR3_DATA_F0_0__REG DENALI_CTL_227
#define LPDDR4__MR3_DATA_F0_0__FLD LPDDR4__DENALI_CTL_227__MR3_DATA_F0_0

#define LPDDR4__DENALI_CTL_228_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_228_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_228__MR3_DATA_F1_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_228__MR3_DATA_F1_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_228__MR3_DATA_F1_0_WIDTH                          17U
#define LPDDR4__MR3_DATA_F1_0__REG DENALI_CTL_228
#define LPDDR4__MR3_DATA_F1_0__FLD LPDDR4__DENALI_CTL_228__MR3_DATA_F1_0

#define LPDDR4__DENALI_CTL_229_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_229_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_229__MR3_DATA_F2_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_229__MR3_DATA_F2_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_229__MR3_DATA_F2_0_WIDTH                          17U
#define LPDDR4__MR3_DATA_F2_0__REG DENALI_CTL_229
#define LPDDR4__MR3_DATA_F2_0__FLD LPDDR4__DENALI_CTL_229__MR3_DATA_F2_0

#define LPDDR4__DENALI_CTL_230_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_230_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_230__MR3_DATA_F0_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_230__MR3_DATA_F0_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_230__MR3_DATA_F0_1_WIDTH                          17U
#define LPDDR4__MR3_DATA_F0_1__REG DENALI_CTL_230
#define LPDDR4__MR3_DATA_F0_1__FLD LPDDR4__DENALI_CTL_230__MR3_DATA_F0_1

#define LPDDR4__DENALI_CTL_231_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_231_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_231__MR3_DATA_F1_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_231__MR3_DATA_F1_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_231__MR3_DATA_F1_1_WIDTH                          17U
#define LPDDR4__MR3_DATA_F1_1__REG DENALI_CTL_231
#define LPDDR4__MR3_DATA_F1_1__FLD LPDDR4__DENALI_CTL_231__MR3_DATA_F1_1

#define LPDDR4__DENALI_CTL_232_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_232_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_232__MR3_DATA_F2_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_232__MR3_DATA_F2_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_232__MR3_DATA_F2_1_WIDTH                          17U
#define LPDDR4__MR3_DATA_F2_1__REG DENALI_CTL_232
#define LPDDR4__MR3_DATA_F2_1__FLD LPDDR4__DENALI_CTL_232__MR3_DATA_F2_1

#define LPDDR4__DENALI_CTL_233_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_233_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_233__MR4_DATA_F0_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_233__MR4_DATA_F0_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_233__MR4_DATA_F0_0_WIDTH                          17U
#define LPDDR4__MR4_DATA_F0_0__REG DENALI_CTL_233
#define LPDDR4__MR4_DATA_F0_0__FLD LPDDR4__DENALI_CTL_233__MR4_DATA_F0_0

#define LPDDR4__DENALI_CTL_234_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_234_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_234__MR4_DATA_F1_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_234__MR4_DATA_F1_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_234__MR4_DATA_F1_0_WIDTH                          17U
#define LPDDR4__MR4_DATA_F1_0__REG DENALI_CTL_234
#define LPDDR4__MR4_DATA_F1_0__FLD LPDDR4__DENALI_CTL_234__MR4_DATA_F1_0

#define LPDDR4__DENALI_CTL_235_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_235_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_235__MR4_DATA_F2_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_235__MR4_DATA_F2_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_235__MR4_DATA_F2_0_WIDTH                          17U
#define LPDDR4__MR4_DATA_F2_0__REG DENALI_CTL_235
#define LPDDR4__MR4_DATA_F2_0__FLD LPDDR4__DENALI_CTL_235__MR4_DATA_F2_0

#define LPDDR4__DENALI_CTL_236_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_236_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_236__MR4_DATA_F0_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_236__MR4_DATA_F0_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_236__MR4_DATA_F0_1_WIDTH                          17U
#define LPDDR4__MR4_DATA_F0_1__REG DENALI_CTL_236
#define LPDDR4__MR4_DATA_F0_1__FLD LPDDR4__DENALI_CTL_236__MR4_DATA_F0_1

#define LPDDR4__DENALI_CTL_237_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_237_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_237__MR4_DATA_F1_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_237__MR4_DATA_F1_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_237__MR4_DATA_F1_1_WIDTH                          17U
#define LPDDR4__MR4_DATA_F1_1__REG DENALI_CTL_237
#define LPDDR4__MR4_DATA_F1_1__FLD LPDDR4__DENALI_CTL_237__MR4_DATA_F1_1

#define LPDDR4__DENALI_CTL_238_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_238_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_238__MR4_DATA_F2_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_238__MR4_DATA_F2_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_238__MR4_DATA_F2_1_WIDTH                          17U
#define LPDDR4__MR4_DATA_F2_1__REG DENALI_CTL_238
#define LPDDR4__MR4_DATA_F2_1__FLD LPDDR4__DENALI_CTL_238__MR4_DATA_F2_1

#define LPDDR4__DENALI_CTL_239_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_239_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_239__MR5_DATA_F0_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_239__MR5_DATA_F0_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_239__MR5_DATA_F0_0_WIDTH                          17U
#define LPDDR4__MR5_DATA_F0_0__REG DENALI_CTL_239
#define LPDDR4__MR5_DATA_F0_0__FLD LPDDR4__DENALI_CTL_239__MR5_DATA_F0_0

#define LPDDR4__DENALI_CTL_240_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_240_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_240__MR5_DATA_F1_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_240__MR5_DATA_F1_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_240__MR5_DATA_F1_0_WIDTH                          17U
#define LPDDR4__MR5_DATA_F1_0__REG DENALI_CTL_240
#define LPDDR4__MR5_DATA_F1_0__FLD LPDDR4__DENALI_CTL_240__MR5_DATA_F1_0

#define LPDDR4__DENALI_CTL_241_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_241_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_241__MR5_DATA_F2_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_241__MR5_DATA_F2_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_241__MR5_DATA_F2_0_WIDTH                          17U
#define LPDDR4__MR5_DATA_F2_0__REG DENALI_CTL_241
#define LPDDR4__MR5_DATA_F2_0__FLD LPDDR4__DENALI_CTL_241__MR5_DATA_F2_0

#define LPDDR4__DENALI_CTL_242_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_242_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_242__MR5_DATA_F0_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_242__MR5_DATA_F0_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_242__MR5_DATA_F0_1_WIDTH                          17U
#define LPDDR4__MR5_DATA_F0_1__REG DENALI_CTL_242
#define LPDDR4__MR5_DATA_F0_1__FLD LPDDR4__DENALI_CTL_242__MR5_DATA_F0_1

#define LPDDR4__DENALI_CTL_243_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_243_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_243__MR5_DATA_F1_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_243__MR5_DATA_F1_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_243__MR5_DATA_F1_1_WIDTH                          17U
#define LPDDR4__MR5_DATA_F1_1__REG DENALI_CTL_243
#define LPDDR4__MR5_DATA_F1_1__FLD LPDDR4__DENALI_CTL_243__MR5_DATA_F1_1

#define LPDDR4__DENALI_CTL_244_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_244_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_244__MR5_DATA_F2_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_244__MR5_DATA_F2_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_244__MR5_DATA_F2_1_WIDTH                          17U
#define LPDDR4__MR5_DATA_F2_1__REG DENALI_CTL_244
#define LPDDR4__MR5_DATA_F2_1__FLD LPDDR4__DENALI_CTL_244__MR5_DATA_F2_1

#define LPDDR4__DENALI_CTL_245_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_245_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_245__MR6_DATA_F0_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_245__MR6_DATA_F0_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_245__MR6_DATA_F0_0_WIDTH                          17U
#define LPDDR4__MR6_DATA_F0_0__REG DENALI_CTL_245
#define LPDDR4__MR6_DATA_F0_0__FLD LPDDR4__DENALI_CTL_245__MR6_DATA_F0_0

#define LPDDR4__DENALI_CTL_246_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_246_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_246__MR6_DATA_F1_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_246__MR6_DATA_F1_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_246__MR6_DATA_F1_0_WIDTH                          17U
#define LPDDR4__MR6_DATA_F1_0__REG DENALI_CTL_246
#define LPDDR4__MR6_DATA_F1_0__FLD LPDDR4__DENALI_CTL_246__MR6_DATA_F1_0

#define LPDDR4__DENALI_CTL_247_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_247_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_247__MR6_DATA_F2_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_247__MR6_DATA_F2_0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_247__MR6_DATA_F2_0_WIDTH                          17U
#define LPDDR4__MR6_DATA_F2_0__REG DENALI_CTL_247
#define LPDDR4__MR6_DATA_F2_0__FLD LPDDR4__DENALI_CTL_247__MR6_DATA_F2_0

#define LPDDR4__DENALI_CTL_248_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_248_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_248__MR6_DATA_F0_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_248__MR6_DATA_F0_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_248__MR6_DATA_F0_1_WIDTH                          17U
#define LPDDR4__MR6_DATA_F0_1__REG DENALI_CTL_248
#define LPDDR4__MR6_DATA_F0_1__FLD LPDDR4__DENALI_CTL_248__MR6_DATA_F0_1

#define LPDDR4__DENALI_CTL_249_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_249_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_249__MR6_DATA_F1_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_249__MR6_DATA_F1_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_249__MR6_DATA_F1_1_WIDTH                          17U
#define LPDDR4__MR6_DATA_F1_1__REG DENALI_CTL_249
#define LPDDR4__MR6_DATA_F1_1__FLD LPDDR4__DENALI_CTL_249__MR6_DATA_F1_1

#define LPDDR4__DENALI_CTL_250_READ_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_CTL_250_WRITE_MASK                            0xFF01FFFFU
#define LPDDR4__DENALI_CTL_250__MR6_DATA_F2_1_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_CTL_250__MR6_DATA_F2_1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_250__MR6_DATA_F2_1_WIDTH                          17U
#define LPDDR4__MR6_DATA_F2_1__REG DENALI_CTL_250
#define LPDDR4__MR6_DATA_F2_1__FLD LPDDR4__DENALI_CTL_250__MR6_DATA_F2_1

#define LPDDR4__DENALI_CTL_250__MR8_DATA_0_MASK                      0xFF000000U
#define LPDDR4__DENALI_CTL_250__MR8_DATA_0_SHIFT                             24U
#define LPDDR4__DENALI_CTL_250__MR8_DATA_0_WIDTH                              8U
#define LPDDR4__MR8_DATA_0__REG DENALI_CTL_250
#define LPDDR4__MR8_DATA_0__FLD LPDDR4__DENALI_CTL_250__MR8_DATA_0

#define LPDDR4__DENALI_CTL_251_READ_MASK                             0x01FFFFFFU
#define LPDDR4__DENALI_CTL_251_WRITE_MASK                            0x01FFFFFFU
#define LPDDR4__DENALI_CTL_251__MR8_DATA_1_MASK                      0x000000FFU
#define LPDDR4__DENALI_CTL_251__MR8_DATA_1_SHIFT                              0U
#define LPDDR4__DENALI_CTL_251__MR8_DATA_1_WIDTH                              8U
#define LPDDR4__MR8_DATA_1__REG DENALI_CTL_251
#define LPDDR4__MR8_DATA_1__FLD LPDDR4__DENALI_CTL_251__MR8_DATA_1

#define LPDDR4__DENALI_CTL_251__MR10_DATA_F0_0_MASK                  0x01FFFF00U
#define LPDDR4__DENALI_CTL_251__MR10_DATA_F0_0_SHIFT                          8U
#define LPDDR4__DENALI_CTL_251__MR10_DATA_F0_0_WIDTH                         17U
#define LPDDR4__MR10_DATA_F0_0__REG DENALI_CTL_251
#define LPDDR4__MR10_DATA_F0_0__FLD LPDDR4__DENALI_CTL_251__MR10_DATA_F0_0

#define LPDDR4__DENALI_CTL_252_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_252_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_252__MR10_DATA_F1_0_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_252__MR10_DATA_F1_0_SHIFT                          0U
#define LPDDR4__DENALI_CTL_252__MR10_DATA_F1_0_WIDTH                         17U
#define LPDDR4__MR10_DATA_F1_0__REG DENALI_CTL_252
#define LPDDR4__MR10_DATA_F1_0__FLD LPDDR4__DENALI_CTL_252__MR10_DATA_F1_0

#define LPDDR4__DENALI_CTL_253_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_253_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_253__MR10_DATA_F2_0_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_253__MR10_DATA_F2_0_SHIFT                          0U
#define LPDDR4__DENALI_CTL_253__MR10_DATA_F2_0_WIDTH                         17U
#define LPDDR4__MR10_DATA_F2_0__REG DENALI_CTL_253
#define LPDDR4__MR10_DATA_F2_0__FLD LPDDR4__DENALI_CTL_253__MR10_DATA_F2_0

#define LPDDR4__DENALI_CTL_254_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_254_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_254__MR10_DATA_F0_1_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_254__MR10_DATA_F0_1_SHIFT                          0U
#define LPDDR4__DENALI_CTL_254__MR10_DATA_F0_1_WIDTH                         17U
#define LPDDR4__MR10_DATA_F0_1__REG DENALI_CTL_254
#define LPDDR4__MR10_DATA_F0_1__FLD LPDDR4__DENALI_CTL_254__MR10_DATA_F0_1

#define LPDDR4__DENALI_CTL_255_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_255_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_255__MR10_DATA_F1_1_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_255__MR10_DATA_F1_1_SHIFT                          0U
#define LPDDR4__DENALI_CTL_255__MR10_DATA_F1_1_WIDTH                         17U
#define LPDDR4__MR10_DATA_F1_1__REG DENALI_CTL_255
#define LPDDR4__MR10_DATA_F1_1__FLD LPDDR4__DENALI_CTL_255__MR10_DATA_F1_1

#define LPDDR4__DENALI_CTL_256_READ_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_CTL_256_WRITE_MASK                            0xFF01FFFFU
#define LPDDR4__DENALI_CTL_256__MR10_DATA_F2_1_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_256__MR10_DATA_F2_1_SHIFT                          0U
#define LPDDR4__DENALI_CTL_256__MR10_DATA_F2_1_WIDTH                         17U
#define LPDDR4__MR10_DATA_F2_1__REG DENALI_CTL_256
#define LPDDR4__MR10_DATA_F2_1__FLD LPDDR4__DENALI_CTL_256__MR10_DATA_F2_1

#define LPDDR4__DENALI_CTL_256__MR11_DATA_F0_0_MASK                  0xFF000000U
#define LPDDR4__DENALI_CTL_256__MR11_DATA_F0_0_SHIFT                         24U
#define LPDDR4__DENALI_CTL_256__MR11_DATA_F0_0_WIDTH                          8U
#define LPDDR4__MR11_DATA_F0_0__REG DENALI_CTL_256
#define LPDDR4__MR11_DATA_F0_0__FLD LPDDR4__DENALI_CTL_256__MR11_DATA_F0_0

#define LPDDR4__DENALI_CTL_257_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_257_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_257__MR11_DATA_F1_0_MASK                  0x000000FFU
#define LPDDR4__DENALI_CTL_257__MR11_DATA_F1_0_SHIFT                          0U
#define LPDDR4__DENALI_CTL_257__MR11_DATA_F1_0_WIDTH                          8U
#define LPDDR4__MR11_DATA_F1_0__REG DENALI_CTL_257
#define LPDDR4__MR11_DATA_F1_0__FLD LPDDR4__DENALI_CTL_257__MR11_DATA_F1_0

#define LPDDR4__DENALI_CTL_257__MR11_DATA_F2_0_MASK                  0x0000FF00U
#define LPDDR4__DENALI_CTL_257__MR11_DATA_F2_0_SHIFT                          8U
#define LPDDR4__DENALI_CTL_257__MR11_DATA_F2_0_WIDTH                          8U
#define LPDDR4__MR11_DATA_F2_0__REG DENALI_CTL_257
#define LPDDR4__MR11_DATA_F2_0__FLD LPDDR4__DENALI_CTL_257__MR11_DATA_F2_0

#define LPDDR4__DENALI_CTL_257__MR11_DATA_F0_1_MASK                  0x00FF0000U
#define LPDDR4__DENALI_CTL_257__MR11_DATA_F0_1_SHIFT                         16U
#define LPDDR4__DENALI_CTL_257__MR11_DATA_F0_1_WIDTH                          8U
#define LPDDR4__MR11_DATA_F0_1__REG DENALI_CTL_257
#define LPDDR4__MR11_DATA_F0_1__FLD LPDDR4__DENALI_CTL_257__MR11_DATA_F0_1

#define LPDDR4__DENALI_CTL_257__MR11_DATA_F1_1_MASK                  0xFF000000U
#define LPDDR4__DENALI_CTL_257__MR11_DATA_F1_1_SHIFT                         24U
#define LPDDR4__DENALI_CTL_257__MR11_DATA_F1_1_WIDTH                          8U
#define LPDDR4__MR11_DATA_F1_1__REG DENALI_CTL_257
#define LPDDR4__MR11_DATA_F1_1__FLD LPDDR4__DENALI_CTL_257__MR11_DATA_F1_1

#define LPDDR4__DENALI_CTL_258_READ_MASK                             0x01FFFFFFU
#define LPDDR4__DENALI_CTL_258_WRITE_MASK                            0x01FFFFFFU
#define LPDDR4__DENALI_CTL_258__MR11_DATA_F2_1_MASK                  0x000000FFU
#define LPDDR4__DENALI_CTL_258__MR11_DATA_F2_1_SHIFT                          0U
#define LPDDR4__DENALI_CTL_258__MR11_DATA_F2_1_WIDTH                          8U
#define LPDDR4__MR11_DATA_F2_1__REG DENALI_CTL_258
#define LPDDR4__MR11_DATA_F2_1__FLD LPDDR4__DENALI_CTL_258__MR11_DATA_F2_1

#define LPDDR4__DENALI_CTL_258__MR12_DATA_F0_0_MASK                  0x01FFFF00U
#define LPDDR4__DENALI_CTL_258__MR12_DATA_F0_0_SHIFT                          8U
#define LPDDR4__DENALI_CTL_258__MR12_DATA_F0_0_WIDTH                         17U
#define LPDDR4__MR12_DATA_F0_0__REG DENALI_CTL_258
#define LPDDR4__MR12_DATA_F0_0__FLD LPDDR4__DENALI_CTL_258__MR12_DATA_F0_0

#define LPDDR4__DENALI_CTL_259_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_259_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_259__MR12_DATA_F1_0_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_259__MR12_DATA_F1_0_SHIFT                          0U
#define LPDDR4__DENALI_CTL_259__MR12_DATA_F1_0_WIDTH                         17U
#define LPDDR4__MR12_DATA_F1_0__REG DENALI_CTL_259
#define LPDDR4__MR12_DATA_F1_0__FLD LPDDR4__DENALI_CTL_259__MR12_DATA_F1_0

#define LPDDR4__DENALI_CTL_260_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_260_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_260__MR12_DATA_F2_0_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_260__MR12_DATA_F2_0_SHIFT                          0U
#define LPDDR4__DENALI_CTL_260__MR12_DATA_F2_0_WIDTH                         17U
#define LPDDR4__MR12_DATA_F2_0__REG DENALI_CTL_260
#define LPDDR4__MR12_DATA_F2_0__FLD LPDDR4__DENALI_CTL_260__MR12_DATA_F2_0

#define LPDDR4__DENALI_CTL_261_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_261_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_261__MR12_DATA_F0_1_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_261__MR12_DATA_F0_1_SHIFT                          0U
#define LPDDR4__DENALI_CTL_261__MR12_DATA_F0_1_WIDTH                         17U
#define LPDDR4__MR12_DATA_F0_1__REG DENALI_CTL_261
#define LPDDR4__MR12_DATA_F0_1__FLD LPDDR4__DENALI_CTL_261__MR12_DATA_F0_1

#define LPDDR4__DENALI_CTL_262_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_262_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_262__MR12_DATA_F1_1_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_262__MR12_DATA_F1_1_SHIFT                          0U
#define LPDDR4__DENALI_CTL_262__MR12_DATA_F1_1_WIDTH                         17U
#define LPDDR4__MR12_DATA_F1_1__REG DENALI_CTL_262
#define LPDDR4__MR12_DATA_F1_1__FLD LPDDR4__DENALI_CTL_262__MR12_DATA_F1_1

#define LPDDR4__DENALI_CTL_263_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_263_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_263__MR12_DATA_F2_1_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_263__MR12_DATA_F2_1_SHIFT                          0U
#define LPDDR4__DENALI_CTL_263__MR12_DATA_F2_1_WIDTH                         17U
#define LPDDR4__MR12_DATA_F2_1__REG DENALI_CTL_263
#define LPDDR4__MR12_DATA_F2_1__FLD LPDDR4__DENALI_CTL_263__MR12_DATA_F2_1

#define LPDDR4__DENALI_CTL_264_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_264_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_264__MR13_DATA_0_MASK                     0x0001FFFFU
#define LPDDR4__DENALI_CTL_264__MR13_DATA_0_SHIFT                             0U
#define LPDDR4__DENALI_CTL_264__MR13_DATA_0_WIDTH                            17U
#define LPDDR4__MR13_DATA_0__REG DENALI_CTL_264
#define LPDDR4__MR13_DATA_0__FLD LPDDR4__DENALI_CTL_264__MR13_DATA_0

#define LPDDR4__DENALI_CTL_265_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_265_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_265__MR13_DATA_1_MASK                     0x0001FFFFU
#define LPDDR4__DENALI_CTL_265__MR13_DATA_1_SHIFT                             0U
#define LPDDR4__DENALI_CTL_265__MR13_DATA_1_WIDTH                            17U
#define LPDDR4__MR13_DATA_1__REG DENALI_CTL_265
#define LPDDR4__MR13_DATA_1__FLD LPDDR4__DENALI_CTL_265__MR13_DATA_1

#define LPDDR4__DENALI_CTL_266_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_266_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_266__MR14_DATA_F0_0_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_266__MR14_DATA_F0_0_SHIFT                          0U
#define LPDDR4__DENALI_CTL_266__MR14_DATA_F0_0_WIDTH                         17U
#define LPDDR4__MR14_DATA_F0_0__REG DENALI_CTL_266
#define LPDDR4__MR14_DATA_F0_0__FLD LPDDR4__DENALI_CTL_266__MR14_DATA_F0_0

#define LPDDR4__DENALI_CTL_267_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_267_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_267__MR14_DATA_F1_0_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_267__MR14_DATA_F1_0_SHIFT                          0U
#define LPDDR4__DENALI_CTL_267__MR14_DATA_F1_0_WIDTH                         17U
#define LPDDR4__MR14_DATA_F1_0__REG DENALI_CTL_267
#define LPDDR4__MR14_DATA_F1_0__FLD LPDDR4__DENALI_CTL_267__MR14_DATA_F1_0

#define LPDDR4__DENALI_CTL_268_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_268_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_268__MR14_DATA_F2_0_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_268__MR14_DATA_F2_0_SHIFT                          0U
#define LPDDR4__DENALI_CTL_268__MR14_DATA_F2_0_WIDTH                         17U
#define LPDDR4__MR14_DATA_F2_0__REG DENALI_CTL_268
#define LPDDR4__MR14_DATA_F2_0__FLD LPDDR4__DENALI_CTL_268__MR14_DATA_F2_0

#define LPDDR4__DENALI_CTL_269_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_269_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_269__MR14_DATA_F0_1_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_269__MR14_DATA_F0_1_SHIFT                          0U
#define LPDDR4__DENALI_CTL_269__MR14_DATA_F0_1_WIDTH                         17U
#define LPDDR4__MR14_DATA_F0_1__REG DENALI_CTL_269
#define LPDDR4__MR14_DATA_F0_1__FLD LPDDR4__DENALI_CTL_269__MR14_DATA_F0_1

#define LPDDR4__DENALI_CTL_270_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_270_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_270__MR14_DATA_F1_1_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_270__MR14_DATA_F1_1_SHIFT                          0U
#define LPDDR4__DENALI_CTL_270__MR14_DATA_F1_1_WIDTH                         17U
#define LPDDR4__MR14_DATA_F1_1__REG DENALI_CTL_270
#define LPDDR4__MR14_DATA_F1_1__FLD LPDDR4__DENALI_CTL_270__MR14_DATA_F1_1

#define LPDDR4__DENALI_CTL_271_READ_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_CTL_271_WRITE_MASK                            0xFF01FFFFU
#define LPDDR4__DENALI_CTL_271__MR14_DATA_F2_1_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_271__MR14_DATA_F2_1_SHIFT                          0U
#define LPDDR4__DENALI_CTL_271__MR14_DATA_F2_1_WIDTH                         17U
#define LPDDR4__MR14_DATA_F2_1__REG DENALI_CTL_271
#define LPDDR4__MR14_DATA_F2_1__FLD LPDDR4__DENALI_CTL_271__MR14_DATA_F2_1

#define LPDDR4__DENALI_CTL_271__MR16_DATA_0_MASK                     0xFF000000U
#define LPDDR4__DENALI_CTL_271__MR16_DATA_0_SHIFT                            24U
#define LPDDR4__DENALI_CTL_271__MR16_DATA_0_WIDTH                             8U
#define LPDDR4__MR16_DATA_0__REG DENALI_CTL_271
#define LPDDR4__MR16_DATA_0__FLD LPDDR4__DENALI_CTL_271__MR16_DATA_0

#define LPDDR4__DENALI_CTL_272_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_272_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_272__MR16_DATA_1_MASK                     0x000000FFU
#define LPDDR4__DENALI_CTL_272__MR16_DATA_1_SHIFT                             0U
#define LPDDR4__DENALI_CTL_272__MR16_DATA_1_WIDTH                             8U
#define LPDDR4__MR16_DATA_1__REG DENALI_CTL_272
#define LPDDR4__MR16_DATA_1__FLD LPDDR4__DENALI_CTL_272__MR16_DATA_1

#define LPDDR4__DENALI_CTL_272__MR17_DATA_0_MASK                     0x0000FF00U
#define LPDDR4__DENALI_CTL_272__MR17_DATA_0_SHIFT                             8U
#define LPDDR4__DENALI_CTL_272__MR17_DATA_0_WIDTH                             8U
#define LPDDR4__MR17_DATA_0__REG DENALI_CTL_272
#define LPDDR4__MR17_DATA_0__FLD LPDDR4__DENALI_CTL_272__MR17_DATA_0

#define LPDDR4__DENALI_CTL_272__MR17_DATA_1_MASK                     0x00FF0000U
#define LPDDR4__DENALI_CTL_272__MR17_DATA_1_SHIFT                            16U
#define LPDDR4__DENALI_CTL_272__MR17_DATA_1_WIDTH                             8U
#define LPDDR4__MR17_DATA_1__REG DENALI_CTL_272
#define LPDDR4__MR17_DATA_1__FLD LPDDR4__DENALI_CTL_272__MR17_DATA_1

#define LPDDR4__DENALI_CTL_272__MR20_DATA_0_MASK                     0xFF000000U
#define LPDDR4__DENALI_CTL_272__MR20_DATA_0_SHIFT                            24U
#define LPDDR4__DENALI_CTL_272__MR20_DATA_0_WIDTH                             8U
#define LPDDR4__MR20_DATA_0__REG DENALI_CTL_272
#define LPDDR4__MR20_DATA_0__FLD LPDDR4__DENALI_CTL_272__MR20_DATA_0

#define LPDDR4__DENALI_CTL_273_READ_MASK                             0x01FFFFFFU
#define LPDDR4__DENALI_CTL_273_WRITE_MASK                            0x01FFFFFFU
#define LPDDR4__DENALI_CTL_273__MR20_DATA_1_MASK                     0x000000FFU
#define LPDDR4__DENALI_CTL_273__MR20_DATA_1_SHIFT                             0U
#define LPDDR4__DENALI_CTL_273__MR20_DATA_1_WIDTH                             8U
#define LPDDR4__MR20_DATA_1__REG DENALI_CTL_273
#define LPDDR4__MR20_DATA_1__FLD LPDDR4__DENALI_CTL_273__MR20_DATA_1

#define LPDDR4__DENALI_CTL_273__MR22_DATA_F0_0_MASK                  0x01FFFF00U
#define LPDDR4__DENALI_CTL_273__MR22_DATA_F0_0_SHIFT                          8U
#define LPDDR4__DENALI_CTL_273__MR22_DATA_F0_0_WIDTH                         17U
#define LPDDR4__MR22_DATA_F0_0__REG DENALI_CTL_273
#define LPDDR4__MR22_DATA_F0_0__FLD LPDDR4__DENALI_CTL_273__MR22_DATA_F0_0

#define LPDDR4__DENALI_CTL_274_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_274_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_274__MR22_DATA_F1_0_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_274__MR22_DATA_F1_0_SHIFT                          0U
#define LPDDR4__DENALI_CTL_274__MR22_DATA_F1_0_WIDTH                         17U
#define LPDDR4__MR22_DATA_F1_0__REG DENALI_CTL_274
#define LPDDR4__MR22_DATA_F1_0__FLD LPDDR4__DENALI_CTL_274__MR22_DATA_F1_0

#define LPDDR4__DENALI_CTL_275_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_275_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_275__MR22_DATA_F2_0_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_275__MR22_DATA_F2_0_SHIFT                          0U
#define LPDDR4__DENALI_CTL_275__MR22_DATA_F2_0_WIDTH                         17U
#define LPDDR4__MR22_DATA_F2_0__REG DENALI_CTL_275
#define LPDDR4__MR22_DATA_F2_0__FLD LPDDR4__DENALI_CTL_275__MR22_DATA_F2_0

#define LPDDR4__DENALI_CTL_276_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_276_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_276__MR22_DATA_F0_1_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_276__MR22_DATA_F0_1_SHIFT                          0U
#define LPDDR4__DENALI_CTL_276__MR22_DATA_F0_1_WIDTH                         17U
#define LPDDR4__MR22_DATA_F0_1__REG DENALI_CTL_276
#define LPDDR4__MR22_DATA_F0_1__FLD LPDDR4__DENALI_CTL_276__MR22_DATA_F0_1

#define LPDDR4__DENALI_CTL_277_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_277_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_277__MR22_DATA_F1_1_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_277__MR22_DATA_F1_1_SHIFT                          0U
#define LPDDR4__DENALI_CTL_277__MR22_DATA_F1_1_WIDTH                         17U
#define LPDDR4__MR22_DATA_F1_1__REG DENALI_CTL_277
#define LPDDR4__MR22_DATA_F1_1__FLD LPDDR4__DENALI_CTL_277__MR22_DATA_F1_1

#define LPDDR4__DENALI_CTL_278_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_CTL_278_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_CTL_278__MR22_DATA_F2_1_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_CTL_278__MR22_DATA_F2_1_SHIFT                          0U
#define LPDDR4__DENALI_CTL_278__MR22_DATA_F2_1_WIDTH                         17U
#define LPDDR4__MR22_DATA_F2_1__REG DENALI_CTL_278
#define LPDDR4__MR22_DATA_F2_1__FLD LPDDR4__DENALI_CTL_278__MR22_DATA_F2_1

#define LPDDR4__DENALI_CTL_279_READ_MASK                             0x0101FFFFU
#define LPDDR4__DENALI_CTL_279_WRITE_MASK                            0x0101FFFFU
#define LPDDR4__DENALI_CTL_279__MR23_DATA_MASK                       0x0001FFFFU
#define LPDDR4__DENALI_CTL_279__MR23_DATA_SHIFT                               0U
#define LPDDR4__DENALI_CTL_279__MR23_DATA_WIDTH                              17U
#define LPDDR4__MR23_DATA__REG DENALI_CTL_279
#define LPDDR4__MR23_DATA__FLD LPDDR4__DENALI_CTL_279__MR23_DATA

#define LPDDR4__DENALI_CTL_279__MR_FSP_DATA_VALID_F0_MASK            0x01000000U
#define LPDDR4__DENALI_CTL_279__MR_FSP_DATA_VALID_F0_SHIFT                   24U
#define LPDDR4__DENALI_CTL_279__MR_FSP_DATA_VALID_F0_WIDTH                    1U
#define LPDDR4__DENALI_CTL_279__MR_FSP_DATA_VALID_F0_WOCLR                    0U
#define LPDDR4__DENALI_CTL_279__MR_FSP_DATA_VALID_F0_WOSET                    0U
#define LPDDR4__MR_FSP_DATA_VALID_F0__REG DENALI_CTL_279
#define LPDDR4__MR_FSP_DATA_VALID_F0__FLD LPDDR4__DENALI_CTL_279__MR_FSP_DATA_VALID_F0

#define LPDDR4__DENALI_CTL_280_READ_MASK                             0x01010101U
#define LPDDR4__DENALI_CTL_280_WRITE_MASK                            0x01010101U
#define LPDDR4__DENALI_CTL_280__MR_FSP_DATA_VALID_F1_MASK            0x00000001U
#define LPDDR4__DENALI_CTL_280__MR_FSP_DATA_VALID_F1_SHIFT                    0U
#define LPDDR4__DENALI_CTL_280__MR_FSP_DATA_VALID_F1_WIDTH                    1U
#define LPDDR4__DENALI_CTL_280__MR_FSP_DATA_VALID_F1_WOCLR                    0U
#define LPDDR4__DENALI_CTL_280__MR_FSP_DATA_VALID_F1_WOSET                    0U
#define LPDDR4__MR_FSP_DATA_VALID_F1__REG DENALI_CTL_280
#define LPDDR4__MR_FSP_DATA_VALID_F1__FLD LPDDR4__DENALI_CTL_280__MR_FSP_DATA_VALID_F1

#define LPDDR4__DENALI_CTL_280__MR_FSP_DATA_VALID_F2_MASK            0x00000100U
#define LPDDR4__DENALI_CTL_280__MR_FSP_DATA_VALID_F2_SHIFT                    8U
#define LPDDR4__DENALI_CTL_280__MR_FSP_DATA_VALID_F2_WIDTH                    1U
#define LPDDR4__DENALI_CTL_280__MR_FSP_DATA_VALID_F2_WOCLR                    0U
#define LPDDR4__DENALI_CTL_280__MR_FSP_DATA_VALID_F2_WOSET                    0U
#define LPDDR4__MR_FSP_DATA_VALID_F2__REG DENALI_CTL_280
#define LPDDR4__MR_FSP_DATA_VALID_F2__FLD LPDDR4__DENALI_CTL_280__MR_FSP_DATA_VALID_F2

#define LPDDR4__DENALI_CTL_280__DFS_FSP_INSYNC_ACTIVE_MASK           0x00010000U
#define LPDDR4__DENALI_CTL_280__DFS_FSP_INSYNC_ACTIVE_SHIFT                  16U
#define LPDDR4__DENALI_CTL_280__DFS_FSP_INSYNC_ACTIVE_WIDTH                   1U
#define LPDDR4__DENALI_CTL_280__DFS_FSP_INSYNC_ACTIVE_WOCLR                   0U
#define LPDDR4__DENALI_CTL_280__DFS_FSP_INSYNC_ACTIVE_WOSET                   0U
#define LPDDR4__DFS_FSP_INSYNC_ACTIVE__REG DENALI_CTL_280
#define LPDDR4__DFS_FSP_INSYNC_ACTIVE__FLD LPDDR4__DENALI_CTL_280__DFS_FSP_INSYNC_ACTIVE

#define LPDDR4__DENALI_CTL_280__DFS_FSP_INSYNC_INACTIVE_MASK         0x01000000U
#define LPDDR4__DENALI_CTL_280__DFS_FSP_INSYNC_INACTIVE_SHIFT                24U
#define LPDDR4__DENALI_CTL_280__DFS_FSP_INSYNC_INACTIVE_WIDTH                 1U
#define LPDDR4__DENALI_CTL_280__DFS_FSP_INSYNC_INACTIVE_WOCLR                 0U
#define LPDDR4__DENALI_CTL_280__DFS_FSP_INSYNC_INACTIVE_WOSET                 0U
#define LPDDR4__DFS_FSP_INSYNC_INACTIVE__REG DENALI_CTL_280
#define LPDDR4__DFS_FSP_INSYNC_INACTIVE__FLD LPDDR4__DENALI_CTL_280__DFS_FSP_INSYNC_INACTIVE

#define LPDDR4__DENALI_CTL_281_READ_MASK                             0x01010101U
#define LPDDR4__DENALI_CTL_281_WRITE_MASK                            0x01010101U
#define LPDDR4__DENALI_CTL_281__FSP_PHY_UPDATE_MRW_MASK              0x00000001U
#define LPDDR4__DENALI_CTL_281__FSP_PHY_UPDATE_MRW_SHIFT                      0U
#define LPDDR4__DENALI_CTL_281__FSP_PHY_UPDATE_MRW_WIDTH                      1U
#define LPDDR4__DENALI_CTL_281__FSP_PHY_UPDATE_MRW_WOCLR                      0U
#define LPDDR4__DENALI_CTL_281__FSP_PHY_UPDATE_MRW_WOSET                      0U
#define LPDDR4__FSP_PHY_UPDATE_MRW__REG DENALI_CTL_281
#define LPDDR4__FSP_PHY_UPDATE_MRW__FLD LPDDR4__DENALI_CTL_281__FSP_PHY_UPDATE_MRW

#define LPDDR4__DENALI_CTL_281__DFS_ALWAYS_WRITE_FSP_MASK            0x00000100U
#define LPDDR4__DENALI_CTL_281__DFS_ALWAYS_WRITE_FSP_SHIFT                    8U
#define LPDDR4__DENALI_CTL_281__DFS_ALWAYS_WRITE_FSP_WIDTH                    1U
#define LPDDR4__DENALI_CTL_281__DFS_ALWAYS_WRITE_FSP_WOCLR                    0U
#define LPDDR4__DENALI_CTL_281__DFS_ALWAYS_WRITE_FSP_WOSET                    0U
#define LPDDR4__DFS_ALWAYS_WRITE_FSP__REG DENALI_CTL_281
#define LPDDR4__DFS_ALWAYS_WRITE_FSP__FLD LPDDR4__DENALI_CTL_281__DFS_ALWAYS_WRITE_FSP

#define LPDDR4__DENALI_CTL_281__FSP_STATUS_MASK                      0x00010000U
#define LPDDR4__DENALI_CTL_281__FSP_STATUS_SHIFT                             16U
#define LPDDR4__DENALI_CTL_281__FSP_STATUS_WIDTH                              1U
#define LPDDR4__DENALI_CTL_281__FSP_STATUS_WOCLR                              0U
#define LPDDR4__DENALI_CTL_281__FSP_STATUS_WOSET                              0U
#define LPDDR4__FSP_STATUS__REG DENALI_CTL_281
#define LPDDR4__FSP_STATUS__FLD LPDDR4__DENALI_CTL_281__FSP_STATUS

#define LPDDR4__DENALI_CTL_281__FSP_OP_CURRENT_MASK                  0x01000000U
#define LPDDR4__DENALI_CTL_281__FSP_OP_CURRENT_SHIFT                         24U
#define LPDDR4__DENALI_CTL_281__FSP_OP_CURRENT_WIDTH                          1U
#define LPDDR4__DENALI_CTL_281__FSP_OP_CURRENT_WOCLR                          0U
#define LPDDR4__DENALI_CTL_281__FSP_OP_CURRENT_WOSET                          0U
#define LPDDR4__FSP_OP_CURRENT__REG DENALI_CTL_281
#define LPDDR4__FSP_OP_CURRENT__FLD LPDDR4__DENALI_CTL_281__FSP_OP_CURRENT

#define LPDDR4__DENALI_CTL_282_READ_MASK                             0x03010101U
#define LPDDR4__DENALI_CTL_282_WRITE_MASK                            0x03010101U
#define LPDDR4__DENALI_CTL_282__FSP_WR_CURRENT_MASK                  0x00000001U
#define LPDDR4__DENALI_CTL_282__FSP_WR_CURRENT_SHIFT                          0U
#define LPDDR4__DENALI_CTL_282__FSP_WR_CURRENT_WIDTH                          1U
#define LPDDR4__DENALI_CTL_282__FSP_WR_CURRENT_WOCLR                          0U
#define LPDDR4__DENALI_CTL_282__FSP_WR_CURRENT_WOSET                          0U
#define LPDDR4__FSP_WR_CURRENT__REG DENALI_CTL_282
#define LPDDR4__FSP_WR_CURRENT__FLD LPDDR4__DENALI_CTL_282__FSP_WR_CURRENT

#define LPDDR4__DENALI_CTL_282__FSP0_FRC_VALID_MASK                  0x00000100U
#define LPDDR4__DENALI_CTL_282__FSP0_FRC_VALID_SHIFT                          8U
#define LPDDR4__DENALI_CTL_282__FSP0_FRC_VALID_WIDTH                          1U
#define LPDDR4__DENALI_CTL_282__FSP0_FRC_VALID_WOCLR                          0U
#define LPDDR4__DENALI_CTL_282__FSP0_FRC_VALID_WOSET                          0U
#define LPDDR4__FSP0_FRC_VALID__REG DENALI_CTL_282
#define LPDDR4__FSP0_FRC_VALID__FLD LPDDR4__DENALI_CTL_282__FSP0_FRC_VALID

#define LPDDR4__DENALI_CTL_282__FSP1_FRC_VALID_MASK                  0x00010000U
#define LPDDR4__DENALI_CTL_282__FSP1_FRC_VALID_SHIFT                         16U
#define LPDDR4__DENALI_CTL_282__FSP1_FRC_VALID_WIDTH                          1U
#define LPDDR4__DENALI_CTL_282__FSP1_FRC_VALID_WOCLR                          0U
#define LPDDR4__DENALI_CTL_282__FSP1_FRC_VALID_WOSET                          0U
#define LPDDR4__FSP1_FRC_VALID__REG DENALI_CTL_282
#define LPDDR4__FSP1_FRC_VALID__FLD LPDDR4__DENALI_CTL_282__FSP1_FRC_VALID

#define LPDDR4__DENALI_CTL_282__FSP0_FRC_MASK                        0x03000000U
#define LPDDR4__DENALI_CTL_282__FSP0_FRC_SHIFT                               24U
#define LPDDR4__DENALI_CTL_282__FSP0_FRC_WIDTH                                2U
#define LPDDR4__FSP0_FRC__REG DENALI_CTL_282
#define LPDDR4__FSP0_FRC__FLD LPDDR4__DENALI_CTL_282__FSP0_FRC

#define LPDDR4__DENALI_CTL_283_READ_MASK                             0x3F030003U
#define LPDDR4__DENALI_CTL_283_WRITE_MASK                            0x3F030003U
#define LPDDR4__DENALI_CTL_283__FSP1_FRC_MASK                        0x00000003U
#define LPDDR4__DENALI_CTL_283__FSP1_FRC_SHIFT                                0U
#define LPDDR4__DENALI_CTL_283__FSP1_FRC_WIDTH                                2U
#define LPDDR4__FSP1_FRC__REG DENALI_CTL_283
#define LPDDR4__FSP1_FRC__FLD LPDDR4__DENALI_CTL_283__FSP1_FRC

#define LPDDR4__DENALI_CTL_283__BIST_GO_MASK                         0x00000100U
#define LPDDR4__DENALI_CTL_283__BIST_GO_SHIFT                                 8U
#define LPDDR4__DENALI_CTL_283__BIST_GO_WIDTH                                 1U
#define LPDDR4__DENALI_CTL_283__BIST_GO_WOCLR                                 0U
#define LPDDR4__DENALI_CTL_283__BIST_GO_WOSET                                 0U
#define LPDDR4__BIST_GO__REG DENALI_CTL_283
#define LPDDR4__BIST_GO__FLD LPDDR4__DENALI_CTL_283__BIST_GO

#define LPDDR4__DENALI_CTL_283__BIST_RESULT_MASK                     0x00030000U
#define LPDDR4__DENALI_CTL_283__BIST_RESULT_SHIFT                            16U
#define LPDDR4__DENALI_CTL_283__BIST_RESULT_WIDTH                             2U
#define LPDDR4__BIST_RESULT__REG DENALI_CTL_283
#define LPDDR4__BIST_RESULT__FLD LPDDR4__DENALI_CTL_283__BIST_RESULT

#define LPDDR4__DENALI_CTL_283__ADDR_SPACE_MASK                      0x3F000000U
#define LPDDR4__DENALI_CTL_283__ADDR_SPACE_SHIFT                             24U
#define LPDDR4__DENALI_CTL_283__ADDR_SPACE_WIDTH                              6U
#define LPDDR4__ADDR_SPACE__REG DENALI_CTL_283
#define LPDDR4__ADDR_SPACE__FLD LPDDR4__DENALI_CTL_283__ADDR_SPACE

#define LPDDR4__DENALI_CTL_284_READ_MASK                             0x00000101U
#define LPDDR4__DENALI_CTL_284_WRITE_MASK                            0x00000101U
#define LPDDR4__DENALI_CTL_284__BIST_DATA_CHECK_MASK                 0x00000001U
#define LPDDR4__DENALI_CTL_284__BIST_DATA_CHECK_SHIFT                         0U
#define LPDDR4__DENALI_CTL_284__BIST_DATA_CHECK_WIDTH                         1U
#define LPDDR4__DENALI_CTL_284__BIST_DATA_CHECK_WOCLR                         0U
#define LPDDR4__DENALI_CTL_284__BIST_DATA_CHECK_WOSET                         0U
#define LPDDR4__BIST_DATA_CHECK__REG DENALI_CTL_284
#define LPDDR4__BIST_DATA_CHECK__FLD LPDDR4__DENALI_CTL_284__BIST_DATA_CHECK

#define LPDDR4__DENALI_CTL_284__BIST_ADDR_CHECK_MASK                 0x00000100U
#define LPDDR4__DENALI_CTL_284__BIST_ADDR_CHECK_SHIFT                         8U
#define LPDDR4__DENALI_CTL_284__BIST_ADDR_CHECK_WIDTH                         1U
#define LPDDR4__DENALI_CTL_284__BIST_ADDR_CHECK_WOCLR                         0U
#define LPDDR4__DENALI_CTL_284__BIST_ADDR_CHECK_WOSET                         0U
#define LPDDR4__BIST_ADDR_CHECK__REG DENALI_CTL_284
#define LPDDR4__BIST_ADDR_CHECK__FLD LPDDR4__DENALI_CTL_284__BIST_ADDR_CHECK

#define LPDDR4__DENALI_CTL_285_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_285_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_285__BIST_START_ADDRESS_0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_285__BIST_START_ADDRESS_0_SHIFT                    0U
#define LPDDR4__DENALI_CTL_285__BIST_START_ADDRESS_0_WIDTH                   32U
#define LPDDR4__BIST_START_ADDRESS_0__REG DENALI_CTL_285
#define LPDDR4__BIST_START_ADDRESS_0__FLD LPDDR4__DENALI_CTL_285__BIST_START_ADDRESS_0

#define LPDDR4__DENALI_CTL_286_READ_MASK                             0x00000007U
#define LPDDR4__DENALI_CTL_286_WRITE_MASK                            0x00000007U
#define LPDDR4__DENALI_CTL_286__BIST_START_ADDRESS_1_MASK            0x00000007U
#define LPDDR4__DENALI_CTL_286__BIST_START_ADDRESS_1_SHIFT                    0U
#define LPDDR4__DENALI_CTL_286__BIST_START_ADDRESS_1_WIDTH                    3U
#define LPDDR4__BIST_START_ADDRESS_1__REG DENALI_CTL_286
#define LPDDR4__BIST_START_ADDRESS_1__FLD LPDDR4__DENALI_CTL_286__BIST_START_ADDRESS_1

#define LPDDR4__DENALI_CTL_287_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_287_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_287__BIST_DATA_MASK_0_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_287__BIST_DATA_MASK_0_SHIFT                        0U
#define LPDDR4__DENALI_CTL_287__BIST_DATA_MASK_0_WIDTH                       32U
#define LPDDR4__BIST_DATA_MASK_0__REG DENALI_CTL_287
#define LPDDR4__BIST_DATA_MASK_0__FLD LPDDR4__DENALI_CTL_287__BIST_DATA_MASK_0

#define LPDDR4__DENALI_CTL_288_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_288_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_288__BIST_DATA_MASK_1_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_288__BIST_DATA_MASK_1_SHIFT                        0U
#define LPDDR4__DENALI_CTL_288__BIST_DATA_MASK_1_WIDTH                       32U
#define LPDDR4__BIST_DATA_MASK_1__REG DENALI_CTL_288
#define LPDDR4__BIST_DATA_MASK_1__FLD LPDDR4__DENALI_CTL_288__BIST_DATA_MASK_1

#define LPDDR4__DENALI_CTL_289_READ_MASK                             0x00000007U
#define LPDDR4__DENALI_CTL_289_WRITE_MASK                            0x00000007U
#define LPDDR4__DENALI_CTL_289__BIST_TEST_MODE_MASK                  0x00000007U
#define LPDDR4__DENALI_CTL_289__BIST_TEST_MODE_SHIFT                          0U
#define LPDDR4__DENALI_CTL_289__BIST_TEST_MODE_WIDTH                          3U
#define LPDDR4__BIST_TEST_MODE__REG DENALI_CTL_289
#define LPDDR4__BIST_TEST_MODE__FLD LPDDR4__DENALI_CTL_289__BIST_TEST_MODE

#define LPDDR4__DENALI_CTL_290_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_290_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_290__BIST_DATA_PATTERN_0_MASK             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_290__BIST_DATA_PATTERN_0_SHIFT                     0U
#define LPDDR4__DENALI_CTL_290__BIST_DATA_PATTERN_0_WIDTH                    32U
#define LPDDR4__BIST_DATA_PATTERN_0__REG DENALI_CTL_290
#define LPDDR4__BIST_DATA_PATTERN_0__FLD LPDDR4__DENALI_CTL_290__BIST_DATA_PATTERN_0

#define LPDDR4__DENALI_CTL_291_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_291_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_291__BIST_DATA_PATTERN_1_MASK             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_291__BIST_DATA_PATTERN_1_SHIFT                     0U
#define LPDDR4__DENALI_CTL_291__BIST_DATA_PATTERN_1_WIDTH                    32U
#define LPDDR4__BIST_DATA_PATTERN_1__REG DENALI_CTL_291
#define LPDDR4__BIST_DATA_PATTERN_1__FLD LPDDR4__DENALI_CTL_291__BIST_DATA_PATTERN_1

#define LPDDR4__DENALI_CTL_292_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_292_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_292__BIST_DATA_PATTERN_2_MASK             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_292__BIST_DATA_PATTERN_2_SHIFT                     0U
#define LPDDR4__DENALI_CTL_292__BIST_DATA_PATTERN_2_WIDTH                    32U
#define LPDDR4__BIST_DATA_PATTERN_2__REG DENALI_CTL_292
#define LPDDR4__BIST_DATA_PATTERN_2__FLD LPDDR4__DENALI_CTL_292__BIST_DATA_PATTERN_2

#define LPDDR4__DENALI_CTL_293_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_293_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_293__BIST_DATA_PATTERN_3_MASK             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_293__BIST_DATA_PATTERN_3_SHIFT                     0U
#define LPDDR4__DENALI_CTL_293__BIST_DATA_PATTERN_3_WIDTH                    32U
#define LPDDR4__BIST_DATA_PATTERN_3__REG DENALI_CTL_293
#define LPDDR4__BIST_DATA_PATTERN_3__FLD LPDDR4__DENALI_CTL_293__BIST_DATA_PATTERN_3

#define LPDDR4__DENALI_CTL_294_READ_MASK                             0x000FFF01U
#define LPDDR4__DENALI_CTL_294_WRITE_MASK                            0x000FFF01U
#define LPDDR4__DENALI_CTL_294__BIST_RET_STATE_MASK                  0x00000001U
#define LPDDR4__DENALI_CTL_294__BIST_RET_STATE_SHIFT                          0U
#define LPDDR4__DENALI_CTL_294__BIST_RET_STATE_WIDTH                          1U
#define LPDDR4__DENALI_CTL_294__BIST_RET_STATE_WOCLR                          0U
#define LPDDR4__DENALI_CTL_294__BIST_RET_STATE_WOSET                          0U
#define LPDDR4__BIST_RET_STATE__REG DENALI_CTL_294
#define LPDDR4__BIST_RET_STATE__FLD LPDDR4__DENALI_CTL_294__BIST_RET_STATE

#define LPDDR4__DENALI_CTL_294__BIST_ERR_STOP_MASK                   0x000FFF00U
#define LPDDR4__DENALI_CTL_294__BIST_ERR_STOP_SHIFT                           8U
#define LPDDR4__DENALI_CTL_294__BIST_ERR_STOP_WIDTH                          12U
#define LPDDR4__BIST_ERR_STOP__REG DENALI_CTL_294
#define LPDDR4__BIST_ERR_STOP__FLD LPDDR4__DENALI_CTL_294__BIST_ERR_STOP

#define LPDDR4__DENALI_CTL_295_READ_MASK                             0x1F000FFFU
#define LPDDR4__DENALI_CTL_295_WRITE_MASK                            0x1F000FFFU
#define LPDDR4__DENALI_CTL_295__BIST_ERR_COUNT_MASK                  0x00000FFFU
#define LPDDR4__DENALI_CTL_295__BIST_ERR_COUNT_SHIFT                          0U
#define LPDDR4__DENALI_CTL_295__BIST_ERR_COUNT_WIDTH                         12U
#define LPDDR4__BIST_ERR_COUNT__REG DENALI_CTL_295
#define LPDDR4__BIST_ERR_COUNT__FLD LPDDR4__DENALI_CTL_295__BIST_ERR_COUNT

#define LPDDR4__DENALI_CTL_295__BIST_RET_STATE_EXIT_MASK             0x00010000U
#define LPDDR4__DENALI_CTL_295__BIST_RET_STATE_EXIT_SHIFT                    16U
#define LPDDR4__DENALI_CTL_295__BIST_RET_STATE_EXIT_WIDTH                     1U
#define LPDDR4__DENALI_CTL_295__BIST_RET_STATE_EXIT_WOCLR                     0U
#define LPDDR4__DENALI_CTL_295__BIST_RET_STATE_EXIT_WOSET                     0U
#define LPDDR4__BIST_RET_STATE_EXIT__REG DENALI_CTL_295
#define LPDDR4__BIST_RET_STATE_EXIT__FLD LPDDR4__DENALI_CTL_295__BIST_RET_STATE_EXIT

#define LPDDR4__DENALI_CTL_295__LONG_COUNT_MASK_MASK                 0x1F000000U
#define LPDDR4__DENALI_CTL_295__LONG_COUNT_MASK_SHIFT                        24U
#define LPDDR4__DENALI_CTL_295__LONG_COUNT_MASK_WIDTH                         5U
#define LPDDR4__LONG_COUNT_MASK__REG DENALI_CTL_295
#define LPDDR4__LONG_COUNT_MASK__FLD LPDDR4__DENALI_CTL_295__LONG_COUNT_MASK

#define LPDDR4__DENALI_CTL_296_READ_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_CTL_296_WRITE_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_CTL_296__AREF_NORM_THRESHOLD_MASK             0x0000001FU
#define LPDDR4__DENALI_CTL_296__AREF_NORM_THRESHOLD_SHIFT                     0U
#define LPDDR4__DENALI_CTL_296__AREF_NORM_THRESHOLD_WIDTH                     5U
#define LPDDR4__AREF_NORM_THRESHOLD__REG DENALI_CTL_296
#define LPDDR4__AREF_NORM_THRESHOLD__FLD LPDDR4__DENALI_CTL_296__AREF_NORM_THRESHOLD

#define LPDDR4__DENALI_CTL_296__AREF_HIGH_THRESHOLD_MASK             0x00001F00U
#define LPDDR4__DENALI_CTL_296__AREF_HIGH_THRESHOLD_SHIFT                     8U
#define LPDDR4__DENALI_CTL_296__AREF_HIGH_THRESHOLD_WIDTH                     5U
#define LPDDR4__AREF_HIGH_THRESHOLD__REG DENALI_CTL_296
#define LPDDR4__AREF_HIGH_THRESHOLD__FLD LPDDR4__DENALI_CTL_296__AREF_HIGH_THRESHOLD

#define LPDDR4__DENALI_CTL_296__AREF_MAX_DEFICIT_MASK                0x001F0000U
#define LPDDR4__DENALI_CTL_296__AREF_MAX_DEFICIT_SHIFT                       16U
#define LPDDR4__DENALI_CTL_296__AREF_MAX_DEFICIT_WIDTH                        5U
#define LPDDR4__AREF_MAX_DEFICIT__REG DENALI_CTL_296
#define LPDDR4__AREF_MAX_DEFICIT__FLD LPDDR4__DENALI_CTL_296__AREF_MAX_DEFICIT

#define LPDDR4__DENALI_CTL_296__AREF_MAX_CREDIT_MASK                 0x1F000000U
#define LPDDR4__DENALI_CTL_296__AREF_MAX_CREDIT_SHIFT                        24U
#define LPDDR4__DENALI_CTL_296__AREF_MAX_CREDIT_WIDTH                         5U
#define LPDDR4__AREF_MAX_CREDIT__REG DENALI_CTL_296
#define LPDDR4__AREF_MAX_CREDIT__FLD LPDDR4__DENALI_CTL_296__AREF_MAX_CREDIT

#define LPDDR4__DENALI_CTL_297_READ_MASK                             0xFFFF070FU
#define LPDDR4__DENALI_CTL_297_WRITE_MASK                            0xFFFF070FU
#define LPDDR4__DENALI_CTL_297__AREF_CMD_MAX_PER_TREFI_MASK          0x0000000FU
#define LPDDR4__DENALI_CTL_297__AREF_CMD_MAX_PER_TREFI_SHIFT                  0U
#define LPDDR4__DENALI_CTL_297__AREF_CMD_MAX_PER_TREFI_WIDTH                  4U
#define LPDDR4__AREF_CMD_MAX_PER_TREFI__REG DENALI_CTL_297
#define LPDDR4__AREF_CMD_MAX_PER_TREFI__FLD LPDDR4__DENALI_CTL_297__AREF_CMD_MAX_PER_TREFI

#define LPDDR4__DENALI_CTL_297__ZQCS_OPT_THRESHOLD_MASK              0x00000700U
#define LPDDR4__DENALI_CTL_297__ZQCS_OPT_THRESHOLD_SHIFT                      8U
#define LPDDR4__DENALI_CTL_297__ZQCS_OPT_THRESHOLD_WIDTH                      3U
#define LPDDR4__ZQCS_OPT_THRESHOLD__REG DENALI_CTL_297
#define LPDDR4__ZQCS_OPT_THRESHOLD__FLD LPDDR4__DENALI_CTL_297__ZQCS_OPT_THRESHOLD

#define LPDDR4__DENALI_CTL_297__ZQ_CALSTART_NORM_THRESHOLD_F0_MASK   0xFFFF0000U
#define LPDDR4__DENALI_CTL_297__ZQ_CALSTART_NORM_THRESHOLD_F0_SHIFT          16U
#define LPDDR4__DENALI_CTL_297__ZQ_CALSTART_NORM_THRESHOLD_F0_WIDTH          16U
#define LPDDR4__ZQ_CALSTART_NORM_THRESHOLD_F0__REG DENALI_CTL_297
#define LPDDR4__ZQ_CALSTART_NORM_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_297__ZQ_CALSTART_NORM_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_298_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_298_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_298__ZQ_CALSTART_HIGH_THRESHOLD_F0_MASK   0x0000FFFFU
#define LPDDR4__DENALI_CTL_298__ZQ_CALSTART_HIGH_THRESHOLD_F0_SHIFT           0U
#define LPDDR4__DENALI_CTL_298__ZQ_CALSTART_HIGH_THRESHOLD_F0_WIDTH          16U
#define LPDDR4__ZQ_CALSTART_HIGH_THRESHOLD_F0__REG DENALI_CTL_298
#define LPDDR4__ZQ_CALSTART_HIGH_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_298__ZQ_CALSTART_HIGH_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_298__ZQ_CALLATCH_HIGH_THRESHOLD_F0_MASK   0xFFFF0000U
#define LPDDR4__DENALI_CTL_298__ZQ_CALLATCH_HIGH_THRESHOLD_F0_SHIFT          16U
#define LPDDR4__DENALI_CTL_298__ZQ_CALLATCH_HIGH_THRESHOLD_F0_WIDTH          16U
#define LPDDR4__ZQ_CALLATCH_HIGH_THRESHOLD_F0__REG DENALI_CTL_298
#define LPDDR4__ZQ_CALLATCH_HIGH_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_298__ZQ_CALLATCH_HIGH_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_299_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_299_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_299__ZQ_CS_NORM_THRESHOLD_F0_MASK         0x0000FFFFU
#define LPDDR4__DENALI_CTL_299__ZQ_CS_NORM_THRESHOLD_F0_SHIFT                 0U
#define LPDDR4__DENALI_CTL_299__ZQ_CS_NORM_THRESHOLD_F0_WIDTH                16U
#define LPDDR4__ZQ_CS_NORM_THRESHOLD_F0__REG DENALI_CTL_299
#define LPDDR4__ZQ_CS_NORM_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_299__ZQ_CS_NORM_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_299__ZQ_CS_HIGH_THRESHOLD_F0_MASK         0xFFFF0000U
#define LPDDR4__DENALI_CTL_299__ZQ_CS_HIGH_THRESHOLD_F0_SHIFT                16U
#define LPDDR4__DENALI_CTL_299__ZQ_CS_HIGH_THRESHOLD_F0_WIDTH                16U
#define LPDDR4__ZQ_CS_HIGH_THRESHOLD_F0__REG DENALI_CTL_299
#define LPDDR4__ZQ_CS_HIGH_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_299__ZQ_CS_HIGH_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_300_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_300_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_300__ZQ_CALSTART_TIMEOUT_F0_MASK          0x0000FFFFU
#define LPDDR4__DENALI_CTL_300__ZQ_CALSTART_TIMEOUT_F0_SHIFT                  0U
#define LPDDR4__DENALI_CTL_300__ZQ_CALSTART_TIMEOUT_F0_WIDTH                 16U
#define LPDDR4__ZQ_CALSTART_TIMEOUT_F0__REG DENALI_CTL_300
#define LPDDR4__ZQ_CALSTART_TIMEOUT_F0__FLD LPDDR4__DENALI_CTL_300__ZQ_CALSTART_TIMEOUT_F0

#define LPDDR4__DENALI_CTL_300__ZQ_CALLATCH_TIMEOUT_F0_MASK          0xFFFF0000U
#define LPDDR4__DENALI_CTL_300__ZQ_CALLATCH_TIMEOUT_F0_SHIFT                 16U
#define LPDDR4__DENALI_CTL_300__ZQ_CALLATCH_TIMEOUT_F0_WIDTH                 16U
#define LPDDR4__ZQ_CALLATCH_TIMEOUT_F0__REG DENALI_CTL_300
#define LPDDR4__ZQ_CALLATCH_TIMEOUT_F0__FLD LPDDR4__DENALI_CTL_300__ZQ_CALLATCH_TIMEOUT_F0

#define LPDDR4__DENALI_CTL_301_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_301_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_301__ZQ_CS_TIMEOUT_F0_MASK                0x0000FFFFU
#define LPDDR4__DENALI_CTL_301__ZQ_CS_TIMEOUT_F0_SHIFT                        0U
#define LPDDR4__DENALI_CTL_301__ZQ_CS_TIMEOUT_F0_WIDTH                       16U
#define LPDDR4__ZQ_CS_TIMEOUT_F0__REG DENALI_CTL_301
#define LPDDR4__ZQ_CS_TIMEOUT_F0__FLD LPDDR4__DENALI_CTL_301__ZQ_CS_TIMEOUT_F0

#define LPDDR4__DENALI_CTL_301__ZQ_PROMOTE_THRESHOLD_F0_MASK         0xFFFF0000U
#define LPDDR4__DENALI_CTL_301__ZQ_PROMOTE_THRESHOLD_F0_SHIFT                16U
#define LPDDR4__DENALI_CTL_301__ZQ_PROMOTE_THRESHOLD_F0_WIDTH                16U
#define LPDDR4__ZQ_PROMOTE_THRESHOLD_F0__REG DENALI_CTL_301
#define LPDDR4__ZQ_PROMOTE_THRESHOLD_F0__FLD LPDDR4__DENALI_CTL_301__ZQ_PROMOTE_THRESHOLD_F0

#define LPDDR4__DENALI_CTL_302_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_302_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_302__ZQ_CALSTART_NORM_THRESHOLD_F1_MASK   0x0000FFFFU
#define LPDDR4__DENALI_CTL_302__ZQ_CALSTART_NORM_THRESHOLD_F1_SHIFT           0U
#define LPDDR4__DENALI_CTL_302__ZQ_CALSTART_NORM_THRESHOLD_F1_WIDTH          16U
#define LPDDR4__ZQ_CALSTART_NORM_THRESHOLD_F1__REG DENALI_CTL_302
#define LPDDR4__ZQ_CALSTART_NORM_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_302__ZQ_CALSTART_NORM_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_302__ZQ_CALSTART_HIGH_THRESHOLD_F1_MASK   0xFFFF0000U
#define LPDDR4__DENALI_CTL_302__ZQ_CALSTART_HIGH_THRESHOLD_F1_SHIFT          16U
#define LPDDR4__DENALI_CTL_302__ZQ_CALSTART_HIGH_THRESHOLD_F1_WIDTH          16U
#define LPDDR4__ZQ_CALSTART_HIGH_THRESHOLD_F1__REG DENALI_CTL_302
#define LPDDR4__ZQ_CALSTART_HIGH_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_302__ZQ_CALSTART_HIGH_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_303_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_303_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_303__ZQ_CALLATCH_HIGH_THRESHOLD_F1_MASK   0x0000FFFFU
#define LPDDR4__DENALI_CTL_303__ZQ_CALLATCH_HIGH_THRESHOLD_F1_SHIFT           0U
#define LPDDR4__DENALI_CTL_303__ZQ_CALLATCH_HIGH_THRESHOLD_F1_WIDTH          16U
#define LPDDR4__ZQ_CALLATCH_HIGH_THRESHOLD_F1__REG DENALI_CTL_303
#define LPDDR4__ZQ_CALLATCH_HIGH_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_303__ZQ_CALLATCH_HIGH_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_303__ZQ_CS_NORM_THRESHOLD_F1_MASK         0xFFFF0000U
#define LPDDR4__DENALI_CTL_303__ZQ_CS_NORM_THRESHOLD_F1_SHIFT                16U
#define LPDDR4__DENALI_CTL_303__ZQ_CS_NORM_THRESHOLD_F1_WIDTH                16U
#define LPDDR4__ZQ_CS_NORM_THRESHOLD_F1__REG DENALI_CTL_303
#define LPDDR4__ZQ_CS_NORM_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_303__ZQ_CS_NORM_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_304_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_304_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_304__ZQ_CS_HIGH_THRESHOLD_F1_MASK         0x0000FFFFU
#define LPDDR4__DENALI_CTL_304__ZQ_CS_HIGH_THRESHOLD_F1_SHIFT                 0U
#define LPDDR4__DENALI_CTL_304__ZQ_CS_HIGH_THRESHOLD_F1_WIDTH                16U
#define LPDDR4__ZQ_CS_HIGH_THRESHOLD_F1__REG DENALI_CTL_304
#define LPDDR4__ZQ_CS_HIGH_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_304__ZQ_CS_HIGH_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_304__ZQ_CALSTART_TIMEOUT_F1_MASK          0xFFFF0000U
#define LPDDR4__DENALI_CTL_304__ZQ_CALSTART_TIMEOUT_F1_SHIFT                 16U
#define LPDDR4__DENALI_CTL_304__ZQ_CALSTART_TIMEOUT_F1_WIDTH                 16U
#define LPDDR4__ZQ_CALSTART_TIMEOUT_F1__REG DENALI_CTL_304
#define LPDDR4__ZQ_CALSTART_TIMEOUT_F1__FLD LPDDR4__DENALI_CTL_304__ZQ_CALSTART_TIMEOUT_F1

#define LPDDR4__DENALI_CTL_305_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_305_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_305__ZQ_CALLATCH_TIMEOUT_F1_MASK          0x0000FFFFU
#define LPDDR4__DENALI_CTL_305__ZQ_CALLATCH_TIMEOUT_F1_SHIFT                  0U
#define LPDDR4__DENALI_CTL_305__ZQ_CALLATCH_TIMEOUT_F1_WIDTH                 16U
#define LPDDR4__ZQ_CALLATCH_TIMEOUT_F1__REG DENALI_CTL_305
#define LPDDR4__ZQ_CALLATCH_TIMEOUT_F1__FLD LPDDR4__DENALI_CTL_305__ZQ_CALLATCH_TIMEOUT_F1

#define LPDDR4__DENALI_CTL_305__ZQ_CS_TIMEOUT_F1_MASK                0xFFFF0000U
#define LPDDR4__DENALI_CTL_305__ZQ_CS_TIMEOUT_F1_SHIFT                       16U
#define LPDDR4__DENALI_CTL_305__ZQ_CS_TIMEOUT_F1_WIDTH                       16U
#define LPDDR4__ZQ_CS_TIMEOUT_F1__REG DENALI_CTL_305
#define LPDDR4__ZQ_CS_TIMEOUT_F1__FLD LPDDR4__DENALI_CTL_305__ZQ_CS_TIMEOUT_F1

#define LPDDR4__DENALI_CTL_306_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_306_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_306__ZQ_PROMOTE_THRESHOLD_F1_MASK         0x0000FFFFU
#define LPDDR4__DENALI_CTL_306__ZQ_PROMOTE_THRESHOLD_F1_SHIFT                 0U
#define LPDDR4__DENALI_CTL_306__ZQ_PROMOTE_THRESHOLD_F1_WIDTH                16U
#define LPDDR4__ZQ_PROMOTE_THRESHOLD_F1__REG DENALI_CTL_306
#define LPDDR4__ZQ_PROMOTE_THRESHOLD_F1__FLD LPDDR4__DENALI_CTL_306__ZQ_PROMOTE_THRESHOLD_F1

#define LPDDR4__DENALI_CTL_306__ZQ_CALSTART_NORM_THRESHOLD_F2_MASK   0xFFFF0000U
#define LPDDR4__DENALI_CTL_306__ZQ_CALSTART_NORM_THRESHOLD_F2_SHIFT          16U
#define LPDDR4__DENALI_CTL_306__ZQ_CALSTART_NORM_THRESHOLD_F2_WIDTH          16U
#define LPDDR4__ZQ_CALSTART_NORM_THRESHOLD_F2__REG DENALI_CTL_306
#define LPDDR4__ZQ_CALSTART_NORM_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_306__ZQ_CALSTART_NORM_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_307_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_307_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_307__ZQ_CALSTART_HIGH_THRESHOLD_F2_MASK   0x0000FFFFU
#define LPDDR4__DENALI_CTL_307__ZQ_CALSTART_HIGH_THRESHOLD_F2_SHIFT           0U
#define LPDDR4__DENALI_CTL_307__ZQ_CALSTART_HIGH_THRESHOLD_F2_WIDTH          16U
#define LPDDR4__ZQ_CALSTART_HIGH_THRESHOLD_F2__REG DENALI_CTL_307
#define LPDDR4__ZQ_CALSTART_HIGH_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_307__ZQ_CALSTART_HIGH_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_307__ZQ_CALLATCH_HIGH_THRESHOLD_F2_MASK   0xFFFF0000U
#define LPDDR4__DENALI_CTL_307__ZQ_CALLATCH_HIGH_THRESHOLD_F2_SHIFT          16U
#define LPDDR4__DENALI_CTL_307__ZQ_CALLATCH_HIGH_THRESHOLD_F2_WIDTH          16U
#define LPDDR4__ZQ_CALLATCH_HIGH_THRESHOLD_F2__REG DENALI_CTL_307
#define LPDDR4__ZQ_CALLATCH_HIGH_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_307__ZQ_CALLATCH_HIGH_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_308_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_308_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_308__ZQ_CS_NORM_THRESHOLD_F2_MASK         0x0000FFFFU
#define LPDDR4__DENALI_CTL_308__ZQ_CS_NORM_THRESHOLD_F2_SHIFT                 0U
#define LPDDR4__DENALI_CTL_308__ZQ_CS_NORM_THRESHOLD_F2_WIDTH                16U
#define LPDDR4__ZQ_CS_NORM_THRESHOLD_F2__REG DENALI_CTL_308
#define LPDDR4__ZQ_CS_NORM_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_308__ZQ_CS_NORM_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_308__ZQ_CS_HIGH_THRESHOLD_F2_MASK         0xFFFF0000U
#define LPDDR4__DENALI_CTL_308__ZQ_CS_HIGH_THRESHOLD_F2_SHIFT                16U
#define LPDDR4__DENALI_CTL_308__ZQ_CS_HIGH_THRESHOLD_F2_WIDTH                16U
#define LPDDR4__ZQ_CS_HIGH_THRESHOLD_F2__REG DENALI_CTL_308
#define LPDDR4__ZQ_CS_HIGH_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_308__ZQ_CS_HIGH_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_309_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_309_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_309__ZQ_CALSTART_TIMEOUT_F2_MASK          0x0000FFFFU
#define LPDDR4__DENALI_CTL_309__ZQ_CALSTART_TIMEOUT_F2_SHIFT                  0U
#define LPDDR4__DENALI_CTL_309__ZQ_CALSTART_TIMEOUT_F2_WIDTH                 16U
#define LPDDR4__ZQ_CALSTART_TIMEOUT_F2__REG DENALI_CTL_309
#define LPDDR4__ZQ_CALSTART_TIMEOUT_F2__FLD LPDDR4__DENALI_CTL_309__ZQ_CALSTART_TIMEOUT_F2

#define LPDDR4__DENALI_CTL_309__ZQ_CALLATCH_TIMEOUT_F2_MASK          0xFFFF0000U
#define LPDDR4__DENALI_CTL_309__ZQ_CALLATCH_TIMEOUT_F2_SHIFT                 16U
#define LPDDR4__DENALI_CTL_309__ZQ_CALLATCH_TIMEOUT_F2_WIDTH                 16U
#define LPDDR4__ZQ_CALLATCH_TIMEOUT_F2__REG DENALI_CTL_309
#define LPDDR4__ZQ_CALLATCH_TIMEOUT_F2__FLD LPDDR4__DENALI_CTL_309__ZQ_CALLATCH_TIMEOUT_F2

#define LPDDR4__DENALI_CTL_310_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_310_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_310__ZQ_CS_TIMEOUT_F2_MASK                0x0000FFFFU
#define LPDDR4__DENALI_CTL_310__ZQ_CS_TIMEOUT_F2_SHIFT                        0U
#define LPDDR4__DENALI_CTL_310__ZQ_CS_TIMEOUT_F2_WIDTH                       16U
#define LPDDR4__ZQ_CS_TIMEOUT_F2__REG DENALI_CTL_310
#define LPDDR4__ZQ_CS_TIMEOUT_F2__FLD LPDDR4__DENALI_CTL_310__ZQ_CS_TIMEOUT_F2

#define LPDDR4__DENALI_CTL_310__ZQ_PROMOTE_THRESHOLD_F2_MASK         0xFFFF0000U
#define LPDDR4__DENALI_CTL_310__ZQ_PROMOTE_THRESHOLD_F2_SHIFT                16U
#define LPDDR4__DENALI_CTL_310__ZQ_PROMOTE_THRESHOLD_F2_WIDTH                16U
#define LPDDR4__ZQ_PROMOTE_THRESHOLD_F2__REG DENALI_CTL_310
#define LPDDR4__ZQ_PROMOTE_THRESHOLD_F2__FLD LPDDR4__DENALI_CTL_310__ZQ_PROMOTE_THRESHOLD_F2

#define LPDDR4__DENALI_CTL_311_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_CTL_311_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_CTL_311__TIMEOUT_TIMER_LOG_MASK               0x000000FFU
#define LPDDR4__DENALI_CTL_311__TIMEOUT_TIMER_LOG_SHIFT                       0U
#define LPDDR4__DENALI_CTL_311__TIMEOUT_TIMER_LOG_WIDTH                       8U
#define LPDDR4__TIMEOUT_TIMER_LOG__REG DENALI_CTL_311
#define LPDDR4__TIMEOUT_TIMER_LOG__FLD LPDDR4__DENALI_CTL_311__TIMEOUT_TIMER_LOG

#define LPDDR4__DENALI_CTL_311__ZQINIT_F0_MASK                       0x000FFF00U
#define LPDDR4__DENALI_CTL_311__ZQINIT_F0_SHIFT                               8U
#define LPDDR4__DENALI_CTL_311__ZQINIT_F0_WIDTH                              12U
#define LPDDR4__ZQINIT_F0__REG DENALI_CTL_311
#define LPDDR4__ZQINIT_F0__FLD LPDDR4__DENALI_CTL_311__ZQINIT_F0

#define LPDDR4__DENALI_CTL_312_READ_MASK                             0x0FFF0FFFU
#define LPDDR4__DENALI_CTL_312_WRITE_MASK                            0x0FFF0FFFU
#define LPDDR4__DENALI_CTL_312__ZQCL_F0_MASK                         0x00000FFFU
#define LPDDR4__DENALI_CTL_312__ZQCL_F0_SHIFT                                 0U
#define LPDDR4__DENALI_CTL_312__ZQCL_F0_WIDTH                                12U
#define LPDDR4__ZQCL_F0__REG DENALI_CTL_312
#define LPDDR4__ZQCL_F0__FLD LPDDR4__DENALI_CTL_312__ZQCL_F0

#define LPDDR4__DENALI_CTL_312__ZQCS_F0_MASK                         0x0FFF0000U
#define LPDDR4__DENALI_CTL_312__ZQCS_F0_SHIFT                                16U
#define LPDDR4__DENALI_CTL_312__ZQCS_F0_WIDTH                                12U
#define LPDDR4__ZQCS_F0__REG DENALI_CTL_312
#define LPDDR4__ZQCS_F0__FLD LPDDR4__DENALI_CTL_312__ZQCS_F0

#define LPDDR4__DENALI_CTL_313_READ_MASK                             0x007F0FFFU
#define LPDDR4__DENALI_CTL_313_WRITE_MASK                            0x007F0FFFU
#define LPDDR4__DENALI_CTL_313__TZQCAL_F0_MASK                       0x00000FFFU
#define LPDDR4__DENALI_CTL_313__TZQCAL_F0_SHIFT                               0U
#define LPDDR4__DENALI_CTL_313__TZQCAL_F0_WIDTH                              12U
#define LPDDR4__TZQCAL_F0__REG DENALI_CTL_313
#define LPDDR4__TZQCAL_F0__FLD LPDDR4__DENALI_CTL_313__TZQCAL_F0

#define LPDDR4__DENALI_CTL_313__TZQLAT_F0_MASK                       0x007F0000U
#define LPDDR4__DENALI_CTL_313__TZQLAT_F0_SHIFT                              16U
#define LPDDR4__DENALI_CTL_313__TZQLAT_F0_WIDTH                               7U
#define LPDDR4__TZQLAT_F0__REG DENALI_CTL_313
#define LPDDR4__TZQLAT_F0__FLD LPDDR4__DENALI_CTL_313__TZQLAT_F0

#define LPDDR4__DENALI_CTL_314_READ_MASK                             0x0FFF0FFFU
#define LPDDR4__DENALI_CTL_314_WRITE_MASK                            0x0FFF0FFFU
#define LPDDR4__DENALI_CTL_314__ZQINIT_F1_MASK                       0x00000FFFU
#define LPDDR4__DENALI_CTL_314__ZQINIT_F1_SHIFT                               0U
#define LPDDR4__DENALI_CTL_314__ZQINIT_F1_WIDTH                              12U
#define LPDDR4__ZQINIT_F1__REG DENALI_CTL_314
#define LPDDR4__ZQINIT_F1__FLD LPDDR4__DENALI_CTL_314__ZQINIT_F1

#define LPDDR4__DENALI_CTL_314__ZQCL_F1_MASK                         0x0FFF0000U
#define LPDDR4__DENALI_CTL_314__ZQCL_F1_SHIFT                                16U
#define LPDDR4__DENALI_CTL_314__ZQCL_F1_WIDTH                                12U
#define LPDDR4__ZQCL_F1__REG DENALI_CTL_314
#define LPDDR4__ZQCL_F1__FLD LPDDR4__DENALI_CTL_314__ZQCL_F1

#define LPDDR4__DENALI_CTL_315_READ_MASK                             0x0FFF0FFFU
#define LPDDR4__DENALI_CTL_315_WRITE_MASK                            0x0FFF0FFFU
#define LPDDR4__DENALI_CTL_315__ZQCS_F1_MASK                         0x00000FFFU
#define LPDDR4__DENALI_CTL_315__ZQCS_F1_SHIFT                                 0U
#define LPDDR4__DENALI_CTL_315__ZQCS_F1_WIDTH                                12U
#define LPDDR4__ZQCS_F1__REG DENALI_CTL_315
#define LPDDR4__ZQCS_F1__FLD LPDDR4__DENALI_CTL_315__ZQCS_F1

#define LPDDR4__DENALI_CTL_315__TZQCAL_F1_MASK                       0x0FFF0000U
#define LPDDR4__DENALI_CTL_315__TZQCAL_F1_SHIFT                              16U
#define LPDDR4__DENALI_CTL_315__TZQCAL_F1_WIDTH                              12U
#define LPDDR4__TZQCAL_F1__REG DENALI_CTL_315
#define LPDDR4__TZQCAL_F1__FLD LPDDR4__DENALI_CTL_315__TZQCAL_F1

#define LPDDR4__DENALI_CTL_316_READ_MASK                             0x000FFF7FU
#define LPDDR4__DENALI_CTL_316_WRITE_MASK                            0x000FFF7FU
#define LPDDR4__DENALI_CTL_316__TZQLAT_F1_MASK                       0x0000007FU
#define LPDDR4__DENALI_CTL_316__TZQLAT_F1_SHIFT                               0U
#define LPDDR4__DENALI_CTL_316__TZQLAT_F1_WIDTH                               7U
#define LPDDR4__TZQLAT_F1__REG DENALI_CTL_316
#define LPDDR4__TZQLAT_F1__FLD LPDDR4__DENALI_CTL_316__TZQLAT_F1

#define LPDDR4__DENALI_CTL_316__ZQINIT_F2_MASK                       0x000FFF00U
#define LPDDR4__DENALI_CTL_316__ZQINIT_F2_SHIFT                               8U
#define LPDDR4__DENALI_CTL_316__ZQINIT_F2_WIDTH                              12U
#define LPDDR4__ZQINIT_F2__REG DENALI_CTL_316
#define LPDDR4__ZQINIT_F2__FLD LPDDR4__DENALI_CTL_316__ZQINIT_F2

#define LPDDR4__DENALI_CTL_317_READ_MASK                             0x0FFF0FFFU
#define LPDDR4__DENALI_CTL_317_WRITE_MASK                            0x0FFF0FFFU
#define LPDDR4__DENALI_CTL_317__ZQCL_F2_MASK                         0x00000FFFU
#define LPDDR4__DENALI_CTL_317__ZQCL_F2_SHIFT                                 0U
#define LPDDR4__DENALI_CTL_317__ZQCL_F2_WIDTH                                12U
#define LPDDR4__ZQCL_F2__REG DENALI_CTL_317
#define LPDDR4__ZQCL_F2__FLD LPDDR4__DENALI_CTL_317__ZQCL_F2

#define LPDDR4__DENALI_CTL_317__ZQCS_F2_MASK                         0x0FFF0000U
#define LPDDR4__DENALI_CTL_317__ZQCS_F2_SHIFT                                16U
#define LPDDR4__DENALI_CTL_317__ZQCS_F2_WIDTH                                12U
#define LPDDR4__ZQCS_F2__REG DENALI_CTL_317
#define LPDDR4__ZQCS_F2__FLD LPDDR4__DENALI_CTL_317__ZQCS_F2

#define LPDDR4__DENALI_CTL_318_READ_MASK                             0x037F0FFFU
#define LPDDR4__DENALI_CTL_318_WRITE_MASK                            0x037F0FFFU
#define LPDDR4__DENALI_CTL_318__TZQCAL_F2_MASK                       0x00000FFFU
#define LPDDR4__DENALI_CTL_318__TZQCAL_F2_SHIFT                               0U
#define LPDDR4__DENALI_CTL_318__TZQCAL_F2_WIDTH                              12U
#define LPDDR4__TZQCAL_F2__REG DENALI_CTL_318
#define LPDDR4__TZQCAL_F2__FLD LPDDR4__DENALI_CTL_318__TZQCAL_F2

#define LPDDR4__DENALI_CTL_318__TZQLAT_F2_MASK                       0x007F0000U
#define LPDDR4__DENALI_CTL_318__TZQLAT_F2_SHIFT                              16U
#define LPDDR4__DENALI_CTL_318__TZQLAT_F2_WIDTH                               7U
#define LPDDR4__TZQLAT_F2__REG DENALI_CTL_318
#define LPDDR4__TZQLAT_F2__FLD LPDDR4__DENALI_CTL_318__TZQLAT_F2

#define LPDDR4__DENALI_CTL_318__ZQ_SW_REQ_START_LATCH_MAP_MASK       0x03000000U
#define LPDDR4__DENALI_CTL_318__ZQ_SW_REQ_START_LATCH_MAP_SHIFT              24U
#define LPDDR4__DENALI_CTL_318__ZQ_SW_REQ_START_LATCH_MAP_WIDTH               2U
#define LPDDR4__ZQ_SW_REQ_START_LATCH_MAP__REG DENALI_CTL_318
#define LPDDR4__ZQ_SW_REQ_START_LATCH_MAP__FLD LPDDR4__DENALI_CTL_318__ZQ_SW_REQ_START_LATCH_MAP

#define LPDDR4__DENALI_CTL_319_READ_MASK                             0x0FFF0100U
#define LPDDR4__DENALI_CTL_319_WRITE_MASK                            0x0FFF0100U
#define LPDDR4__DENALI_CTL_319__ZQ_REQ_MASK                          0x0000000FU
#define LPDDR4__DENALI_CTL_319__ZQ_REQ_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_319__ZQ_REQ_WIDTH                                  4U
#define LPDDR4__ZQ_REQ__REG DENALI_CTL_319
#define LPDDR4__ZQ_REQ__FLD LPDDR4__DENALI_CTL_319__ZQ_REQ

#define LPDDR4__DENALI_CTL_319__ZQ_REQ_PENDING_MASK                  0x00000100U
#define LPDDR4__DENALI_CTL_319__ZQ_REQ_PENDING_SHIFT                          8U
#define LPDDR4__DENALI_CTL_319__ZQ_REQ_PENDING_WIDTH                          1U
#define LPDDR4__DENALI_CTL_319__ZQ_REQ_PENDING_WOCLR                          0U
#define LPDDR4__DENALI_CTL_319__ZQ_REQ_PENDING_WOSET                          0U
#define LPDDR4__ZQ_REQ_PENDING__REG DENALI_CTL_319
#define LPDDR4__ZQ_REQ_PENDING__FLD LPDDR4__DENALI_CTL_319__ZQ_REQ_PENDING

#define LPDDR4__DENALI_CTL_319__ZQRESET_F0_MASK                      0x0FFF0000U
#define LPDDR4__DENALI_CTL_319__ZQRESET_F0_SHIFT                             16U
#define LPDDR4__DENALI_CTL_319__ZQRESET_F0_WIDTH                             12U
#define LPDDR4__ZQRESET_F0__REG DENALI_CTL_319
#define LPDDR4__ZQRESET_F0__FLD LPDDR4__DENALI_CTL_319__ZQRESET_F0

#define LPDDR4__DENALI_CTL_320_READ_MASK                             0x0FFF0FFFU
#define LPDDR4__DENALI_CTL_320_WRITE_MASK                            0x0FFF0FFFU
#define LPDDR4__DENALI_CTL_320__ZQRESET_F1_MASK                      0x00000FFFU
#define LPDDR4__DENALI_CTL_320__ZQRESET_F1_SHIFT                              0U
#define LPDDR4__DENALI_CTL_320__ZQRESET_F1_WIDTH                             12U
#define LPDDR4__ZQRESET_F1__REG DENALI_CTL_320
#define LPDDR4__ZQRESET_F1__FLD LPDDR4__DENALI_CTL_320__ZQRESET_F1

#define LPDDR4__DENALI_CTL_320__ZQRESET_F2_MASK                      0x0FFF0000U
#define LPDDR4__DENALI_CTL_320__ZQRESET_F2_SHIFT                             16U
#define LPDDR4__DENALI_CTL_320__ZQRESET_F2_WIDTH                             12U
#define LPDDR4__ZQRESET_F2__REG DENALI_CTL_320
#define LPDDR4__ZQRESET_F2__FLD LPDDR4__DENALI_CTL_320__ZQRESET_F2

#define LPDDR4__DENALI_CTL_321_READ_MASK                             0x03030101U
#define LPDDR4__DENALI_CTL_321_WRITE_MASK                            0x03030101U
#define LPDDR4__DENALI_CTL_321__NO_ZQ_INIT_MASK                      0x00000001U
#define LPDDR4__DENALI_CTL_321__NO_ZQ_INIT_SHIFT                              0U
#define LPDDR4__DENALI_CTL_321__NO_ZQ_INIT_WIDTH                              1U
#define LPDDR4__DENALI_CTL_321__NO_ZQ_INIT_WOCLR                              0U
#define LPDDR4__DENALI_CTL_321__NO_ZQ_INIT_WOSET                              0U
#define LPDDR4__NO_ZQ_INIT__REG DENALI_CTL_321
#define LPDDR4__NO_ZQ_INIT__FLD LPDDR4__DENALI_CTL_321__NO_ZQ_INIT

#define LPDDR4__DENALI_CTL_321__ZQCS_ROTATE_MASK                     0x00000100U
#define LPDDR4__DENALI_CTL_321__ZQCS_ROTATE_SHIFT                             8U
#define LPDDR4__DENALI_CTL_321__ZQCS_ROTATE_WIDTH                             1U
#define LPDDR4__DENALI_CTL_321__ZQCS_ROTATE_WOCLR                             0U
#define LPDDR4__DENALI_CTL_321__ZQCS_ROTATE_WOSET                             0U
#define LPDDR4__ZQCS_ROTATE__REG DENALI_CTL_321
#define LPDDR4__ZQCS_ROTATE__FLD LPDDR4__DENALI_CTL_321__ZQCS_ROTATE

#define LPDDR4__DENALI_CTL_321__ZQ_CAL_START_MAP_0_MASK              0x00030000U
#define LPDDR4__DENALI_CTL_321__ZQ_CAL_START_MAP_0_SHIFT                     16U
#define LPDDR4__DENALI_CTL_321__ZQ_CAL_START_MAP_0_WIDTH                      2U
#define LPDDR4__ZQ_CAL_START_MAP_0__REG DENALI_CTL_321
#define LPDDR4__ZQ_CAL_START_MAP_0__FLD LPDDR4__DENALI_CTL_321__ZQ_CAL_START_MAP_0

#define LPDDR4__DENALI_CTL_321__ZQ_CAL_LATCH_MAP_0_MASK              0x03000000U
#define LPDDR4__DENALI_CTL_321__ZQ_CAL_LATCH_MAP_0_SHIFT                     24U
#define LPDDR4__DENALI_CTL_321__ZQ_CAL_LATCH_MAP_0_WIDTH                      2U
#define LPDDR4__ZQ_CAL_LATCH_MAP_0__REG DENALI_CTL_321
#define LPDDR4__ZQ_CAL_LATCH_MAP_0__FLD LPDDR4__DENALI_CTL_321__ZQ_CAL_LATCH_MAP_0

#define LPDDR4__DENALI_CTL_322_READ_MASK                             0x03030303U
#define LPDDR4__DENALI_CTL_322_WRITE_MASK                            0x03030303U
#define LPDDR4__DENALI_CTL_322__ZQ_CAL_START_MAP_1_MASK              0x00000003U
#define LPDDR4__DENALI_CTL_322__ZQ_CAL_START_MAP_1_SHIFT                      0U
#define LPDDR4__DENALI_CTL_322__ZQ_CAL_START_MAP_1_WIDTH                      2U
#define LPDDR4__ZQ_CAL_START_MAP_1__REG DENALI_CTL_322
#define LPDDR4__ZQ_CAL_START_MAP_1__FLD LPDDR4__DENALI_CTL_322__ZQ_CAL_START_MAP_1

#define LPDDR4__DENALI_CTL_322__ZQ_CAL_LATCH_MAP_1_MASK              0x00000300U
#define LPDDR4__DENALI_CTL_322__ZQ_CAL_LATCH_MAP_1_SHIFT                      8U
#define LPDDR4__DENALI_CTL_322__ZQ_CAL_LATCH_MAP_1_WIDTH                      2U
#define LPDDR4__ZQ_CAL_LATCH_MAP_1__REG DENALI_CTL_322
#define LPDDR4__ZQ_CAL_LATCH_MAP_1__FLD LPDDR4__DENALI_CTL_322__ZQ_CAL_LATCH_MAP_1

#define LPDDR4__DENALI_CTL_322__BANK_DIFF_0_MASK                     0x00030000U
#define LPDDR4__DENALI_CTL_322__BANK_DIFF_0_SHIFT                            16U
#define LPDDR4__DENALI_CTL_322__BANK_DIFF_0_WIDTH                             2U
#define LPDDR4__BANK_DIFF_0__REG DENALI_CTL_322
#define LPDDR4__BANK_DIFF_0__FLD LPDDR4__DENALI_CTL_322__BANK_DIFF_0

#define LPDDR4__DENALI_CTL_322__BANK_DIFF_1_MASK                     0x03000000U
#define LPDDR4__DENALI_CTL_322__BANK_DIFF_1_SHIFT                            24U
#define LPDDR4__DENALI_CTL_322__BANK_DIFF_1_WIDTH                             2U
#define LPDDR4__BANK_DIFF_1__REG DENALI_CTL_322
#define LPDDR4__BANK_DIFF_1__FLD LPDDR4__DENALI_CTL_322__BANK_DIFF_1

#define LPDDR4__DENALI_CTL_323_READ_MASK                             0x0F0F0707U
#define LPDDR4__DENALI_CTL_323_WRITE_MASK                            0x0F0F0707U
#define LPDDR4__DENALI_CTL_323__ROW_DIFF_0_MASK                      0x00000007U
#define LPDDR4__DENALI_CTL_323__ROW_DIFF_0_SHIFT                              0U
#define LPDDR4__DENALI_CTL_323__ROW_DIFF_0_WIDTH                              3U
#define LPDDR4__ROW_DIFF_0__REG DENALI_CTL_323
#define LPDDR4__ROW_DIFF_0__FLD LPDDR4__DENALI_CTL_323__ROW_DIFF_0

#define LPDDR4__DENALI_CTL_323__ROW_DIFF_1_MASK                      0x00000700U
#define LPDDR4__DENALI_CTL_323__ROW_DIFF_1_SHIFT                              8U
#define LPDDR4__DENALI_CTL_323__ROW_DIFF_1_WIDTH                              3U
#define LPDDR4__ROW_DIFF_1__REG DENALI_CTL_323
#define LPDDR4__ROW_DIFF_1__FLD LPDDR4__DENALI_CTL_323__ROW_DIFF_1

#define LPDDR4__DENALI_CTL_323__COL_DIFF_0_MASK                      0x000F0000U
#define LPDDR4__DENALI_CTL_323__COL_DIFF_0_SHIFT                             16U
#define LPDDR4__DENALI_CTL_323__COL_DIFF_0_WIDTH                              4U
#define LPDDR4__COL_DIFF_0__REG DENALI_CTL_323
#define LPDDR4__COL_DIFF_0__FLD LPDDR4__DENALI_CTL_323__COL_DIFF_0

#define LPDDR4__DENALI_CTL_323__COL_DIFF_1_MASK                      0x0F000000U
#define LPDDR4__DENALI_CTL_323__COL_DIFF_1_SHIFT                             24U
#define LPDDR4__DENALI_CTL_323__COL_DIFF_1_WIDTH                              4U
#define LPDDR4__COL_DIFF_1__REG DENALI_CTL_323
#define LPDDR4__COL_DIFF_1__FLD LPDDR4__DENALI_CTL_323__COL_DIFF_1

#define LPDDR4__DENALI_CTL_324_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_324_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_324__CS_VAL_LOWER_0_MASK                  0x0000FFFFU
#define LPDDR4__DENALI_CTL_324__CS_VAL_LOWER_0_SHIFT                          0U
#define LPDDR4__DENALI_CTL_324__CS_VAL_LOWER_0_WIDTH                         16U
#define LPDDR4__CS_VAL_LOWER_0__REG DENALI_CTL_324
#define LPDDR4__CS_VAL_LOWER_0__FLD LPDDR4__DENALI_CTL_324__CS_VAL_LOWER_0

#define LPDDR4__DENALI_CTL_324__CS_VAL_UPPER_0_MASK                  0xFFFF0000U
#define LPDDR4__DENALI_CTL_324__CS_VAL_UPPER_0_SHIFT                         16U
#define LPDDR4__DENALI_CTL_324__CS_VAL_UPPER_0_WIDTH                         16U
#define LPDDR4__CS_VAL_UPPER_0__REG DENALI_CTL_324
#define LPDDR4__CS_VAL_UPPER_0__FLD LPDDR4__DENALI_CTL_324__CS_VAL_UPPER_0

#define LPDDR4__DENALI_CTL_325_READ_MASK                             0x00FFFF03U
#define LPDDR4__DENALI_CTL_325_WRITE_MASK                            0x00FFFF03U
#define LPDDR4__DENALI_CTL_325__ROW_START_VAL_0_MASK                 0x00000003U
#define LPDDR4__DENALI_CTL_325__ROW_START_VAL_0_SHIFT                         0U
#define LPDDR4__DENALI_CTL_325__ROW_START_VAL_0_WIDTH                         2U
#define LPDDR4__ROW_START_VAL_0__REG DENALI_CTL_325
#define LPDDR4__ROW_START_VAL_0__FLD LPDDR4__DENALI_CTL_325__ROW_START_VAL_0

#define LPDDR4__DENALI_CTL_325__CS_MSK_0_MASK                        0x00FFFF00U
#define LPDDR4__DENALI_CTL_325__CS_MSK_0_SHIFT                                8U
#define LPDDR4__DENALI_CTL_325__CS_MSK_0_WIDTH                               16U
#define LPDDR4__CS_MSK_0__REG DENALI_CTL_325
#define LPDDR4__CS_MSK_0__FLD LPDDR4__DENALI_CTL_325__CS_MSK_0

#define LPDDR4__DENALI_CTL_326_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_326_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_326__CS_VAL_LOWER_1_MASK                  0x0000FFFFU
#define LPDDR4__DENALI_CTL_326__CS_VAL_LOWER_1_SHIFT                          0U
#define LPDDR4__DENALI_CTL_326__CS_VAL_LOWER_1_WIDTH                         16U
#define LPDDR4__CS_VAL_LOWER_1__REG DENALI_CTL_326
#define LPDDR4__CS_VAL_LOWER_1__FLD LPDDR4__DENALI_CTL_326__CS_VAL_LOWER_1

#define LPDDR4__DENALI_CTL_326__CS_VAL_UPPER_1_MASK                  0xFFFF0000U
#define LPDDR4__DENALI_CTL_326__CS_VAL_UPPER_1_SHIFT                         16U
#define LPDDR4__DENALI_CTL_326__CS_VAL_UPPER_1_WIDTH                         16U
#define LPDDR4__CS_VAL_UPPER_1__REG DENALI_CTL_326
#define LPDDR4__CS_VAL_UPPER_1__FLD LPDDR4__DENALI_CTL_326__CS_VAL_UPPER_1

#define LPDDR4__DENALI_CTL_327_READ_MASK                             0x03FFFF03U
#define LPDDR4__DENALI_CTL_327_WRITE_MASK                            0x03FFFF03U
#define LPDDR4__DENALI_CTL_327__ROW_START_VAL_1_MASK                 0x00000003U
#define LPDDR4__DENALI_CTL_327__ROW_START_VAL_1_SHIFT                         0U
#define LPDDR4__DENALI_CTL_327__ROW_START_VAL_1_WIDTH                         2U
#define LPDDR4__ROW_START_VAL_1__REG DENALI_CTL_327
#define LPDDR4__ROW_START_VAL_1__FLD LPDDR4__DENALI_CTL_327__ROW_START_VAL_1

#define LPDDR4__DENALI_CTL_327__CS_MSK_1_MASK                        0x00FFFF00U
#define LPDDR4__DENALI_CTL_327__CS_MSK_1_SHIFT                                8U
#define LPDDR4__DENALI_CTL_327__CS_MSK_1_WIDTH                               16U
#define LPDDR4__CS_MSK_1__REG DENALI_CTL_327
#define LPDDR4__CS_MSK_1__FLD LPDDR4__DENALI_CTL_327__CS_MSK_1

#define LPDDR4__DENALI_CTL_327__CS_MAP_NON_POW2_MASK                 0x03000000U
#define LPDDR4__DENALI_CTL_327__CS_MAP_NON_POW2_SHIFT                        24U
#define LPDDR4__DENALI_CTL_327__CS_MAP_NON_POW2_WIDTH                         2U
#define LPDDR4__CS_MAP_NON_POW2__REG DENALI_CTL_327
#define LPDDR4__CS_MAP_NON_POW2__FLD LPDDR4__DENALI_CTL_327__CS_MAP_NON_POW2

#define LPDDR4__DENALI_CTL_328_READ_MASK                             0x1F011F01U
#define LPDDR4__DENALI_CTL_328_WRITE_MASK                            0x1F011F01U
#define LPDDR4__DENALI_CTL_328__CS_LOWER_ADDR_EN_MASK                0x00000001U
#define LPDDR4__DENALI_CTL_328__CS_LOWER_ADDR_EN_SHIFT                        0U
#define LPDDR4__DENALI_CTL_328__CS_LOWER_ADDR_EN_WIDTH                        1U
#define LPDDR4__DENALI_CTL_328__CS_LOWER_ADDR_EN_WOCLR                        0U
#define LPDDR4__DENALI_CTL_328__CS_LOWER_ADDR_EN_WOSET                        0U
#define LPDDR4__CS_LOWER_ADDR_EN__REG DENALI_CTL_328
#define LPDDR4__CS_LOWER_ADDR_EN__FLD LPDDR4__DENALI_CTL_328__CS_LOWER_ADDR_EN

#define LPDDR4__DENALI_CTL_328__MC_RESERVED8_MASK                    0x00001F00U
#define LPDDR4__DENALI_CTL_328__MC_RESERVED8_SHIFT                            8U
#define LPDDR4__DENALI_CTL_328__MC_RESERVED8_WIDTH                            5U
#define LPDDR4__MC_RESERVED8__REG DENALI_CTL_328
#define LPDDR4__MC_RESERVED8__FLD LPDDR4__DENALI_CTL_328__MC_RESERVED8

#define LPDDR4__DENALI_CTL_328__MC_RESERVED9_MASK                    0x00010000U
#define LPDDR4__DENALI_CTL_328__MC_RESERVED9_SHIFT                           16U
#define LPDDR4__DENALI_CTL_328__MC_RESERVED9_WIDTH                            1U
#define LPDDR4__DENALI_CTL_328__MC_RESERVED9_WOCLR                            0U
#define LPDDR4__DENALI_CTL_328__MC_RESERVED9_WOSET                            0U
#define LPDDR4__MC_RESERVED9__REG DENALI_CTL_328
#define LPDDR4__MC_RESERVED9__FLD LPDDR4__DENALI_CTL_328__MC_RESERVED9

#define LPDDR4__DENALI_CTL_328__APREBIT_MASK                         0x1F000000U
#define LPDDR4__DENALI_CTL_328__APREBIT_SHIFT                                24U
#define LPDDR4__DENALI_CTL_328__APREBIT_WIDTH                                 5U
#define LPDDR4__APREBIT__REG DENALI_CTL_328
#define LPDDR4__APREBIT__FLD LPDDR4__DENALI_CTL_328__APREBIT

#define LPDDR4__DENALI_CTL_329_READ_MASK                             0x0101FFFFU
#define LPDDR4__DENALI_CTL_329_WRITE_MASK                            0x0101FFFFU
#define LPDDR4__DENALI_CTL_329__AGE_COUNT_MASK                       0x000000FFU
#define LPDDR4__DENALI_CTL_329__AGE_COUNT_SHIFT                               0U
#define LPDDR4__DENALI_CTL_329__AGE_COUNT_WIDTH                               8U
#define LPDDR4__AGE_COUNT__REG DENALI_CTL_329
#define LPDDR4__AGE_COUNT__FLD LPDDR4__DENALI_CTL_329__AGE_COUNT

#define LPDDR4__DENALI_CTL_329__COMMAND_AGE_COUNT_MASK               0x0000FF00U
#define LPDDR4__DENALI_CTL_329__COMMAND_AGE_COUNT_SHIFT                       8U
#define LPDDR4__DENALI_CTL_329__COMMAND_AGE_COUNT_WIDTH                       8U
#define LPDDR4__COMMAND_AGE_COUNT__REG DENALI_CTL_329
#define LPDDR4__COMMAND_AGE_COUNT__FLD LPDDR4__DENALI_CTL_329__COMMAND_AGE_COUNT

#define LPDDR4__DENALI_CTL_329__ADDR_CMP_EN_MASK                     0x00010000U
#define LPDDR4__DENALI_CTL_329__ADDR_CMP_EN_SHIFT                            16U
#define LPDDR4__DENALI_CTL_329__ADDR_CMP_EN_WIDTH                             1U
#define LPDDR4__DENALI_CTL_329__ADDR_CMP_EN_WOCLR                             0U
#define LPDDR4__DENALI_CTL_329__ADDR_CMP_EN_WOSET                             0U
#define LPDDR4__ADDR_CMP_EN__REG DENALI_CTL_329
#define LPDDR4__ADDR_CMP_EN__FLD LPDDR4__DENALI_CTL_329__ADDR_CMP_EN

#define LPDDR4__DENALI_CTL_329__ADDR_COLLISION_MPM_DIS_MASK          0x01000000U
#define LPDDR4__DENALI_CTL_329__ADDR_COLLISION_MPM_DIS_SHIFT                 24U
#define LPDDR4__DENALI_CTL_329__ADDR_COLLISION_MPM_DIS_WIDTH                  1U
#define LPDDR4__DENALI_CTL_329__ADDR_COLLISION_MPM_DIS_WOCLR                  0U
#define LPDDR4__DENALI_CTL_329__ADDR_COLLISION_MPM_DIS_WOSET                  0U
#define LPDDR4__ADDR_COLLISION_MPM_DIS__REG DENALI_CTL_329
#define LPDDR4__ADDR_COLLISION_MPM_DIS__FLD LPDDR4__DENALI_CTL_329__ADDR_COLLISION_MPM_DIS

#define LPDDR4__DENALI_CTL_330_READ_MASK                             0x01010101U
#define LPDDR4__DENALI_CTL_330_WRITE_MASK                            0x01010101U
#define LPDDR4__DENALI_CTL_330__BANK_SPLIT_EN_MASK                   0x00000001U
#define LPDDR4__DENALI_CTL_330__BANK_SPLIT_EN_SHIFT                           0U
#define LPDDR4__DENALI_CTL_330__BANK_SPLIT_EN_WIDTH                           1U
#define LPDDR4__DENALI_CTL_330__BANK_SPLIT_EN_WOCLR                           0U
#define LPDDR4__DENALI_CTL_330__BANK_SPLIT_EN_WOSET                           0U
#define LPDDR4__BANK_SPLIT_EN__REG DENALI_CTL_330
#define LPDDR4__BANK_SPLIT_EN__FLD LPDDR4__DENALI_CTL_330__BANK_SPLIT_EN

#define LPDDR4__DENALI_CTL_330__PLACEMENT_EN_MASK                    0x00000100U
#define LPDDR4__DENALI_CTL_330__PLACEMENT_EN_SHIFT                            8U
#define LPDDR4__DENALI_CTL_330__PLACEMENT_EN_WIDTH                            1U
#define LPDDR4__DENALI_CTL_330__PLACEMENT_EN_WOCLR                            0U
#define LPDDR4__DENALI_CTL_330__PLACEMENT_EN_WOSET                            0U
#define LPDDR4__PLACEMENT_EN__REG DENALI_CTL_330
#define LPDDR4__PLACEMENT_EN__FLD LPDDR4__DENALI_CTL_330__PLACEMENT_EN

#define LPDDR4__DENALI_CTL_330__PRIORITY_EN_MASK                     0x00010000U
#define LPDDR4__DENALI_CTL_330__PRIORITY_EN_SHIFT                            16U
#define LPDDR4__DENALI_CTL_330__PRIORITY_EN_WIDTH                             1U
#define LPDDR4__DENALI_CTL_330__PRIORITY_EN_WOCLR                             0U
#define LPDDR4__DENALI_CTL_330__PRIORITY_EN_WOSET                             0U
#define LPDDR4__PRIORITY_EN__REG DENALI_CTL_330
#define LPDDR4__PRIORITY_EN__FLD LPDDR4__DENALI_CTL_330__PRIORITY_EN

#define LPDDR4__DENALI_CTL_330__RW_SAME_EN_MASK                      0x01000000U
#define LPDDR4__DENALI_CTL_330__RW_SAME_EN_SHIFT                             24U
#define LPDDR4__DENALI_CTL_330__RW_SAME_EN_WIDTH                              1U
#define LPDDR4__DENALI_CTL_330__RW_SAME_EN_WOCLR                              0U
#define LPDDR4__DENALI_CTL_330__RW_SAME_EN_WOSET                              0U
#define LPDDR4__RW_SAME_EN__REG DENALI_CTL_330
#define LPDDR4__RW_SAME_EN__FLD LPDDR4__DENALI_CTL_330__RW_SAME_EN

#define LPDDR4__DENALI_CTL_331_READ_MASK                             0x03010101U
#define LPDDR4__DENALI_CTL_331_WRITE_MASK                            0x03010101U
#define LPDDR4__DENALI_CTL_331__RW_SAME_PAGE_EN_MASK                 0x00000001U
#define LPDDR4__DENALI_CTL_331__RW_SAME_PAGE_EN_SHIFT                         0U
#define LPDDR4__DENALI_CTL_331__RW_SAME_PAGE_EN_WIDTH                         1U
#define LPDDR4__DENALI_CTL_331__RW_SAME_PAGE_EN_WOCLR                         0U
#define LPDDR4__DENALI_CTL_331__RW_SAME_PAGE_EN_WOSET                         0U
#define LPDDR4__RW_SAME_PAGE_EN__REG DENALI_CTL_331
#define LPDDR4__RW_SAME_PAGE_EN__FLD LPDDR4__DENALI_CTL_331__RW_SAME_PAGE_EN

#define LPDDR4__DENALI_CTL_331__CS_SAME_EN_MASK                      0x00000100U
#define LPDDR4__DENALI_CTL_331__CS_SAME_EN_SHIFT                              8U
#define LPDDR4__DENALI_CTL_331__CS_SAME_EN_WIDTH                              1U
#define LPDDR4__DENALI_CTL_331__CS_SAME_EN_WOCLR                              0U
#define LPDDR4__DENALI_CTL_331__CS_SAME_EN_WOSET                              0U
#define LPDDR4__CS_SAME_EN__REG DENALI_CTL_331
#define LPDDR4__CS_SAME_EN__FLD LPDDR4__DENALI_CTL_331__CS_SAME_EN

#define LPDDR4__DENALI_CTL_331__W2R_SPLIT_EN_MASK                    0x00010000U
#define LPDDR4__DENALI_CTL_331__W2R_SPLIT_EN_SHIFT                           16U
#define LPDDR4__DENALI_CTL_331__W2R_SPLIT_EN_WIDTH                            1U
#define LPDDR4__DENALI_CTL_331__W2R_SPLIT_EN_WOCLR                            0U
#define LPDDR4__DENALI_CTL_331__W2R_SPLIT_EN_WOSET                            0U
#define LPDDR4__W2R_SPLIT_EN__REG DENALI_CTL_331
#define LPDDR4__W2R_SPLIT_EN__FLD LPDDR4__DENALI_CTL_331__W2R_SPLIT_EN

#define LPDDR4__DENALI_CTL_331__DISABLE_RW_GROUP_W_BNK_CONFLICT_MASK 0x03000000U
#define LPDDR4__DENALI_CTL_331__DISABLE_RW_GROUP_W_BNK_CONFLICT_SHIFT        24U
#define LPDDR4__DENALI_CTL_331__DISABLE_RW_GROUP_W_BNK_CONFLICT_WIDTH         2U
#define LPDDR4__DISABLE_RW_GROUP_W_BNK_CONFLICT__REG DENALI_CTL_331
#define LPDDR4__DISABLE_RW_GROUP_W_BNK_CONFLICT__FLD LPDDR4__DENALI_CTL_331__DISABLE_RW_GROUP_W_BNK_CONFLICT

#define LPDDR4__DENALI_CTL_332_READ_MASK                             0x0301011FU
#define LPDDR4__DENALI_CTL_332_WRITE_MASK                            0x0301011FU
#define LPDDR4__DENALI_CTL_332__NUM_Q_ENTRIES_ACT_DISABLE_MASK       0x0000001FU
#define LPDDR4__DENALI_CTL_332__NUM_Q_ENTRIES_ACT_DISABLE_SHIFT               0U
#define LPDDR4__DENALI_CTL_332__NUM_Q_ENTRIES_ACT_DISABLE_WIDTH               5U
#define LPDDR4__NUM_Q_ENTRIES_ACT_DISABLE__REG DENALI_CTL_332
#define LPDDR4__NUM_Q_ENTRIES_ACT_DISABLE__FLD LPDDR4__DENALI_CTL_332__NUM_Q_ENTRIES_ACT_DISABLE

#define LPDDR4__DENALI_CTL_332__SWAP_EN_MASK                         0x00000100U
#define LPDDR4__DENALI_CTL_332__SWAP_EN_SHIFT                                 8U
#define LPDDR4__DENALI_CTL_332__SWAP_EN_WIDTH                                 1U
#define LPDDR4__DENALI_CTL_332__SWAP_EN_WOCLR                                 0U
#define LPDDR4__DENALI_CTL_332__SWAP_EN_WOSET                                 0U
#define LPDDR4__SWAP_EN__REG DENALI_CTL_332
#define LPDDR4__SWAP_EN__FLD LPDDR4__DENALI_CTL_332__SWAP_EN

#define LPDDR4__DENALI_CTL_332__DISABLE_RD_INTERLEAVE_MASK           0x00010000U
#define LPDDR4__DENALI_CTL_332__DISABLE_RD_INTERLEAVE_SHIFT                  16U
#define LPDDR4__DENALI_CTL_332__DISABLE_RD_INTERLEAVE_WIDTH                   1U
#define LPDDR4__DENALI_CTL_332__DISABLE_RD_INTERLEAVE_WOCLR                   0U
#define LPDDR4__DENALI_CTL_332__DISABLE_RD_INTERLEAVE_WOSET                   0U
#define LPDDR4__DISABLE_RD_INTERLEAVE__REG DENALI_CTL_332
#define LPDDR4__DISABLE_RD_INTERLEAVE__FLD LPDDR4__DENALI_CTL_332__DISABLE_RD_INTERLEAVE

#define LPDDR4__DENALI_CTL_332__INHIBIT_DRAM_CMD_MASK                0x03000000U
#define LPDDR4__DENALI_CTL_332__INHIBIT_DRAM_CMD_SHIFT                       24U
#define LPDDR4__DENALI_CTL_332__INHIBIT_DRAM_CMD_WIDTH                        2U
#define LPDDR4__INHIBIT_DRAM_CMD__REG DENALI_CTL_332
#define LPDDR4__INHIBIT_DRAM_CMD__FLD LPDDR4__DENALI_CTL_332__INHIBIT_DRAM_CMD

#define LPDDR4__DENALI_CTL_333_READ_MASK                             0x07010F03U
#define LPDDR4__DENALI_CTL_333_WRITE_MASK                            0x07010F03U
#define LPDDR4__DENALI_CTL_333__CS_MAP_MASK                          0x00000003U
#define LPDDR4__DENALI_CTL_333__CS_MAP_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_333__CS_MAP_WIDTH                                  2U
#define LPDDR4__CS_MAP__REG DENALI_CTL_333
#define LPDDR4__CS_MAP__FLD LPDDR4__DENALI_CTL_333__CS_MAP

#define LPDDR4__DENALI_CTL_333__BURST_ON_FLY_BIT_MASK                0x00000F00U
#define LPDDR4__DENALI_CTL_333__BURST_ON_FLY_BIT_SHIFT                        8U
#define LPDDR4__DENALI_CTL_333__BURST_ON_FLY_BIT_WIDTH                        4U
#define LPDDR4__BURST_ON_FLY_BIT__REG DENALI_CTL_333
#define LPDDR4__BURST_ON_FLY_BIT__FLD LPDDR4__DENALI_CTL_333__BURST_ON_FLY_BIT

#define LPDDR4__DENALI_CTL_333__MEM_DP_REDUCTION_MASK                0x00010000U
#define LPDDR4__DENALI_CTL_333__MEM_DP_REDUCTION_SHIFT                       16U
#define LPDDR4__DENALI_CTL_333__MEM_DP_REDUCTION_WIDTH                        1U
#define LPDDR4__DENALI_CTL_333__MEM_DP_REDUCTION_WOCLR                        0U
#define LPDDR4__DENALI_CTL_333__MEM_DP_REDUCTION_WOSET                        0U
#define LPDDR4__MEM_DP_REDUCTION__REG DENALI_CTL_333
#define LPDDR4__MEM_DP_REDUCTION__FLD LPDDR4__DENALI_CTL_333__MEM_DP_REDUCTION

#define LPDDR4__DENALI_CTL_333__MEMDATA_RATIO_0_MASK                 0x07000000U
#define LPDDR4__DENALI_CTL_333__MEMDATA_RATIO_0_SHIFT                        24U
#define LPDDR4__DENALI_CTL_333__MEMDATA_RATIO_0_WIDTH                         3U
#define LPDDR4__MEMDATA_RATIO_0__REG DENALI_CTL_333
#define LPDDR4__MEMDATA_RATIO_0__FLD LPDDR4__DENALI_CTL_333__MEMDATA_RATIO_0

#define LPDDR4__DENALI_CTL_334_READ_MASK                             0x0F0F0F07U
#define LPDDR4__DENALI_CTL_334_WRITE_MASK                            0x0F0F0F07U
#define LPDDR4__DENALI_CTL_334__MEMDATA_RATIO_1_MASK                 0x00000007U
#define LPDDR4__DENALI_CTL_334__MEMDATA_RATIO_1_SHIFT                         0U
#define LPDDR4__DENALI_CTL_334__MEMDATA_RATIO_1_WIDTH                         3U
#define LPDDR4__MEMDATA_RATIO_1__REG DENALI_CTL_334
#define LPDDR4__MEMDATA_RATIO_1__FLD LPDDR4__DENALI_CTL_334__MEMDATA_RATIO_1

#define LPDDR4__DENALI_CTL_334__DEVICE0_BYTE0_CS0_MASK               0x00000F00U
#define LPDDR4__DENALI_CTL_334__DEVICE0_BYTE0_CS0_SHIFT                       8U
#define LPDDR4__DENALI_CTL_334__DEVICE0_BYTE0_CS0_WIDTH                       4U
#define LPDDR4__DEVICE0_BYTE0_CS0__REG DENALI_CTL_334
#define LPDDR4__DEVICE0_BYTE0_CS0__FLD LPDDR4__DENALI_CTL_334__DEVICE0_BYTE0_CS0

#define LPDDR4__DENALI_CTL_334__DEVICE1_BYTE0_CS0_MASK               0x000F0000U
#define LPDDR4__DENALI_CTL_334__DEVICE1_BYTE0_CS0_SHIFT                      16U
#define LPDDR4__DENALI_CTL_334__DEVICE1_BYTE0_CS0_WIDTH                       4U
#define LPDDR4__DEVICE1_BYTE0_CS0__REG DENALI_CTL_334
#define LPDDR4__DEVICE1_BYTE0_CS0__FLD LPDDR4__DENALI_CTL_334__DEVICE1_BYTE0_CS0

#define LPDDR4__DENALI_CTL_334__DEVICE2_BYTE0_CS0_MASK               0x0F000000U
#define LPDDR4__DENALI_CTL_334__DEVICE2_BYTE0_CS0_SHIFT                      24U
#define LPDDR4__DENALI_CTL_334__DEVICE2_BYTE0_CS0_WIDTH                       4U
#define LPDDR4__DEVICE2_BYTE0_CS0__REG DENALI_CTL_334
#define LPDDR4__DEVICE2_BYTE0_CS0__FLD LPDDR4__DENALI_CTL_334__DEVICE2_BYTE0_CS0

#define LPDDR4__DENALI_CTL_335_READ_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_335_WRITE_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_335__DEVICE3_BYTE0_CS0_MASK               0x0000000FU
#define LPDDR4__DENALI_CTL_335__DEVICE3_BYTE0_CS0_SHIFT                       0U
#define LPDDR4__DENALI_CTL_335__DEVICE3_BYTE0_CS0_WIDTH                       4U
#define LPDDR4__DEVICE3_BYTE0_CS0__REG DENALI_CTL_335
#define LPDDR4__DEVICE3_BYTE0_CS0__FLD LPDDR4__DENALI_CTL_335__DEVICE3_BYTE0_CS0

#define LPDDR4__DENALI_CTL_335__DEVICE0_BYTE0_CS1_MASK               0x00000F00U
#define LPDDR4__DENALI_CTL_335__DEVICE0_BYTE0_CS1_SHIFT                       8U
#define LPDDR4__DENALI_CTL_335__DEVICE0_BYTE0_CS1_WIDTH                       4U
#define LPDDR4__DEVICE0_BYTE0_CS1__REG DENALI_CTL_335
#define LPDDR4__DEVICE0_BYTE0_CS1__FLD LPDDR4__DENALI_CTL_335__DEVICE0_BYTE0_CS1

#define LPDDR4__DENALI_CTL_335__DEVICE1_BYTE0_CS1_MASK               0x000F0000U
#define LPDDR4__DENALI_CTL_335__DEVICE1_BYTE0_CS1_SHIFT                      16U
#define LPDDR4__DENALI_CTL_335__DEVICE1_BYTE0_CS1_WIDTH                       4U
#define LPDDR4__DEVICE1_BYTE0_CS1__REG DENALI_CTL_335
#define LPDDR4__DEVICE1_BYTE0_CS1__FLD LPDDR4__DENALI_CTL_335__DEVICE1_BYTE0_CS1

#define LPDDR4__DENALI_CTL_335__DEVICE2_BYTE0_CS1_MASK               0x0F000000U
#define LPDDR4__DENALI_CTL_335__DEVICE2_BYTE0_CS1_SHIFT                      24U
#define LPDDR4__DENALI_CTL_335__DEVICE2_BYTE0_CS1_WIDTH                       4U
#define LPDDR4__DEVICE2_BYTE0_CS1__REG DENALI_CTL_335
#define LPDDR4__DEVICE2_BYTE0_CS1__FLD LPDDR4__DENALI_CTL_335__DEVICE2_BYTE0_CS1

#define LPDDR4__DENALI_CTL_336_READ_MASK                             0x03011F0FU
#define LPDDR4__DENALI_CTL_336_WRITE_MASK                            0x03011F0FU
#define LPDDR4__DENALI_CTL_336__DEVICE3_BYTE0_CS1_MASK               0x0000000FU
#define LPDDR4__DENALI_CTL_336__DEVICE3_BYTE0_CS1_SHIFT                       0U
#define LPDDR4__DENALI_CTL_336__DEVICE3_BYTE0_CS1_WIDTH                       4U
#define LPDDR4__DEVICE3_BYTE0_CS1__REG DENALI_CTL_336
#define LPDDR4__DEVICE3_BYTE0_CS1__FLD LPDDR4__DENALI_CTL_336__DEVICE3_BYTE0_CS1

#define LPDDR4__DENALI_CTL_336__Q_FULLNESS_MASK                      0x00001F00U
#define LPDDR4__DENALI_CTL_336__Q_FULLNESS_SHIFT                              8U
#define LPDDR4__DENALI_CTL_336__Q_FULLNESS_WIDTH                              5U
#define LPDDR4__Q_FULLNESS__REG DENALI_CTL_336
#define LPDDR4__Q_FULLNESS__FLD LPDDR4__DENALI_CTL_336__Q_FULLNESS

#define LPDDR4__DENALI_CTL_336__IN_ORDER_ACCEPT_MASK                 0x00010000U
#define LPDDR4__DENALI_CTL_336__IN_ORDER_ACCEPT_SHIFT                        16U
#define LPDDR4__DENALI_CTL_336__IN_ORDER_ACCEPT_WIDTH                         1U
#define LPDDR4__DENALI_CTL_336__IN_ORDER_ACCEPT_WOCLR                         0U
#define LPDDR4__DENALI_CTL_336__IN_ORDER_ACCEPT_WOSET                         0U
#define LPDDR4__IN_ORDER_ACCEPT__REG DENALI_CTL_336
#define LPDDR4__IN_ORDER_ACCEPT__FLD LPDDR4__DENALI_CTL_336__IN_ORDER_ACCEPT

#define LPDDR4__DENALI_CTL_336__WR_ORDER_REQ_MASK                    0x03000000U
#define LPDDR4__DENALI_CTL_336__WR_ORDER_REQ_SHIFT                           24U
#define LPDDR4__DENALI_CTL_336__WR_ORDER_REQ_WIDTH                            2U
#define LPDDR4__WR_ORDER_REQ__REG DENALI_CTL_336
#define LPDDR4__WR_ORDER_REQ__FLD LPDDR4__DENALI_CTL_336__WR_ORDER_REQ

#define LPDDR4__DENALI_CTL_337_READ_MASK                             0x01010001U
#define LPDDR4__DENALI_CTL_337_WRITE_MASK                            0x01010001U
#define LPDDR4__DENALI_CTL_337__CONTROLLER_BUSY_MASK                 0x00000001U
#define LPDDR4__DENALI_CTL_337__CONTROLLER_BUSY_SHIFT                         0U
#define LPDDR4__DENALI_CTL_337__CONTROLLER_BUSY_WIDTH                         1U
#define LPDDR4__DENALI_CTL_337__CONTROLLER_BUSY_WOCLR                         0U
#define LPDDR4__DENALI_CTL_337__CONTROLLER_BUSY_WOSET                         0U
#define LPDDR4__CONTROLLER_BUSY__REG DENALI_CTL_337
#define LPDDR4__CONTROLLER_BUSY__FLD LPDDR4__DENALI_CTL_337__CONTROLLER_BUSY

#define LPDDR4__DENALI_CTL_337__CTRLUPD_REQ_MASK                     0x00000100U
#define LPDDR4__DENALI_CTL_337__CTRLUPD_REQ_SHIFT                             8U
#define LPDDR4__DENALI_CTL_337__CTRLUPD_REQ_WIDTH                             1U
#define LPDDR4__DENALI_CTL_337__CTRLUPD_REQ_WOCLR                             0U
#define LPDDR4__DENALI_CTL_337__CTRLUPD_REQ_WOSET                             0U
#define LPDDR4__CTRLUPD_REQ__REG DENALI_CTL_337
#define LPDDR4__CTRLUPD_REQ__FLD LPDDR4__DENALI_CTL_337__CTRLUPD_REQ

#define LPDDR4__DENALI_CTL_337__CTRLUPD_REQ_PER_AREF_EN_MASK         0x00010000U
#define LPDDR4__DENALI_CTL_337__CTRLUPD_REQ_PER_AREF_EN_SHIFT                16U
#define LPDDR4__DENALI_CTL_337__CTRLUPD_REQ_PER_AREF_EN_WIDTH                 1U
#define LPDDR4__DENALI_CTL_337__CTRLUPD_REQ_PER_AREF_EN_WOCLR                 0U
#define LPDDR4__DENALI_CTL_337__CTRLUPD_REQ_PER_AREF_EN_WOSET                 0U
#define LPDDR4__CTRLUPD_REQ_PER_AREF_EN__REG DENALI_CTL_337
#define LPDDR4__CTRLUPD_REQ_PER_AREF_EN__FLD LPDDR4__DENALI_CTL_337__CTRLUPD_REQ_PER_AREF_EN

#define LPDDR4__DENALI_CTL_337__CTRLUPD_AREF_HP_ENABLE_MASK          0x01000000U
#define LPDDR4__DENALI_CTL_337__CTRLUPD_AREF_HP_ENABLE_SHIFT                 24U
#define LPDDR4__DENALI_CTL_337__CTRLUPD_AREF_HP_ENABLE_WIDTH                  1U
#define LPDDR4__DENALI_CTL_337__CTRLUPD_AREF_HP_ENABLE_WOCLR                  0U
#define LPDDR4__DENALI_CTL_337__CTRLUPD_AREF_HP_ENABLE_WOSET                  0U
#define LPDDR4__CTRLUPD_AREF_HP_ENABLE__REG DENALI_CTL_337
#define LPDDR4__CTRLUPD_AREF_HP_ENABLE__FLD LPDDR4__DENALI_CTL_337__CTRLUPD_AREF_HP_ENABLE

#define LPDDR4__DENALI_CTL_338_READ_MASK                             0x01030303U
#define LPDDR4__DENALI_CTL_338_WRITE_MASK                            0x01030303U
#define LPDDR4__DENALI_CTL_338__PREAMBLE_SUPPORT_F0_MASK             0x00000003U
#define LPDDR4__DENALI_CTL_338__PREAMBLE_SUPPORT_F0_SHIFT                     0U
#define LPDDR4__DENALI_CTL_338__PREAMBLE_SUPPORT_F0_WIDTH                     2U
#define LPDDR4__PREAMBLE_SUPPORT_F0__REG DENALI_CTL_338
#define LPDDR4__PREAMBLE_SUPPORT_F0__FLD LPDDR4__DENALI_CTL_338__PREAMBLE_SUPPORT_F0

#define LPDDR4__DENALI_CTL_338__PREAMBLE_SUPPORT_F1_MASK             0x00000300U
#define LPDDR4__DENALI_CTL_338__PREAMBLE_SUPPORT_F1_SHIFT                     8U
#define LPDDR4__DENALI_CTL_338__PREAMBLE_SUPPORT_F1_WIDTH                     2U
#define LPDDR4__PREAMBLE_SUPPORT_F1__REG DENALI_CTL_338
#define LPDDR4__PREAMBLE_SUPPORT_F1__FLD LPDDR4__DENALI_CTL_338__PREAMBLE_SUPPORT_F1

#define LPDDR4__DENALI_CTL_338__PREAMBLE_SUPPORT_F2_MASK             0x00030000U
#define LPDDR4__DENALI_CTL_338__PREAMBLE_SUPPORT_F2_SHIFT                    16U
#define LPDDR4__DENALI_CTL_338__PREAMBLE_SUPPORT_F2_WIDTH                     2U
#define LPDDR4__PREAMBLE_SUPPORT_F2__REG DENALI_CTL_338
#define LPDDR4__PREAMBLE_SUPPORT_F2__FLD LPDDR4__DENALI_CTL_338__PREAMBLE_SUPPORT_F2

#define LPDDR4__DENALI_CTL_338__RD_PREAMBLE_TRAINING_EN_MASK         0x01000000U
#define LPDDR4__DENALI_CTL_338__RD_PREAMBLE_TRAINING_EN_SHIFT                24U
#define LPDDR4__DENALI_CTL_338__RD_PREAMBLE_TRAINING_EN_WIDTH                 1U
#define LPDDR4__DENALI_CTL_338__RD_PREAMBLE_TRAINING_EN_WOCLR                 0U
#define LPDDR4__DENALI_CTL_338__RD_PREAMBLE_TRAINING_EN_WOSET                 0U
#define LPDDR4__RD_PREAMBLE_TRAINING_EN__REG DENALI_CTL_338
#define LPDDR4__RD_PREAMBLE_TRAINING_EN__FLD LPDDR4__DENALI_CTL_338__RD_PREAMBLE_TRAINING_EN

#define LPDDR4__DENALI_CTL_339_READ_MASK                             0x001F0101U
#define LPDDR4__DENALI_CTL_339_WRITE_MASK                            0x001F0101U
#define LPDDR4__DENALI_CTL_339__WR_DBI_EN_MASK                       0x00000001U
#define LPDDR4__DENALI_CTL_339__WR_DBI_EN_SHIFT                               0U
#define LPDDR4__DENALI_CTL_339__WR_DBI_EN_WIDTH                               1U
#define LPDDR4__DENALI_CTL_339__WR_DBI_EN_WOCLR                               0U
#define LPDDR4__DENALI_CTL_339__WR_DBI_EN_WOSET                               0U
#define LPDDR4__WR_DBI_EN__REG DENALI_CTL_339
#define LPDDR4__WR_DBI_EN__FLD LPDDR4__DENALI_CTL_339__WR_DBI_EN

#define LPDDR4__DENALI_CTL_339__RD_DBI_EN_MASK                       0x00000100U
#define LPDDR4__DENALI_CTL_339__RD_DBI_EN_SHIFT                               8U
#define LPDDR4__DENALI_CTL_339__RD_DBI_EN_WIDTH                               1U
#define LPDDR4__DENALI_CTL_339__RD_DBI_EN_WOCLR                               0U
#define LPDDR4__DENALI_CTL_339__RD_DBI_EN_WOSET                               0U
#define LPDDR4__RD_DBI_EN__REG DENALI_CTL_339
#define LPDDR4__RD_DBI_EN__FLD LPDDR4__DENALI_CTL_339__RD_DBI_EN

#define LPDDR4__DENALI_CTL_339__DFI_ERROR_MASK                       0x001F0000U
#define LPDDR4__DENALI_CTL_339__DFI_ERROR_SHIFT                              16U
#define LPDDR4__DENALI_CTL_339__DFI_ERROR_WIDTH                               5U
#define LPDDR4__DFI_ERROR__REG DENALI_CTL_339
#define LPDDR4__DFI_ERROR__FLD LPDDR4__DENALI_CTL_339__DFI_ERROR

#define LPDDR4__DENALI_CTL_340_READ_MASK                             0x010FFFFFU
#define LPDDR4__DENALI_CTL_340_WRITE_MASK                            0x010FFFFFU
#define LPDDR4__DENALI_CTL_340__DFI_ERROR_INFO_MASK                  0x000FFFFFU
#define LPDDR4__DENALI_CTL_340__DFI_ERROR_INFO_SHIFT                          0U
#define LPDDR4__DENALI_CTL_340__DFI_ERROR_INFO_WIDTH                         20U
#define LPDDR4__DFI_ERROR_INFO__REG DENALI_CTL_340
#define LPDDR4__DFI_ERROR_INFO__FLD LPDDR4__DENALI_CTL_340__DFI_ERROR_INFO

#define LPDDR4__DENALI_CTL_340__BG_ROTATE_EN_MASK                    0x01000000U
#define LPDDR4__DENALI_CTL_340__BG_ROTATE_EN_SHIFT                           24U
#define LPDDR4__DENALI_CTL_340__BG_ROTATE_EN_WIDTH                            1U
#define LPDDR4__DENALI_CTL_340__BG_ROTATE_EN_WOCLR                            0U
#define LPDDR4__DENALI_CTL_340__BG_ROTATE_EN_WOSET                            0U
#define LPDDR4__BG_ROTATE_EN__REG DENALI_CTL_340
#define LPDDR4__BG_ROTATE_EN__FLD LPDDR4__DENALI_CTL_340__BG_ROTATE_EN

#define LPDDR4__DENALI_CTL_341__MC_RESERVED10_MASK                   0x00000001U
#define LPDDR4__DENALI_CTL_341__MC_RESERVED10_SHIFT                           0U
#define LPDDR4__DENALI_CTL_341__MC_RESERVED10_WIDTH                           1U
#define LPDDR4__DENALI_CTL_341__MC_RESERVED10_WOCLR                           0U
#define LPDDR4__DENALI_CTL_341__MC_RESERVED10_WOSET                           0U
#define LPDDR4__MC_RESERVED10__REG DENALI_CTL_341
#define LPDDR4__MC_RESERVED10__FLD LPDDR4__DENALI_CTL_341__MC_RESERVED10

#define LPDDR4__DENALI_CTL_342_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_342_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_342__INT_STATUS_MASTER_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_342__INT_STATUS_MASTER_SHIFT                       0U
#define LPDDR4__DENALI_CTL_342__INT_STATUS_MASTER_WIDTH                      32U
#define LPDDR4__INT_STATUS_MASTER__REG DENALI_CTL_342
#define LPDDR4__INT_STATUS_MASTER__FLD LPDDR4__DENALI_CTL_342__INT_STATUS_MASTER

#define LPDDR4__DENALI_CTL_343_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_343_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_343__INT_MASK_MASTER_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_343__INT_MASK_MASTER_SHIFT                         0U
#define LPDDR4__DENALI_CTL_343__INT_MASK_MASTER_WIDTH                        32U
#define LPDDR4__INT_MASK_MASTER__REG DENALI_CTL_343
#define LPDDR4__INT_MASK_MASTER__FLD LPDDR4__DENALI_CTL_343__INT_MASK_MASTER

#define LPDDR4__DENALI_CTL_344_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_344_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_344__INT_STATUS_TIMEOUT_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_344__INT_STATUS_TIMEOUT_SHIFT                      0U
#define LPDDR4__DENALI_CTL_344__INT_STATUS_TIMEOUT_WIDTH                     32U
#define LPDDR4__INT_STATUS_TIMEOUT__REG DENALI_CTL_344
#define LPDDR4__INT_STATUS_TIMEOUT__FLD LPDDR4__DENALI_CTL_344__INT_STATUS_TIMEOUT

#define LPDDR4__DENALI_CTL_345_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_345_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_345__MC_RESERVED11_MASK                   0x0000FFFFU
#define LPDDR4__DENALI_CTL_345__MC_RESERVED11_SHIFT                           0U
#define LPDDR4__DENALI_CTL_345__MC_RESERVED11_WIDTH                          16U
#define LPDDR4__MC_RESERVED11__REG DENALI_CTL_345
#define LPDDR4__MC_RESERVED11__FLD LPDDR4__DENALI_CTL_345__MC_RESERVED11

#define LPDDR4__DENALI_CTL_345__INT_STATUS_LOWPOWER_MASK             0xFFFF0000U
#define LPDDR4__DENALI_CTL_345__INT_STATUS_LOWPOWER_SHIFT                    16U
#define LPDDR4__DENALI_CTL_345__INT_STATUS_LOWPOWER_WIDTH                    16U
#define LPDDR4__INT_STATUS_LOWPOWER__REG DENALI_CTL_345
#define LPDDR4__INT_STATUS_LOWPOWER__FLD LPDDR4__DENALI_CTL_345__INT_STATUS_LOWPOWER

#define LPDDR4__DENALI_CTL_346_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_346_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_346__MC_RESERVED12_MASK                   0x0000FFFFU
#define LPDDR4__DENALI_CTL_346__MC_RESERVED12_SHIFT                           0U
#define LPDDR4__DENALI_CTL_346__MC_RESERVED12_WIDTH                          16U
#define LPDDR4__MC_RESERVED12__REG DENALI_CTL_346
#define LPDDR4__MC_RESERVED12__FLD LPDDR4__DENALI_CTL_346__MC_RESERVED12

#define LPDDR4__DENALI_CTL_346__MC_RESERVED13_MASK                   0xFFFF0000U
#define LPDDR4__DENALI_CTL_346__MC_RESERVED13_SHIFT                          16U
#define LPDDR4__DENALI_CTL_346__MC_RESERVED13_WIDTH                          16U
#define LPDDR4__MC_RESERVED13__REG DENALI_CTL_346
#define LPDDR4__MC_RESERVED13__FLD LPDDR4__DENALI_CTL_346__MC_RESERVED13

#define LPDDR4__DENALI_CTL_347_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_347_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_347__INT_STATUS_TRAINING_MASK             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_347__INT_STATUS_TRAINING_SHIFT                     0U
#define LPDDR4__DENALI_CTL_347__INT_STATUS_TRAINING_WIDTH                    32U
#define LPDDR4__INT_STATUS_TRAINING__REG DENALI_CTL_347
#define LPDDR4__INT_STATUS_TRAINING__FLD LPDDR4__DENALI_CTL_347__INT_STATUS_TRAINING

#define LPDDR4__DENALI_CTL_348_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_348_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_348__INT_STATUS_USERIF_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_348__INT_STATUS_USERIF_SHIFT                       0U
#define LPDDR4__DENALI_CTL_348__INT_STATUS_USERIF_WIDTH                      32U
#define LPDDR4__INT_STATUS_USERIF__REG DENALI_CTL_348
#define LPDDR4__INT_STATUS_USERIF__FLD LPDDR4__DENALI_CTL_348__INT_STATUS_USERIF

#define LPDDR4__DENALI_CTL_349_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_349_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_349__INT_STATUS_MISC_MASK                 0x0000FFFFU
#define LPDDR4__DENALI_CTL_349__INT_STATUS_MISC_SHIFT                         0U
#define LPDDR4__DENALI_CTL_349__INT_STATUS_MISC_WIDTH                        16U
#define LPDDR4__INT_STATUS_MISC__REG DENALI_CTL_349
#define LPDDR4__INT_STATUS_MISC__FLD LPDDR4__DENALI_CTL_349__INT_STATUS_MISC

#define LPDDR4__DENALI_CTL_349__INT_STATUS_BIST_MASK                 0x00FF0000U
#define LPDDR4__DENALI_CTL_349__INT_STATUS_BIST_SHIFT                        16U
#define LPDDR4__DENALI_CTL_349__INT_STATUS_BIST_WIDTH                         8U
#define LPDDR4__INT_STATUS_BIST__REG DENALI_CTL_349
#define LPDDR4__INT_STATUS_BIST__FLD LPDDR4__DENALI_CTL_349__INT_STATUS_BIST

#define LPDDR4__DENALI_CTL_349__MC_RESERVED14_MASK                   0xFF000000U
#define LPDDR4__DENALI_CTL_349__MC_RESERVED14_SHIFT                          24U
#define LPDDR4__DENALI_CTL_349__MC_RESERVED14_WIDTH                           8U
#define LPDDR4__MC_RESERVED14__REG DENALI_CTL_349
#define LPDDR4__MC_RESERVED14__FLD LPDDR4__DENALI_CTL_349__MC_RESERVED14

#define LPDDR4__DENALI_CTL_350_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_350_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_350__INT_STATUS_DFI_MASK                  0x000000FFU
#define LPDDR4__DENALI_CTL_350__INT_STATUS_DFI_SHIFT                          0U
#define LPDDR4__DENALI_CTL_350__INT_STATUS_DFI_WIDTH                          8U
#define LPDDR4__INT_STATUS_DFI__REG DENALI_CTL_350
#define LPDDR4__INT_STATUS_DFI__FLD LPDDR4__DENALI_CTL_350__INT_STATUS_DFI

#define LPDDR4__DENALI_CTL_350__MC_RESERVED15_MASK                   0x0000FF00U
#define LPDDR4__DENALI_CTL_350__MC_RESERVED15_SHIFT                           8U
#define LPDDR4__DENALI_CTL_350__MC_RESERVED15_WIDTH                           8U
#define LPDDR4__MC_RESERVED15__REG DENALI_CTL_350
#define LPDDR4__MC_RESERVED15__FLD LPDDR4__DENALI_CTL_350__MC_RESERVED15

#define LPDDR4__DENALI_CTL_350__INT_STATUS_FREQ_MASK                 0x00FF0000U
#define LPDDR4__DENALI_CTL_350__INT_STATUS_FREQ_SHIFT                        16U
#define LPDDR4__DENALI_CTL_350__INT_STATUS_FREQ_WIDTH                         8U
#define LPDDR4__INT_STATUS_FREQ__REG DENALI_CTL_350
#define LPDDR4__INT_STATUS_FREQ__FLD LPDDR4__DENALI_CTL_350__INT_STATUS_FREQ

#define LPDDR4__DENALI_CTL_350__INT_STATUS_INIT_MASK                 0xFF000000U
#define LPDDR4__DENALI_CTL_350__INT_STATUS_INIT_SHIFT                        24U
#define LPDDR4__DENALI_CTL_350__INT_STATUS_INIT_WIDTH                         8U
#define LPDDR4__INT_STATUS_INIT__REG DENALI_CTL_350
#define LPDDR4__INT_STATUS_INIT__FLD LPDDR4__DENALI_CTL_350__INT_STATUS_INIT

#define LPDDR4__DENALI_CTL_351_READ_MASK                             0x0000FFFFU
#define LPDDR4__DENALI_CTL_351_WRITE_MASK                            0x0000FFFFU
#define LPDDR4__DENALI_CTL_351__INT_STATUS_MODE_MASK                 0x000000FFU
#define LPDDR4__DENALI_CTL_351__INT_STATUS_MODE_SHIFT                         0U
#define LPDDR4__DENALI_CTL_351__INT_STATUS_MODE_WIDTH                         8U
#define LPDDR4__INT_STATUS_MODE__REG DENALI_CTL_351
#define LPDDR4__INT_STATUS_MODE__FLD LPDDR4__DENALI_CTL_351__INT_STATUS_MODE

#define LPDDR4__DENALI_CTL_351__INT_STATUS_PARITY_MASK               0x0000FF00U
#define LPDDR4__DENALI_CTL_351__INT_STATUS_PARITY_SHIFT                       8U
#define LPDDR4__DENALI_CTL_351__INT_STATUS_PARITY_WIDTH                       8U
#define LPDDR4__INT_STATUS_PARITY__REG DENALI_CTL_351
#define LPDDR4__INT_STATUS_PARITY__FLD LPDDR4__DENALI_CTL_351__INT_STATUS_PARITY

#define LPDDR4__DENALI_CTL_352__INT_ACK_TIMEOUT_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_352__INT_ACK_TIMEOUT_SHIFT                         0U
#define LPDDR4__DENALI_CTL_352__INT_ACK_TIMEOUT_WIDTH                        32U
#define LPDDR4__INT_ACK_TIMEOUT__REG DENALI_CTL_352
#define LPDDR4__INT_ACK_TIMEOUT__FLD LPDDR4__DENALI_CTL_352__INT_ACK_TIMEOUT

#define LPDDR4__DENALI_CTL_353__MC_RESERVED16_MASK                   0x0000FFFFU
#define LPDDR4__DENALI_CTL_353__MC_RESERVED16_SHIFT                           0U
#define LPDDR4__DENALI_CTL_353__MC_RESERVED16_WIDTH                          16U
#define LPDDR4__MC_RESERVED16__REG DENALI_CTL_353
#define LPDDR4__MC_RESERVED16__FLD LPDDR4__DENALI_CTL_353__MC_RESERVED16

#define LPDDR4__DENALI_CTL_353__INT_ACK_LOWPOWER_MASK                0xFFFF0000U
#define LPDDR4__DENALI_CTL_353__INT_ACK_LOWPOWER_SHIFT                       16U
#define LPDDR4__DENALI_CTL_353__INT_ACK_LOWPOWER_WIDTH                       16U
#define LPDDR4__INT_ACK_LOWPOWER__REG DENALI_CTL_353
#define LPDDR4__INT_ACK_LOWPOWER__FLD LPDDR4__DENALI_CTL_353__INT_ACK_LOWPOWER

#define LPDDR4__DENALI_CTL_354__MC_RESERVED17_MASK                   0x0000FFFFU
#define LPDDR4__DENALI_CTL_354__MC_RESERVED17_SHIFT                           0U
#define LPDDR4__DENALI_CTL_354__MC_RESERVED17_WIDTH                          16U
#define LPDDR4__MC_RESERVED17__REG DENALI_CTL_354
#define LPDDR4__MC_RESERVED17__FLD LPDDR4__DENALI_CTL_354__MC_RESERVED17

#define LPDDR4__DENALI_CTL_354__MC_RESERVED18_MASK                   0xFFFF0000U
#define LPDDR4__DENALI_CTL_354__MC_RESERVED18_SHIFT                          16U
#define LPDDR4__DENALI_CTL_354__MC_RESERVED18_WIDTH                          16U
#define LPDDR4__MC_RESERVED18__REG DENALI_CTL_354
#define LPDDR4__MC_RESERVED18__FLD LPDDR4__DENALI_CTL_354__MC_RESERVED18

#define LPDDR4__DENALI_CTL_355__INT_ACK_TRAINING_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_355__INT_ACK_TRAINING_SHIFT                        0U
#define LPDDR4__DENALI_CTL_355__INT_ACK_TRAINING_WIDTH                       32U
#define LPDDR4__INT_ACK_TRAINING__REG DENALI_CTL_355
#define LPDDR4__INT_ACK_TRAINING__FLD LPDDR4__DENALI_CTL_355__INT_ACK_TRAINING

#define LPDDR4__DENALI_CTL_356__INT_ACK_USERIF_MASK                  0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_356__INT_ACK_USERIF_SHIFT                          0U
#define LPDDR4__DENALI_CTL_356__INT_ACK_USERIF_WIDTH                         32U
#define LPDDR4__INT_ACK_USERIF__REG DENALI_CTL_356
#define LPDDR4__INT_ACK_USERIF__FLD LPDDR4__DENALI_CTL_356__INT_ACK_USERIF

#define LPDDR4__DENALI_CTL_357__INT_ACK_MISC_MASK                    0x0000FFFFU
#define LPDDR4__DENALI_CTL_357__INT_ACK_MISC_SHIFT                            0U
#define LPDDR4__DENALI_CTL_357__INT_ACK_MISC_WIDTH                           16U
#define LPDDR4__INT_ACK_MISC__REG DENALI_CTL_357
#define LPDDR4__INT_ACK_MISC__FLD LPDDR4__DENALI_CTL_357__INT_ACK_MISC

#define LPDDR4__DENALI_CTL_357__INT_ACK_BIST_MASK                    0x00FF0000U
#define LPDDR4__DENALI_CTL_357__INT_ACK_BIST_SHIFT                           16U
#define LPDDR4__DENALI_CTL_357__INT_ACK_BIST_WIDTH                            8U
#define LPDDR4__INT_ACK_BIST__REG DENALI_CTL_357
#define LPDDR4__INT_ACK_BIST__FLD LPDDR4__DENALI_CTL_357__INT_ACK_BIST

#define LPDDR4__DENALI_CTL_357__MC_RESERVED19_MASK                   0xFF000000U
#define LPDDR4__DENALI_CTL_357__MC_RESERVED19_SHIFT                          24U
#define LPDDR4__DENALI_CTL_357__MC_RESERVED19_WIDTH                           8U
#define LPDDR4__MC_RESERVED19__REG DENALI_CTL_357
#define LPDDR4__MC_RESERVED19__FLD LPDDR4__DENALI_CTL_357__MC_RESERVED19

#define LPDDR4__DENALI_CTL_358__INT_ACK_DFI_MASK                     0x000000FFU
#define LPDDR4__DENALI_CTL_358__INT_ACK_DFI_SHIFT                             0U
#define LPDDR4__DENALI_CTL_358__INT_ACK_DFI_WIDTH                             8U
#define LPDDR4__INT_ACK_DFI__REG DENALI_CTL_358
#define LPDDR4__INT_ACK_DFI__FLD LPDDR4__DENALI_CTL_358__INT_ACK_DFI

#define LPDDR4__DENALI_CTL_358__MC_RESERVED20_MASK                   0x0000FF00U
#define LPDDR4__DENALI_CTL_358__MC_RESERVED20_SHIFT                           8U
#define LPDDR4__DENALI_CTL_358__MC_RESERVED20_WIDTH                           8U
#define LPDDR4__MC_RESERVED20__REG DENALI_CTL_358
#define LPDDR4__MC_RESERVED20__FLD LPDDR4__DENALI_CTL_358__MC_RESERVED20

#define LPDDR4__DENALI_CTL_358__INT_ACK_FREQ_MASK                    0x00FF0000U
#define LPDDR4__DENALI_CTL_358__INT_ACK_FREQ_SHIFT                           16U
#define LPDDR4__DENALI_CTL_358__INT_ACK_FREQ_WIDTH                            8U
#define LPDDR4__INT_ACK_FREQ__REG DENALI_CTL_358
#define LPDDR4__INT_ACK_FREQ__FLD LPDDR4__DENALI_CTL_358__INT_ACK_FREQ

#define LPDDR4__DENALI_CTL_358__INT_ACK_INIT_MASK                    0xFF000000U
#define LPDDR4__DENALI_CTL_358__INT_ACK_INIT_SHIFT                           24U
#define LPDDR4__DENALI_CTL_358__INT_ACK_INIT_WIDTH                            8U
#define LPDDR4__INT_ACK_INIT__REG DENALI_CTL_358
#define LPDDR4__INT_ACK_INIT__FLD LPDDR4__DENALI_CTL_358__INT_ACK_INIT

#define LPDDR4__DENALI_CTL_359__INT_ACK_MODE_MASK                    0x000000FFU
#define LPDDR4__DENALI_CTL_359__INT_ACK_MODE_SHIFT                            0U
#define LPDDR4__DENALI_CTL_359__INT_ACK_MODE_WIDTH                            8U
#define LPDDR4__INT_ACK_MODE__REG DENALI_CTL_359
#define LPDDR4__INT_ACK_MODE__FLD LPDDR4__DENALI_CTL_359__INT_ACK_MODE

#define LPDDR4__DENALI_CTL_359__INT_ACK_PARITY_MASK                  0x0000FF00U
#define LPDDR4__DENALI_CTL_359__INT_ACK_PARITY_SHIFT                          8U
#define LPDDR4__DENALI_CTL_359__INT_ACK_PARITY_WIDTH                          8U
#define LPDDR4__INT_ACK_PARITY__REG DENALI_CTL_359
#define LPDDR4__INT_ACK_PARITY__FLD LPDDR4__DENALI_CTL_359__INT_ACK_PARITY

#define LPDDR4__DENALI_CTL_360_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_360_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_360__INT_MASK_TIMEOUT_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_360__INT_MASK_TIMEOUT_SHIFT                        0U
#define LPDDR4__DENALI_CTL_360__INT_MASK_TIMEOUT_WIDTH                       32U
#define LPDDR4__INT_MASK_TIMEOUT__REG DENALI_CTL_360
#define LPDDR4__INT_MASK_TIMEOUT__FLD LPDDR4__DENALI_CTL_360__INT_MASK_TIMEOUT

#define LPDDR4__DENALI_CTL_361_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_361_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_361__MC_RESERVED21_MASK                   0x0000FFFFU
#define LPDDR4__DENALI_CTL_361__MC_RESERVED21_SHIFT                           0U
#define LPDDR4__DENALI_CTL_361__MC_RESERVED21_WIDTH                          16U
#define LPDDR4__MC_RESERVED21__REG DENALI_CTL_361
#define LPDDR4__MC_RESERVED21__FLD LPDDR4__DENALI_CTL_361__MC_RESERVED21

#define LPDDR4__DENALI_CTL_361__INT_MASK_LOWPOWER_MASK               0xFFFF0000U
#define LPDDR4__DENALI_CTL_361__INT_MASK_LOWPOWER_SHIFT                      16U
#define LPDDR4__DENALI_CTL_361__INT_MASK_LOWPOWER_WIDTH                      16U
#define LPDDR4__INT_MASK_LOWPOWER__REG DENALI_CTL_361
#define LPDDR4__INT_MASK_LOWPOWER__FLD LPDDR4__DENALI_CTL_361__INT_MASK_LOWPOWER

#define LPDDR4__DENALI_CTL_362_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_362_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_362__MC_RESERVED22_MASK                   0x0000FFFFU
#define LPDDR4__DENALI_CTL_362__MC_RESERVED22_SHIFT                           0U
#define LPDDR4__DENALI_CTL_362__MC_RESERVED22_WIDTH                          16U
#define LPDDR4__MC_RESERVED22__REG DENALI_CTL_362
#define LPDDR4__MC_RESERVED22__FLD LPDDR4__DENALI_CTL_362__MC_RESERVED22

#define LPDDR4__DENALI_CTL_362__MC_RESERVED23_MASK                   0xFFFF0000U
#define LPDDR4__DENALI_CTL_362__MC_RESERVED23_SHIFT                          16U
#define LPDDR4__DENALI_CTL_362__MC_RESERVED23_WIDTH                          16U
#define LPDDR4__MC_RESERVED23__REG DENALI_CTL_362
#define LPDDR4__MC_RESERVED23__FLD LPDDR4__DENALI_CTL_362__MC_RESERVED23

#define LPDDR4__DENALI_CTL_363_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_363_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_363__INT_MASK_TRAINING_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_363__INT_MASK_TRAINING_SHIFT                       0U
#define LPDDR4__DENALI_CTL_363__INT_MASK_TRAINING_WIDTH                      32U
#define LPDDR4__INT_MASK_TRAINING__REG DENALI_CTL_363
#define LPDDR4__INT_MASK_TRAINING__FLD LPDDR4__DENALI_CTL_363__INT_MASK_TRAINING

#define LPDDR4__DENALI_CTL_364_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_364_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_364__INT_MASK_USERIF_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_364__INT_MASK_USERIF_SHIFT                         0U
#define LPDDR4__DENALI_CTL_364__INT_MASK_USERIF_WIDTH                        32U
#define LPDDR4__INT_MASK_USERIF__REG DENALI_CTL_364
#define LPDDR4__INT_MASK_USERIF__FLD LPDDR4__DENALI_CTL_364__INT_MASK_USERIF

#define LPDDR4__DENALI_CTL_365_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_365_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_365__INT_MASK_MISC_MASK                   0x0000FFFFU
#define LPDDR4__DENALI_CTL_365__INT_MASK_MISC_SHIFT                           0U
#define LPDDR4__DENALI_CTL_365__INT_MASK_MISC_WIDTH                          16U
#define LPDDR4__INT_MASK_MISC__REG DENALI_CTL_365
#define LPDDR4__INT_MASK_MISC__FLD LPDDR4__DENALI_CTL_365__INT_MASK_MISC

#define LPDDR4__DENALI_CTL_365__INT_MASK_BIST_MASK                   0x00FF0000U
#define LPDDR4__DENALI_CTL_365__INT_MASK_BIST_SHIFT                          16U
#define LPDDR4__DENALI_CTL_365__INT_MASK_BIST_WIDTH                           8U
#define LPDDR4__INT_MASK_BIST__REG DENALI_CTL_365
#define LPDDR4__INT_MASK_BIST__FLD LPDDR4__DENALI_CTL_365__INT_MASK_BIST

#define LPDDR4__DENALI_CTL_365__MC_RESERVED24_MASK                   0xFF000000U
#define LPDDR4__DENALI_CTL_365__MC_RESERVED24_SHIFT                          24U
#define LPDDR4__DENALI_CTL_365__MC_RESERVED24_WIDTH                           8U
#define LPDDR4__MC_RESERVED24__REG DENALI_CTL_365
#define LPDDR4__MC_RESERVED24__FLD LPDDR4__DENALI_CTL_365__MC_RESERVED24

#define LPDDR4__DENALI_CTL_366_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_366_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_366__INT_MASK_DFI_MASK                    0x000000FFU
#define LPDDR4__DENALI_CTL_366__INT_MASK_DFI_SHIFT                            0U
#define LPDDR4__DENALI_CTL_366__INT_MASK_DFI_WIDTH                            8U
#define LPDDR4__INT_MASK_DFI__REG DENALI_CTL_366
#define LPDDR4__INT_MASK_DFI__FLD LPDDR4__DENALI_CTL_366__INT_MASK_DFI

#define LPDDR4__DENALI_CTL_366__MC_RESERVED25_MASK                   0x0000FF00U
#define LPDDR4__DENALI_CTL_366__MC_RESERVED25_SHIFT                           8U
#define LPDDR4__DENALI_CTL_366__MC_RESERVED25_WIDTH                           8U
#define LPDDR4__MC_RESERVED25__REG DENALI_CTL_366
#define LPDDR4__MC_RESERVED25__FLD LPDDR4__DENALI_CTL_366__MC_RESERVED25

#define LPDDR4__DENALI_CTL_366__INT_MASK_FREQ_MASK                   0x00FF0000U
#define LPDDR4__DENALI_CTL_366__INT_MASK_FREQ_SHIFT                          16U
#define LPDDR4__DENALI_CTL_366__INT_MASK_FREQ_WIDTH                           8U
#define LPDDR4__INT_MASK_FREQ__REG DENALI_CTL_366
#define LPDDR4__INT_MASK_FREQ__FLD LPDDR4__DENALI_CTL_366__INT_MASK_FREQ

#define LPDDR4__DENALI_CTL_366__INT_MASK_INIT_MASK                   0xFF000000U
#define LPDDR4__DENALI_CTL_366__INT_MASK_INIT_SHIFT                          24U
#define LPDDR4__DENALI_CTL_366__INT_MASK_INIT_WIDTH                           8U
#define LPDDR4__INT_MASK_INIT__REG DENALI_CTL_366
#define LPDDR4__INT_MASK_INIT__FLD LPDDR4__DENALI_CTL_366__INT_MASK_INIT

#define LPDDR4__DENALI_CTL_367_READ_MASK                             0x0000FFFFU
#define LPDDR4__DENALI_CTL_367_WRITE_MASK                            0x0000FFFFU
#define LPDDR4__DENALI_CTL_367__INT_MASK_MODE_MASK                   0x000000FFU
#define LPDDR4__DENALI_CTL_367__INT_MASK_MODE_SHIFT                           0U
#define LPDDR4__DENALI_CTL_367__INT_MASK_MODE_WIDTH                           8U
#define LPDDR4__INT_MASK_MODE__REG DENALI_CTL_367
#define LPDDR4__INT_MASK_MODE__FLD LPDDR4__DENALI_CTL_367__INT_MASK_MODE

#define LPDDR4__DENALI_CTL_367__INT_MASK_PARITY_MASK                 0x0000FF00U
#define LPDDR4__DENALI_CTL_367__INT_MASK_PARITY_SHIFT                         8U
#define LPDDR4__DENALI_CTL_367__INT_MASK_PARITY_WIDTH                         8U
#define LPDDR4__INT_MASK_PARITY__REG DENALI_CTL_367
#define LPDDR4__INT_MASK_PARITY__FLD LPDDR4__DENALI_CTL_367__INT_MASK_PARITY

#define LPDDR4__DENALI_CTL_368_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_368_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_368__OUT_OF_RANGE_ADDR_0_MASK             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_368__OUT_OF_RANGE_ADDR_0_SHIFT                     0U
#define LPDDR4__DENALI_CTL_368__OUT_OF_RANGE_ADDR_0_WIDTH                    32U
#define LPDDR4__OUT_OF_RANGE_ADDR_0__REG DENALI_CTL_368
#define LPDDR4__OUT_OF_RANGE_ADDR_0__FLD LPDDR4__DENALI_CTL_368__OUT_OF_RANGE_ADDR_0

#define LPDDR4__DENALI_CTL_369_READ_MASK                             0x7F0FFF07U
#define LPDDR4__DENALI_CTL_369_WRITE_MASK                            0x7F0FFF07U
#define LPDDR4__DENALI_CTL_369__OUT_OF_RANGE_ADDR_1_MASK             0x00000007U
#define LPDDR4__DENALI_CTL_369__OUT_OF_RANGE_ADDR_1_SHIFT                     0U
#define LPDDR4__DENALI_CTL_369__OUT_OF_RANGE_ADDR_1_WIDTH                     3U
#define LPDDR4__OUT_OF_RANGE_ADDR_1__REG DENALI_CTL_369
#define LPDDR4__OUT_OF_RANGE_ADDR_1__FLD LPDDR4__DENALI_CTL_369__OUT_OF_RANGE_ADDR_1

#define LPDDR4__DENALI_CTL_369__OUT_OF_RANGE_LENGTH_MASK             0x000FFF00U
#define LPDDR4__DENALI_CTL_369__OUT_OF_RANGE_LENGTH_SHIFT                     8U
#define LPDDR4__DENALI_CTL_369__OUT_OF_RANGE_LENGTH_WIDTH                    12U
#define LPDDR4__OUT_OF_RANGE_LENGTH__REG DENALI_CTL_369
#define LPDDR4__OUT_OF_RANGE_LENGTH__FLD LPDDR4__DENALI_CTL_369__OUT_OF_RANGE_LENGTH

#define LPDDR4__DENALI_CTL_369__OUT_OF_RANGE_TYPE_MASK               0x7F000000U
#define LPDDR4__DENALI_CTL_369__OUT_OF_RANGE_TYPE_SHIFT                      24U
#define LPDDR4__DENALI_CTL_369__OUT_OF_RANGE_TYPE_WIDTH                       7U
#define LPDDR4__OUT_OF_RANGE_TYPE__REG DENALI_CTL_369
#define LPDDR4__OUT_OF_RANGE_TYPE__FLD LPDDR4__DENALI_CTL_369__OUT_OF_RANGE_TYPE

#define LPDDR4__DENALI_CTL_370_READ_MASK                             0x0000003FU
#define LPDDR4__DENALI_CTL_370_WRITE_MASK                            0x0000003FU
#define LPDDR4__DENALI_CTL_370__OUT_OF_RANGE_SOURCE_ID_MASK          0x0000003FU
#define LPDDR4__DENALI_CTL_370__OUT_OF_RANGE_SOURCE_ID_SHIFT                  0U
#define LPDDR4__DENALI_CTL_370__OUT_OF_RANGE_SOURCE_ID_WIDTH                  6U
#define LPDDR4__OUT_OF_RANGE_SOURCE_ID__REG DENALI_CTL_370
#define LPDDR4__OUT_OF_RANGE_SOURCE_ID__FLD LPDDR4__DENALI_CTL_370__OUT_OF_RANGE_SOURCE_ID

#define LPDDR4__DENALI_CTL_371_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_371_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_371__BIST_EXP_DATA_0_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_371__BIST_EXP_DATA_0_SHIFT                         0U
#define LPDDR4__DENALI_CTL_371__BIST_EXP_DATA_0_WIDTH                        32U
#define LPDDR4__BIST_EXP_DATA_0__REG DENALI_CTL_371
#define LPDDR4__BIST_EXP_DATA_0__FLD LPDDR4__DENALI_CTL_371__BIST_EXP_DATA_0

#define LPDDR4__DENALI_CTL_372_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_372_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_372__BIST_EXP_DATA_1_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_372__BIST_EXP_DATA_1_SHIFT                         0U
#define LPDDR4__DENALI_CTL_372__BIST_EXP_DATA_1_WIDTH                        32U
#define LPDDR4__BIST_EXP_DATA_1__REG DENALI_CTL_372
#define LPDDR4__BIST_EXP_DATA_1__FLD LPDDR4__DENALI_CTL_372__BIST_EXP_DATA_1

#define LPDDR4__DENALI_CTL_373_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_373_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_373__BIST_EXP_DATA_2_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_373__BIST_EXP_DATA_2_SHIFT                         0U
#define LPDDR4__DENALI_CTL_373__BIST_EXP_DATA_2_WIDTH                        32U
#define LPDDR4__BIST_EXP_DATA_2__REG DENALI_CTL_373
#define LPDDR4__BIST_EXP_DATA_2__FLD LPDDR4__DENALI_CTL_373__BIST_EXP_DATA_2

#define LPDDR4__DENALI_CTL_374_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_374_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_374__BIST_EXP_DATA_3_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_374__BIST_EXP_DATA_3_SHIFT                         0U
#define LPDDR4__DENALI_CTL_374__BIST_EXP_DATA_3_WIDTH                        32U
#define LPDDR4__BIST_EXP_DATA_3__REG DENALI_CTL_374
#define LPDDR4__BIST_EXP_DATA_3__FLD LPDDR4__DENALI_CTL_374__BIST_EXP_DATA_3

#define LPDDR4__DENALI_CTL_375_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_375_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_375__BIST_FAIL_DATA_0_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_375__BIST_FAIL_DATA_0_SHIFT                        0U
#define LPDDR4__DENALI_CTL_375__BIST_FAIL_DATA_0_WIDTH                       32U
#define LPDDR4__BIST_FAIL_DATA_0__REG DENALI_CTL_375
#define LPDDR4__BIST_FAIL_DATA_0__FLD LPDDR4__DENALI_CTL_375__BIST_FAIL_DATA_0

#define LPDDR4__DENALI_CTL_376_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_376_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_376__BIST_FAIL_DATA_1_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_376__BIST_FAIL_DATA_1_SHIFT                        0U
#define LPDDR4__DENALI_CTL_376__BIST_FAIL_DATA_1_WIDTH                       32U
#define LPDDR4__BIST_FAIL_DATA_1__REG DENALI_CTL_376
#define LPDDR4__BIST_FAIL_DATA_1__FLD LPDDR4__DENALI_CTL_376__BIST_FAIL_DATA_1

#define LPDDR4__DENALI_CTL_377_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_377_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_377__BIST_FAIL_DATA_2_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_377__BIST_FAIL_DATA_2_SHIFT                        0U
#define LPDDR4__DENALI_CTL_377__BIST_FAIL_DATA_2_WIDTH                       32U
#define LPDDR4__BIST_FAIL_DATA_2__REG DENALI_CTL_377
#define LPDDR4__BIST_FAIL_DATA_2__FLD LPDDR4__DENALI_CTL_377__BIST_FAIL_DATA_2

#define LPDDR4__DENALI_CTL_378_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_378_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_378__BIST_FAIL_DATA_3_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_378__BIST_FAIL_DATA_3_SHIFT                        0U
#define LPDDR4__DENALI_CTL_378__BIST_FAIL_DATA_3_WIDTH                       32U
#define LPDDR4__BIST_FAIL_DATA_3__REG DENALI_CTL_378
#define LPDDR4__BIST_FAIL_DATA_3__FLD LPDDR4__DENALI_CTL_378__BIST_FAIL_DATA_3

#define LPDDR4__DENALI_CTL_379_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_379_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_379__BIST_FAIL_ADDR_0_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_379__BIST_FAIL_ADDR_0_SHIFT                        0U
#define LPDDR4__DENALI_CTL_379__BIST_FAIL_ADDR_0_WIDTH                       32U
#define LPDDR4__BIST_FAIL_ADDR_0__REG DENALI_CTL_379
#define LPDDR4__BIST_FAIL_ADDR_0__FLD LPDDR4__DENALI_CTL_379__BIST_FAIL_ADDR_0

#define LPDDR4__DENALI_CTL_380_READ_MASK                             0x00000007U
#define LPDDR4__DENALI_CTL_380_WRITE_MASK                            0x00000007U
#define LPDDR4__DENALI_CTL_380__BIST_FAIL_ADDR_1_MASK                0x00000007U
#define LPDDR4__DENALI_CTL_380__BIST_FAIL_ADDR_1_SHIFT                        0U
#define LPDDR4__DENALI_CTL_380__BIST_FAIL_ADDR_1_WIDTH                        3U
#define LPDDR4__BIST_FAIL_ADDR_1__REG DENALI_CTL_380
#define LPDDR4__BIST_FAIL_ADDR_1__FLD LPDDR4__DENALI_CTL_380__BIST_FAIL_ADDR_1

#define LPDDR4__DENALI_CTL_381_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_381_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_381__PORT_CMD_ERROR_ADDR_0_MASK           0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_381__PORT_CMD_ERROR_ADDR_0_SHIFT                   0U
#define LPDDR4__DENALI_CTL_381__PORT_CMD_ERROR_ADDR_0_WIDTH                  32U
#define LPDDR4__PORT_CMD_ERROR_ADDR_0__REG DENALI_CTL_381
#define LPDDR4__PORT_CMD_ERROR_ADDR_0__FLD LPDDR4__DENALI_CTL_381__PORT_CMD_ERROR_ADDR_0

#define LPDDR4__DENALI_CTL_382_READ_MASK                             0xFF033F07U
#define LPDDR4__DENALI_CTL_382_WRITE_MASK                            0xFF033F07U
#define LPDDR4__DENALI_CTL_382__PORT_CMD_ERROR_ADDR_1_MASK           0x00000007U
#define LPDDR4__DENALI_CTL_382__PORT_CMD_ERROR_ADDR_1_SHIFT                   0U
#define LPDDR4__DENALI_CTL_382__PORT_CMD_ERROR_ADDR_1_WIDTH                   3U
#define LPDDR4__PORT_CMD_ERROR_ADDR_1__REG DENALI_CTL_382
#define LPDDR4__PORT_CMD_ERROR_ADDR_1__FLD LPDDR4__DENALI_CTL_382__PORT_CMD_ERROR_ADDR_1

#define LPDDR4__DENALI_CTL_382__PORT_CMD_ERROR_ID_MASK               0x00003F00U
#define LPDDR4__DENALI_CTL_382__PORT_CMD_ERROR_ID_SHIFT                       8U
#define LPDDR4__DENALI_CTL_382__PORT_CMD_ERROR_ID_WIDTH                       6U
#define LPDDR4__PORT_CMD_ERROR_ID__REG DENALI_CTL_382
#define LPDDR4__PORT_CMD_ERROR_ID__FLD LPDDR4__DENALI_CTL_382__PORT_CMD_ERROR_ID

#define LPDDR4__DENALI_CTL_382__PORT_CMD_ERROR_TYPE_MASK             0x00030000U
#define LPDDR4__DENALI_CTL_382__PORT_CMD_ERROR_TYPE_SHIFT                    16U
#define LPDDR4__DENALI_CTL_382__PORT_CMD_ERROR_TYPE_WIDTH                     2U
#define LPDDR4__PORT_CMD_ERROR_TYPE__REG DENALI_CTL_382
#define LPDDR4__PORT_CMD_ERROR_TYPE__FLD LPDDR4__DENALI_CTL_382__PORT_CMD_ERROR_TYPE

#define LPDDR4__DENALI_CTL_382__TODTL_2CMD_F0_MASK                   0xFF000000U
#define LPDDR4__DENALI_CTL_382__TODTL_2CMD_F0_SHIFT                          24U
#define LPDDR4__DENALI_CTL_382__TODTL_2CMD_F0_WIDTH                           8U
#define LPDDR4__TODTL_2CMD_F0__REG DENALI_CTL_382
#define LPDDR4__TODTL_2CMD_F0__FLD LPDDR4__DENALI_CTL_382__TODTL_2CMD_F0

#define LPDDR4__DENALI_CTL_383_READ_MASK                             0x0FFF0F0FU
#define LPDDR4__DENALI_CTL_383_WRITE_MASK                            0x0FFF0F0FU
#define LPDDR4__DENALI_CTL_383__TODTH_WR_F0_MASK                     0x0000000FU
#define LPDDR4__DENALI_CTL_383__TODTH_WR_F0_SHIFT                             0U
#define LPDDR4__DENALI_CTL_383__TODTH_WR_F0_WIDTH                             4U
#define LPDDR4__TODTH_WR_F0__REG DENALI_CTL_383
#define LPDDR4__TODTH_WR_F0__FLD LPDDR4__DENALI_CTL_383__TODTH_WR_F0

#define LPDDR4__DENALI_CTL_383__TODTH_RD_F0_MASK                     0x00000F00U
#define LPDDR4__DENALI_CTL_383__TODTH_RD_F0_SHIFT                             8U
#define LPDDR4__DENALI_CTL_383__TODTH_RD_F0_WIDTH                             4U
#define LPDDR4__TODTH_RD_F0__REG DENALI_CTL_383
#define LPDDR4__TODTH_RD_F0__FLD LPDDR4__DENALI_CTL_383__TODTH_RD_F0

#define LPDDR4__DENALI_CTL_383__TODTL_2CMD_F1_MASK                   0x00FF0000U
#define LPDDR4__DENALI_CTL_383__TODTL_2CMD_F1_SHIFT                          16U
#define LPDDR4__DENALI_CTL_383__TODTL_2CMD_F1_WIDTH                           8U
#define LPDDR4__TODTL_2CMD_F1__REG DENALI_CTL_383
#define LPDDR4__TODTL_2CMD_F1__FLD LPDDR4__DENALI_CTL_383__TODTL_2CMD_F1

#define LPDDR4__DENALI_CTL_383__TODTH_WR_F1_MASK                     0x0F000000U
#define LPDDR4__DENALI_CTL_383__TODTH_WR_F1_SHIFT                            24U
#define LPDDR4__DENALI_CTL_383__TODTH_WR_F1_WIDTH                             4U
#define LPDDR4__TODTH_WR_F1__REG DENALI_CTL_383
#define LPDDR4__TODTH_WR_F1__FLD LPDDR4__DENALI_CTL_383__TODTH_WR_F1

#define LPDDR4__DENALI_CTL_384_READ_MASK                             0x0F0FFF0FU
#define LPDDR4__DENALI_CTL_384_WRITE_MASK                            0x0F0FFF0FU
#define LPDDR4__DENALI_CTL_384__TODTH_RD_F1_MASK                     0x0000000FU
#define LPDDR4__DENALI_CTL_384__TODTH_RD_F1_SHIFT                             0U
#define LPDDR4__DENALI_CTL_384__TODTH_RD_F1_WIDTH                             4U
#define LPDDR4__TODTH_RD_F1__REG DENALI_CTL_384
#define LPDDR4__TODTH_RD_F1__FLD LPDDR4__DENALI_CTL_384__TODTH_RD_F1

#define LPDDR4__DENALI_CTL_384__TODTL_2CMD_F2_MASK                   0x0000FF00U
#define LPDDR4__DENALI_CTL_384__TODTL_2CMD_F2_SHIFT                           8U
#define LPDDR4__DENALI_CTL_384__TODTL_2CMD_F2_WIDTH                           8U
#define LPDDR4__TODTL_2CMD_F2__REG DENALI_CTL_384
#define LPDDR4__TODTL_2CMD_F2__FLD LPDDR4__DENALI_CTL_384__TODTL_2CMD_F2

#define LPDDR4__DENALI_CTL_384__TODTH_WR_F2_MASK                     0x000F0000U
#define LPDDR4__DENALI_CTL_384__TODTH_WR_F2_SHIFT                            16U
#define LPDDR4__DENALI_CTL_384__TODTH_WR_F2_WIDTH                             4U
#define LPDDR4__TODTH_WR_F2__REG DENALI_CTL_384
#define LPDDR4__TODTH_WR_F2__FLD LPDDR4__DENALI_CTL_384__TODTH_WR_F2

#define LPDDR4__DENALI_CTL_384__TODTH_RD_F2_MASK                     0x0F000000U
#define LPDDR4__DENALI_CTL_384__TODTH_RD_F2_SHIFT                            24U
#define LPDDR4__DENALI_CTL_384__TODTH_RD_F2_WIDTH                             4U
#define LPDDR4__TODTH_RD_F2__REG DENALI_CTL_384
#define LPDDR4__TODTH_RD_F2__FLD LPDDR4__DENALI_CTL_384__TODTH_RD_F2

#define LPDDR4__DENALI_CTL_385_READ_MASK                             0x01010101U
#define LPDDR4__DENALI_CTL_385_WRITE_MASK                            0x01010101U
#define LPDDR4__DENALI_CTL_385__ODT_EN_F0_MASK                       0x00000001U
#define LPDDR4__DENALI_CTL_385__ODT_EN_F0_SHIFT                               0U
#define LPDDR4__DENALI_CTL_385__ODT_EN_F0_WIDTH                               1U
#define LPDDR4__DENALI_CTL_385__ODT_EN_F0_WOCLR                               0U
#define LPDDR4__DENALI_CTL_385__ODT_EN_F0_WOSET                               0U
#define LPDDR4__ODT_EN_F0__REG DENALI_CTL_385
#define LPDDR4__ODT_EN_F0__FLD LPDDR4__DENALI_CTL_385__ODT_EN_F0

#define LPDDR4__DENALI_CTL_385__ODT_EN_F1_MASK                       0x00000100U
#define LPDDR4__DENALI_CTL_385__ODT_EN_F1_SHIFT                               8U
#define LPDDR4__DENALI_CTL_385__ODT_EN_F1_WIDTH                               1U
#define LPDDR4__DENALI_CTL_385__ODT_EN_F1_WOCLR                               0U
#define LPDDR4__DENALI_CTL_385__ODT_EN_F1_WOSET                               0U
#define LPDDR4__ODT_EN_F1__REG DENALI_CTL_385
#define LPDDR4__ODT_EN_F1__FLD LPDDR4__DENALI_CTL_385__ODT_EN_F1

#define LPDDR4__DENALI_CTL_385__ODT_EN_F2_MASK                       0x00010000U
#define LPDDR4__DENALI_CTL_385__ODT_EN_F2_SHIFT                              16U
#define LPDDR4__DENALI_CTL_385__ODT_EN_F2_WIDTH                               1U
#define LPDDR4__DENALI_CTL_385__ODT_EN_F2_WOCLR                               0U
#define LPDDR4__DENALI_CTL_385__ODT_EN_F2_WOSET                               0U
#define LPDDR4__ODT_EN_F2__REG DENALI_CTL_385
#define LPDDR4__ODT_EN_F2__FLD LPDDR4__DENALI_CTL_385__ODT_EN_F2

#define LPDDR4__DENALI_CTL_385__EN_ODT_ASSERT_EXCEPT_RD_MASK         0x01000000U
#define LPDDR4__DENALI_CTL_385__EN_ODT_ASSERT_EXCEPT_RD_SHIFT                24U
#define LPDDR4__DENALI_CTL_385__EN_ODT_ASSERT_EXCEPT_RD_WIDTH                 1U
#define LPDDR4__DENALI_CTL_385__EN_ODT_ASSERT_EXCEPT_RD_WOCLR                 0U
#define LPDDR4__DENALI_CTL_385__EN_ODT_ASSERT_EXCEPT_RD_WOSET                 0U
#define LPDDR4__EN_ODT_ASSERT_EXCEPT_RD__REG DENALI_CTL_385
#define LPDDR4__EN_ODT_ASSERT_EXCEPT_RD__FLD LPDDR4__DENALI_CTL_385__EN_ODT_ASSERT_EXCEPT_RD

#define LPDDR4__DENALI_CTL_386_READ_MASK                             0x033F3F3FU
#define LPDDR4__DENALI_CTL_386_WRITE_MASK                            0x033F3F3FU
#define LPDDR4__DENALI_CTL_386__WR_TO_ODTH_F0_MASK                   0x0000003FU
#define LPDDR4__DENALI_CTL_386__WR_TO_ODTH_F0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_386__WR_TO_ODTH_F0_WIDTH                           6U
#define LPDDR4__WR_TO_ODTH_F0__REG DENALI_CTL_386
#define LPDDR4__WR_TO_ODTH_F0__FLD LPDDR4__DENALI_CTL_386__WR_TO_ODTH_F0

#define LPDDR4__DENALI_CTL_386__WR_TO_ODTH_F1_MASK                   0x00003F00U
#define LPDDR4__DENALI_CTL_386__WR_TO_ODTH_F1_SHIFT                           8U
#define LPDDR4__DENALI_CTL_386__WR_TO_ODTH_F1_WIDTH                           6U
#define LPDDR4__WR_TO_ODTH_F1__REG DENALI_CTL_386
#define LPDDR4__WR_TO_ODTH_F1__FLD LPDDR4__DENALI_CTL_386__WR_TO_ODTH_F1

#define LPDDR4__DENALI_CTL_386__WR_TO_ODTH_F2_MASK                   0x003F0000U
#define LPDDR4__DENALI_CTL_386__WR_TO_ODTH_F2_SHIFT                          16U
#define LPDDR4__DENALI_CTL_386__WR_TO_ODTH_F2_WIDTH                           6U
#define LPDDR4__WR_TO_ODTH_F2__REG DENALI_CTL_386
#define LPDDR4__WR_TO_ODTH_F2__FLD LPDDR4__DENALI_CTL_386__WR_TO_ODTH_F2

#define LPDDR4__DENALI_CTL_386__ODT_RD_MAP_CS0_MASK                  0x03000000U
#define LPDDR4__DENALI_CTL_386__ODT_RD_MAP_CS0_SHIFT                         24U
#define LPDDR4__DENALI_CTL_386__ODT_RD_MAP_CS0_WIDTH                          2U
#define LPDDR4__ODT_RD_MAP_CS0__REG DENALI_CTL_386
#define LPDDR4__ODT_RD_MAP_CS0__FLD LPDDR4__DENALI_CTL_386__ODT_RD_MAP_CS0

#define LPDDR4__DENALI_CTL_387_READ_MASK                             0x3F030303U
#define LPDDR4__DENALI_CTL_387_WRITE_MASK                            0x3F030303U
#define LPDDR4__DENALI_CTL_387__ODT_WR_MAP_CS0_MASK                  0x00000003U
#define LPDDR4__DENALI_CTL_387__ODT_WR_MAP_CS0_SHIFT                          0U
#define LPDDR4__DENALI_CTL_387__ODT_WR_MAP_CS0_WIDTH                          2U
#define LPDDR4__ODT_WR_MAP_CS0__REG DENALI_CTL_387
#define LPDDR4__ODT_WR_MAP_CS0__FLD LPDDR4__DENALI_CTL_387__ODT_WR_MAP_CS0

#define LPDDR4__DENALI_CTL_387__ODT_RD_MAP_CS1_MASK                  0x00000300U
#define LPDDR4__DENALI_CTL_387__ODT_RD_MAP_CS1_SHIFT                          8U
#define LPDDR4__DENALI_CTL_387__ODT_RD_MAP_CS1_WIDTH                          2U
#define LPDDR4__ODT_RD_MAP_CS1__REG DENALI_CTL_387
#define LPDDR4__ODT_RD_MAP_CS1__FLD LPDDR4__DENALI_CTL_387__ODT_RD_MAP_CS1

#define LPDDR4__DENALI_CTL_387__ODT_WR_MAP_CS1_MASK                  0x00030000U
#define LPDDR4__DENALI_CTL_387__ODT_WR_MAP_CS1_SHIFT                         16U
#define LPDDR4__DENALI_CTL_387__ODT_WR_MAP_CS1_WIDTH                          2U
#define LPDDR4__ODT_WR_MAP_CS1__REG DENALI_CTL_387
#define LPDDR4__ODT_WR_MAP_CS1__FLD LPDDR4__DENALI_CTL_387__ODT_WR_MAP_CS1

#define LPDDR4__DENALI_CTL_387__RD_TO_ODTH_F0_MASK                   0x3F000000U
#define LPDDR4__DENALI_CTL_387__RD_TO_ODTH_F0_SHIFT                          24U
#define LPDDR4__DENALI_CTL_387__RD_TO_ODTH_F0_WIDTH                           6U
#define LPDDR4__RD_TO_ODTH_F0__REG DENALI_CTL_387
#define LPDDR4__RD_TO_ODTH_F0__FLD LPDDR4__DENALI_CTL_387__RD_TO_ODTH_F0

#define LPDDR4__DENALI_CTL_388_READ_MASK                             0x1F1F3F3FU
#define LPDDR4__DENALI_CTL_388_WRITE_MASK                            0x1F1F3F3FU
#define LPDDR4__DENALI_CTL_388__RD_TO_ODTH_F1_MASK                   0x0000003FU
#define LPDDR4__DENALI_CTL_388__RD_TO_ODTH_F1_SHIFT                           0U
#define LPDDR4__DENALI_CTL_388__RD_TO_ODTH_F1_WIDTH                           6U
#define LPDDR4__RD_TO_ODTH_F1__REG DENALI_CTL_388
#define LPDDR4__RD_TO_ODTH_F1__FLD LPDDR4__DENALI_CTL_388__RD_TO_ODTH_F1

#define LPDDR4__DENALI_CTL_388__RD_TO_ODTH_F2_MASK                   0x00003F00U
#define LPDDR4__DENALI_CTL_388__RD_TO_ODTH_F2_SHIFT                           8U
#define LPDDR4__DENALI_CTL_388__RD_TO_ODTH_F2_WIDTH                           6U
#define LPDDR4__RD_TO_ODTH_F2__REG DENALI_CTL_388
#define LPDDR4__RD_TO_ODTH_F2__FLD LPDDR4__DENALI_CTL_388__RD_TO_ODTH_F2

#define LPDDR4__DENALI_CTL_388__RW2MRW_DLY_F0_MASK                   0x001F0000U
#define LPDDR4__DENALI_CTL_388__RW2MRW_DLY_F0_SHIFT                          16U
#define LPDDR4__DENALI_CTL_388__RW2MRW_DLY_F0_WIDTH                           5U
#define LPDDR4__RW2MRW_DLY_F0__REG DENALI_CTL_388
#define LPDDR4__RW2MRW_DLY_F0__FLD LPDDR4__DENALI_CTL_388__RW2MRW_DLY_F0

#define LPDDR4__DENALI_CTL_388__RW2MRW_DLY_F1_MASK                   0x1F000000U
#define LPDDR4__DENALI_CTL_388__RW2MRW_DLY_F1_SHIFT                          24U
#define LPDDR4__DENALI_CTL_388__RW2MRW_DLY_F1_WIDTH                           5U
#define LPDDR4__RW2MRW_DLY_F1__REG DENALI_CTL_388
#define LPDDR4__RW2MRW_DLY_F1__FLD LPDDR4__DENALI_CTL_388__RW2MRW_DLY_F1

#define LPDDR4__DENALI_CTL_389_READ_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_CTL_389_WRITE_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_CTL_389__RW2MRW_DLY_F2_MASK                   0x0000001FU
#define LPDDR4__DENALI_CTL_389__RW2MRW_DLY_F2_SHIFT                           0U
#define LPDDR4__DENALI_CTL_389__RW2MRW_DLY_F2_WIDTH                           5U
#define LPDDR4__RW2MRW_DLY_F2__REG DENALI_CTL_389
#define LPDDR4__RW2MRW_DLY_F2__FLD LPDDR4__DENALI_CTL_389__RW2MRW_DLY_F2

#define LPDDR4__DENALI_CTL_389__R2R_DIFFCS_DLY_F0_MASK               0x00001F00U
#define LPDDR4__DENALI_CTL_389__R2R_DIFFCS_DLY_F0_SHIFT                       8U
#define LPDDR4__DENALI_CTL_389__R2R_DIFFCS_DLY_F0_WIDTH                       5U
#define LPDDR4__R2R_DIFFCS_DLY_F0__REG DENALI_CTL_389
#define LPDDR4__R2R_DIFFCS_DLY_F0__FLD LPDDR4__DENALI_CTL_389__R2R_DIFFCS_DLY_F0

#define LPDDR4__DENALI_CTL_389__R2W_DIFFCS_DLY_F0_MASK               0x001F0000U
#define LPDDR4__DENALI_CTL_389__R2W_DIFFCS_DLY_F0_SHIFT                      16U
#define LPDDR4__DENALI_CTL_389__R2W_DIFFCS_DLY_F0_WIDTH                       5U
#define LPDDR4__R2W_DIFFCS_DLY_F0__REG DENALI_CTL_389
#define LPDDR4__R2W_DIFFCS_DLY_F0__FLD LPDDR4__DENALI_CTL_389__R2W_DIFFCS_DLY_F0

#define LPDDR4__DENALI_CTL_389__W2R_DIFFCS_DLY_F0_MASK               0x1F000000U
#define LPDDR4__DENALI_CTL_389__W2R_DIFFCS_DLY_F0_SHIFT                      24U
#define LPDDR4__DENALI_CTL_389__W2R_DIFFCS_DLY_F0_WIDTH                       5U
#define LPDDR4__W2R_DIFFCS_DLY_F0__REG DENALI_CTL_389
#define LPDDR4__W2R_DIFFCS_DLY_F0__FLD LPDDR4__DENALI_CTL_389__W2R_DIFFCS_DLY_F0

#define LPDDR4__DENALI_CTL_390_READ_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_CTL_390_WRITE_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_CTL_390__W2W_DIFFCS_DLY_F0_MASK               0x0000001FU
#define LPDDR4__DENALI_CTL_390__W2W_DIFFCS_DLY_F0_SHIFT                       0U
#define LPDDR4__DENALI_CTL_390__W2W_DIFFCS_DLY_F0_WIDTH                       5U
#define LPDDR4__W2W_DIFFCS_DLY_F0__REG DENALI_CTL_390
#define LPDDR4__W2W_DIFFCS_DLY_F0__FLD LPDDR4__DENALI_CTL_390__W2W_DIFFCS_DLY_F0

#define LPDDR4__DENALI_CTL_390__R2R_DIFFCS_DLY_F1_MASK               0x00001F00U
#define LPDDR4__DENALI_CTL_390__R2R_DIFFCS_DLY_F1_SHIFT                       8U
#define LPDDR4__DENALI_CTL_390__R2R_DIFFCS_DLY_F1_WIDTH                       5U
#define LPDDR4__R2R_DIFFCS_DLY_F1__REG DENALI_CTL_390
#define LPDDR4__R2R_DIFFCS_DLY_F1__FLD LPDDR4__DENALI_CTL_390__R2R_DIFFCS_DLY_F1

#define LPDDR4__DENALI_CTL_390__R2W_DIFFCS_DLY_F1_MASK               0x001F0000U
#define LPDDR4__DENALI_CTL_390__R2W_DIFFCS_DLY_F1_SHIFT                      16U
#define LPDDR4__DENALI_CTL_390__R2W_DIFFCS_DLY_F1_WIDTH                       5U
#define LPDDR4__R2W_DIFFCS_DLY_F1__REG DENALI_CTL_390
#define LPDDR4__R2W_DIFFCS_DLY_F1__FLD LPDDR4__DENALI_CTL_390__R2W_DIFFCS_DLY_F1

#define LPDDR4__DENALI_CTL_390__W2R_DIFFCS_DLY_F1_MASK               0x1F000000U
#define LPDDR4__DENALI_CTL_390__W2R_DIFFCS_DLY_F1_SHIFT                      24U
#define LPDDR4__DENALI_CTL_390__W2R_DIFFCS_DLY_F1_WIDTH                       5U
#define LPDDR4__W2R_DIFFCS_DLY_F1__REG DENALI_CTL_390
#define LPDDR4__W2R_DIFFCS_DLY_F1__FLD LPDDR4__DENALI_CTL_390__W2R_DIFFCS_DLY_F1

#define LPDDR4__DENALI_CTL_391_READ_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_CTL_391_WRITE_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_CTL_391__W2W_DIFFCS_DLY_F1_MASK               0x0000001FU
#define LPDDR4__DENALI_CTL_391__W2W_DIFFCS_DLY_F1_SHIFT                       0U
#define LPDDR4__DENALI_CTL_391__W2W_DIFFCS_DLY_F1_WIDTH                       5U
#define LPDDR4__W2W_DIFFCS_DLY_F1__REG DENALI_CTL_391
#define LPDDR4__W2W_DIFFCS_DLY_F1__FLD LPDDR4__DENALI_CTL_391__W2W_DIFFCS_DLY_F1

#define LPDDR4__DENALI_CTL_391__R2R_DIFFCS_DLY_F2_MASK               0x00001F00U
#define LPDDR4__DENALI_CTL_391__R2R_DIFFCS_DLY_F2_SHIFT                       8U
#define LPDDR4__DENALI_CTL_391__R2R_DIFFCS_DLY_F2_WIDTH                       5U
#define LPDDR4__R2R_DIFFCS_DLY_F2__REG DENALI_CTL_391
#define LPDDR4__R2R_DIFFCS_DLY_F2__FLD LPDDR4__DENALI_CTL_391__R2R_DIFFCS_DLY_F2

#define LPDDR4__DENALI_CTL_391__R2W_DIFFCS_DLY_F2_MASK               0x001F0000U
#define LPDDR4__DENALI_CTL_391__R2W_DIFFCS_DLY_F2_SHIFT                      16U
#define LPDDR4__DENALI_CTL_391__R2W_DIFFCS_DLY_F2_WIDTH                       5U
#define LPDDR4__R2W_DIFFCS_DLY_F2__REG DENALI_CTL_391
#define LPDDR4__R2W_DIFFCS_DLY_F2__FLD LPDDR4__DENALI_CTL_391__R2W_DIFFCS_DLY_F2

#define LPDDR4__DENALI_CTL_391__W2R_DIFFCS_DLY_F2_MASK               0x1F000000U
#define LPDDR4__DENALI_CTL_391__W2R_DIFFCS_DLY_F2_SHIFT                      24U
#define LPDDR4__DENALI_CTL_391__W2R_DIFFCS_DLY_F2_WIDTH                       5U
#define LPDDR4__W2R_DIFFCS_DLY_F2__REG DENALI_CTL_391
#define LPDDR4__W2R_DIFFCS_DLY_F2__FLD LPDDR4__DENALI_CTL_391__W2R_DIFFCS_DLY_F2

#define LPDDR4__DENALI_CTL_392_READ_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_CTL_392_WRITE_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_CTL_392__W2W_DIFFCS_DLY_F2_MASK               0x0000001FU
#define LPDDR4__DENALI_CTL_392__W2W_DIFFCS_DLY_F2_SHIFT                       0U
#define LPDDR4__DENALI_CTL_392__W2W_DIFFCS_DLY_F2_WIDTH                       5U
#define LPDDR4__W2W_DIFFCS_DLY_F2__REG DENALI_CTL_392
#define LPDDR4__W2W_DIFFCS_DLY_F2__FLD LPDDR4__DENALI_CTL_392__W2W_DIFFCS_DLY_F2

#define LPDDR4__DENALI_CTL_392__R2W_SAMECS_DLY_F0_MASK               0x00001F00U
#define LPDDR4__DENALI_CTL_392__R2W_SAMECS_DLY_F0_SHIFT                       8U
#define LPDDR4__DENALI_CTL_392__R2W_SAMECS_DLY_F0_WIDTH                       5U
#define LPDDR4__R2W_SAMECS_DLY_F0__REG DENALI_CTL_392
#define LPDDR4__R2W_SAMECS_DLY_F0__FLD LPDDR4__DENALI_CTL_392__R2W_SAMECS_DLY_F0

#define LPDDR4__DENALI_CTL_392__R2W_SAMECS_DLY_F1_MASK               0x001F0000U
#define LPDDR4__DENALI_CTL_392__R2W_SAMECS_DLY_F1_SHIFT                      16U
#define LPDDR4__DENALI_CTL_392__R2W_SAMECS_DLY_F1_WIDTH                       5U
#define LPDDR4__R2W_SAMECS_DLY_F1__REG DENALI_CTL_392
#define LPDDR4__R2W_SAMECS_DLY_F1__FLD LPDDR4__DENALI_CTL_392__R2W_SAMECS_DLY_F1

#define LPDDR4__DENALI_CTL_392__R2W_SAMECS_DLY_F2_MASK               0x1F000000U
#define LPDDR4__DENALI_CTL_392__R2W_SAMECS_DLY_F2_SHIFT                      24U
#define LPDDR4__DENALI_CTL_392__R2W_SAMECS_DLY_F2_WIDTH                       5U
#define LPDDR4__R2W_SAMECS_DLY_F2__REG DENALI_CTL_392
#define LPDDR4__R2W_SAMECS_DLY_F2__FLD LPDDR4__DENALI_CTL_392__R2W_SAMECS_DLY_F2

#define LPDDR4__DENALI_CTL_393_READ_MASK                             0x0F1F1F1FU
#define LPDDR4__DENALI_CTL_393_WRITE_MASK                            0x0F1F1F1FU
#define LPDDR4__DENALI_CTL_393__R2R_SAMECS_DLY_MASK                  0x0000001FU
#define LPDDR4__DENALI_CTL_393__R2R_SAMECS_DLY_SHIFT                          0U
#define LPDDR4__DENALI_CTL_393__R2R_SAMECS_DLY_WIDTH                          5U
#define LPDDR4__R2R_SAMECS_DLY__REG DENALI_CTL_393
#define LPDDR4__R2R_SAMECS_DLY__FLD LPDDR4__DENALI_CTL_393__R2R_SAMECS_DLY

#define LPDDR4__DENALI_CTL_393__W2R_SAMECS_DLY_MASK                  0x00001F00U
#define LPDDR4__DENALI_CTL_393__W2R_SAMECS_DLY_SHIFT                          8U
#define LPDDR4__DENALI_CTL_393__W2R_SAMECS_DLY_WIDTH                          5U
#define LPDDR4__W2R_SAMECS_DLY__REG DENALI_CTL_393
#define LPDDR4__W2R_SAMECS_DLY__FLD LPDDR4__DENALI_CTL_393__W2R_SAMECS_DLY

#define LPDDR4__DENALI_CTL_393__W2W_SAMECS_DLY_MASK                  0x001F0000U
#define LPDDR4__DENALI_CTL_393__W2W_SAMECS_DLY_SHIFT                         16U
#define LPDDR4__DENALI_CTL_393__W2W_SAMECS_DLY_WIDTH                          5U
#define LPDDR4__W2W_SAMECS_DLY__REG DENALI_CTL_393
#define LPDDR4__W2W_SAMECS_DLY__FLD LPDDR4__DENALI_CTL_393__W2W_SAMECS_DLY

#define LPDDR4__DENALI_CTL_393__TDQSCK_MAX_F0_MASK                   0x0F000000U
#define LPDDR4__DENALI_CTL_393__TDQSCK_MAX_F0_SHIFT                          24U
#define LPDDR4__DENALI_CTL_393__TDQSCK_MAX_F0_WIDTH                           4U
#define LPDDR4__TDQSCK_MAX_F0__REG DENALI_CTL_393
#define LPDDR4__TDQSCK_MAX_F0__FLD LPDDR4__DENALI_CTL_393__TDQSCK_MAX_F0

#define LPDDR4__DENALI_CTL_394_READ_MASK                             0x0F070F07U
#define LPDDR4__DENALI_CTL_394_WRITE_MASK                            0x0F070F07U
#define LPDDR4__DENALI_CTL_394__TDQSCK_MIN_F0_MASK                   0x00000007U
#define LPDDR4__DENALI_CTL_394__TDQSCK_MIN_F0_SHIFT                           0U
#define LPDDR4__DENALI_CTL_394__TDQSCK_MIN_F0_WIDTH                           3U
#define LPDDR4__TDQSCK_MIN_F0__REG DENALI_CTL_394
#define LPDDR4__TDQSCK_MIN_F0__FLD LPDDR4__DENALI_CTL_394__TDQSCK_MIN_F0

#define LPDDR4__DENALI_CTL_394__TDQSCK_MAX_F1_MASK                   0x00000F00U
#define LPDDR4__DENALI_CTL_394__TDQSCK_MAX_F1_SHIFT                           8U
#define LPDDR4__DENALI_CTL_394__TDQSCK_MAX_F1_WIDTH                           4U
#define LPDDR4__TDQSCK_MAX_F1__REG DENALI_CTL_394
#define LPDDR4__TDQSCK_MAX_F1__FLD LPDDR4__DENALI_CTL_394__TDQSCK_MAX_F1

#define LPDDR4__DENALI_CTL_394__TDQSCK_MIN_F1_MASK                   0x00070000U
#define LPDDR4__DENALI_CTL_394__TDQSCK_MIN_F1_SHIFT                          16U
#define LPDDR4__DENALI_CTL_394__TDQSCK_MIN_F1_WIDTH                           3U
#define LPDDR4__TDQSCK_MIN_F1__REG DENALI_CTL_394
#define LPDDR4__TDQSCK_MIN_F1__FLD LPDDR4__DENALI_CTL_394__TDQSCK_MIN_F1

#define LPDDR4__DENALI_CTL_394__TDQSCK_MAX_F2_MASK                   0x0F000000U
#define LPDDR4__DENALI_CTL_394__TDQSCK_MAX_F2_SHIFT                          24U
#define LPDDR4__DENALI_CTL_394__TDQSCK_MAX_F2_WIDTH                           4U
#define LPDDR4__TDQSCK_MAX_F2__REG DENALI_CTL_394
#define LPDDR4__TDQSCK_MAX_F2__FLD LPDDR4__DENALI_CTL_394__TDQSCK_MAX_F2

#define LPDDR4__DENALI_CTL_395_READ_MASK                             0x07010107U
#define LPDDR4__DENALI_CTL_395_WRITE_MASK                            0x07010107U
#define LPDDR4__DENALI_CTL_395__TDQSCK_MIN_F2_MASK                   0x00000007U
#define LPDDR4__DENALI_CTL_395__TDQSCK_MIN_F2_SHIFT                           0U
#define LPDDR4__DENALI_CTL_395__TDQSCK_MIN_F2_WIDTH                           3U
#define LPDDR4__TDQSCK_MIN_F2__REG DENALI_CTL_395
#define LPDDR4__TDQSCK_MIN_F2__FLD LPDDR4__DENALI_CTL_395__TDQSCK_MIN_F2

#define LPDDR4__DENALI_CTL_395__AXI0_ALL_STROBES_USED_ENABLE_MASK    0x00000100U
#define LPDDR4__DENALI_CTL_395__AXI0_ALL_STROBES_USED_ENABLE_SHIFT            8U
#define LPDDR4__DENALI_CTL_395__AXI0_ALL_STROBES_USED_ENABLE_WIDTH            1U
#define LPDDR4__DENALI_CTL_395__AXI0_ALL_STROBES_USED_ENABLE_WOCLR            0U
#define LPDDR4__DENALI_CTL_395__AXI0_ALL_STROBES_USED_ENABLE_WOSET            0U
#define LPDDR4__AXI0_ALL_STROBES_USED_ENABLE__REG DENALI_CTL_395
#define LPDDR4__AXI0_ALL_STROBES_USED_ENABLE__FLD LPDDR4__DENALI_CTL_395__AXI0_ALL_STROBES_USED_ENABLE

#define LPDDR4__DENALI_CTL_395__AXI0_FIXED_PORT_PRIORITY_ENABLE_MASK 0x00010000U
#define LPDDR4__DENALI_CTL_395__AXI0_FIXED_PORT_PRIORITY_ENABLE_SHIFT        16U
#define LPDDR4__DENALI_CTL_395__AXI0_FIXED_PORT_PRIORITY_ENABLE_WIDTH         1U
#define LPDDR4__DENALI_CTL_395__AXI0_FIXED_PORT_PRIORITY_ENABLE_WOCLR         0U
#define LPDDR4__DENALI_CTL_395__AXI0_FIXED_PORT_PRIORITY_ENABLE_WOSET         0U
#define LPDDR4__AXI0_FIXED_PORT_PRIORITY_ENABLE__REG DENALI_CTL_395
#define LPDDR4__AXI0_FIXED_PORT_PRIORITY_ENABLE__FLD LPDDR4__DENALI_CTL_395__AXI0_FIXED_PORT_PRIORITY_ENABLE

#define LPDDR4__DENALI_CTL_395__AXI0_R_PRIORITY_MASK                 0x07000000U
#define LPDDR4__DENALI_CTL_395__AXI0_R_PRIORITY_SHIFT                        24U
#define LPDDR4__DENALI_CTL_395__AXI0_R_PRIORITY_WIDTH                         3U
#define LPDDR4__AXI0_R_PRIORITY__REG DENALI_CTL_395
#define LPDDR4__AXI0_R_PRIORITY__FLD LPDDR4__DENALI_CTL_395__AXI0_R_PRIORITY

#define LPDDR4__DENALI_CTL_396_READ_MASK                             0xFF010307U
#define LPDDR4__DENALI_CTL_396_WRITE_MASK                            0xFF010307U
#define LPDDR4__DENALI_CTL_396__AXI0_W_PRIORITY_MASK                 0x00000007U
#define LPDDR4__DENALI_CTL_396__AXI0_W_PRIORITY_SHIFT                         0U
#define LPDDR4__DENALI_CTL_396__AXI0_W_PRIORITY_WIDTH                         3U
#define LPDDR4__AXI0_W_PRIORITY__REG DENALI_CTL_396
#define LPDDR4__AXI0_W_PRIORITY__FLD LPDDR4__DENALI_CTL_396__AXI0_W_PRIORITY

#define LPDDR4__DENALI_CTL_396__CKE_STATUS_MASK                      0x00000300U
#define LPDDR4__DENALI_CTL_396__CKE_STATUS_SHIFT                              8U
#define LPDDR4__DENALI_CTL_396__CKE_STATUS_WIDTH                              2U
#define LPDDR4__CKE_STATUS__REG DENALI_CTL_396
#define LPDDR4__CKE_STATUS__FLD LPDDR4__DENALI_CTL_396__CKE_STATUS

#define LPDDR4__DENALI_CTL_396__MEM_RST_VALID_MASK                   0x00010000U
#define LPDDR4__DENALI_CTL_396__MEM_RST_VALID_SHIFT                          16U
#define LPDDR4__DENALI_CTL_396__MEM_RST_VALID_WIDTH                           1U
#define LPDDR4__DENALI_CTL_396__MEM_RST_VALID_WOCLR                           0U
#define LPDDR4__DENALI_CTL_396__MEM_RST_VALID_WOSET                           0U
#define LPDDR4__MEM_RST_VALID__REG DENALI_CTL_396
#define LPDDR4__MEM_RST_VALID__FLD LPDDR4__DENALI_CTL_396__MEM_RST_VALID

#define LPDDR4__DENALI_CTL_396__TDFI_PHY_RDLAT_F0_MASK               0xFF000000U
#define LPDDR4__DENALI_CTL_396__TDFI_PHY_RDLAT_F0_SHIFT                      24U
#define LPDDR4__DENALI_CTL_396__TDFI_PHY_RDLAT_F0_WIDTH                       8U
#define LPDDR4__TDFI_PHY_RDLAT_F0__REG DENALI_CTL_396
#define LPDDR4__TDFI_PHY_RDLAT_F0__FLD LPDDR4__DENALI_CTL_396__TDFI_PHY_RDLAT_F0

#define LPDDR4__DENALI_CTL_397_READ_MASK                             0x001FFFFFU
#define LPDDR4__DENALI_CTL_397_WRITE_MASK                            0x001FFFFFU
#define LPDDR4__DENALI_CTL_397__TDFI_CTRLUPD_MAX_F0_MASK             0x001FFFFFU
#define LPDDR4__DENALI_CTL_397__TDFI_CTRLUPD_MAX_F0_SHIFT                     0U
#define LPDDR4__DENALI_CTL_397__TDFI_CTRLUPD_MAX_F0_WIDTH                    21U
#define LPDDR4__TDFI_CTRLUPD_MAX_F0__REG DENALI_CTL_397
#define LPDDR4__TDFI_CTRLUPD_MAX_F0__FLD LPDDR4__DENALI_CTL_397__TDFI_CTRLUPD_MAX_F0

#define LPDDR4__DENALI_CTL_398_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_398_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_398__TDFI_PHYUPD_TYPE0_F0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_398__TDFI_PHYUPD_TYPE0_F0_SHIFT                    0U
#define LPDDR4__DENALI_CTL_398__TDFI_PHYUPD_TYPE0_F0_WIDTH                   32U
#define LPDDR4__TDFI_PHYUPD_TYPE0_F0__REG DENALI_CTL_398
#define LPDDR4__TDFI_PHYUPD_TYPE0_F0__FLD LPDDR4__DENALI_CTL_398__TDFI_PHYUPD_TYPE0_F0

#define LPDDR4__DENALI_CTL_399_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_399_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_399__TDFI_PHYUPD_TYPE1_F0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_399__TDFI_PHYUPD_TYPE1_F0_SHIFT                    0U
#define LPDDR4__DENALI_CTL_399__TDFI_PHYUPD_TYPE1_F0_WIDTH                   32U
#define LPDDR4__TDFI_PHYUPD_TYPE1_F0__REG DENALI_CTL_399
#define LPDDR4__TDFI_PHYUPD_TYPE1_F0__FLD LPDDR4__DENALI_CTL_399__TDFI_PHYUPD_TYPE1_F0

#define LPDDR4__DENALI_CTL_400_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_400_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_400__TDFI_PHYUPD_TYPE2_F0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_400__TDFI_PHYUPD_TYPE2_F0_SHIFT                    0U
#define LPDDR4__DENALI_CTL_400__TDFI_PHYUPD_TYPE2_F0_WIDTH                   32U
#define LPDDR4__TDFI_PHYUPD_TYPE2_F0__REG DENALI_CTL_400
#define LPDDR4__TDFI_PHYUPD_TYPE2_F0__FLD LPDDR4__DENALI_CTL_400__TDFI_PHYUPD_TYPE2_F0

#define LPDDR4__DENALI_CTL_401_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_401_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_401__TDFI_PHYUPD_TYPE3_F0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_401__TDFI_PHYUPD_TYPE3_F0_SHIFT                    0U
#define LPDDR4__DENALI_CTL_401__TDFI_PHYUPD_TYPE3_F0_WIDTH                   32U
#define LPDDR4__TDFI_PHYUPD_TYPE3_F0__REG DENALI_CTL_401
#define LPDDR4__TDFI_PHYUPD_TYPE3_F0__FLD LPDDR4__DENALI_CTL_401__TDFI_PHYUPD_TYPE3_F0

#define LPDDR4__DENALI_CTL_402_READ_MASK                             0x007FFFFFU
#define LPDDR4__DENALI_CTL_402_WRITE_MASK                            0x007FFFFFU
#define LPDDR4__DENALI_CTL_402__TDFI_PHYUPD_RESP_F0_MASK             0x007FFFFFU
#define LPDDR4__DENALI_CTL_402__TDFI_PHYUPD_RESP_F0_SHIFT                     0U
#define LPDDR4__DENALI_CTL_402__TDFI_PHYUPD_RESP_F0_WIDTH                    23U
#define LPDDR4__TDFI_PHYUPD_RESP_F0__REG DENALI_CTL_402
#define LPDDR4__TDFI_PHYUPD_RESP_F0__FLD LPDDR4__DENALI_CTL_402__TDFI_PHYUPD_RESP_F0

#define LPDDR4__DENALI_CTL_403_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_403_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_403__TDFI_CTRLUPD_INTERVAL_F0_MASK        0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_403__TDFI_CTRLUPD_INTERVAL_F0_SHIFT                0U
#define LPDDR4__DENALI_CTL_403__TDFI_CTRLUPD_INTERVAL_F0_WIDTH               32U
#define LPDDR4__TDFI_CTRLUPD_INTERVAL_F0__REG DENALI_CTL_403
#define LPDDR4__TDFI_CTRLUPD_INTERVAL_F0__FLD LPDDR4__DENALI_CTL_403__TDFI_CTRLUPD_INTERVAL_F0

#define LPDDR4__DENALI_CTL_404_READ_MASK                             0xFFFF070FU
#define LPDDR4__DENALI_CTL_404_WRITE_MASK                            0xFFFF070FU
#define LPDDR4__DENALI_CTL_404__TDFI_CTRL_DELAY_F0_MASK              0x0000000FU
#define LPDDR4__DENALI_CTL_404__TDFI_CTRL_DELAY_F0_SHIFT                      0U
#define LPDDR4__DENALI_CTL_404__TDFI_CTRL_DELAY_F0_WIDTH                      4U
#define LPDDR4__TDFI_CTRL_DELAY_F0__REG DENALI_CTL_404
#define LPDDR4__TDFI_CTRL_DELAY_F0__FLD LPDDR4__DENALI_CTL_404__TDFI_CTRL_DELAY_F0

#define LPDDR4__DENALI_CTL_404__TDFI_PHY_WRDATA_F0_MASK              0x00000700U
#define LPDDR4__DENALI_CTL_404__TDFI_PHY_WRDATA_F0_SHIFT                      8U
#define LPDDR4__DENALI_CTL_404__TDFI_PHY_WRDATA_F0_WIDTH                      3U
#define LPDDR4__TDFI_PHY_WRDATA_F0__REG DENALI_CTL_404
#define LPDDR4__TDFI_PHY_WRDATA_F0__FLD LPDDR4__DENALI_CTL_404__TDFI_PHY_WRDATA_F0

#define LPDDR4__DENALI_CTL_404__TDFI_RDCSLAT_F0_MASK                 0x00FF0000U
#define LPDDR4__DENALI_CTL_404__TDFI_RDCSLAT_F0_SHIFT                        16U
#define LPDDR4__DENALI_CTL_404__TDFI_RDCSLAT_F0_WIDTH                         8U
#define LPDDR4__TDFI_RDCSLAT_F0__REG DENALI_CTL_404
#define LPDDR4__TDFI_RDCSLAT_F0__FLD LPDDR4__DENALI_CTL_404__TDFI_RDCSLAT_F0

#define LPDDR4__DENALI_CTL_404__TDFI_RDDATA_EN_F0_MASK               0xFF000000U
#define LPDDR4__DENALI_CTL_404__TDFI_RDDATA_EN_F0_SHIFT                      24U
#define LPDDR4__DENALI_CTL_404__TDFI_RDDATA_EN_F0_WIDTH                       8U
#define LPDDR4__TDFI_RDDATA_EN_F0__REG DENALI_CTL_404
#define LPDDR4__TDFI_RDDATA_EN_F0__FLD LPDDR4__DENALI_CTL_404__TDFI_RDDATA_EN_F0

#define LPDDR4__DENALI_CTL_405_READ_MASK                             0xFF7FFFFFU
#define LPDDR4__DENALI_CTL_405_WRITE_MASK                            0xFF7FFFFFU
#define LPDDR4__DENALI_CTL_405__TDFI_WRCSLAT_F0_MASK                 0x000000FFU
#define LPDDR4__DENALI_CTL_405__TDFI_WRCSLAT_F0_SHIFT                         0U
#define LPDDR4__DENALI_CTL_405__TDFI_WRCSLAT_F0_WIDTH                         8U
#define LPDDR4__TDFI_WRCSLAT_F0__REG DENALI_CTL_405
#define LPDDR4__TDFI_WRCSLAT_F0__FLD LPDDR4__DENALI_CTL_405__TDFI_WRCSLAT_F0

#define LPDDR4__DENALI_CTL_405__TDFI_PHY_WRLAT_F0_MASK               0x0000FF00U
#define LPDDR4__DENALI_CTL_405__TDFI_PHY_WRLAT_F0_SHIFT                       8U
#define LPDDR4__DENALI_CTL_405__TDFI_PHY_WRLAT_F0_WIDTH                       8U
#define LPDDR4__TDFI_PHY_WRLAT_F0__REG DENALI_CTL_405
#define LPDDR4__TDFI_PHY_WRLAT_F0__FLD LPDDR4__DENALI_CTL_405__TDFI_PHY_WRLAT_F0

#define LPDDR4__DENALI_CTL_405__TDFI_CTRLMSG_RESP_F0_MASK            0x007F0000U
#define LPDDR4__DENALI_CTL_405__TDFI_CTRLMSG_RESP_F0_SHIFT                   16U
#define LPDDR4__DENALI_CTL_405__TDFI_CTRLMSG_RESP_F0_WIDTH                    7U
#define LPDDR4__TDFI_CTRLMSG_RESP_F0__REG DENALI_CTL_405
#define LPDDR4__TDFI_CTRLMSG_RESP_F0__FLD LPDDR4__DENALI_CTL_405__TDFI_CTRLMSG_RESP_F0

#define LPDDR4__DENALI_CTL_405__TDFI_PHY_RDLAT_F1_MASK               0xFF000000U
#define LPDDR4__DENALI_CTL_405__TDFI_PHY_RDLAT_F1_SHIFT                      24U
#define LPDDR4__DENALI_CTL_405__TDFI_PHY_RDLAT_F1_WIDTH                       8U
#define LPDDR4__TDFI_PHY_RDLAT_F1__REG DENALI_CTL_405
#define LPDDR4__TDFI_PHY_RDLAT_F1__FLD LPDDR4__DENALI_CTL_405__TDFI_PHY_RDLAT_F1

#define LPDDR4__DENALI_CTL_406_READ_MASK                             0x001FFFFFU
#define LPDDR4__DENALI_CTL_406_WRITE_MASK                            0x001FFFFFU
#define LPDDR4__DENALI_CTL_406__TDFI_CTRLUPD_MAX_F1_MASK             0x001FFFFFU
#define LPDDR4__DENALI_CTL_406__TDFI_CTRLUPD_MAX_F1_SHIFT                     0U
#define LPDDR4__DENALI_CTL_406__TDFI_CTRLUPD_MAX_F1_WIDTH                    21U
#define LPDDR4__TDFI_CTRLUPD_MAX_F1__REG DENALI_CTL_406
#define LPDDR4__TDFI_CTRLUPD_MAX_F1__FLD LPDDR4__DENALI_CTL_406__TDFI_CTRLUPD_MAX_F1

#define LPDDR4__DENALI_CTL_407_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_407_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_407__TDFI_PHYUPD_TYPE0_F1_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_407__TDFI_PHYUPD_TYPE0_F1_SHIFT                    0U
#define LPDDR4__DENALI_CTL_407__TDFI_PHYUPD_TYPE0_F1_WIDTH                   32U
#define LPDDR4__TDFI_PHYUPD_TYPE0_F1__REG DENALI_CTL_407
#define LPDDR4__TDFI_PHYUPD_TYPE0_F1__FLD LPDDR4__DENALI_CTL_407__TDFI_PHYUPD_TYPE0_F1

#define LPDDR4__DENALI_CTL_408_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_408_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_408__TDFI_PHYUPD_TYPE1_F1_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_408__TDFI_PHYUPD_TYPE1_F1_SHIFT                    0U
#define LPDDR4__DENALI_CTL_408__TDFI_PHYUPD_TYPE1_F1_WIDTH                   32U
#define LPDDR4__TDFI_PHYUPD_TYPE1_F1__REG DENALI_CTL_408
#define LPDDR4__TDFI_PHYUPD_TYPE1_F1__FLD LPDDR4__DENALI_CTL_408__TDFI_PHYUPD_TYPE1_F1

#define LPDDR4__DENALI_CTL_409_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_409_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_409__TDFI_PHYUPD_TYPE2_F1_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_409__TDFI_PHYUPD_TYPE2_F1_SHIFT                    0U
#define LPDDR4__DENALI_CTL_409__TDFI_PHYUPD_TYPE2_F1_WIDTH                   32U
#define LPDDR4__TDFI_PHYUPD_TYPE2_F1__REG DENALI_CTL_409
#define LPDDR4__TDFI_PHYUPD_TYPE2_F1__FLD LPDDR4__DENALI_CTL_409__TDFI_PHYUPD_TYPE2_F1

#define LPDDR4__DENALI_CTL_410_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_410_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_410__TDFI_PHYUPD_TYPE3_F1_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_410__TDFI_PHYUPD_TYPE3_F1_SHIFT                    0U
#define LPDDR4__DENALI_CTL_410__TDFI_PHYUPD_TYPE3_F1_WIDTH                   32U
#define LPDDR4__TDFI_PHYUPD_TYPE3_F1__REG DENALI_CTL_410
#define LPDDR4__TDFI_PHYUPD_TYPE3_F1__FLD LPDDR4__DENALI_CTL_410__TDFI_PHYUPD_TYPE3_F1

#define LPDDR4__DENALI_CTL_411_READ_MASK                             0x007FFFFFU
#define LPDDR4__DENALI_CTL_411_WRITE_MASK                            0x007FFFFFU
#define LPDDR4__DENALI_CTL_411__TDFI_PHYUPD_RESP_F1_MASK             0x007FFFFFU
#define LPDDR4__DENALI_CTL_411__TDFI_PHYUPD_RESP_F1_SHIFT                     0U
#define LPDDR4__DENALI_CTL_411__TDFI_PHYUPD_RESP_F1_WIDTH                    23U
#define LPDDR4__TDFI_PHYUPD_RESP_F1__REG DENALI_CTL_411
#define LPDDR4__TDFI_PHYUPD_RESP_F1__FLD LPDDR4__DENALI_CTL_411__TDFI_PHYUPD_RESP_F1

#define LPDDR4__DENALI_CTL_412_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_412_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_412__TDFI_CTRLUPD_INTERVAL_F1_MASK        0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_412__TDFI_CTRLUPD_INTERVAL_F1_SHIFT                0U
#define LPDDR4__DENALI_CTL_412__TDFI_CTRLUPD_INTERVAL_F1_WIDTH               32U
#define LPDDR4__TDFI_CTRLUPD_INTERVAL_F1__REG DENALI_CTL_412
#define LPDDR4__TDFI_CTRLUPD_INTERVAL_F1__FLD LPDDR4__DENALI_CTL_412__TDFI_CTRLUPD_INTERVAL_F1

#define LPDDR4__DENALI_CTL_413_READ_MASK                             0xFFFF070FU
#define LPDDR4__DENALI_CTL_413_WRITE_MASK                            0xFFFF070FU
#define LPDDR4__DENALI_CTL_413__TDFI_CTRL_DELAY_F1_MASK              0x0000000FU
#define LPDDR4__DENALI_CTL_413__TDFI_CTRL_DELAY_F1_SHIFT                      0U
#define LPDDR4__DENALI_CTL_413__TDFI_CTRL_DELAY_F1_WIDTH                      4U
#define LPDDR4__TDFI_CTRL_DELAY_F1__REG DENALI_CTL_413
#define LPDDR4__TDFI_CTRL_DELAY_F1__FLD LPDDR4__DENALI_CTL_413__TDFI_CTRL_DELAY_F1

#define LPDDR4__DENALI_CTL_413__TDFI_PHY_WRDATA_F1_MASK              0x00000700U
#define LPDDR4__DENALI_CTL_413__TDFI_PHY_WRDATA_F1_SHIFT                      8U
#define LPDDR4__DENALI_CTL_413__TDFI_PHY_WRDATA_F1_WIDTH                      3U
#define LPDDR4__TDFI_PHY_WRDATA_F1__REG DENALI_CTL_413
#define LPDDR4__TDFI_PHY_WRDATA_F1__FLD LPDDR4__DENALI_CTL_413__TDFI_PHY_WRDATA_F1

#define LPDDR4__DENALI_CTL_413__TDFI_RDCSLAT_F1_MASK                 0x00FF0000U
#define LPDDR4__DENALI_CTL_413__TDFI_RDCSLAT_F1_SHIFT                        16U
#define LPDDR4__DENALI_CTL_413__TDFI_RDCSLAT_F1_WIDTH                         8U
#define LPDDR4__TDFI_RDCSLAT_F1__REG DENALI_CTL_413
#define LPDDR4__TDFI_RDCSLAT_F1__FLD LPDDR4__DENALI_CTL_413__TDFI_RDCSLAT_F1

#define LPDDR4__DENALI_CTL_413__TDFI_RDDATA_EN_F1_MASK               0xFF000000U
#define LPDDR4__DENALI_CTL_413__TDFI_RDDATA_EN_F1_SHIFT                      24U
#define LPDDR4__DENALI_CTL_413__TDFI_RDDATA_EN_F1_WIDTH                       8U
#define LPDDR4__TDFI_RDDATA_EN_F1__REG DENALI_CTL_413
#define LPDDR4__TDFI_RDDATA_EN_F1__FLD LPDDR4__DENALI_CTL_413__TDFI_RDDATA_EN_F1

#define LPDDR4__DENALI_CTL_414_READ_MASK                             0xFF7FFFFFU
#define LPDDR4__DENALI_CTL_414_WRITE_MASK                            0xFF7FFFFFU
#define LPDDR4__DENALI_CTL_414__TDFI_WRCSLAT_F1_MASK                 0x000000FFU
#define LPDDR4__DENALI_CTL_414__TDFI_WRCSLAT_F1_SHIFT                         0U
#define LPDDR4__DENALI_CTL_414__TDFI_WRCSLAT_F1_WIDTH                         8U
#define LPDDR4__TDFI_WRCSLAT_F1__REG DENALI_CTL_414
#define LPDDR4__TDFI_WRCSLAT_F1__FLD LPDDR4__DENALI_CTL_414__TDFI_WRCSLAT_F1

#define LPDDR4__DENALI_CTL_414__TDFI_PHY_WRLAT_F1_MASK               0x0000FF00U
#define LPDDR4__DENALI_CTL_414__TDFI_PHY_WRLAT_F1_SHIFT                       8U
#define LPDDR4__DENALI_CTL_414__TDFI_PHY_WRLAT_F1_WIDTH                       8U
#define LPDDR4__TDFI_PHY_WRLAT_F1__REG DENALI_CTL_414
#define LPDDR4__TDFI_PHY_WRLAT_F1__FLD LPDDR4__DENALI_CTL_414__TDFI_PHY_WRLAT_F1

#define LPDDR4__DENALI_CTL_414__TDFI_CTRLMSG_RESP_F1_MASK            0x007F0000U
#define LPDDR4__DENALI_CTL_414__TDFI_CTRLMSG_RESP_F1_SHIFT                   16U
#define LPDDR4__DENALI_CTL_414__TDFI_CTRLMSG_RESP_F1_WIDTH                    7U
#define LPDDR4__TDFI_CTRLMSG_RESP_F1__REG DENALI_CTL_414
#define LPDDR4__TDFI_CTRLMSG_RESP_F1__FLD LPDDR4__DENALI_CTL_414__TDFI_CTRLMSG_RESP_F1

#define LPDDR4__DENALI_CTL_414__TDFI_PHY_RDLAT_F2_MASK               0xFF000000U
#define LPDDR4__DENALI_CTL_414__TDFI_PHY_RDLAT_F2_SHIFT                      24U
#define LPDDR4__DENALI_CTL_414__TDFI_PHY_RDLAT_F2_WIDTH                       8U
#define LPDDR4__TDFI_PHY_RDLAT_F2__REG DENALI_CTL_414
#define LPDDR4__TDFI_PHY_RDLAT_F2__FLD LPDDR4__DENALI_CTL_414__TDFI_PHY_RDLAT_F2

#define LPDDR4__DENALI_CTL_415_READ_MASK                             0x001FFFFFU
#define LPDDR4__DENALI_CTL_415_WRITE_MASK                            0x001FFFFFU
#define LPDDR4__DENALI_CTL_415__TDFI_CTRLUPD_MAX_F2_MASK             0x001FFFFFU
#define LPDDR4__DENALI_CTL_415__TDFI_CTRLUPD_MAX_F2_SHIFT                     0U
#define LPDDR4__DENALI_CTL_415__TDFI_CTRLUPD_MAX_F2_WIDTH                    21U
#define LPDDR4__TDFI_CTRLUPD_MAX_F2__REG DENALI_CTL_415
#define LPDDR4__TDFI_CTRLUPD_MAX_F2__FLD LPDDR4__DENALI_CTL_415__TDFI_CTRLUPD_MAX_F2

#define LPDDR4__DENALI_CTL_416_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_416_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_416__TDFI_PHYUPD_TYPE0_F2_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_416__TDFI_PHYUPD_TYPE0_F2_SHIFT                    0U
#define LPDDR4__DENALI_CTL_416__TDFI_PHYUPD_TYPE0_F2_WIDTH                   32U
#define LPDDR4__TDFI_PHYUPD_TYPE0_F2__REG DENALI_CTL_416
#define LPDDR4__TDFI_PHYUPD_TYPE0_F2__FLD LPDDR4__DENALI_CTL_416__TDFI_PHYUPD_TYPE0_F2

#define LPDDR4__DENALI_CTL_417_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_417_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_417__TDFI_PHYUPD_TYPE1_F2_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_417__TDFI_PHYUPD_TYPE1_F2_SHIFT                    0U
#define LPDDR4__DENALI_CTL_417__TDFI_PHYUPD_TYPE1_F2_WIDTH                   32U
#define LPDDR4__TDFI_PHYUPD_TYPE1_F2__REG DENALI_CTL_417
#define LPDDR4__TDFI_PHYUPD_TYPE1_F2__FLD LPDDR4__DENALI_CTL_417__TDFI_PHYUPD_TYPE1_F2

#define LPDDR4__DENALI_CTL_418_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_418_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_418__TDFI_PHYUPD_TYPE2_F2_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_418__TDFI_PHYUPD_TYPE2_F2_SHIFT                    0U
#define LPDDR4__DENALI_CTL_418__TDFI_PHYUPD_TYPE2_F2_WIDTH                   32U
#define LPDDR4__TDFI_PHYUPD_TYPE2_F2__REG DENALI_CTL_418
#define LPDDR4__TDFI_PHYUPD_TYPE2_F2__FLD LPDDR4__DENALI_CTL_418__TDFI_PHYUPD_TYPE2_F2

#define LPDDR4__DENALI_CTL_419_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_419_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_419__TDFI_PHYUPD_TYPE3_F2_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_419__TDFI_PHYUPD_TYPE3_F2_SHIFT                    0U
#define LPDDR4__DENALI_CTL_419__TDFI_PHYUPD_TYPE3_F2_WIDTH                   32U
#define LPDDR4__TDFI_PHYUPD_TYPE3_F2__REG DENALI_CTL_419
#define LPDDR4__TDFI_PHYUPD_TYPE3_F2__FLD LPDDR4__DENALI_CTL_419__TDFI_PHYUPD_TYPE3_F2

#define LPDDR4__DENALI_CTL_420_READ_MASK                             0x007FFFFFU
#define LPDDR4__DENALI_CTL_420_WRITE_MASK                            0x007FFFFFU
#define LPDDR4__DENALI_CTL_420__TDFI_PHYUPD_RESP_F2_MASK             0x007FFFFFU
#define LPDDR4__DENALI_CTL_420__TDFI_PHYUPD_RESP_F2_SHIFT                     0U
#define LPDDR4__DENALI_CTL_420__TDFI_PHYUPD_RESP_F2_WIDTH                    23U
#define LPDDR4__TDFI_PHYUPD_RESP_F2__REG DENALI_CTL_420
#define LPDDR4__TDFI_PHYUPD_RESP_F2__FLD LPDDR4__DENALI_CTL_420__TDFI_PHYUPD_RESP_F2

#define LPDDR4__DENALI_CTL_421_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_421_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_421__TDFI_CTRLUPD_INTERVAL_F2_MASK        0xFFFFFFFFU
#define LPDDR4__DENALI_CTL_421__TDFI_CTRLUPD_INTERVAL_F2_SHIFT                0U
#define LPDDR4__DENALI_CTL_421__TDFI_CTRLUPD_INTERVAL_F2_WIDTH               32U
#define LPDDR4__TDFI_CTRLUPD_INTERVAL_F2__REG DENALI_CTL_421
#define LPDDR4__TDFI_CTRLUPD_INTERVAL_F2__FLD LPDDR4__DENALI_CTL_421__TDFI_CTRLUPD_INTERVAL_F2

#define LPDDR4__DENALI_CTL_422_READ_MASK                             0xFFFF070FU
#define LPDDR4__DENALI_CTL_422_WRITE_MASK                            0xFFFF070FU
#define LPDDR4__DENALI_CTL_422__TDFI_CTRL_DELAY_F2_MASK              0x0000000FU
#define LPDDR4__DENALI_CTL_422__TDFI_CTRL_DELAY_F2_SHIFT                      0U
#define LPDDR4__DENALI_CTL_422__TDFI_CTRL_DELAY_F2_WIDTH                      4U
#define LPDDR4__TDFI_CTRL_DELAY_F2__REG DENALI_CTL_422
#define LPDDR4__TDFI_CTRL_DELAY_F2__FLD LPDDR4__DENALI_CTL_422__TDFI_CTRL_DELAY_F2

#define LPDDR4__DENALI_CTL_422__TDFI_PHY_WRDATA_F2_MASK              0x00000700U
#define LPDDR4__DENALI_CTL_422__TDFI_PHY_WRDATA_F2_SHIFT                      8U
#define LPDDR4__DENALI_CTL_422__TDFI_PHY_WRDATA_F2_WIDTH                      3U
#define LPDDR4__TDFI_PHY_WRDATA_F2__REG DENALI_CTL_422
#define LPDDR4__TDFI_PHY_WRDATA_F2__FLD LPDDR4__DENALI_CTL_422__TDFI_PHY_WRDATA_F2

#define LPDDR4__DENALI_CTL_422__TDFI_RDCSLAT_F2_MASK                 0x00FF0000U
#define LPDDR4__DENALI_CTL_422__TDFI_RDCSLAT_F2_SHIFT                        16U
#define LPDDR4__DENALI_CTL_422__TDFI_RDCSLAT_F2_WIDTH                         8U
#define LPDDR4__TDFI_RDCSLAT_F2__REG DENALI_CTL_422
#define LPDDR4__TDFI_RDCSLAT_F2__FLD LPDDR4__DENALI_CTL_422__TDFI_RDCSLAT_F2

#define LPDDR4__DENALI_CTL_422__TDFI_RDDATA_EN_F2_MASK               0xFF000000U
#define LPDDR4__DENALI_CTL_422__TDFI_RDDATA_EN_F2_SHIFT                      24U
#define LPDDR4__DENALI_CTL_422__TDFI_RDDATA_EN_F2_WIDTH                       8U
#define LPDDR4__TDFI_RDDATA_EN_F2__REG DENALI_CTL_422
#define LPDDR4__TDFI_RDDATA_EN_F2__FLD LPDDR4__DENALI_CTL_422__TDFI_RDDATA_EN_F2

#define LPDDR4__DENALI_CTL_423_READ_MASK                             0x007FFFFFU
#define LPDDR4__DENALI_CTL_423_WRITE_MASK                            0x007FFFFFU
#define LPDDR4__DENALI_CTL_423__TDFI_WRCSLAT_F2_MASK                 0x000000FFU
#define LPDDR4__DENALI_CTL_423__TDFI_WRCSLAT_F2_SHIFT                         0U
#define LPDDR4__DENALI_CTL_423__TDFI_WRCSLAT_F2_WIDTH                         8U
#define LPDDR4__TDFI_WRCSLAT_F2__REG DENALI_CTL_423
#define LPDDR4__TDFI_WRCSLAT_F2__FLD LPDDR4__DENALI_CTL_423__TDFI_WRCSLAT_F2

#define LPDDR4__DENALI_CTL_423__TDFI_PHY_WRLAT_F2_MASK               0x0000FF00U
#define LPDDR4__DENALI_CTL_423__TDFI_PHY_WRLAT_F2_SHIFT                       8U
#define LPDDR4__DENALI_CTL_423__TDFI_PHY_WRLAT_F2_WIDTH                       8U
#define LPDDR4__TDFI_PHY_WRLAT_F2__REG DENALI_CTL_423
#define LPDDR4__TDFI_PHY_WRLAT_F2__FLD LPDDR4__DENALI_CTL_423__TDFI_PHY_WRLAT_F2

#define LPDDR4__DENALI_CTL_423__TDFI_CTRLMSG_RESP_F2_MASK            0x007F0000U
#define LPDDR4__DENALI_CTL_423__TDFI_CTRLMSG_RESP_F2_SHIFT                   16U
#define LPDDR4__DENALI_CTL_423__TDFI_CTRLMSG_RESP_F2_WIDTH                    7U
#define LPDDR4__TDFI_CTRLMSG_RESP_F2__REG DENALI_CTL_423
#define LPDDR4__TDFI_CTRLMSG_RESP_F2__FLD LPDDR4__DENALI_CTL_423__TDFI_CTRLMSG_RESP_F2

#define LPDDR4__DENALI_CTL_424_READ_MASK                             0x7FFFFFFFU
#define LPDDR4__DENALI_CTL_424_WRITE_MASK                            0x7FFFFFFFU
#define LPDDR4__DENALI_CTL_424__DLL_RST_DELAY_MASK                   0x0000FFFFU
#define LPDDR4__DENALI_CTL_424__DLL_RST_DELAY_SHIFT                           0U
#define LPDDR4__DENALI_CTL_424__DLL_RST_DELAY_WIDTH                          16U
#define LPDDR4__DLL_RST_DELAY__REG DENALI_CTL_424
#define LPDDR4__DLL_RST_DELAY__FLD LPDDR4__DENALI_CTL_424__DLL_RST_DELAY

#define LPDDR4__DENALI_CTL_424__DLL_RST_ADJ_DLY_MASK                 0x00FF0000U
#define LPDDR4__DENALI_CTL_424__DLL_RST_ADJ_DLY_SHIFT                        16U
#define LPDDR4__DENALI_CTL_424__DLL_RST_ADJ_DLY_WIDTH                         8U
#define LPDDR4__DLL_RST_ADJ_DLY__REG DENALI_CTL_424
#define LPDDR4__DLL_RST_ADJ_DLY__FLD LPDDR4__DENALI_CTL_424__DLL_RST_ADJ_DLY

#define LPDDR4__DENALI_CTL_424__UPDATE_ERROR_STATUS_MASK             0x7F000000U
#define LPDDR4__DENALI_CTL_424__UPDATE_ERROR_STATUS_SHIFT                    24U
#define LPDDR4__DENALI_CTL_424__UPDATE_ERROR_STATUS_WIDTH                     7U
#define LPDDR4__UPDATE_ERROR_STATUS__REG DENALI_CTL_424
#define LPDDR4__UPDATE_ERROR_STATUS__FLD LPDDR4__DENALI_CTL_424__UPDATE_ERROR_STATUS

#define LPDDR4__DENALI_CTL_425_READ_MASK                             0x0FFFFF03U
#define LPDDR4__DENALI_CTL_425_WRITE_MASK                            0x0FFFFF03U
#define LPDDR4__DENALI_CTL_425__DRAM_CLK_DISABLE_MASK                0x00000003U
#define LPDDR4__DENALI_CTL_425__DRAM_CLK_DISABLE_SHIFT                        0U
#define LPDDR4__DENALI_CTL_425__DRAM_CLK_DISABLE_WIDTH                        2U
#define LPDDR4__DRAM_CLK_DISABLE__REG DENALI_CTL_425
#define LPDDR4__DRAM_CLK_DISABLE__FLD LPDDR4__DENALI_CTL_425__DRAM_CLK_DISABLE

#define LPDDR4__DENALI_CTL_425__TDFI_CTRLUPD_MIN_MASK                0x00FFFF00U
#define LPDDR4__DENALI_CTL_425__TDFI_CTRLUPD_MIN_SHIFT                        8U
#define LPDDR4__DENALI_CTL_425__TDFI_CTRLUPD_MIN_WIDTH                       16U
#define LPDDR4__TDFI_CTRLUPD_MIN__REG DENALI_CTL_425
#define LPDDR4__TDFI_CTRLUPD_MIN__FLD LPDDR4__DENALI_CTL_425__TDFI_CTRLUPD_MIN

#define LPDDR4__DENALI_CTL_425__TDFI_DRAM_CLK_DISABLE_MASK           0x0F000000U
#define LPDDR4__DENALI_CTL_425__TDFI_DRAM_CLK_DISABLE_SHIFT                  24U
#define LPDDR4__DENALI_CTL_425__TDFI_DRAM_CLK_DISABLE_WIDTH                   4U
#define LPDDR4__TDFI_DRAM_CLK_DISABLE__REG DENALI_CTL_425
#define LPDDR4__TDFI_DRAM_CLK_DISABLE__FLD LPDDR4__DENALI_CTL_425__TDFI_DRAM_CLK_DISABLE

#define LPDDR4__DENALI_CTL_426_READ_MASK                             0x01FF070FU
#define LPDDR4__DENALI_CTL_426_WRITE_MASK                            0x01FF070FU
#define LPDDR4__DENALI_CTL_426__TDFI_DRAM_CLK_ENABLE_MASK            0x0000000FU
#define LPDDR4__DENALI_CTL_426__TDFI_DRAM_CLK_ENABLE_SHIFT                    0U
#define LPDDR4__DENALI_CTL_426__TDFI_DRAM_CLK_ENABLE_WIDTH                    4U
#define LPDDR4__TDFI_DRAM_CLK_ENABLE__REG DENALI_CTL_426
#define LPDDR4__TDFI_DRAM_CLK_ENABLE__FLD LPDDR4__DENALI_CTL_426__TDFI_DRAM_CLK_ENABLE

#define LPDDR4__DENALI_CTL_426__TDFI_PARIN_LAT_MASK                  0x00000700U
#define LPDDR4__DENALI_CTL_426__TDFI_PARIN_LAT_SHIFT                          8U
#define LPDDR4__DENALI_CTL_426__TDFI_PARIN_LAT_WIDTH                          3U
#define LPDDR4__TDFI_PARIN_LAT__REG DENALI_CTL_426
#define LPDDR4__TDFI_PARIN_LAT__FLD LPDDR4__DENALI_CTL_426__TDFI_PARIN_LAT

#define LPDDR4__DENALI_CTL_426__TDFI_WRDATA_DELAY_MASK               0x00FF0000U
#define LPDDR4__DENALI_CTL_426__TDFI_WRDATA_DELAY_SHIFT                      16U
#define LPDDR4__DENALI_CTL_426__TDFI_WRDATA_DELAY_WIDTH                       8U
#define LPDDR4__TDFI_WRDATA_DELAY__REG DENALI_CTL_426
#define LPDDR4__TDFI_WRDATA_DELAY__FLD LPDDR4__DENALI_CTL_426__TDFI_WRDATA_DELAY

#define LPDDR4__DENALI_CTL_426__DISABLE_MEMORY_MASKED_WRITE_MASK     0x01000000U
#define LPDDR4__DENALI_CTL_426__DISABLE_MEMORY_MASKED_WRITE_SHIFT            24U
#define LPDDR4__DENALI_CTL_426__DISABLE_MEMORY_MASKED_WRITE_WIDTH             1U
#define LPDDR4__DENALI_CTL_426__DISABLE_MEMORY_MASKED_WRITE_WOCLR             0U
#define LPDDR4__DENALI_CTL_426__DISABLE_MEMORY_MASKED_WRITE_WOSET             0U
#define LPDDR4__DISABLE_MEMORY_MASKED_WRITE__REG DENALI_CTL_426
#define LPDDR4__DISABLE_MEMORY_MASKED_WRITE__FLD LPDDR4__DENALI_CTL_426__DISABLE_MEMORY_MASKED_WRITE

#define LPDDR4__DENALI_CTL_427_READ_MASK                             0x07070701U
#define LPDDR4__DENALI_CTL_427_WRITE_MASK                            0x07070701U
#define LPDDR4__DENALI_CTL_427__MULTI_CHANNEL_ZQ_CAL_MASTER_MASK     0x00000001U
#define LPDDR4__DENALI_CTL_427__MULTI_CHANNEL_ZQ_CAL_MASTER_SHIFT             0U
#define LPDDR4__DENALI_CTL_427__MULTI_CHANNEL_ZQ_CAL_MASTER_WIDTH             1U
#define LPDDR4__DENALI_CTL_427__MULTI_CHANNEL_ZQ_CAL_MASTER_WOCLR             0U
#define LPDDR4__DENALI_CTL_427__MULTI_CHANNEL_ZQ_CAL_MASTER_WOSET             0U
#define LPDDR4__MULTI_CHANNEL_ZQ_CAL_MASTER__REG DENALI_CTL_427
#define LPDDR4__MULTI_CHANNEL_ZQ_CAL_MASTER__FLD LPDDR4__DENALI_CTL_427__MULTI_CHANNEL_ZQ_CAL_MASTER

#define LPDDR4__DENALI_CTL_427__STRATEGY_2TICK_COUNT_MASK            0x00000700U
#define LPDDR4__DENALI_CTL_427__STRATEGY_2TICK_COUNT_SHIFT                    8U
#define LPDDR4__DENALI_CTL_427__STRATEGY_2TICK_COUNT_WIDTH                    3U
#define LPDDR4__STRATEGY_2TICK_COUNT__REG DENALI_CTL_427
#define LPDDR4__STRATEGY_2TICK_COUNT__FLD LPDDR4__DENALI_CTL_427__STRATEGY_2TICK_COUNT

#define LPDDR4__DENALI_CTL_427__BANK_ACTIVATE_2TICK_COUNT_MASK       0x00070000U
#define LPDDR4__DENALI_CTL_427__BANK_ACTIVATE_2TICK_COUNT_SHIFT              16U
#define LPDDR4__DENALI_CTL_427__BANK_ACTIVATE_2TICK_COUNT_WIDTH               3U
#define LPDDR4__BANK_ACTIVATE_2TICK_COUNT__REG DENALI_CTL_427
#define LPDDR4__BANK_ACTIVATE_2TICK_COUNT__FLD LPDDR4__DENALI_CTL_427__BANK_ACTIVATE_2TICK_COUNT

#define LPDDR4__DENALI_CTL_427__PRE_2TICK_COUNT_MASK                 0x07000000U
#define LPDDR4__DENALI_CTL_427__PRE_2TICK_COUNT_SHIFT                        24U
#define LPDDR4__DENALI_CTL_427__PRE_2TICK_COUNT_WIDTH                         3U
#define LPDDR4__PRE_2TICK_COUNT__REG DENALI_CTL_427
#define LPDDR4__PRE_2TICK_COUNT__FLD LPDDR4__DENALI_CTL_427__PRE_2TICK_COUNT

#define LPDDR4__DENALI_CTL_428_READ_MASK                             0x0F070707U
#define LPDDR4__DENALI_CTL_428_WRITE_MASK                            0x0F070707U
#define LPDDR4__DENALI_CTL_428__STRATEGY_4TICK_COUNT_MASK            0x00000007U
#define LPDDR4__DENALI_CTL_428__STRATEGY_4TICK_COUNT_SHIFT                    0U
#define LPDDR4__DENALI_CTL_428__STRATEGY_4TICK_COUNT_WIDTH                    3U
#define LPDDR4__STRATEGY_4TICK_COUNT__REG DENALI_CTL_428
#define LPDDR4__STRATEGY_4TICK_COUNT__FLD LPDDR4__DENALI_CTL_428__STRATEGY_4TICK_COUNT

#define LPDDR4__DENALI_CTL_428__BANK_ACTIVATE_4TICK_COUNT_MASK       0x00000700U
#define LPDDR4__DENALI_CTL_428__BANK_ACTIVATE_4TICK_COUNT_SHIFT               8U
#define LPDDR4__DENALI_CTL_428__BANK_ACTIVATE_4TICK_COUNT_WIDTH               3U
#define LPDDR4__BANK_ACTIVATE_4TICK_COUNT__REG DENALI_CTL_428
#define LPDDR4__BANK_ACTIVATE_4TICK_COUNT__FLD LPDDR4__DENALI_CTL_428__BANK_ACTIVATE_4TICK_COUNT

#define LPDDR4__DENALI_CTL_428__PRE_4TICK_COUNT_MASK                 0x00070000U
#define LPDDR4__DENALI_CTL_428__PRE_4TICK_COUNT_SHIFT                        16U
#define LPDDR4__DENALI_CTL_428__PRE_4TICK_COUNT_WIDTH                         3U
#define LPDDR4__PRE_4TICK_COUNT__REG DENALI_CTL_428
#define LPDDR4__PRE_4TICK_COUNT__FLD LPDDR4__DENALI_CTL_428__PRE_4TICK_COUNT

#define LPDDR4__DENALI_CTL_428__TMP_2X4_TICK_PLUS_ADJ_MASK           0x0F000000U
#define LPDDR4__DENALI_CTL_428__TMP_2X4_TICK_PLUS_ADJ_SHIFT                  24U
#define LPDDR4__DENALI_CTL_428__TMP_2X4_TICK_PLUS_ADJ_WIDTH                   4U
#define LPDDR4__TMP_2X4_TICK_PLUS_ADJ__REG DENALI_CTL_428
#define LPDDR4__TMP_2X4_TICK_PLUS_ADJ__FLD LPDDR4__DENALI_CTL_428__TMP_2X4_TICK_PLUS_ADJ

#define LPDDR4__DENALI_CTL_429_READ_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_429_WRITE_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_429__TMP_2X4_TICK_MINUS_ADJ_MASK          0x0000000FU
#define LPDDR4__DENALI_CTL_429__TMP_2X4_TICK_MINUS_ADJ_SHIFT                  0U
#define LPDDR4__DENALI_CTL_429__TMP_2X4_TICK_MINUS_ADJ_WIDTH                  4U
#define LPDDR4__TMP_2X4_TICK_MINUS_ADJ__REG DENALI_CTL_429
#define LPDDR4__TMP_2X4_TICK_MINUS_ADJ__FLD LPDDR4__DENALI_CTL_429__TMP_2X4_TICK_MINUS_ADJ

#define LPDDR4__DENALI_CTL_429__TMP_NXN_TICK_PLUS_ADJ_MASK           0x00000F00U
#define LPDDR4__DENALI_CTL_429__TMP_NXN_TICK_PLUS_ADJ_SHIFT                   8U
#define LPDDR4__DENALI_CTL_429__TMP_NXN_TICK_PLUS_ADJ_WIDTH                   4U
#define LPDDR4__TMP_NXN_TICK_PLUS_ADJ__REG DENALI_CTL_429
#define LPDDR4__TMP_NXN_TICK_PLUS_ADJ__FLD LPDDR4__DENALI_CTL_429__TMP_NXN_TICK_PLUS_ADJ

#define LPDDR4__DENALI_CTL_429__TMP_NXN_TICK_MINUS_ADJ_MASK          0x000F0000U
#define LPDDR4__DENALI_CTL_429__TMP_NXN_TICK_MINUS_ADJ_SHIFT                 16U
#define LPDDR4__DENALI_CTL_429__TMP_NXN_TICK_MINUS_ADJ_WIDTH                  4U
#define LPDDR4__TMP_NXN_TICK_MINUS_ADJ__REG DENALI_CTL_429
#define LPDDR4__TMP_NXN_TICK_MINUS_ADJ__FLD LPDDR4__DENALI_CTL_429__TMP_NXN_TICK_MINUS_ADJ

#define LPDDR4__DENALI_CTL_429__ODT_TICK_PLUS_ADJ_MASK               0x0F000000U
#define LPDDR4__DENALI_CTL_429__ODT_TICK_PLUS_ADJ_SHIFT                      24U
#define LPDDR4__DENALI_CTL_429__ODT_TICK_PLUS_ADJ_WIDTH                       4U
#define LPDDR4__ODT_TICK_PLUS_ADJ__REG DENALI_CTL_429
#define LPDDR4__ODT_TICK_PLUS_ADJ__FLD LPDDR4__DENALI_CTL_429__ODT_TICK_PLUS_ADJ

#define LPDDR4__DENALI_CTL_430_READ_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_430_WRITE_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_430__ODT_TICK_MINUS_ADJ_MASK              0x0000000FU
#define LPDDR4__DENALI_CTL_430__ODT_TICK_MINUS_ADJ_SHIFT                      0U
#define LPDDR4__DENALI_CTL_430__ODT_TICK_MINUS_ADJ_WIDTH                      4U
#define LPDDR4__ODT_TICK_MINUS_ADJ__REG DENALI_CTL_430
#define LPDDR4__ODT_TICK_MINUS_ADJ__FLD LPDDR4__DENALI_CTL_430__ODT_TICK_MINUS_ADJ

#define LPDDR4__DENALI_CTL_430__TRAS_TICK_PLUS_ADJ_MASK              0x00000F00U
#define LPDDR4__DENALI_CTL_430__TRAS_TICK_PLUS_ADJ_SHIFT                      8U
#define LPDDR4__DENALI_CTL_430__TRAS_TICK_PLUS_ADJ_WIDTH                      4U
#define LPDDR4__TRAS_TICK_PLUS_ADJ__REG DENALI_CTL_430
#define LPDDR4__TRAS_TICK_PLUS_ADJ__FLD LPDDR4__DENALI_CTL_430__TRAS_TICK_PLUS_ADJ

#define LPDDR4__DENALI_CTL_430__TRAS_TICK_MINUS_ADJ_MASK             0x000F0000U
#define LPDDR4__DENALI_CTL_430__TRAS_TICK_MINUS_ADJ_SHIFT                    16U
#define LPDDR4__DENALI_CTL_430__TRAS_TICK_MINUS_ADJ_WIDTH                     4U
#define LPDDR4__TRAS_TICK_MINUS_ADJ__REG DENALI_CTL_430
#define LPDDR4__TRAS_TICK_MINUS_ADJ__FLD LPDDR4__DENALI_CTL_430__TRAS_TICK_MINUS_ADJ

#define LPDDR4__DENALI_CTL_430__TRP_TICK_PLUS_ADJ_MASK               0x0F000000U
#define LPDDR4__DENALI_CTL_430__TRP_TICK_PLUS_ADJ_SHIFT                      24U
#define LPDDR4__DENALI_CTL_430__TRP_TICK_PLUS_ADJ_WIDTH                       4U
#define LPDDR4__TRP_TICK_PLUS_ADJ__REG DENALI_CTL_430
#define LPDDR4__TRP_TICK_PLUS_ADJ__FLD LPDDR4__DENALI_CTL_430__TRP_TICK_PLUS_ADJ

#define LPDDR4__DENALI_CTL_431_READ_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_431_WRITE_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_431__TRP_TICK_MINUS_ADJ_MASK              0x0000000FU
#define LPDDR4__DENALI_CTL_431__TRP_TICK_MINUS_ADJ_SHIFT                      0U
#define LPDDR4__DENALI_CTL_431__TRP_TICK_MINUS_ADJ_WIDTH                      4U
#define LPDDR4__TRP_TICK_MINUS_ADJ__REG DENALI_CTL_431
#define LPDDR4__TRP_TICK_MINUS_ADJ__FLD LPDDR4__DENALI_CTL_431__TRP_TICK_MINUS_ADJ

#define LPDDR4__DENALI_CTL_431__TWR_TICK_PLUS_ADJ_MASK               0x00000F00U
#define LPDDR4__DENALI_CTL_431__TWR_TICK_PLUS_ADJ_SHIFT                       8U
#define LPDDR4__DENALI_CTL_431__TWR_TICK_PLUS_ADJ_WIDTH                       4U
#define LPDDR4__TWR_TICK_PLUS_ADJ__REG DENALI_CTL_431
#define LPDDR4__TWR_TICK_PLUS_ADJ__FLD LPDDR4__DENALI_CTL_431__TWR_TICK_PLUS_ADJ

#define LPDDR4__DENALI_CTL_431__TWR_TICK_MINUS_ADJ_MASK              0x000F0000U
#define LPDDR4__DENALI_CTL_431__TWR_TICK_MINUS_ADJ_SHIFT                     16U
#define LPDDR4__DENALI_CTL_431__TWR_TICK_MINUS_ADJ_WIDTH                      4U
#define LPDDR4__TWR_TICK_MINUS_ADJ__REG DENALI_CTL_431
#define LPDDR4__TWR_TICK_MINUS_ADJ__FLD LPDDR4__DENALI_CTL_431__TWR_TICK_MINUS_ADJ

#define LPDDR4__DENALI_CTL_431__TMP_4X2_TICK_PLUS_ADJ_MASK           0x0F000000U
#define LPDDR4__DENALI_CTL_431__TMP_4X2_TICK_PLUS_ADJ_SHIFT                  24U
#define LPDDR4__DENALI_CTL_431__TMP_4X2_TICK_PLUS_ADJ_WIDTH                   4U
#define LPDDR4__TMP_4X2_TICK_PLUS_ADJ__REG DENALI_CTL_431
#define LPDDR4__TMP_4X2_TICK_PLUS_ADJ__FLD LPDDR4__DENALI_CTL_431__TMP_4X2_TICK_PLUS_ADJ

#define LPDDR4__DENALI_CTL_432_READ_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_432_WRITE_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_CTL_432__TMP_4X2_TICK_MINUS_ADJ_MASK          0x0000000FU
#define LPDDR4__DENALI_CTL_432__TMP_4X2_TICK_MINUS_ADJ_SHIFT                  0U
#define LPDDR4__DENALI_CTL_432__TMP_4X2_TICK_MINUS_ADJ_WIDTH                  4U
#define LPDDR4__TMP_4X2_TICK_MINUS_ADJ__REG DENALI_CTL_432
#define LPDDR4__TMP_4X2_TICK_MINUS_ADJ__FLD LPDDR4__DENALI_CTL_432__TMP_4X2_TICK_MINUS_ADJ

#define LPDDR4__DENALI_CTL_432__TRFC_TICK_PLUS_ADJ_MASK              0x00000F00U
#define LPDDR4__DENALI_CTL_432__TRFC_TICK_PLUS_ADJ_SHIFT                      8U
#define LPDDR4__DENALI_CTL_432__TRFC_TICK_PLUS_ADJ_WIDTH                      4U
#define LPDDR4__TRFC_TICK_PLUS_ADJ__REG DENALI_CTL_432
#define LPDDR4__TRFC_TICK_PLUS_ADJ__FLD LPDDR4__DENALI_CTL_432__TRFC_TICK_PLUS_ADJ

#define LPDDR4__DENALI_CTL_432__TRFC_TICK_MINUS_ADJ_MASK             0x000F0000U
#define LPDDR4__DENALI_CTL_432__TRFC_TICK_MINUS_ADJ_SHIFT                    16U
#define LPDDR4__DENALI_CTL_432__TRFC_TICK_MINUS_ADJ_WIDTH                     4U
#define LPDDR4__TRFC_TICK_MINUS_ADJ__REG DENALI_CTL_432
#define LPDDR4__TRFC_TICK_MINUS_ADJ__FLD LPDDR4__DENALI_CTL_432__TRFC_TICK_MINUS_ADJ

#define LPDDR4__DENALI_CTL_432__RL_TICK_PLUS_ADJ_MASK                0x0F000000U
#define LPDDR4__DENALI_CTL_432__RL_TICK_PLUS_ADJ_SHIFT                       24U
#define LPDDR4__DENALI_CTL_432__RL_TICK_PLUS_ADJ_WIDTH                        4U
#define LPDDR4__RL_TICK_PLUS_ADJ__REG DENALI_CTL_432
#define LPDDR4__RL_TICK_PLUS_ADJ__FLD LPDDR4__DENALI_CTL_432__RL_TICK_PLUS_ADJ

#define LPDDR4__DENALI_CTL_433_READ_MASK                             0xFF0F0F0FU
#define LPDDR4__DENALI_CTL_433_WRITE_MASK                            0xFF0F0F0FU
#define LPDDR4__DENALI_CTL_433__RL_TICK_MINUS_ADJ_MASK               0x0000000FU
#define LPDDR4__DENALI_CTL_433__RL_TICK_MINUS_ADJ_SHIFT                       0U
#define LPDDR4__DENALI_CTL_433__RL_TICK_MINUS_ADJ_WIDTH                       4U
#define LPDDR4__RL_TICK_MINUS_ADJ__REG DENALI_CTL_433
#define LPDDR4__RL_TICK_MINUS_ADJ__FLD LPDDR4__DENALI_CTL_433__RL_TICK_MINUS_ADJ

#define LPDDR4__DENALI_CTL_433__WL_TICK_PLUS_ADJ_MASK                0x00000F00U
#define LPDDR4__DENALI_CTL_433__WL_TICK_PLUS_ADJ_SHIFT                        8U
#define LPDDR4__DENALI_CTL_433__WL_TICK_PLUS_ADJ_WIDTH                        4U
#define LPDDR4__WL_TICK_PLUS_ADJ__REG DENALI_CTL_433
#define LPDDR4__WL_TICK_PLUS_ADJ__FLD LPDDR4__DENALI_CTL_433__WL_TICK_PLUS_ADJ

#define LPDDR4__DENALI_CTL_433__WL_TICK_MINUS_ADJ_MASK               0x000F0000U
#define LPDDR4__DENALI_CTL_433__WL_TICK_MINUS_ADJ_SHIFT                      16U
#define LPDDR4__DENALI_CTL_433__WL_TICK_MINUS_ADJ_WIDTH                       4U
#define LPDDR4__WL_TICK_MINUS_ADJ__REG DENALI_CTL_433
#define LPDDR4__WL_TICK_MINUS_ADJ__FLD LPDDR4__DENALI_CTL_433__WL_TICK_MINUS_ADJ

#define LPDDR4__DENALI_CTL_433__NWR_F0_MASK                          0xFF000000U
#define LPDDR4__DENALI_CTL_433__NWR_F0_SHIFT                                 24U
#define LPDDR4__DENALI_CTL_433__NWR_F0_WIDTH                                  8U
#define LPDDR4__NWR_F0__REG DENALI_CTL_433
#define LPDDR4__NWR_F0__FLD LPDDR4__DENALI_CTL_433__NWR_F0

#define LPDDR4__DENALI_CTL_434_READ_MASK                             0x0000FFFFU
#define LPDDR4__DENALI_CTL_434_WRITE_MASK                            0x0000FFFFU
#define LPDDR4__DENALI_CTL_434__NWR_F1_MASK                          0x000000FFU
#define LPDDR4__DENALI_CTL_434__NWR_F1_SHIFT                                  0U
#define LPDDR4__DENALI_CTL_434__NWR_F1_WIDTH                                  8U
#define LPDDR4__NWR_F1__REG DENALI_CTL_434
#define LPDDR4__NWR_F1__FLD LPDDR4__DENALI_CTL_434__NWR_F1

#define LPDDR4__DENALI_CTL_434__NWR_F2_MASK                          0x0000FF00U
#define LPDDR4__DENALI_CTL_434__NWR_F2_SHIFT                                  8U
#define LPDDR4__DENALI_CTL_434__NWR_F2_WIDTH                                  8U
#define LPDDR4__NWR_F2__REG DENALI_CTL_434
#define LPDDR4__NWR_F2__FLD LPDDR4__DENALI_CTL_434__NWR_F2

#endif /* REG_LPDDR4_DDR_CONTROLLER_MACROS_H_ */

/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */

