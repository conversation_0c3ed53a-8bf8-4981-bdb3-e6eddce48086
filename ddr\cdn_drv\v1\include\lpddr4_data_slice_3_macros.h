/* parasoft-begin-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" parasoft-begin-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */

/**********************************************************************
 * Copyright (C) 2012-2022 Cadence Design Systems, Inc.
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 
 * 1. Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THIS FILE IS AUTOMATICALLY GENERATED, DO NOT EDIT
 *
 **********************************************************************/

#ifndef REG_LPDDR4_DATA_SLICE_3_MACROS_H_
#define REG_LPDDR4_DATA_SLICE_3_MACROS_H_

#define LPDDR4__DENALI_PHY_768_READ_MASK                             0x07FF7F07U
#define LPDDR4__DENALI_PHY_768_WRITE_MASK                            0x07FF7F07U
#define LPDDR4__DENALI_PHY_768__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_3_MASK  0x00000007U
#define LPDDR4__DENALI_PHY_768__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_3_SHIFT          0U
#define LPDDR4__DENALI_PHY_768__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_3_WIDTH          3U
#define LPDDR4__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_3__REG DENALI_PHY_768
#define LPDDR4__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_3__FLD LPDDR4__DENALI_PHY_768__PHY_LP4_BOOT_RX_PCLK_CLK_SEL_3

#define LPDDR4__DENALI_PHY_768__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_3_MASK 0x00007F00U
#define LPDDR4__DENALI_PHY_768__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_3_SHIFT        8U
#define LPDDR4__DENALI_PHY_768__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_3_WIDTH        7U
#define LPDDR4__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_3__REG DENALI_PHY_768
#define LPDDR4__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_3__FLD LPDDR4__DENALI_PHY_768__PHY_LP4_BOOT_PAD_DSLICE_IO_CFG_3

#define LPDDR4__DENALI_PHY_768__PHY_CLK_WR_BYPASS_SLAVE_DELAY_3_MASK 0x07FF0000U
#define LPDDR4__DENALI_PHY_768__PHY_CLK_WR_BYPASS_SLAVE_DELAY_3_SHIFT        16U
#define LPDDR4__DENALI_PHY_768__PHY_CLK_WR_BYPASS_SLAVE_DELAY_3_WIDTH        11U
#define LPDDR4__PHY_CLK_WR_BYPASS_SLAVE_DELAY_3__REG DENALI_PHY_768
#define LPDDR4__PHY_CLK_WR_BYPASS_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_768__PHY_CLK_WR_BYPASS_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_769_READ_MASK                             0x0703FF0FU
#define LPDDR4__DENALI_PHY_769_WRITE_MASK                            0x0703FF0FU
#define LPDDR4__DENALI_PHY_769__PHY_IO_PAD_DELAY_TIMING_BYPASS_3_MASK 0x0000000FU
#define LPDDR4__DENALI_PHY_769__PHY_IO_PAD_DELAY_TIMING_BYPASS_3_SHIFT        0U
#define LPDDR4__DENALI_PHY_769__PHY_IO_PAD_DELAY_TIMING_BYPASS_3_WIDTH        4U
#define LPDDR4__PHY_IO_PAD_DELAY_TIMING_BYPASS_3__REG DENALI_PHY_769
#define LPDDR4__PHY_IO_PAD_DELAY_TIMING_BYPASS_3__FLD LPDDR4__DENALI_PHY_769__PHY_IO_PAD_DELAY_TIMING_BYPASS_3

#define LPDDR4__DENALI_PHY_769__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_3_MASK 0x0003FF00U
#define LPDDR4__DENALI_PHY_769__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_3_SHIFT      8U
#define LPDDR4__DENALI_PHY_769__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_3_WIDTH     10U
#define LPDDR4__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_3__REG DENALI_PHY_769
#define LPDDR4__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_3__FLD LPDDR4__DENALI_PHY_769__PHY_CLK_WRDQS_SLAVE_DELAY_BYPASS_3

#define LPDDR4__DENALI_PHY_769__PHY_WRITE_PATH_LAT_ADD_BYPASS_3_MASK 0x07000000U
#define LPDDR4__DENALI_PHY_769__PHY_WRITE_PATH_LAT_ADD_BYPASS_3_SHIFT        24U
#define LPDDR4__DENALI_PHY_769__PHY_WRITE_PATH_LAT_ADD_BYPASS_3_WIDTH         3U
#define LPDDR4__PHY_WRITE_PATH_LAT_ADD_BYPASS_3__REG DENALI_PHY_769
#define LPDDR4__PHY_WRITE_PATH_LAT_ADD_BYPASS_3__FLD LPDDR4__DENALI_PHY_769__PHY_WRITE_PATH_LAT_ADD_BYPASS_3

#define LPDDR4__DENALI_PHY_770_READ_MASK                             0x010303FFU
#define LPDDR4__DENALI_PHY_770_WRITE_MASK                            0x010303FFU
#define LPDDR4__DENALI_PHY_770__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_3_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_770__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_3_SHIFT     0U
#define LPDDR4__DENALI_PHY_770__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_3_WIDTH    10U
#define LPDDR4__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_3__REG DENALI_PHY_770
#define LPDDR4__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_770__PHY_RDDQS_GATE_BYPASS_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_770__PHY_BYPASS_TWO_CYC_PREAMBLE_3_MASK   0x00030000U
#define LPDDR4__DENALI_PHY_770__PHY_BYPASS_TWO_CYC_PREAMBLE_3_SHIFT          16U
#define LPDDR4__DENALI_PHY_770__PHY_BYPASS_TWO_CYC_PREAMBLE_3_WIDTH           2U
#define LPDDR4__PHY_BYPASS_TWO_CYC_PREAMBLE_3__REG DENALI_PHY_770
#define LPDDR4__PHY_BYPASS_TWO_CYC_PREAMBLE_3__FLD LPDDR4__DENALI_PHY_770__PHY_BYPASS_TWO_CYC_PREAMBLE_3

#define LPDDR4__DENALI_PHY_770__PHY_CLK_BYPASS_OVERRIDE_3_MASK       0x01000000U
#define LPDDR4__DENALI_PHY_770__PHY_CLK_BYPASS_OVERRIDE_3_SHIFT              24U
#define LPDDR4__DENALI_PHY_770__PHY_CLK_BYPASS_OVERRIDE_3_WIDTH               1U
#define LPDDR4__DENALI_PHY_770__PHY_CLK_BYPASS_OVERRIDE_3_WOCLR               0U
#define LPDDR4__DENALI_PHY_770__PHY_CLK_BYPASS_OVERRIDE_3_WOSET               0U
#define LPDDR4__PHY_CLK_BYPASS_OVERRIDE_3__REG DENALI_PHY_770
#define LPDDR4__PHY_CLK_BYPASS_OVERRIDE_3__FLD LPDDR4__DENALI_PHY_770__PHY_CLK_BYPASS_OVERRIDE_3

#define LPDDR4__DENALI_PHY_771_READ_MASK                             0x3F3F3F3FU
#define LPDDR4__DENALI_PHY_771_WRITE_MASK                            0x3F3F3F3FU
#define LPDDR4__DENALI_PHY_771__PHY_SW_WRDQ0_SHIFT_3_MASK            0x0000003FU
#define LPDDR4__DENALI_PHY_771__PHY_SW_WRDQ0_SHIFT_3_SHIFT                    0U
#define LPDDR4__DENALI_PHY_771__PHY_SW_WRDQ0_SHIFT_3_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ0_SHIFT_3__REG DENALI_PHY_771
#define LPDDR4__PHY_SW_WRDQ0_SHIFT_3__FLD LPDDR4__DENALI_PHY_771__PHY_SW_WRDQ0_SHIFT_3

#define LPDDR4__DENALI_PHY_771__PHY_SW_WRDQ1_SHIFT_3_MASK            0x00003F00U
#define LPDDR4__DENALI_PHY_771__PHY_SW_WRDQ1_SHIFT_3_SHIFT                    8U
#define LPDDR4__DENALI_PHY_771__PHY_SW_WRDQ1_SHIFT_3_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ1_SHIFT_3__REG DENALI_PHY_771
#define LPDDR4__PHY_SW_WRDQ1_SHIFT_3__FLD LPDDR4__DENALI_PHY_771__PHY_SW_WRDQ1_SHIFT_3

#define LPDDR4__DENALI_PHY_771__PHY_SW_WRDQ2_SHIFT_3_MASK            0x003F0000U
#define LPDDR4__DENALI_PHY_771__PHY_SW_WRDQ2_SHIFT_3_SHIFT                   16U
#define LPDDR4__DENALI_PHY_771__PHY_SW_WRDQ2_SHIFT_3_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ2_SHIFT_3__REG DENALI_PHY_771
#define LPDDR4__PHY_SW_WRDQ2_SHIFT_3__FLD LPDDR4__DENALI_PHY_771__PHY_SW_WRDQ2_SHIFT_3

#define LPDDR4__DENALI_PHY_771__PHY_SW_WRDQ3_SHIFT_3_MASK            0x3F000000U
#define LPDDR4__DENALI_PHY_771__PHY_SW_WRDQ3_SHIFT_3_SHIFT                   24U
#define LPDDR4__DENALI_PHY_771__PHY_SW_WRDQ3_SHIFT_3_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ3_SHIFT_3__REG DENALI_PHY_771
#define LPDDR4__PHY_SW_WRDQ3_SHIFT_3__FLD LPDDR4__DENALI_PHY_771__PHY_SW_WRDQ3_SHIFT_3

#define LPDDR4__DENALI_PHY_772_READ_MASK                             0x3F3F3F3FU
#define LPDDR4__DENALI_PHY_772_WRITE_MASK                            0x3F3F3F3FU
#define LPDDR4__DENALI_PHY_772__PHY_SW_WRDQ4_SHIFT_3_MASK            0x0000003FU
#define LPDDR4__DENALI_PHY_772__PHY_SW_WRDQ4_SHIFT_3_SHIFT                    0U
#define LPDDR4__DENALI_PHY_772__PHY_SW_WRDQ4_SHIFT_3_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ4_SHIFT_3__REG DENALI_PHY_772
#define LPDDR4__PHY_SW_WRDQ4_SHIFT_3__FLD LPDDR4__DENALI_PHY_772__PHY_SW_WRDQ4_SHIFT_3

#define LPDDR4__DENALI_PHY_772__PHY_SW_WRDQ5_SHIFT_3_MASK            0x00003F00U
#define LPDDR4__DENALI_PHY_772__PHY_SW_WRDQ5_SHIFT_3_SHIFT                    8U
#define LPDDR4__DENALI_PHY_772__PHY_SW_WRDQ5_SHIFT_3_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ5_SHIFT_3__REG DENALI_PHY_772
#define LPDDR4__PHY_SW_WRDQ5_SHIFT_3__FLD LPDDR4__DENALI_PHY_772__PHY_SW_WRDQ5_SHIFT_3

#define LPDDR4__DENALI_PHY_772__PHY_SW_WRDQ6_SHIFT_3_MASK            0x003F0000U
#define LPDDR4__DENALI_PHY_772__PHY_SW_WRDQ6_SHIFT_3_SHIFT                   16U
#define LPDDR4__DENALI_PHY_772__PHY_SW_WRDQ6_SHIFT_3_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ6_SHIFT_3__REG DENALI_PHY_772
#define LPDDR4__PHY_SW_WRDQ6_SHIFT_3__FLD LPDDR4__DENALI_PHY_772__PHY_SW_WRDQ6_SHIFT_3

#define LPDDR4__DENALI_PHY_772__PHY_SW_WRDQ7_SHIFT_3_MASK            0x3F000000U
#define LPDDR4__DENALI_PHY_772__PHY_SW_WRDQ7_SHIFT_3_SHIFT                   24U
#define LPDDR4__DENALI_PHY_772__PHY_SW_WRDQ7_SHIFT_3_WIDTH                    6U
#define LPDDR4__PHY_SW_WRDQ7_SHIFT_3__REG DENALI_PHY_772
#define LPDDR4__PHY_SW_WRDQ7_SHIFT_3__FLD LPDDR4__DENALI_PHY_772__PHY_SW_WRDQ7_SHIFT_3

#define LPDDR4__DENALI_PHY_773_READ_MASK                             0x01030F3FU
#define LPDDR4__DENALI_PHY_773_WRITE_MASK                            0x01030F3FU
#define LPDDR4__DENALI_PHY_773__PHY_SW_WRDM_SHIFT_3_MASK             0x0000003FU
#define LPDDR4__DENALI_PHY_773__PHY_SW_WRDM_SHIFT_3_SHIFT                     0U
#define LPDDR4__DENALI_PHY_773__PHY_SW_WRDM_SHIFT_3_WIDTH                     6U
#define LPDDR4__PHY_SW_WRDM_SHIFT_3__REG DENALI_PHY_773
#define LPDDR4__PHY_SW_WRDM_SHIFT_3__FLD LPDDR4__DENALI_PHY_773__PHY_SW_WRDM_SHIFT_3

#define LPDDR4__DENALI_PHY_773__PHY_SW_WRDQS_SHIFT_3_MASK            0x00000F00U
#define LPDDR4__DENALI_PHY_773__PHY_SW_WRDQS_SHIFT_3_SHIFT                    8U
#define LPDDR4__DENALI_PHY_773__PHY_SW_WRDQS_SHIFT_3_WIDTH                    4U
#define LPDDR4__PHY_SW_WRDQS_SHIFT_3__REG DENALI_PHY_773
#define LPDDR4__PHY_SW_WRDQS_SHIFT_3__FLD LPDDR4__DENALI_PHY_773__PHY_SW_WRDQS_SHIFT_3

#define LPDDR4__DENALI_PHY_773__PHY_PER_RANK_CS_MAP_3_MASK           0x00030000U
#define LPDDR4__DENALI_PHY_773__PHY_PER_RANK_CS_MAP_3_SHIFT                  16U
#define LPDDR4__DENALI_PHY_773__PHY_PER_RANK_CS_MAP_3_WIDTH                   2U
#define LPDDR4__PHY_PER_RANK_CS_MAP_3__REG DENALI_PHY_773
#define LPDDR4__PHY_PER_RANK_CS_MAP_3__FLD LPDDR4__DENALI_PHY_773__PHY_PER_RANK_CS_MAP_3

#define LPDDR4__DENALI_PHY_773__PHY_PER_CS_TRAINING_MULTICAST_EN_3_MASK 0x01000000U
#define LPDDR4__DENALI_PHY_773__PHY_PER_CS_TRAINING_MULTICAST_EN_3_SHIFT     24U
#define LPDDR4__DENALI_PHY_773__PHY_PER_CS_TRAINING_MULTICAST_EN_3_WIDTH      1U
#define LPDDR4__DENALI_PHY_773__PHY_PER_CS_TRAINING_MULTICAST_EN_3_WOCLR      0U
#define LPDDR4__DENALI_PHY_773__PHY_PER_CS_TRAINING_MULTICAST_EN_3_WOSET      0U
#define LPDDR4__PHY_PER_CS_TRAINING_MULTICAST_EN_3__REG DENALI_PHY_773
#define LPDDR4__PHY_PER_CS_TRAINING_MULTICAST_EN_3__FLD LPDDR4__DENALI_PHY_773__PHY_PER_CS_TRAINING_MULTICAST_EN_3

#define LPDDR4__DENALI_PHY_774_READ_MASK                             0x1F1F0301U
#define LPDDR4__DENALI_PHY_774_WRITE_MASK                            0x1F1F0301U
#define LPDDR4__DENALI_PHY_774__PHY_PER_CS_TRAINING_INDEX_3_MASK     0x00000001U
#define LPDDR4__DENALI_PHY_774__PHY_PER_CS_TRAINING_INDEX_3_SHIFT             0U
#define LPDDR4__DENALI_PHY_774__PHY_PER_CS_TRAINING_INDEX_3_WIDTH             1U
#define LPDDR4__DENALI_PHY_774__PHY_PER_CS_TRAINING_INDEX_3_WOCLR             0U
#define LPDDR4__DENALI_PHY_774__PHY_PER_CS_TRAINING_INDEX_3_WOSET             0U
#define LPDDR4__PHY_PER_CS_TRAINING_INDEX_3__REG DENALI_PHY_774
#define LPDDR4__PHY_PER_CS_TRAINING_INDEX_3__FLD LPDDR4__DENALI_PHY_774__PHY_PER_CS_TRAINING_INDEX_3

#define LPDDR4__DENALI_PHY_774__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_3_MASK 0x00000300U
#define LPDDR4__DENALI_PHY_774__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_3_SHIFT         8U
#define LPDDR4__DENALI_PHY_774__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_3_WIDTH         2U
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_3__REG DENALI_PHY_774
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_3__FLD LPDDR4__DENALI_PHY_774__PHY_LP4_BOOT_RDDATA_EN_IE_DLY_3

#define LPDDR4__DENALI_PHY_774__PHY_LP4_BOOT_RDDATA_EN_DLY_3_MASK    0x001F0000U
#define LPDDR4__DENALI_PHY_774__PHY_LP4_BOOT_RDDATA_EN_DLY_3_SHIFT           16U
#define LPDDR4__DENALI_PHY_774__PHY_LP4_BOOT_RDDATA_EN_DLY_3_WIDTH            5U
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_DLY_3__REG DENALI_PHY_774
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_DLY_3__FLD LPDDR4__DENALI_PHY_774__PHY_LP4_BOOT_RDDATA_EN_DLY_3

#define LPDDR4__DENALI_PHY_774__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_3_MASK 0x1F000000U
#define LPDDR4__DENALI_PHY_774__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_3_SHIFT      24U
#define LPDDR4__DENALI_PHY_774__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_3_WIDTH       5U
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_3__REG DENALI_PHY_774
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_3__FLD LPDDR4__DENALI_PHY_774__PHY_LP4_BOOT_RDDATA_EN_TSEL_DLY_3

#define LPDDR4__DENALI_PHY_775_READ_MASK                             0x1F030F0FU
#define LPDDR4__DENALI_PHY_775_WRITE_MASK                            0x1F030F0FU
#define LPDDR4__DENALI_PHY_775__PHY_LP4_BOOT_RPTR_UPDATE_3_MASK      0x0000000FU
#define LPDDR4__DENALI_PHY_775__PHY_LP4_BOOT_RPTR_UPDATE_3_SHIFT              0U
#define LPDDR4__DENALI_PHY_775__PHY_LP4_BOOT_RPTR_UPDATE_3_WIDTH              4U
#define LPDDR4__PHY_LP4_BOOT_RPTR_UPDATE_3__REG DENALI_PHY_775
#define LPDDR4__PHY_LP4_BOOT_RPTR_UPDATE_3__FLD LPDDR4__DENALI_PHY_775__PHY_LP4_BOOT_RPTR_UPDATE_3

#define LPDDR4__DENALI_PHY_775__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_3_MASK 0x00000F00U
#define LPDDR4__DENALI_PHY_775__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_3_SHIFT     8U
#define LPDDR4__DENALI_PHY_775__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_3_WIDTH     4U
#define LPDDR4__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_3__REG DENALI_PHY_775
#define LPDDR4__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_3__FLD LPDDR4__DENALI_PHY_775__PHY_LP4_BOOT_RDDQS_LATENCY_ADJUST_3

#define LPDDR4__DENALI_PHY_775__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_3_MASK 0x00030000U
#define LPDDR4__DENALI_PHY_775__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_3_SHIFT     16U
#define LPDDR4__DENALI_PHY_775__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_3_WIDTH      2U
#define LPDDR4__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_3__REG DENALI_PHY_775
#define LPDDR4__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_3__FLD LPDDR4__DENALI_PHY_775__PHY_LP4_BOOT_WRPATH_GATE_DISABLE_3

#define LPDDR4__DENALI_PHY_775__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_3_MASK 0x1F000000U
#define LPDDR4__DENALI_PHY_775__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_3_SHIFT        24U
#define LPDDR4__DENALI_PHY_775__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_3_WIDTH         5U
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_3__REG DENALI_PHY_775
#define LPDDR4__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_3__FLD LPDDR4__DENALI_PHY_775__PHY_LP4_BOOT_RDDATA_EN_OE_DLY_3

#define LPDDR4__DENALI_PHY_776_READ_MASK                             0x0101FF03U
#define LPDDR4__DENALI_PHY_776_WRITE_MASK                            0x0101FF03U
#define LPDDR4__DENALI_PHY_776__PHY_CTRL_LPBK_EN_3_MASK              0x00000003U
#define LPDDR4__DENALI_PHY_776__PHY_CTRL_LPBK_EN_3_SHIFT                      0U
#define LPDDR4__DENALI_PHY_776__PHY_CTRL_LPBK_EN_3_WIDTH                      2U
#define LPDDR4__PHY_CTRL_LPBK_EN_3__REG DENALI_PHY_776
#define LPDDR4__PHY_CTRL_LPBK_EN_3__FLD LPDDR4__DENALI_PHY_776__PHY_CTRL_LPBK_EN_3

#define LPDDR4__DENALI_PHY_776__PHY_LPBK_CONTROL_3_MASK              0x0001FF00U
#define LPDDR4__DENALI_PHY_776__PHY_LPBK_CONTROL_3_SHIFT                      8U
#define LPDDR4__DENALI_PHY_776__PHY_LPBK_CONTROL_3_WIDTH                      9U
#define LPDDR4__PHY_LPBK_CONTROL_3__REG DENALI_PHY_776
#define LPDDR4__PHY_LPBK_CONTROL_3__FLD LPDDR4__DENALI_PHY_776__PHY_LPBK_CONTROL_3

#define LPDDR4__DENALI_PHY_776__PHY_LPBK_DFX_TIMEOUT_EN_3_MASK       0x01000000U
#define LPDDR4__DENALI_PHY_776__PHY_LPBK_DFX_TIMEOUT_EN_3_SHIFT              24U
#define LPDDR4__DENALI_PHY_776__PHY_LPBK_DFX_TIMEOUT_EN_3_WIDTH               1U
#define LPDDR4__DENALI_PHY_776__PHY_LPBK_DFX_TIMEOUT_EN_3_WOCLR               0U
#define LPDDR4__DENALI_PHY_776__PHY_LPBK_DFX_TIMEOUT_EN_3_WOSET               0U
#define LPDDR4__PHY_LPBK_DFX_TIMEOUT_EN_3__REG DENALI_PHY_776
#define LPDDR4__PHY_LPBK_DFX_TIMEOUT_EN_3__FLD LPDDR4__DENALI_PHY_776__PHY_LPBK_DFX_TIMEOUT_EN_3

#define LPDDR4__DENALI_PHY_777_READ_MASK                             0x00000001U
#define LPDDR4__DENALI_PHY_777_WRITE_MASK                            0x00000001U
#define LPDDR4__DENALI_PHY_777__PHY_GATE_DELAY_COMP_DISABLE_3_MASK   0x00000001U
#define LPDDR4__DENALI_PHY_777__PHY_GATE_DELAY_COMP_DISABLE_3_SHIFT           0U
#define LPDDR4__DENALI_PHY_777__PHY_GATE_DELAY_COMP_DISABLE_3_WIDTH           1U
#define LPDDR4__DENALI_PHY_777__PHY_GATE_DELAY_COMP_DISABLE_3_WOCLR           0U
#define LPDDR4__DENALI_PHY_777__PHY_GATE_DELAY_COMP_DISABLE_3_WOSET           0U
#define LPDDR4__PHY_GATE_DELAY_COMP_DISABLE_3__REG DENALI_PHY_777
#define LPDDR4__PHY_GATE_DELAY_COMP_DISABLE_3__FLD LPDDR4__DENALI_PHY_777__PHY_GATE_DELAY_COMP_DISABLE_3

#define LPDDR4__DENALI_PHY_778_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_778_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_778__PHY_AUTO_TIMING_MARGIN_CONTROL_3_MASK 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_778__PHY_AUTO_TIMING_MARGIN_CONTROL_3_SHIFT        0U
#define LPDDR4__DENALI_PHY_778__PHY_AUTO_TIMING_MARGIN_CONTROL_3_WIDTH       32U
#define LPDDR4__PHY_AUTO_TIMING_MARGIN_CONTROL_3__REG DENALI_PHY_778
#define LPDDR4__PHY_AUTO_TIMING_MARGIN_CONTROL_3__FLD LPDDR4__DENALI_PHY_778__PHY_AUTO_TIMING_MARGIN_CONTROL_3

#define LPDDR4__DENALI_PHY_779_READ_MASK                             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_779_WRITE_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_779__PHY_AUTO_TIMING_MARGIN_OBS_3_MASK    0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_779__PHY_AUTO_TIMING_MARGIN_OBS_3_SHIFT            0U
#define LPDDR4__DENALI_PHY_779__PHY_AUTO_TIMING_MARGIN_OBS_3_WIDTH           28U
#define LPDDR4__PHY_AUTO_TIMING_MARGIN_OBS_3__REG DENALI_PHY_779
#define LPDDR4__PHY_AUTO_TIMING_MARGIN_OBS_3__FLD LPDDR4__DENALI_PHY_779__PHY_AUTO_TIMING_MARGIN_OBS_3

#define LPDDR4__DENALI_PHY_780_READ_MASK                             0x7F0101FFU
#define LPDDR4__DENALI_PHY_780_WRITE_MASK                            0x7F0101FFU
#define LPDDR4__DENALI_PHY_780__PHY_DQ_IDLE_3_MASK                   0x000001FFU
#define LPDDR4__DENALI_PHY_780__PHY_DQ_IDLE_3_SHIFT                           0U
#define LPDDR4__DENALI_PHY_780__PHY_DQ_IDLE_3_WIDTH                           9U
#define LPDDR4__PHY_DQ_IDLE_3__REG DENALI_PHY_780
#define LPDDR4__PHY_DQ_IDLE_3__FLD LPDDR4__DENALI_PHY_780__PHY_DQ_IDLE_3

#define LPDDR4__DENALI_PHY_780__PHY_PDA_MODE_EN_3_MASK               0x00010000U
#define LPDDR4__DENALI_PHY_780__PHY_PDA_MODE_EN_3_SHIFT                      16U
#define LPDDR4__DENALI_PHY_780__PHY_PDA_MODE_EN_3_WIDTH                       1U
#define LPDDR4__DENALI_PHY_780__PHY_PDA_MODE_EN_3_WOCLR                       0U
#define LPDDR4__DENALI_PHY_780__PHY_PDA_MODE_EN_3_WOSET                       0U
#define LPDDR4__PHY_PDA_MODE_EN_3__REG DENALI_PHY_780
#define LPDDR4__PHY_PDA_MODE_EN_3__FLD LPDDR4__DENALI_PHY_780__PHY_PDA_MODE_EN_3

#define LPDDR4__DENALI_PHY_780__PHY_PRBS_PATTERN_START_3_MASK        0x7F000000U
#define LPDDR4__DENALI_PHY_780__PHY_PRBS_PATTERN_START_3_SHIFT               24U
#define LPDDR4__DENALI_PHY_780__PHY_PRBS_PATTERN_START_3_WIDTH                7U
#define LPDDR4__PHY_PRBS_PATTERN_START_3__REG DENALI_PHY_780
#define LPDDR4__PHY_PRBS_PATTERN_START_3__FLD LPDDR4__DENALI_PHY_780__PHY_PRBS_PATTERN_START_3

#define LPDDR4__DENALI_PHY_781_READ_MASK                             0x010101FFU
#define LPDDR4__DENALI_PHY_781_WRITE_MASK                            0x010101FFU
#define LPDDR4__DENALI_PHY_781__PHY_PRBS_PATTERN_MASK_3_MASK         0x000001FFU
#define LPDDR4__DENALI_PHY_781__PHY_PRBS_PATTERN_MASK_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_781__PHY_PRBS_PATTERN_MASK_3_WIDTH                 9U
#define LPDDR4__PHY_PRBS_PATTERN_MASK_3__REG DENALI_PHY_781
#define LPDDR4__PHY_PRBS_PATTERN_MASK_3__FLD LPDDR4__DENALI_PHY_781__PHY_PRBS_PATTERN_MASK_3

#define LPDDR4__DENALI_PHY_781__PHY_RDLVL_MULTI_PATT_ENABLE_3_MASK   0x00010000U
#define LPDDR4__DENALI_PHY_781__PHY_RDLVL_MULTI_PATT_ENABLE_3_SHIFT          16U
#define LPDDR4__DENALI_PHY_781__PHY_RDLVL_MULTI_PATT_ENABLE_3_WIDTH           1U
#define LPDDR4__DENALI_PHY_781__PHY_RDLVL_MULTI_PATT_ENABLE_3_WOCLR           0U
#define LPDDR4__DENALI_PHY_781__PHY_RDLVL_MULTI_PATT_ENABLE_3_WOSET           0U
#define LPDDR4__PHY_RDLVL_MULTI_PATT_ENABLE_3__REG DENALI_PHY_781
#define LPDDR4__PHY_RDLVL_MULTI_PATT_ENABLE_3__FLD LPDDR4__DENALI_PHY_781__PHY_RDLVL_MULTI_PATT_ENABLE_3

#define LPDDR4__DENALI_PHY_781__PHY_RDLVL_MULTI_PATT_RST_DISABLE_3_MASK 0x01000000U
#define LPDDR4__DENALI_PHY_781__PHY_RDLVL_MULTI_PATT_RST_DISABLE_3_SHIFT     24U
#define LPDDR4__DENALI_PHY_781__PHY_RDLVL_MULTI_PATT_RST_DISABLE_3_WIDTH      1U
#define LPDDR4__DENALI_PHY_781__PHY_RDLVL_MULTI_PATT_RST_DISABLE_3_WOCLR      0U
#define LPDDR4__DENALI_PHY_781__PHY_RDLVL_MULTI_PATT_RST_DISABLE_3_WOSET      0U
#define LPDDR4__PHY_RDLVL_MULTI_PATT_RST_DISABLE_3__REG DENALI_PHY_781
#define LPDDR4__PHY_RDLVL_MULTI_PATT_RST_DISABLE_3__FLD LPDDR4__DENALI_PHY_781__PHY_RDLVL_MULTI_PATT_RST_DISABLE_3

#define LPDDR4__DENALI_PHY_782_READ_MASK                             0x03FF7F3FU
#define LPDDR4__DENALI_PHY_782_WRITE_MASK                            0x03FF7F3FU
#define LPDDR4__DENALI_PHY_782__PHY_VREF_INITIAL_STEPSIZE_3_MASK     0x0000003FU
#define LPDDR4__DENALI_PHY_782__PHY_VREF_INITIAL_STEPSIZE_3_SHIFT             0U
#define LPDDR4__DENALI_PHY_782__PHY_VREF_INITIAL_STEPSIZE_3_WIDTH             6U
#define LPDDR4__PHY_VREF_INITIAL_STEPSIZE_3__REG DENALI_PHY_782
#define LPDDR4__PHY_VREF_INITIAL_STEPSIZE_3__FLD LPDDR4__DENALI_PHY_782__PHY_VREF_INITIAL_STEPSIZE_3

#define LPDDR4__DENALI_PHY_782__PHY_VREF_TRAIN_OBS_3_MASK            0x00007F00U
#define LPDDR4__DENALI_PHY_782__PHY_VREF_TRAIN_OBS_3_SHIFT                    8U
#define LPDDR4__DENALI_PHY_782__PHY_VREF_TRAIN_OBS_3_WIDTH                    7U
#define LPDDR4__PHY_VREF_TRAIN_OBS_3__REG DENALI_PHY_782
#define LPDDR4__PHY_VREF_TRAIN_OBS_3__FLD LPDDR4__DENALI_PHY_782__PHY_VREF_TRAIN_OBS_3

#define LPDDR4__DENALI_PHY_782__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_3_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_782__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_3_SHIFT      16U
#define LPDDR4__DENALI_PHY_782__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_3_WIDTH      10U
#define LPDDR4__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_3__REG DENALI_PHY_782
#define LPDDR4__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_782__PHY_RDDQS_DQ_BYPASS_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_783_READ_MASK                             0x01FF000FU
#define LPDDR4__DENALI_PHY_783_WRITE_MASK                            0x01FF000FU
#define LPDDR4__DENALI_PHY_783__PHY_GATE_ERROR_DELAY_SELECT_3_MASK   0x0000000FU
#define LPDDR4__DENALI_PHY_783__PHY_GATE_ERROR_DELAY_SELECT_3_SHIFT           0U
#define LPDDR4__DENALI_PHY_783__PHY_GATE_ERROR_DELAY_SELECT_3_WIDTH           4U
#define LPDDR4__PHY_GATE_ERROR_DELAY_SELECT_3__REG DENALI_PHY_783
#define LPDDR4__PHY_GATE_ERROR_DELAY_SELECT_3__FLD LPDDR4__DENALI_PHY_783__PHY_GATE_ERROR_DELAY_SELECT_3

#define LPDDR4__DENALI_PHY_783__SC_PHY_SNAP_OBS_REGS_3_MASK          0x00000100U
#define LPDDR4__DENALI_PHY_783__SC_PHY_SNAP_OBS_REGS_3_SHIFT                  8U
#define LPDDR4__DENALI_PHY_783__SC_PHY_SNAP_OBS_REGS_3_WIDTH                  1U
#define LPDDR4__DENALI_PHY_783__SC_PHY_SNAP_OBS_REGS_3_WOCLR                  0U
#define LPDDR4__DENALI_PHY_783__SC_PHY_SNAP_OBS_REGS_3_WOSET                  0U
#define LPDDR4__SC_PHY_SNAP_OBS_REGS_3__REG DENALI_PHY_783
#define LPDDR4__SC_PHY_SNAP_OBS_REGS_3__FLD LPDDR4__DENALI_PHY_783__SC_PHY_SNAP_OBS_REGS_3

#define LPDDR4__DENALI_PHY_783__PHY_GATE_SMPL1_SLAVE_DELAY_3_MASK    0x01FF0000U
#define LPDDR4__DENALI_PHY_783__PHY_GATE_SMPL1_SLAVE_DELAY_3_SHIFT           16U
#define LPDDR4__DENALI_PHY_783__PHY_GATE_SMPL1_SLAVE_DELAY_3_WIDTH            9U
#define LPDDR4__PHY_GATE_SMPL1_SLAVE_DELAY_3__REG DENALI_PHY_783
#define LPDDR4__PHY_GATE_SMPL1_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_783__PHY_GATE_SMPL1_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_784_READ_MASK                             0x01FF0701U
#define LPDDR4__DENALI_PHY_784_WRITE_MASK                            0x01FF0701U
#define LPDDR4__DENALI_PHY_784__PHY_LPDDR_3_MASK                     0x00000001U
#define LPDDR4__DENALI_PHY_784__PHY_LPDDR_3_SHIFT                             0U
#define LPDDR4__DENALI_PHY_784__PHY_LPDDR_3_WIDTH                             1U
#define LPDDR4__DENALI_PHY_784__PHY_LPDDR_3_WOCLR                             0U
#define LPDDR4__DENALI_PHY_784__PHY_LPDDR_3_WOSET                             0U
#define LPDDR4__PHY_LPDDR_3__REG DENALI_PHY_784
#define LPDDR4__PHY_LPDDR_3__FLD LPDDR4__DENALI_PHY_784__PHY_LPDDR_3

#define LPDDR4__DENALI_PHY_784__PHY_MEM_CLASS_3_MASK                 0x00000700U
#define LPDDR4__DENALI_PHY_784__PHY_MEM_CLASS_3_SHIFT                         8U
#define LPDDR4__DENALI_PHY_784__PHY_MEM_CLASS_3_WIDTH                         3U
#define LPDDR4__PHY_MEM_CLASS_3__REG DENALI_PHY_784
#define LPDDR4__PHY_MEM_CLASS_3__FLD LPDDR4__DENALI_PHY_784__PHY_MEM_CLASS_3

#define LPDDR4__DENALI_PHY_784__PHY_GATE_SMPL2_SLAVE_DELAY_3_MASK    0x01FF0000U
#define LPDDR4__DENALI_PHY_784__PHY_GATE_SMPL2_SLAVE_DELAY_3_SHIFT           16U
#define LPDDR4__DENALI_PHY_784__PHY_GATE_SMPL2_SLAVE_DELAY_3_WIDTH            9U
#define LPDDR4__PHY_GATE_SMPL2_SLAVE_DELAY_3__REG DENALI_PHY_784
#define LPDDR4__PHY_GATE_SMPL2_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_784__PHY_GATE_SMPL2_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_785_READ_MASK                             0x00000003U
#define LPDDR4__DENALI_PHY_785_WRITE_MASK                            0x00000003U
#define LPDDR4__DENALI_PHY_785__ON_FLY_GATE_ADJUST_EN_3_MASK         0x00000003U
#define LPDDR4__DENALI_PHY_785__ON_FLY_GATE_ADJUST_EN_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_785__ON_FLY_GATE_ADJUST_EN_3_WIDTH                 2U
#define LPDDR4__ON_FLY_GATE_ADJUST_EN_3__REG DENALI_PHY_785
#define LPDDR4__ON_FLY_GATE_ADJUST_EN_3__FLD LPDDR4__DENALI_PHY_785__ON_FLY_GATE_ADJUST_EN_3

#define LPDDR4__DENALI_PHY_786_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_786_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_786__PHY_GATE_TRACKING_OBS_3_MASK         0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_786__PHY_GATE_TRACKING_OBS_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_786__PHY_GATE_TRACKING_OBS_3_WIDTH                32U
#define LPDDR4__PHY_GATE_TRACKING_OBS_3__REG DENALI_PHY_786
#define LPDDR4__PHY_GATE_TRACKING_OBS_3__FLD LPDDR4__DENALI_PHY_786__PHY_GATE_TRACKING_OBS_3

#define LPDDR4__DENALI_PHY_787_READ_MASK                             0x00000301U
#define LPDDR4__DENALI_PHY_787_WRITE_MASK                            0x00000301U
#define LPDDR4__DENALI_PHY_787__PHY_DFI40_POLARITY_3_MASK            0x00000001U
#define LPDDR4__DENALI_PHY_787__PHY_DFI40_POLARITY_3_SHIFT                    0U
#define LPDDR4__DENALI_PHY_787__PHY_DFI40_POLARITY_3_WIDTH                    1U
#define LPDDR4__DENALI_PHY_787__PHY_DFI40_POLARITY_3_WOCLR                    0U
#define LPDDR4__DENALI_PHY_787__PHY_DFI40_POLARITY_3_WOSET                    0U
#define LPDDR4__PHY_DFI40_POLARITY_3__REG DENALI_PHY_787
#define LPDDR4__PHY_DFI40_POLARITY_3__FLD LPDDR4__DENALI_PHY_787__PHY_DFI40_POLARITY_3

#define LPDDR4__DENALI_PHY_787__PHY_LP4_PST_AMBLE_3_MASK             0x00000300U
#define LPDDR4__DENALI_PHY_787__PHY_LP4_PST_AMBLE_3_SHIFT                     8U
#define LPDDR4__DENALI_PHY_787__PHY_LP4_PST_AMBLE_3_WIDTH                     2U
#define LPDDR4__PHY_LP4_PST_AMBLE_3__REG DENALI_PHY_787
#define LPDDR4__PHY_LP4_PST_AMBLE_3__FLD LPDDR4__DENALI_PHY_787__PHY_LP4_PST_AMBLE_3

#define LPDDR4__DENALI_PHY_788_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_788_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_788__PHY_RDLVL_PATT8_3_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_788__PHY_RDLVL_PATT8_3_SHIFT                       0U
#define LPDDR4__DENALI_PHY_788__PHY_RDLVL_PATT8_3_WIDTH                      32U
#define LPDDR4__PHY_RDLVL_PATT8_3__REG DENALI_PHY_788
#define LPDDR4__PHY_RDLVL_PATT8_3__FLD LPDDR4__DENALI_PHY_788__PHY_RDLVL_PATT8_3

#define LPDDR4__DENALI_PHY_789_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_789_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_789__PHY_RDLVL_PATT9_3_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_789__PHY_RDLVL_PATT9_3_SHIFT                       0U
#define LPDDR4__DENALI_PHY_789__PHY_RDLVL_PATT9_3_WIDTH                      32U
#define LPDDR4__PHY_RDLVL_PATT9_3__REG DENALI_PHY_789
#define LPDDR4__PHY_RDLVL_PATT9_3__FLD LPDDR4__DENALI_PHY_789__PHY_RDLVL_PATT9_3

#define LPDDR4__DENALI_PHY_790_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_790_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_790__PHY_RDLVL_PATT10_3_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_790__PHY_RDLVL_PATT10_3_SHIFT                      0U
#define LPDDR4__DENALI_PHY_790__PHY_RDLVL_PATT10_3_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT10_3__REG DENALI_PHY_790
#define LPDDR4__PHY_RDLVL_PATT10_3__FLD LPDDR4__DENALI_PHY_790__PHY_RDLVL_PATT10_3

#define LPDDR4__DENALI_PHY_791_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_791_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_791__PHY_RDLVL_PATT11_3_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_791__PHY_RDLVL_PATT11_3_SHIFT                      0U
#define LPDDR4__DENALI_PHY_791__PHY_RDLVL_PATT11_3_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT11_3__REG DENALI_PHY_791
#define LPDDR4__PHY_RDLVL_PATT11_3__FLD LPDDR4__DENALI_PHY_791__PHY_RDLVL_PATT11_3

#define LPDDR4__DENALI_PHY_792_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_792_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_792__PHY_RDLVL_PATT12_3_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_792__PHY_RDLVL_PATT12_3_SHIFT                      0U
#define LPDDR4__DENALI_PHY_792__PHY_RDLVL_PATT12_3_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT12_3__REG DENALI_PHY_792
#define LPDDR4__PHY_RDLVL_PATT12_3__FLD LPDDR4__DENALI_PHY_792__PHY_RDLVL_PATT12_3

#define LPDDR4__DENALI_PHY_793_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_793_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_793__PHY_RDLVL_PATT13_3_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_793__PHY_RDLVL_PATT13_3_SHIFT                      0U
#define LPDDR4__DENALI_PHY_793__PHY_RDLVL_PATT13_3_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT13_3__REG DENALI_PHY_793
#define LPDDR4__PHY_RDLVL_PATT13_3__FLD LPDDR4__DENALI_PHY_793__PHY_RDLVL_PATT13_3

#define LPDDR4__DENALI_PHY_794_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_794_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_794__PHY_RDLVL_PATT14_3_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_794__PHY_RDLVL_PATT14_3_SHIFT                      0U
#define LPDDR4__DENALI_PHY_794__PHY_RDLVL_PATT14_3_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT14_3__REG DENALI_PHY_794
#define LPDDR4__PHY_RDLVL_PATT14_3__FLD LPDDR4__DENALI_PHY_794__PHY_RDLVL_PATT14_3

#define LPDDR4__DENALI_PHY_795_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_795_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_795__PHY_RDLVL_PATT15_3_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_795__PHY_RDLVL_PATT15_3_SHIFT                      0U
#define LPDDR4__DENALI_PHY_795__PHY_RDLVL_PATT15_3_WIDTH                     32U
#define LPDDR4__PHY_RDLVL_PATT15_3__REG DENALI_PHY_795
#define LPDDR4__PHY_RDLVL_PATT15_3__FLD LPDDR4__DENALI_PHY_795__PHY_RDLVL_PATT15_3

#define LPDDR4__DENALI_PHY_796_READ_MASK                             0x070F0107U
#define LPDDR4__DENALI_PHY_796_WRITE_MASK                            0x070F0107U
#define LPDDR4__DENALI_PHY_796__PHY_SLAVE_LOOP_CNT_UPDATE_3_MASK     0x00000007U
#define LPDDR4__DENALI_PHY_796__PHY_SLAVE_LOOP_CNT_UPDATE_3_SHIFT             0U
#define LPDDR4__DENALI_PHY_796__PHY_SLAVE_LOOP_CNT_UPDATE_3_WIDTH             3U
#define LPDDR4__PHY_SLAVE_LOOP_CNT_UPDATE_3__REG DENALI_PHY_796
#define LPDDR4__PHY_SLAVE_LOOP_CNT_UPDATE_3__FLD LPDDR4__DENALI_PHY_796__PHY_SLAVE_LOOP_CNT_UPDATE_3

#define LPDDR4__DENALI_PHY_796__PHY_SW_FIFO_PTR_RST_DISABLE_3_MASK   0x00000100U
#define LPDDR4__DENALI_PHY_796__PHY_SW_FIFO_PTR_RST_DISABLE_3_SHIFT           8U
#define LPDDR4__DENALI_PHY_796__PHY_SW_FIFO_PTR_RST_DISABLE_3_WIDTH           1U
#define LPDDR4__DENALI_PHY_796__PHY_SW_FIFO_PTR_RST_DISABLE_3_WOCLR           0U
#define LPDDR4__DENALI_PHY_796__PHY_SW_FIFO_PTR_RST_DISABLE_3_WOSET           0U
#define LPDDR4__PHY_SW_FIFO_PTR_RST_DISABLE_3__REG DENALI_PHY_796
#define LPDDR4__PHY_SW_FIFO_PTR_RST_DISABLE_3__FLD LPDDR4__DENALI_PHY_796__PHY_SW_FIFO_PTR_RST_DISABLE_3

#define LPDDR4__DENALI_PHY_796__PHY_MASTER_DLY_LOCK_OBS_SELECT_3_MASK 0x000F0000U
#define LPDDR4__DENALI_PHY_796__PHY_MASTER_DLY_LOCK_OBS_SELECT_3_SHIFT       16U
#define LPDDR4__DENALI_PHY_796__PHY_MASTER_DLY_LOCK_OBS_SELECT_3_WIDTH        4U
#define LPDDR4__PHY_MASTER_DLY_LOCK_OBS_SELECT_3__REG DENALI_PHY_796
#define LPDDR4__PHY_MASTER_DLY_LOCK_OBS_SELECT_3__FLD LPDDR4__DENALI_PHY_796__PHY_MASTER_DLY_LOCK_OBS_SELECT_3

#define LPDDR4__DENALI_PHY_796__PHY_RDDQ_ENC_OBS_SELECT_3_MASK       0x07000000U
#define LPDDR4__DENALI_PHY_796__PHY_RDDQ_ENC_OBS_SELECT_3_SHIFT              24U
#define LPDDR4__DENALI_PHY_796__PHY_RDDQ_ENC_OBS_SELECT_3_WIDTH               3U
#define LPDDR4__PHY_RDDQ_ENC_OBS_SELECT_3__REG DENALI_PHY_796
#define LPDDR4__PHY_RDDQ_ENC_OBS_SELECT_3__FLD LPDDR4__DENALI_PHY_796__PHY_RDDQ_ENC_OBS_SELECT_3

#define LPDDR4__DENALI_PHY_797_READ_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_797_WRITE_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_797__PHY_RDDQS_DQ_ENC_OBS_SELECT_3_MASK   0x0000000FU
#define LPDDR4__DENALI_PHY_797__PHY_RDDQS_DQ_ENC_OBS_SELECT_3_SHIFT           0U
#define LPDDR4__DENALI_PHY_797__PHY_RDDQS_DQ_ENC_OBS_SELECT_3_WIDTH           4U
#define LPDDR4__PHY_RDDQS_DQ_ENC_OBS_SELECT_3__REG DENALI_PHY_797
#define LPDDR4__PHY_RDDQS_DQ_ENC_OBS_SELECT_3__FLD LPDDR4__DENALI_PHY_797__PHY_RDDQS_DQ_ENC_OBS_SELECT_3

#define LPDDR4__DENALI_PHY_797__PHY_WR_ENC_OBS_SELECT_3_MASK         0x00000F00U
#define LPDDR4__DENALI_PHY_797__PHY_WR_ENC_OBS_SELECT_3_SHIFT                 8U
#define LPDDR4__DENALI_PHY_797__PHY_WR_ENC_OBS_SELECT_3_WIDTH                 4U
#define LPDDR4__PHY_WR_ENC_OBS_SELECT_3__REG DENALI_PHY_797
#define LPDDR4__PHY_WR_ENC_OBS_SELECT_3__FLD LPDDR4__DENALI_PHY_797__PHY_WR_ENC_OBS_SELECT_3

#define LPDDR4__DENALI_PHY_797__PHY_WR_SHIFT_OBS_SELECT_3_MASK       0x000F0000U
#define LPDDR4__DENALI_PHY_797__PHY_WR_SHIFT_OBS_SELECT_3_SHIFT              16U
#define LPDDR4__DENALI_PHY_797__PHY_WR_SHIFT_OBS_SELECT_3_WIDTH               4U
#define LPDDR4__PHY_WR_SHIFT_OBS_SELECT_3__REG DENALI_PHY_797
#define LPDDR4__PHY_WR_SHIFT_OBS_SELECT_3__FLD LPDDR4__DENALI_PHY_797__PHY_WR_SHIFT_OBS_SELECT_3

#define LPDDR4__DENALI_PHY_797__PHY_FIFO_PTR_OBS_SELECT_3_MASK       0x0F000000U
#define LPDDR4__DENALI_PHY_797__PHY_FIFO_PTR_OBS_SELECT_3_SHIFT              24U
#define LPDDR4__DENALI_PHY_797__PHY_FIFO_PTR_OBS_SELECT_3_WIDTH               4U
#define LPDDR4__PHY_FIFO_PTR_OBS_SELECT_3__REG DENALI_PHY_797
#define LPDDR4__PHY_FIFO_PTR_OBS_SELECT_3__FLD LPDDR4__DENALI_PHY_797__PHY_FIFO_PTR_OBS_SELECT_3

#define LPDDR4__DENALI_PHY_798_READ_MASK                             0x3F030001U
#define LPDDR4__DENALI_PHY_798_WRITE_MASK                            0x3F030001U
#define LPDDR4__DENALI_PHY_798__PHY_LVL_DEBUG_MODE_3_MASK            0x00000001U
#define LPDDR4__DENALI_PHY_798__PHY_LVL_DEBUG_MODE_3_SHIFT                    0U
#define LPDDR4__DENALI_PHY_798__PHY_LVL_DEBUG_MODE_3_WIDTH                    1U
#define LPDDR4__DENALI_PHY_798__PHY_LVL_DEBUG_MODE_3_WOCLR                    0U
#define LPDDR4__DENALI_PHY_798__PHY_LVL_DEBUG_MODE_3_WOSET                    0U
#define LPDDR4__PHY_LVL_DEBUG_MODE_3__REG DENALI_PHY_798
#define LPDDR4__PHY_LVL_DEBUG_MODE_3__FLD LPDDR4__DENALI_PHY_798__PHY_LVL_DEBUG_MODE_3

#define LPDDR4__DENALI_PHY_798__SC_PHY_LVL_DEBUG_CONT_3_MASK         0x00000100U
#define LPDDR4__DENALI_PHY_798__SC_PHY_LVL_DEBUG_CONT_3_SHIFT                 8U
#define LPDDR4__DENALI_PHY_798__SC_PHY_LVL_DEBUG_CONT_3_WIDTH                 1U
#define LPDDR4__DENALI_PHY_798__SC_PHY_LVL_DEBUG_CONT_3_WOCLR                 0U
#define LPDDR4__DENALI_PHY_798__SC_PHY_LVL_DEBUG_CONT_3_WOSET                 0U
#define LPDDR4__SC_PHY_LVL_DEBUG_CONT_3__REG DENALI_PHY_798
#define LPDDR4__SC_PHY_LVL_DEBUG_CONT_3__FLD LPDDR4__DENALI_PHY_798__SC_PHY_LVL_DEBUG_CONT_3

#define LPDDR4__DENALI_PHY_798__PHY_WRLVL_ALGO_3_MASK                0x00030000U
#define LPDDR4__DENALI_PHY_798__PHY_WRLVL_ALGO_3_SHIFT                       16U
#define LPDDR4__DENALI_PHY_798__PHY_WRLVL_ALGO_3_WIDTH                        2U
#define LPDDR4__PHY_WRLVL_ALGO_3__REG DENALI_PHY_798
#define LPDDR4__PHY_WRLVL_ALGO_3__FLD LPDDR4__DENALI_PHY_798__PHY_WRLVL_ALGO_3

#define LPDDR4__DENALI_PHY_798__PHY_WRLVL_CAPTURE_CNT_3_MASK         0x3F000000U
#define LPDDR4__DENALI_PHY_798__PHY_WRLVL_CAPTURE_CNT_3_SHIFT                24U
#define LPDDR4__DENALI_PHY_798__PHY_WRLVL_CAPTURE_CNT_3_WIDTH                 6U
#define LPDDR4__PHY_WRLVL_CAPTURE_CNT_3__REG DENALI_PHY_798
#define LPDDR4__PHY_WRLVL_CAPTURE_CNT_3__FLD LPDDR4__DENALI_PHY_798__PHY_WRLVL_CAPTURE_CNT_3

#define LPDDR4__DENALI_PHY_799_READ_MASK                             0x0F3FFF0FU
#define LPDDR4__DENALI_PHY_799_WRITE_MASK                            0x0F3FFF0FU
#define LPDDR4__DENALI_PHY_799__PHY_WRLVL_UPDT_WAIT_CNT_3_MASK       0x0000000FU
#define LPDDR4__DENALI_PHY_799__PHY_WRLVL_UPDT_WAIT_CNT_3_SHIFT               0U
#define LPDDR4__DENALI_PHY_799__PHY_WRLVL_UPDT_WAIT_CNT_3_WIDTH               4U
#define LPDDR4__PHY_WRLVL_UPDT_WAIT_CNT_3__REG DENALI_PHY_799
#define LPDDR4__PHY_WRLVL_UPDT_WAIT_CNT_3__FLD LPDDR4__DENALI_PHY_799__PHY_WRLVL_UPDT_WAIT_CNT_3

#define LPDDR4__DENALI_PHY_799__PHY_DQ_MASK_3_MASK                   0x0000FF00U
#define LPDDR4__DENALI_PHY_799__PHY_DQ_MASK_3_SHIFT                           8U
#define LPDDR4__DENALI_PHY_799__PHY_DQ_MASK_3_WIDTH                           8U
#define LPDDR4__PHY_DQ_MASK_3__REG DENALI_PHY_799
#define LPDDR4__PHY_DQ_MASK_3__FLD LPDDR4__DENALI_PHY_799__PHY_DQ_MASK_3

#define LPDDR4__DENALI_PHY_799__PHY_GTLVL_CAPTURE_CNT_3_MASK         0x003F0000U
#define LPDDR4__DENALI_PHY_799__PHY_GTLVL_CAPTURE_CNT_3_SHIFT                16U
#define LPDDR4__DENALI_PHY_799__PHY_GTLVL_CAPTURE_CNT_3_WIDTH                 6U
#define LPDDR4__PHY_GTLVL_CAPTURE_CNT_3__REG DENALI_PHY_799
#define LPDDR4__PHY_GTLVL_CAPTURE_CNT_3__FLD LPDDR4__DENALI_PHY_799__PHY_GTLVL_CAPTURE_CNT_3

#define LPDDR4__DENALI_PHY_799__PHY_GTLVL_UPDT_WAIT_CNT_3_MASK       0x0F000000U
#define LPDDR4__DENALI_PHY_799__PHY_GTLVL_UPDT_WAIT_CNT_3_SHIFT              24U
#define LPDDR4__DENALI_PHY_799__PHY_GTLVL_UPDT_WAIT_CNT_3_WIDTH               4U
#define LPDDR4__PHY_GTLVL_UPDT_WAIT_CNT_3__REG DENALI_PHY_799
#define LPDDR4__PHY_GTLVL_UPDT_WAIT_CNT_3__FLD LPDDR4__DENALI_PHY_799__PHY_GTLVL_UPDT_WAIT_CNT_3

#define LPDDR4__DENALI_PHY_800_READ_MASK                             0x1F030F3FU
#define LPDDR4__DENALI_PHY_800_WRITE_MASK                            0x1F030F3FU
#define LPDDR4__DENALI_PHY_800__PHY_RDLVL_CAPTURE_CNT_3_MASK         0x0000003FU
#define LPDDR4__DENALI_PHY_800__PHY_RDLVL_CAPTURE_CNT_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_800__PHY_RDLVL_CAPTURE_CNT_3_WIDTH                 6U
#define LPDDR4__PHY_RDLVL_CAPTURE_CNT_3__REG DENALI_PHY_800
#define LPDDR4__PHY_RDLVL_CAPTURE_CNT_3__FLD LPDDR4__DENALI_PHY_800__PHY_RDLVL_CAPTURE_CNT_3

#define LPDDR4__DENALI_PHY_800__PHY_RDLVL_UPDT_WAIT_CNT_3_MASK       0x00000F00U
#define LPDDR4__DENALI_PHY_800__PHY_RDLVL_UPDT_WAIT_CNT_3_SHIFT               8U
#define LPDDR4__DENALI_PHY_800__PHY_RDLVL_UPDT_WAIT_CNT_3_WIDTH               4U
#define LPDDR4__PHY_RDLVL_UPDT_WAIT_CNT_3__REG DENALI_PHY_800
#define LPDDR4__PHY_RDLVL_UPDT_WAIT_CNT_3__FLD LPDDR4__DENALI_PHY_800__PHY_RDLVL_UPDT_WAIT_CNT_3

#define LPDDR4__DENALI_PHY_800__PHY_RDLVL_OP_MODE_3_MASK             0x00030000U
#define LPDDR4__DENALI_PHY_800__PHY_RDLVL_OP_MODE_3_SHIFT                    16U
#define LPDDR4__DENALI_PHY_800__PHY_RDLVL_OP_MODE_3_WIDTH                     2U
#define LPDDR4__PHY_RDLVL_OP_MODE_3__REG DENALI_PHY_800
#define LPDDR4__PHY_RDLVL_OP_MODE_3__FLD LPDDR4__DENALI_PHY_800__PHY_RDLVL_OP_MODE_3

#define LPDDR4__DENALI_PHY_800__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_3_MASK 0x1F000000U
#define LPDDR4__DENALI_PHY_800__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_3_SHIFT        24U
#define LPDDR4__DENALI_PHY_800__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_3_WIDTH         5U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_3__REG DENALI_PHY_800
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_3__FLD LPDDR4__DENALI_PHY_800__PHY_RDLVL_RDDQS_DQ_OBS_SELECT_3

#define LPDDR4__DENALI_PHY_801_READ_MASK                             0x03FFFFFFU
#define LPDDR4__DENALI_PHY_801_WRITE_MASK                            0x03FFFFFFU
#define LPDDR4__DENALI_PHY_801__PHY_RDLVL_DATA_MASK_3_MASK           0x000000FFU
#define LPDDR4__DENALI_PHY_801__PHY_RDLVL_DATA_MASK_3_SHIFT                   0U
#define LPDDR4__DENALI_PHY_801__PHY_RDLVL_DATA_MASK_3_WIDTH                   8U
#define LPDDR4__PHY_RDLVL_DATA_MASK_3__REG DENALI_PHY_801
#define LPDDR4__PHY_RDLVL_DATA_MASK_3__FLD LPDDR4__DENALI_PHY_801__PHY_RDLVL_DATA_MASK_3

#define LPDDR4__DENALI_PHY_801__PHY_RDLVL_DATA_SWIZZLE_3_MASK        0x03FFFF00U
#define LPDDR4__DENALI_PHY_801__PHY_RDLVL_DATA_SWIZZLE_3_SHIFT                8U
#define LPDDR4__DENALI_PHY_801__PHY_RDLVL_DATA_SWIZZLE_3_WIDTH               18U
#define LPDDR4__PHY_RDLVL_DATA_SWIZZLE_3__REG DENALI_PHY_801
#define LPDDR4__PHY_RDLVL_DATA_SWIZZLE_3__FLD LPDDR4__DENALI_PHY_801__PHY_RDLVL_DATA_SWIZZLE_3

#define LPDDR4__DENALI_PHY_802_READ_MASK                             0x00073FFFU
#define LPDDR4__DENALI_PHY_802_WRITE_MASK                            0x00073FFFU
#define LPDDR4__DENALI_PHY_802__PHY_WDQLVL_CLK_JITTER_TOLERANCE_3_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_802__PHY_WDQLVL_CLK_JITTER_TOLERANCE_3_SHIFT       0U
#define LPDDR4__DENALI_PHY_802__PHY_WDQLVL_CLK_JITTER_TOLERANCE_3_WIDTH       8U
#define LPDDR4__PHY_WDQLVL_CLK_JITTER_TOLERANCE_3__REG DENALI_PHY_802
#define LPDDR4__PHY_WDQLVL_CLK_JITTER_TOLERANCE_3__FLD LPDDR4__DENALI_PHY_802__PHY_WDQLVL_CLK_JITTER_TOLERANCE_3

#define LPDDR4__DENALI_PHY_802__PHY_WDQLVL_BURST_CNT_3_MASK          0x00003F00U
#define LPDDR4__DENALI_PHY_802__PHY_WDQLVL_BURST_CNT_3_SHIFT                  8U
#define LPDDR4__DENALI_PHY_802__PHY_WDQLVL_BURST_CNT_3_WIDTH                  6U
#define LPDDR4__PHY_WDQLVL_BURST_CNT_3__REG DENALI_PHY_802
#define LPDDR4__PHY_WDQLVL_BURST_CNT_3__FLD LPDDR4__DENALI_PHY_802__PHY_WDQLVL_BURST_CNT_3

#define LPDDR4__DENALI_PHY_802__PHY_WDQLVL_PATT_3_MASK               0x00070000U
#define LPDDR4__DENALI_PHY_802__PHY_WDQLVL_PATT_3_SHIFT                      16U
#define LPDDR4__DENALI_PHY_802__PHY_WDQLVL_PATT_3_WIDTH                       3U
#define LPDDR4__PHY_WDQLVL_PATT_3__REG DENALI_PHY_802
#define LPDDR4__PHY_WDQLVL_PATT_3__FLD LPDDR4__DENALI_PHY_802__PHY_WDQLVL_PATT_3

#define LPDDR4__DENALI_PHY_803_READ_MASK                             0x0F0F07FFU
#define LPDDR4__DENALI_PHY_803_WRITE_MASK                            0x0F0F07FFU
#define LPDDR4__DENALI_PHY_803__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_3_MASK 0x000007FFU
#define LPDDR4__DENALI_PHY_803__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_3_SHIFT   0U
#define LPDDR4__DENALI_PHY_803__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_3_WIDTH  11U
#define LPDDR4__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_3__REG DENALI_PHY_803
#define LPDDR4__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_3__FLD LPDDR4__DENALI_PHY_803__PHY_WDQLVL_DQDM_SLV_DLY_JUMP_OFFSET_3

#define LPDDR4__DENALI_PHY_803__PHY_WDQLVL_UPDT_WAIT_CNT_3_MASK      0x000F0000U
#define LPDDR4__DENALI_PHY_803__PHY_WDQLVL_UPDT_WAIT_CNT_3_SHIFT             16U
#define LPDDR4__DENALI_PHY_803__PHY_WDQLVL_UPDT_WAIT_CNT_3_WIDTH              4U
#define LPDDR4__PHY_WDQLVL_UPDT_WAIT_CNT_3__REG DENALI_PHY_803
#define LPDDR4__PHY_WDQLVL_UPDT_WAIT_CNT_3__FLD LPDDR4__DENALI_PHY_803__PHY_WDQLVL_UPDT_WAIT_CNT_3

#define LPDDR4__DENALI_PHY_803__PHY_WDQLVL_DQDM_OBS_SELECT_3_MASK    0x0F000000U
#define LPDDR4__DENALI_PHY_803__PHY_WDQLVL_DQDM_OBS_SELECT_3_SHIFT           24U
#define LPDDR4__DENALI_PHY_803__PHY_WDQLVL_DQDM_OBS_SELECT_3_WIDTH            4U
#define LPDDR4__PHY_WDQLVL_DQDM_OBS_SELECT_3__REG DENALI_PHY_803
#define LPDDR4__PHY_WDQLVL_DQDM_OBS_SELECT_3__FLD LPDDR4__DENALI_PHY_803__PHY_WDQLVL_DQDM_OBS_SELECT_3

#define LPDDR4__DENALI_PHY_804_READ_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PHY_804_WRITE_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_804__PHY_WDQLVL_PERIODIC_OBS_SELECT_3_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_804__PHY_WDQLVL_PERIODIC_OBS_SELECT_3_SHIFT        0U
#define LPDDR4__DENALI_PHY_804__PHY_WDQLVL_PERIODIC_OBS_SELECT_3_WIDTH        8U
#define LPDDR4__PHY_WDQLVL_PERIODIC_OBS_SELECT_3__REG DENALI_PHY_804
#define LPDDR4__PHY_WDQLVL_PERIODIC_OBS_SELECT_3__FLD LPDDR4__DENALI_PHY_804__PHY_WDQLVL_PERIODIC_OBS_SELECT_3

#define LPDDR4__DENALI_PHY_804__PHY_WDQLVL_DQ_SLV_DELTA_3_MASK       0x0000FF00U
#define LPDDR4__DENALI_PHY_804__PHY_WDQLVL_DQ_SLV_DELTA_3_SHIFT               8U
#define LPDDR4__DENALI_PHY_804__PHY_WDQLVL_DQ_SLV_DELTA_3_WIDTH               8U
#define LPDDR4__PHY_WDQLVL_DQ_SLV_DELTA_3__REG DENALI_PHY_804
#define LPDDR4__PHY_WDQLVL_DQ_SLV_DELTA_3__FLD LPDDR4__DENALI_PHY_804__PHY_WDQLVL_DQ_SLV_DELTA_3

#define LPDDR4__DENALI_PHY_804__PHY_WDQLVL_DM_DLY_STEP_3_MASK        0x000F0000U
#define LPDDR4__DENALI_PHY_804__PHY_WDQLVL_DM_DLY_STEP_3_SHIFT               16U
#define LPDDR4__DENALI_PHY_804__PHY_WDQLVL_DM_DLY_STEP_3_WIDTH                4U
#define LPDDR4__PHY_WDQLVL_DM_DLY_STEP_3__REG DENALI_PHY_804
#define LPDDR4__PHY_WDQLVL_DM_DLY_STEP_3__FLD LPDDR4__DENALI_PHY_804__PHY_WDQLVL_DM_DLY_STEP_3

#define LPDDR4__DENALI_PHY_804__SC_PHY_WDQLVL_CLR_PREV_RESULTS_3_MASK 0x01000000U
#define LPDDR4__DENALI_PHY_804__SC_PHY_WDQLVL_CLR_PREV_RESULTS_3_SHIFT       24U
#define LPDDR4__DENALI_PHY_804__SC_PHY_WDQLVL_CLR_PREV_RESULTS_3_WIDTH        1U
#define LPDDR4__DENALI_PHY_804__SC_PHY_WDQLVL_CLR_PREV_RESULTS_3_WOCLR        0U
#define LPDDR4__DENALI_PHY_804__SC_PHY_WDQLVL_CLR_PREV_RESULTS_3_WOSET        0U
#define LPDDR4__SC_PHY_WDQLVL_CLR_PREV_RESULTS_3__REG DENALI_PHY_804
#define LPDDR4__SC_PHY_WDQLVL_CLR_PREV_RESULTS_3__FLD LPDDR4__DENALI_PHY_804__SC_PHY_WDQLVL_CLR_PREV_RESULTS_3

#define LPDDR4__DENALI_PHY_805_READ_MASK                             0x000001FFU
#define LPDDR4__DENALI_PHY_805_WRITE_MASK                            0x000001FFU
#define LPDDR4__DENALI_PHY_805__PHY_WDQLVL_DATADM_MASK_3_MASK        0x000001FFU
#define LPDDR4__DENALI_PHY_805__PHY_WDQLVL_DATADM_MASK_3_SHIFT                0U
#define LPDDR4__DENALI_PHY_805__PHY_WDQLVL_DATADM_MASK_3_WIDTH                9U
#define LPDDR4__PHY_WDQLVL_DATADM_MASK_3__REG DENALI_PHY_805
#define LPDDR4__PHY_WDQLVL_DATADM_MASK_3__FLD LPDDR4__DENALI_PHY_805__PHY_WDQLVL_DATADM_MASK_3

#define LPDDR4__DENALI_PHY_806_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_806_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_806__PHY_USER_PATT0_3_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_806__PHY_USER_PATT0_3_SHIFT                        0U
#define LPDDR4__DENALI_PHY_806__PHY_USER_PATT0_3_WIDTH                       32U
#define LPDDR4__PHY_USER_PATT0_3__REG DENALI_PHY_806
#define LPDDR4__PHY_USER_PATT0_3__FLD LPDDR4__DENALI_PHY_806__PHY_USER_PATT0_3

#define LPDDR4__DENALI_PHY_807_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_807_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_807__PHY_USER_PATT1_3_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_807__PHY_USER_PATT1_3_SHIFT                        0U
#define LPDDR4__DENALI_PHY_807__PHY_USER_PATT1_3_WIDTH                       32U
#define LPDDR4__PHY_USER_PATT1_3__REG DENALI_PHY_807
#define LPDDR4__PHY_USER_PATT1_3__FLD LPDDR4__DENALI_PHY_807__PHY_USER_PATT1_3

#define LPDDR4__DENALI_PHY_808_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_808_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_808__PHY_USER_PATT2_3_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_808__PHY_USER_PATT2_3_SHIFT                        0U
#define LPDDR4__DENALI_PHY_808__PHY_USER_PATT2_3_WIDTH                       32U
#define LPDDR4__PHY_USER_PATT2_3__REG DENALI_PHY_808
#define LPDDR4__PHY_USER_PATT2_3__FLD LPDDR4__DENALI_PHY_808__PHY_USER_PATT2_3

#define LPDDR4__DENALI_PHY_809_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_809_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_809__PHY_USER_PATT3_3_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_809__PHY_USER_PATT3_3_SHIFT                        0U
#define LPDDR4__DENALI_PHY_809__PHY_USER_PATT3_3_WIDTH                       32U
#define LPDDR4__PHY_USER_PATT3_3__REG DENALI_PHY_809
#define LPDDR4__PHY_USER_PATT3_3__FLD LPDDR4__DENALI_PHY_809__PHY_USER_PATT3_3

#define LPDDR4__DENALI_PHY_810_READ_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PHY_810_WRITE_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_PHY_810__PHY_USER_PATT4_3_MASK                0x0000FFFFU
#define LPDDR4__DENALI_PHY_810__PHY_USER_PATT4_3_SHIFT                        0U
#define LPDDR4__DENALI_PHY_810__PHY_USER_PATT4_3_WIDTH                       16U
#define LPDDR4__PHY_USER_PATT4_3__REG DENALI_PHY_810
#define LPDDR4__PHY_USER_PATT4_3__FLD LPDDR4__DENALI_PHY_810__PHY_USER_PATT4_3

#define LPDDR4__DENALI_PHY_810__PHY_NTP_MULT_TRAIN_3_MASK            0x00010000U
#define LPDDR4__DENALI_PHY_810__PHY_NTP_MULT_TRAIN_3_SHIFT                   16U
#define LPDDR4__DENALI_PHY_810__PHY_NTP_MULT_TRAIN_3_WIDTH                    1U
#define LPDDR4__DENALI_PHY_810__PHY_NTP_MULT_TRAIN_3_WOCLR                    0U
#define LPDDR4__DENALI_PHY_810__PHY_NTP_MULT_TRAIN_3_WOSET                    0U
#define LPDDR4__PHY_NTP_MULT_TRAIN_3__REG DENALI_PHY_810
#define LPDDR4__PHY_NTP_MULT_TRAIN_3__FLD LPDDR4__DENALI_PHY_810__PHY_NTP_MULT_TRAIN_3

#define LPDDR4__DENALI_PHY_811_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_811_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_811__PHY_NTP_EARLY_THRESHOLD_3_MASK       0x000003FFU
#define LPDDR4__DENALI_PHY_811__PHY_NTP_EARLY_THRESHOLD_3_SHIFT               0U
#define LPDDR4__DENALI_PHY_811__PHY_NTP_EARLY_THRESHOLD_3_WIDTH              10U
#define LPDDR4__PHY_NTP_EARLY_THRESHOLD_3__REG DENALI_PHY_811
#define LPDDR4__PHY_NTP_EARLY_THRESHOLD_3__FLD LPDDR4__DENALI_PHY_811__PHY_NTP_EARLY_THRESHOLD_3

#define LPDDR4__DENALI_PHY_811__PHY_NTP_PERIOD_THRESHOLD_3_MASK      0x03FF0000U
#define LPDDR4__DENALI_PHY_811__PHY_NTP_PERIOD_THRESHOLD_3_SHIFT             16U
#define LPDDR4__DENALI_PHY_811__PHY_NTP_PERIOD_THRESHOLD_3_WIDTH             10U
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_3__REG DENALI_PHY_811
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_3__FLD LPDDR4__DENALI_PHY_811__PHY_NTP_PERIOD_THRESHOLD_3

#define LPDDR4__DENALI_PHY_812_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_812_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_812__PHY_NTP_PERIOD_THRESHOLD_MIN_3_MASK  0x000003FFU
#define LPDDR4__DENALI_PHY_812__PHY_NTP_PERIOD_THRESHOLD_MIN_3_SHIFT          0U
#define LPDDR4__DENALI_PHY_812__PHY_NTP_PERIOD_THRESHOLD_MIN_3_WIDTH         10U
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_MIN_3__REG DENALI_PHY_812
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_MIN_3__FLD LPDDR4__DENALI_PHY_812__PHY_NTP_PERIOD_THRESHOLD_MIN_3

#define LPDDR4__DENALI_PHY_812__PHY_NTP_PERIOD_THRESHOLD_MAX_3_MASK  0x03FF0000U
#define LPDDR4__DENALI_PHY_812__PHY_NTP_PERIOD_THRESHOLD_MAX_3_SHIFT         16U
#define LPDDR4__DENALI_PHY_812__PHY_NTP_PERIOD_THRESHOLD_MAX_3_WIDTH         10U
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_MAX_3__REG DENALI_PHY_812
#define LPDDR4__PHY_NTP_PERIOD_THRESHOLD_MAX_3__FLD LPDDR4__DENALI_PHY_812__PHY_NTP_PERIOD_THRESHOLD_MAX_3

#define LPDDR4__DENALI_PHY_813_READ_MASK                             0x00FF0001U
#define LPDDR4__DENALI_PHY_813_WRITE_MASK                            0x00FF0001U
#define LPDDR4__DENALI_PHY_813__PHY_CALVL_VREF_DRIVING_SLICE_3_MASK  0x00000001U
#define LPDDR4__DENALI_PHY_813__PHY_CALVL_VREF_DRIVING_SLICE_3_SHIFT          0U
#define LPDDR4__DENALI_PHY_813__PHY_CALVL_VREF_DRIVING_SLICE_3_WIDTH          1U
#define LPDDR4__DENALI_PHY_813__PHY_CALVL_VREF_DRIVING_SLICE_3_WOCLR          0U
#define LPDDR4__DENALI_PHY_813__PHY_CALVL_VREF_DRIVING_SLICE_3_WOSET          0U
#define LPDDR4__PHY_CALVL_VREF_DRIVING_SLICE_3__REG DENALI_PHY_813
#define LPDDR4__PHY_CALVL_VREF_DRIVING_SLICE_3__FLD LPDDR4__DENALI_PHY_813__PHY_CALVL_VREF_DRIVING_SLICE_3

#define LPDDR4__DENALI_PHY_813__SC_PHY_MANUAL_CLEAR_3_MASK           0x00003F00U
#define LPDDR4__DENALI_PHY_813__SC_PHY_MANUAL_CLEAR_3_SHIFT                   8U
#define LPDDR4__DENALI_PHY_813__SC_PHY_MANUAL_CLEAR_3_WIDTH                   6U
#define LPDDR4__SC_PHY_MANUAL_CLEAR_3__REG DENALI_PHY_813
#define LPDDR4__SC_PHY_MANUAL_CLEAR_3__FLD LPDDR4__DENALI_PHY_813__SC_PHY_MANUAL_CLEAR_3

#define LPDDR4__DENALI_PHY_813__PHY_FIFO_PTR_OBS_3_MASK              0x00FF0000U
#define LPDDR4__DENALI_PHY_813__PHY_FIFO_PTR_OBS_3_SHIFT                     16U
#define LPDDR4__DENALI_PHY_813__PHY_FIFO_PTR_OBS_3_WIDTH                      8U
#define LPDDR4__PHY_FIFO_PTR_OBS_3__REG DENALI_PHY_813
#define LPDDR4__PHY_FIFO_PTR_OBS_3__FLD LPDDR4__DENALI_PHY_813__PHY_FIFO_PTR_OBS_3

#define LPDDR4__DENALI_PHY_814_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_814_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_814__PHY_LPBK_RESULT_OBS_3_MASK           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_814__PHY_LPBK_RESULT_OBS_3_SHIFT                   0U
#define LPDDR4__DENALI_PHY_814__PHY_LPBK_RESULT_OBS_3_WIDTH                  32U
#define LPDDR4__PHY_LPBK_RESULT_OBS_3__REG DENALI_PHY_814
#define LPDDR4__PHY_LPBK_RESULT_OBS_3__FLD LPDDR4__DENALI_PHY_814__PHY_LPBK_RESULT_OBS_3

#define LPDDR4__DENALI_PHY_815_READ_MASK                             0x07FFFFFFU
#define LPDDR4__DENALI_PHY_815_WRITE_MASK                            0x07FFFFFFU
#define LPDDR4__DENALI_PHY_815__PHY_LPBK_ERROR_COUNT_OBS_3_MASK      0x0000FFFFU
#define LPDDR4__DENALI_PHY_815__PHY_LPBK_ERROR_COUNT_OBS_3_SHIFT              0U
#define LPDDR4__DENALI_PHY_815__PHY_LPBK_ERROR_COUNT_OBS_3_WIDTH             16U
#define LPDDR4__PHY_LPBK_ERROR_COUNT_OBS_3__REG DENALI_PHY_815
#define LPDDR4__PHY_LPBK_ERROR_COUNT_OBS_3__FLD LPDDR4__DENALI_PHY_815__PHY_LPBK_ERROR_COUNT_OBS_3

#define LPDDR4__DENALI_PHY_815__PHY_MASTER_DLY_LOCK_OBS_3_MASK       0x07FF0000U
#define LPDDR4__DENALI_PHY_815__PHY_MASTER_DLY_LOCK_OBS_3_SHIFT              16U
#define LPDDR4__DENALI_PHY_815__PHY_MASTER_DLY_LOCK_OBS_3_WIDTH              11U
#define LPDDR4__PHY_MASTER_DLY_LOCK_OBS_3__REG DENALI_PHY_815
#define LPDDR4__PHY_MASTER_DLY_LOCK_OBS_3__FLD LPDDR4__DENALI_PHY_815__PHY_MASTER_DLY_LOCK_OBS_3

#define LPDDR4__DENALI_PHY_816_READ_MASK                             0xFFFF7F7FU
#define LPDDR4__DENALI_PHY_816_WRITE_MASK                            0xFFFF7F7FU
#define LPDDR4__DENALI_PHY_816__PHY_RDDQ_SLV_DLY_ENC_OBS_3_MASK      0x0000007FU
#define LPDDR4__DENALI_PHY_816__PHY_RDDQ_SLV_DLY_ENC_OBS_3_SHIFT              0U
#define LPDDR4__DENALI_PHY_816__PHY_RDDQ_SLV_DLY_ENC_OBS_3_WIDTH              7U
#define LPDDR4__PHY_RDDQ_SLV_DLY_ENC_OBS_3__REG DENALI_PHY_816
#define LPDDR4__PHY_RDDQ_SLV_DLY_ENC_OBS_3__FLD LPDDR4__DENALI_PHY_816__PHY_RDDQ_SLV_DLY_ENC_OBS_3

#define LPDDR4__DENALI_PHY_816__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_3_MASK 0x00007F00U
#define LPDDR4__DENALI_PHY_816__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_3_SHIFT        8U
#define LPDDR4__DENALI_PHY_816__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_3_WIDTH        7U
#define LPDDR4__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_3__REG DENALI_PHY_816
#define LPDDR4__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_3__FLD LPDDR4__DENALI_PHY_816__PHY_RDDQS_BASE_SLV_DLY_ENC_OBS_3

#define LPDDR4__DENALI_PHY_816__PHY_MEAS_DLY_STEP_VALUE_3_MASK       0x00FF0000U
#define LPDDR4__DENALI_PHY_816__PHY_MEAS_DLY_STEP_VALUE_3_SHIFT              16U
#define LPDDR4__DENALI_PHY_816__PHY_MEAS_DLY_STEP_VALUE_3_WIDTH               8U
#define LPDDR4__PHY_MEAS_DLY_STEP_VALUE_3__REG DENALI_PHY_816
#define LPDDR4__PHY_MEAS_DLY_STEP_VALUE_3__FLD LPDDR4__DENALI_PHY_816__PHY_MEAS_DLY_STEP_VALUE_3

#define LPDDR4__DENALI_PHY_816__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_3_MASK 0xFF000000U
#define LPDDR4__DENALI_PHY_816__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_3_SHIFT 24U
#define LPDDR4__DENALI_PHY_816__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_3_WIDTH 8U
#define LPDDR4__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_3__REG DENALI_PHY_816
#define LPDDR4__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_3__FLD LPDDR4__DENALI_PHY_816__PHY_RDDQS_DQ_RISE_ADDER_SLV_DLY_ENC_OBS_3

#define LPDDR4__DENALI_PHY_817_READ_MASK                             0x7F07FFFFU
#define LPDDR4__DENALI_PHY_817_WRITE_MASK                            0x7F07FFFFU
#define LPDDR4__DENALI_PHY_817__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_3_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_817__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_3_SHIFT 0U
#define LPDDR4__DENALI_PHY_817__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_3_WIDTH 8U
#define LPDDR4__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_3__REG DENALI_PHY_817
#define LPDDR4__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_3__FLD LPDDR4__DENALI_PHY_817__PHY_RDDQS_DQ_FALL_ADDER_SLV_DLY_ENC_OBS_3

#define LPDDR4__DENALI_PHY_817__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_3_MASK 0x0007FF00U
#define LPDDR4__DENALI_PHY_817__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_3_SHIFT        8U
#define LPDDR4__DENALI_PHY_817__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_3_WIDTH       11U
#define LPDDR4__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_3__REG DENALI_PHY_817
#define LPDDR4__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_3__FLD LPDDR4__DENALI_PHY_817__PHY_RDDQS_GATE_SLV_DLY_ENC_OBS_3

#define LPDDR4__DENALI_PHY_817__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_3_MASK 0x7F000000U
#define LPDDR4__DENALI_PHY_817__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_3_SHIFT       24U
#define LPDDR4__DENALI_PHY_817__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_3_WIDTH        7U
#define LPDDR4__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_3__REG DENALI_PHY_817
#define LPDDR4__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_3__FLD LPDDR4__DENALI_PHY_817__PHY_WRDQS_BASE_SLV_DLY_ENC_OBS_3

#define LPDDR4__DENALI_PHY_818_READ_MASK                             0x0007FFFFU
#define LPDDR4__DENALI_PHY_818_WRITE_MASK                            0x0007FFFFU
#define LPDDR4__DENALI_PHY_818__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_3_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_818__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_3_SHIFT         0U
#define LPDDR4__DENALI_PHY_818__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_3_WIDTH         8U
#define LPDDR4__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_3__REG DENALI_PHY_818
#define LPDDR4__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_3__FLD LPDDR4__DENALI_PHY_818__PHY_WRDQ_BASE_SLV_DLY_ENC_OBS_3

#define LPDDR4__DENALI_PHY_818__PHY_WR_ADDER_SLV_DLY_ENC_OBS_3_MASK  0x0000FF00U
#define LPDDR4__DENALI_PHY_818__PHY_WR_ADDER_SLV_DLY_ENC_OBS_3_SHIFT          8U
#define LPDDR4__DENALI_PHY_818__PHY_WR_ADDER_SLV_DLY_ENC_OBS_3_WIDTH          8U
#define LPDDR4__PHY_WR_ADDER_SLV_DLY_ENC_OBS_3__REG DENALI_PHY_818
#define LPDDR4__PHY_WR_ADDER_SLV_DLY_ENC_OBS_3__FLD LPDDR4__DENALI_PHY_818__PHY_WR_ADDER_SLV_DLY_ENC_OBS_3

#define LPDDR4__DENALI_PHY_818__PHY_WR_SHIFT_OBS_3_MASK              0x00070000U
#define LPDDR4__DENALI_PHY_818__PHY_WR_SHIFT_OBS_3_SHIFT                     16U
#define LPDDR4__DENALI_PHY_818__PHY_WR_SHIFT_OBS_3_WIDTH                      3U
#define LPDDR4__PHY_WR_SHIFT_OBS_3__REG DENALI_PHY_818
#define LPDDR4__PHY_WR_SHIFT_OBS_3__FLD LPDDR4__DENALI_PHY_818__PHY_WR_SHIFT_OBS_3

#define LPDDR4__DENALI_PHY_819_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_819_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_819__PHY_WRLVL_HARD0_DELAY_OBS_3_MASK     0x000003FFU
#define LPDDR4__DENALI_PHY_819__PHY_WRLVL_HARD0_DELAY_OBS_3_SHIFT             0U
#define LPDDR4__DENALI_PHY_819__PHY_WRLVL_HARD0_DELAY_OBS_3_WIDTH            10U
#define LPDDR4__PHY_WRLVL_HARD0_DELAY_OBS_3__REG DENALI_PHY_819
#define LPDDR4__PHY_WRLVL_HARD0_DELAY_OBS_3__FLD LPDDR4__DENALI_PHY_819__PHY_WRLVL_HARD0_DELAY_OBS_3

#define LPDDR4__DENALI_PHY_819__PHY_WRLVL_HARD1_DELAY_OBS_3_MASK     0x03FF0000U
#define LPDDR4__DENALI_PHY_819__PHY_WRLVL_HARD1_DELAY_OBS_3_SHIFT            16U
#define LPDDR4__DENALI_PHY_819__PHY_WRLVL_HARD1_DELAY_OBS_3_WIDTH            10U
#define LPDDR4__PHY_WRLVL_HARD1_DELAY_OBS_3__REG DENALI_PHY_819
#define LPDDR4__PHY_WRLVL_HARD1_DELAY_OBS_3__FLD LPDDR4__DENALI_PHY_819__PHY_WRLVL_HARD1_DELAY_OBS_3

#define LPDDR4__DENALI_PHY_820_READ_MASK                             0x001FFFFFU
#define LPDDR4__DENALI_PHY_820_WRITE_MASK                            0x001FFFFFU
#define LPDDR4__DENALI_PHY_820__PHY_WRLVL_STATUS_OBS_3_MASK          0x001FFFFFU
#define LPDDR4__DENALI_PHY_820__PHY_WRLVL_STATUS_OBS_3_SHIFT                  0U
#define LPDDR4__DENALI_PHY_820__PHY_WRLVL_STATUS_OBS_3_WIDTH                 21U
#define LPDDR4__PHY_WRLVL_STATUS_OBS_3__REG DENALI_PHY_820
#define LPDDR4__PHY_WRLVL_STATUS_OBS_3__FLD LPDDR4__DENALI_PHY_820__PHY_WRLVL_STATUS_OBS_3

#define LPDDR4__DENALI_PHY_821_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_821_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_821__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_3_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_821__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_3_SHIFT        0U
#define LPDDR4__DENALI_PHY_821__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_3_WIDTH       10U
#define LPDDR4__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_3__REG DENALI_PHY_821
#define LPDDR4__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_3__FLD LPDDR4__DENALI_PHY_821__PHY_GATE_SMPL1_SLV_DLY_ENC_OBS_3

#define LPDDR4__DENALI_PHY_821__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_3_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_821__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_3_SHIFT       16U
#define LPDDR4__DENALI_PHY_821__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_3_WIDTH       10U
#define LPDDR4__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_3__REG DENALI_PHY_821
#define LPDDR4__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_3__FLD LPDDR4__DENALI_PHY_821__PHY_GATE_SMPL2_SLV_DLY_ENC_OBS_3

#define LPDDR4__DENALI_PHY_822_READ_MASK                             0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_822_WRITE_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_822__PHY_WRLVL_ERROR_OBS_3_MASK           0x0000FFFFU
#define LPDDR4__DENALI_PHY_822__PHY_WRLVL_ERROR_OBS_3_SHIFT                   0U
#define LPDDR4__DENALI_PHY_822__PHY_WRLVL_ERROR_OBS_3_WIDTH                  16U
#define LPDDR4__PHY_WRLVL_ERROR_OBS_3__REG DENALI_PHY_822
#define LPDDR4__PHY_WRLVL_ERROR_OBS_3__FLD LPDDR4__DENALI_PHY_822__PHY_WRLVL_ERROR_OBS_3

#define LPDDR4__DENALI_PHY_822__PHY_GTLVL_HARD0_DELAY_OBS_3_MASK     0x3FFF0000U
#define LPDDR4__DENALI_PHY_822__PHY_GTLVL_HARD0_DELAY_OBS_3_SHIFT            16U
#define LPDDR4__DENALI_PHY_822__PHY_GTLVL_HARD0_DELAY_OBS_3_WIDTH            14U
#define LPDDR4__PHY_GTLVL_HARD0_DELAY_OBS_3__REG DENALI_PHY_822
#define LPDDR4__PHY_GTLVL_HARD0_DELAY_OBS_3__FLD LPDDR4__DENALI_PHY_822__PHY_GTLVL_HARD0_DELAY_OBS_3

#define LPDDR4__DENALI_PHY_823_READ_MASK                             0x00003FFFU
#define LPDDR4__DENALI_PHY_823_WRITE_MASK                            0x00003FFFU
#define LPDDR4__DENALI_PHY_823__PHY_GTLVL_HARD1_DELAY_OBS_3_MASK     0x00003FFFU
#define LPDDR4__DENALI_PHY_823__PHY_GTLVL_HARD1_DELAY_OBS_3_SHIFT             0U
#define LPDDR4__DENALI_PHY_823__PHY_GTLVL_HARD1_DELAY_OBS_3_WIDTH            14U
#define LPDDR4__PHY_GTLVL_HARD1_DELAY_OBS_3__REG DENALI_PHY_823
#define LPDDR4__PHY_GTLVL_HARD1_DELAY_OBS_3__FLD LPDDR4__DENALI_PHY_823__PHY_GTLVL_HARD1_DELAY_OBS_3

#define LPDDR4__DENALI_PHY_824_READ_MASK                             0x0003FFFFU
#define LPDDR4__DENALI_PHY_824_WRITE_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_824__PHY_GTLVL_STATUS_OBS_3_MASK          0x0003FFFFU
#define LPDDR4__DENALI_PHY_824__PHY_GTLVL_STATUS_OBS_3_SHIFT                  0U
#define LPDDR4__DENALI_PHY_824__PHY_GTLVL_STATUS_OBS_3_WIDTH                 18U
#define LPDDR4__PHY_GTLVL_STATUS_OBS_3__REG DENALI_PHY_824
#define LPDDR4__PHY_GTLVL_STATUS_OBS_3__FLD LPDDR4__DENALI_PHY_824__PHY_GTLVL_STATUS_OBS_3

#define LPDDR4__DENALI_PHY_825_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_825_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_825__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_3_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_825__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_3_SHIFT         0U
#define LPDDR4__DENALI_PHY_825__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_3_WIDTH        10U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_3__REG DENALI_PHY_825
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_3__FLD LPDDR4__DENALI_PHY_825__PHY_RDLVL_RDDQS_DQ_LE_DLY_OBS_3

#define LPDDR4__DENALI_PHY_825__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_3_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_825__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_3_SHIFT        16U
#define LPDDR4__DENALI_PHY_825__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_3_WIDTH        10U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_3__REG DENALI_PHY_825
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_3__FLD LPDDR4__DENALI_PHY_825__PHY_RDLVL_RDDQS_DQ_TE_DLY_OBS_3

#define LPDDR4__DENALI_PHY_826_READ_MASK                             0x00000003U
#define LPDDR4__DENALI_PHY_826_WRITE_MASK                            0x00000003U
#define LPDDR4__DENALI_PHY_826__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_3_MASK 0x00000003U
#define LPDDR4__DENALI_PHY_826__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_3_SHIFT    0U
#define LPDDR4__DENALI_PHY_826__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_3_WIDTH    2U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_3__REG DENALI_PHY_826
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_3__FLD LPDDR4__DENALI_PHY_826__PHY_RDLVL_RDDQS_DQ_NUM_WINDOWS_OBS_3

#define LPDDR4__DENALI_PHY_827_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_827_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_827__PHY_RDLVL_STATUS_OBS_3_MASK          0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_827__PHY_RDLVL_STATUS_OBS_3_SHIFT                  0U
#define LPDDR4__DENALI_PHY_827__PHY_RDLVL_STATUS_OBS_3_WIDTH                 32U
#define LPDDR4__PHY_RDLVL_STATUS_OBS_3__REG DENALI_PHY_827
#define LPDDR4__PHY_RDLVL_STATUS_OBS_3__FLD LPDDR4__DENALI_PHY_827__PHY_RDLVL_STATUS_OBS_3

#define LPDDR4__DENALI_PHY_828_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_828_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_828__PHY_WDQLVL_DQDM_LE_DLY_OBS_3_MASK    0x000007FFU
#define LPDDR4__DENALI_PHY_828__PHY_WDQLVL_DQDM_LE_DLY_OBS_3_SHIFT            0U
#define LPDDR4__DENALI_PHY_828__PHY_WDQLVL_DQDM_LE_DLY_OBS_3_WIDTH           11U
#define LPDDR4__PHY_WDQLVL_DQDM_LE_DLY_OBS_3__REG DENALI_PHY_828
#define LPDDR4__PHY_WDQLVL_DQDM_LE_DLY_OBS_3__FLD LPDDR4__DENALI_PHY_828__PHY_WDQLVL_DQDM_LE_DLY_OBS_3

#define LPDDR4__DENALI_PHY_828__PHY_WDQLVL_DQDM_TE_DLY_OBS_3_MASK    0x07FF0000U
#define LPDDR4__DENALI_PHY_828__PHY_WDQLVL_DQDM_TE_DLY_OBS_3_SHIFT           16U
#define LPDDR4__DENALI_PHY_828__PHY_WDQLVL_DQDM_TE_DLY_OBS_3_WIDTH           11U
#define LPDDR4__PHY_WDQLVL_DQDM_TE_DLY_OBS_3__REG DENALI_PHY_828
#define LPDDR4__PHY_WDQLVL_DQDM_TE_DLY_OBS_3__FLD LPDDR4__DENALI_PHY_828__PHY_WDQLVL_DQDM_TE_DLY_OBS_3

#define LPDDR4__DENALI_PHY_829_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_829_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_829__PHY_WDQLVL_STATUS_OBS_3_MASK         0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_829__PHY_WDQLVL_STATUS_OBS_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_829__PHY_WDQLVL_STATUS_OBS_3_WIDTH                32U
#define LPDDR4__PHY_WDQLVL_STATUS_OBS_3__REG DENALI_PHY_829
#define LPDDR4__PHY_WDQLVL_STATUS_OBS_3__FLD LPDDR4__DENALI_PHY_829__PHY_WDQLVL_STATUS_OBS_3

#define LPDDR4__DENALI_PHY_830_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_830_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_830__PHY_WDQLVL_PERIODIC_OBS_3_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_830__PHY_WDQLVL_PERIODIC_OBS_3_SHIFT               0U
#define LPDDR4__DENALI_PHY_830__PHY_WDQLVL_PERIODIC_OBS_3_WIDTH              32U
#define LPDDR4__PHY_WDQLVL_PERIODIC_OBS_3__REG DENALI_PHY_830
#define LPDDR4__PHY_WDQLVL_PERIODIC_OBS_3__FLD LPDDR4__DENALI_PHY_830__PHY_WDQLVL_PERIODIC_OBS_3

#define LPDDR4__DENALI_PHY_831_READ_MASK                             0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_831_WRITE_MASK                            0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_831__PHY_DDL_MODE_3_MASK                  0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_831__PHY_DDL_MODE_3_SHIFT                          0U
#define LPDDR4__DENALI_PHY_831__PHY_DDL_MODE_3_WIDTH                         31U
#define LPDDR4__PHY_DDL_MODE_3__REG DENALI_PHY_831
#define LPDDR4__PHY_DDL_MODE_3__FLD LPDDR4__DENALI_PHY_831__PHY_DDL_MODE_3

#define LPDDR4__DENALI_PHY_832_READ_MASK                             0x0000003FU
#define LPDDR4__DENALI_PHY_832_WRITE_MASK                            0x0000003FU
#define LPDDR4__DENALI_PHY_832__PHY_DDL_MASK_3_MASK                  0x0000003FU
#define LPDDR4__DENALI_PHY_832__PHY_DDL_MASK_3_SHIFT                          0U
#define LPDDR4__DENALI_PHY_832__PHY_DDL_MASK_3_WIDTH                          6U
#define LPDDR4__PHY_DDL_MASK_3__REG DENALI_PHY_832
#define LPDDR4__PHY_DDL_MASK_3__FLD LPDDR4__DENALI_PHY_832__PHY_DDL_MASK_3

#define LPDDR4__DENALI_PHY_833_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_833_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_833__PHY_DDL_TEST_OBS_3_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_833__PHY_DDL_TEST_OBS_3_SHIFT                      0U
#define LPDDR4__DENALI_PHY_833__PHY_DDL_TEST_OBS_3_WIDTH                     32U
#define LPDDR4__PHY_DDL_TEST_OBS_3__REG DENALI_PHY_833
#define LPDDR4__PHY_DDL_TEST_OBS_3__FLD LPDDR4__DENALI_PHY_833__PHY_DDL_TEST_OBS_3

#define LPDDR4__DENALI_PHY_834_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_834_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_834__PHY_DDL_TEST_MSTR_DLY_OBS_3_MASK     0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_834__PHY_DDL_TEST_MSTR_DLY_OBS_3_SHIFT             0U
#define LPDDR4__DENALI_PHY_834__PHY_DDL_TEST_MSTR_DLY_OBS_3_WIDTH            32U
#define LPDDR4__PHY_DDL_TEST_MSTR_DLY_OBS_3__REG DENALI_PHY_834
#define LPDDR4__PHY_DDL_TEST_MSTR_DLY_OBS_3__FLD LPDDR4__DENALI_PHY_834__PHY_DDL_TEST_MSTR_DLY_OBS_3

#define LPDDR4__DENALI_PHY_835_READ_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_835_WRITE_MASK                            0x01FF01FFU
#define LPDDR4__DENALI_PHY_835__PHY_DDL_TRACK_UPD_THRESHOLD_3_MASK   0x000000FFU
#define LPDDR4__DENALI_PHY_835__PHY_DDL_TRACK_UPD_THRESHOLD_3_SHIFT           0U
#define LPDDR4__DENALI_PHY_835__PHY_DDL_TRACK_UPD_THRESHOLD_3_WIDTH           8U
#define LPDDR4__PHY_DDL_TRACK_UPD_THRESHOLD_3__REG DENALI_PHY_835
#define LPDDR4__PHY_DDL_TRACK_UPD_THRESHOLD_3__FLD LPDDR4__DENALI_PHY_835__PHY_DDL_TRACK_UPD_THRESHOLD_3

#define LPDDR4__DENALI_PHY_835__PHY_LP4_WDQS_OE_EXTEND_3_MASK        0x00000100U
#define LPDDR4__DENALI_PHY_835__PHY_LP4_WDQS_OE_EXTEND_3_SHIFT                8U
#define LPDDR4__DENALI_PHY_835__PHY_LP4_WDQS_OE_EXTEND_3_WIDTH                1U
#define LPDDR4__DENALI_PHY_835__PHY_LP4_WDQS_OE_EXTEND_3_WOCLR                0U
#define LPDDR4__DENALI_PHY_835__PHY_LP4_WDQS_OE_EXTEND_3_WOSET                0U
#define LPDDR4__PHY_LP4_WDQS_OE_EXTEND_3__REG DENALI_PHY_835
#define LPDDR4__PHY_LP4_WDQS_OE_EXTEND_3__FLD LPDDR4__DENALI_PHY_835__PHY_LP4_WDQS_OE_EXTEND_3

#define LPDDR4__DENALI_PHY_835__PHY_RX_CAL_DQ0_3_MASK                0x01FF0000U
#define LPDDR4__DENALI_PHY_835__PHY_RX_CAL_DQ0_3_SHIFT                       16U
#define LPDDR4__DENALI_PHY_835__PHY_RX_CAL_DQ0_3_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ0_3__REG DENALI_PHY_835
#define LPDDR4__PHY_RX_CAL_DQ0_3__FLD LPDDR4__DENALI_PHY_835__PHY_RX_CAL_DQ0_3

#define LPDDR4__DENALI_PHY_836_READ_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_836_WRITE_MASK                            0x01FF01FFU
#define LPDDR4__DENALI_PHY_836__PHY_RX_CAL_DQ1_3_MASK                0x000001FFU
#define LPDDR4__DENALI_PHY_836__PHY_RX_CAL_DQ1_3_SHIFT                        0U
#define LPDDR4__DENALI_PHY_836__PHY_RX_CAL_DQ1_3_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ1_3__REG DENALI_PHY_836
#define LPDDR4__PHY_RX_CAL_DQ1_3__FLD LPDDR4__DENALI_PHY_836__PHY_RX_CAL_DQ1_3

#define LPDDR4__DENALI_PHY_836__PHY_RX_CAL_DQ2_3_MASK                0x01FF0000U
#define LPDDR4__DENALI_PHY_836__PHY_RX_CAL_DQ2_3_SHIFT                       16U
#define LPDDR4__DENALI_PHY_836__PHY_RX_CAL_DQ2_3_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ2_3__REG DENALI_PHY_836
#define LPDDR4__PHY_RX_CAL_DQ2_3__FLD LPDDR4__DENALI_PHY_836__PHY_RX_CAL_DQ2_3

#define LPDDR4__DENALI_PHY_837_READ_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_837_WRITE_MASK                            0x01FF01FFU
#define LPDDR4__DENALI_PHY_837__PHY_RX_CAL_DQ3_3_MASK                0x000001FFU
#define LPDDR4__DENALI_PHY_837__PHY_RX_CAL_DQ3_3_SHIFT                        0U
#define LPDDR4__DENALI_PHY_837__PHY_RX_CAL_DQ3_3_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ3_3__REG DENALI_PHY_837
#define LPDDR4__PHY_RX_CAL_DQ3_3__FLD LPDDR4__DENALI_PHY_837__PHY_RX_CAL_DQ3_3

#define LPDDR4__DENALI_PHY_837__PHY_RX_CAL_DQ4_3_MASK                0x01FF0000U
#define LPDDR4__DENALI_PHY_837__PHY_RX_CAL_DQ4_3_SHIFT                       16U
#define LPDDR4__DENALI_PHY_837__PHY_RX_CAL_DQ4_3_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ4_3__REG DENALI_PHY_837
#define LPDDR4__PHY_RX_CAL_DQ4_3__FLD LPDDR4__DENALI_PHY_837__PHY_RX_CAL_DQ4_3

#define LPDDR4__DENALI_PHY_838_READ_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_838_WRITE_MASK                            0x01FF01FFU
#define LPDDR4__DENALI_PHY_838__PHY_RX_CAL_DQ5_3_MASK                0x000001FFU
#define LPDDR4__DENALI_PHY_838__PHY_RX_CAL_DQ5_3_SHIFT                        0U
#define LPDDR4__DENALI_PHY_838__PHY_RX_CAL_DQ5_3_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ5_3__REG DENALI_PHY_838
#define LPDDR4__PHY_RX_CAL_DQ5_3__FLD LPDDR4__DENALI_PHY_838__PHY_RX_CAL_DQ5_3

#define LPDDR4__DENALI_PHY_838__PHY_RX_CAL_DQ6_3_MASK                0x01FF0000U
#define LPDDR4__DENALI_PHY_838__PHY_RX_CAL_DQ6_3_SHIFT                       16U
#define LPDDR4__DENALI_PHY_838__PHY_RX_CAL_DQ6_3_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ6_3__REG DENALI_PHY_838
#define LPDDR4__PHY_RX_CAL_DQ6_3__FLD LPDDR4__DENALI_PHY_838__PHY_RX_CAL_DQ6_3

#define LPDDR4__DENALI_PHY_839_READ_MASK                             0x000001FFU
#define LPDDR4__DENALI_PHY_839_WRITE_MASK                            0x000001FFU
#define LPDDR4__DENALI_PHY_839__PHY_RX_CAL_DQ7_3_MASK                0x000001FFU
#define LPDDR4__DENALI_PHY_839__PHY_RX_CAL_DQ7_3_SHIFT                        0U
#define LPDDR4__DENALI_PHY_839__PHY_RX_CAL_DQ7_3_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQ7_3__REG DENALI_PHY_839
#define LPDDR4__PHY_RX_CAL_DQ7_3__FLD LPDDR4__DENALI_PHY_839__PHY_RX_CAL_DQ7_3

#define LPDDR4__DENALI_PHY_840_READ_MASK                             0x0003FFFFU
#define LPDDR4__DENALI_PHY_840_WRITE_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_840__PHY_RX_CAL_DM_3_MASK                 0x0003FFFFU
#define LPDDR4__DENALI_PHY_840__PHY_RX_CAL_DM_3_SHIFT                         0U
#define LPDDR4__DENALI_PHY_840__PHY_RX_CAL_DM_3_WIDTH                        18U
#define LPDDR4__PHY_RX_CAL_DM_3__REG DENALI_PHY_840
#define LPDDR4__PHY_RX_CAL_DM_3__FLD LPDDR4__DENALI_PHY_840__PHY_RX_CAL_DM_3

#define LPDDR4__DENALI_PHY_841_READ_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PHY_841_WRITE_MASK                            0x01FF01FFU
#define LPDDR4__DENALI_PHY_841__PHY_RX_CAL_DQS_3_MASK                0x000001FFU
#define LPDDR4__DENALI_PHY_841__PHY_RX_CAL_DQS_3_SHIFT                        0U
#define LPDDR4__DENALI_PHY_841__PHY_RX_CAL_DQS_3_WIDTH                        9U
#define LPDDR4__PHY_RX_CAL_DQS_3__REG DENALI_PHY_841
#define LPDDR4__PHY_RX_CAL_DQS_3__FLD LPDDR4__DENALI_PHY_841__PHY_RX_CAL_DQS_3

#define LPDDR4__DENALI_PHY_841__PHY_RX_CAL_FDBK_3_MASK               0x01FF0000U
#define LPDDR4__DENALI_PHY_841__PHY_RX_CAL_FDBK_3_SHIFT                      16U
#define LPDDR4__DENALI_PHY_841__PHY_RX_CAL_FDBK_3_WIDTH                       9U
#define LPDDR4__PHY_RX_CAL_FDBK_3__REG DENALI_PHY_841
#define LPDDR4__PHY_RX_CAL_FDBK_3__FLD LPDDR4__DENALI_PHY_841__PHY_RX_CAL_FDBK_3

#define LPDDR4__DENALI_PHY_842_READ_MASK                             0xFF1F07FFU
#define LPDDR4__DENALI_PHY_842_WRITE_MASK                            0xFF1F07FFU
#define LPDDR4__DENALI_PHY_842__PHY_PAD_RX_BIAS_EN_3_MASK            0x000007FFU
#define LPDDR4__DENALI_PHY_842__PHY_PAD_RX_BIAS_EN_3_SHIFT                    0U
#define LPDDR4__DENALI_PHY_842__PHY_PAD_RX_BIAS_EN_3_WIDTH                   11U
#define LPDDR4__PHY_PAD_RX_BIAS_EN_3__REG DENALI_PHY_842
#define LPDDR4__PHY_PAD_RX_BIAS_EN_3__FLD LPDDR4__DENALI_PHY_842__PHY_PAD_RX_BIAS_EN_3

#define LPDDR4__DENALI_PHY_842__PHY_STATIC_TOG_DISABLE_3_MASK        0x001F0000U
#define LPDDR4__DENALI_PHY_842__PHY_STATIC_TOG_DISABLE_3_SHIFT               16U
#define LPDDR4__DENALI_PHY_842__PHY_STATIC_TOG_DISABLE_3_WIDTH                5U
#define LPDDR4__PHY_STATIC_TOG_DISABLE_3__REG DENALI_PHY_842
#define LPDDR4__PHY_STATIC_TOG_DISABLE_3__FLD LPDDR4__DENALI_PHY_842__PHY_STATIC_TOG_DISABLE_3

#define LPDDR4__DENALI_PHY_842__PHY_DATA_DC_CAL_SAMPLE_WAIT_3_MASK   0xFF000000U
#define LPDDR4__DENALI_PHY_842__PHY_DATA_DC_CAL_SAMPLE_WAIT_3_SHIFT          24U
#define LPDDR4__DENALI_PHY_842__PHY_DATA_DC_CAL_SAMPLE_WAIT_3_WIDTH           8U
#define LPDDR4__PHY_DATA_DC_CAL_SAMPLE_WAIT_3__REG DENALI_PHY_842
#define LPDDR4__PHY_DATA_DC_CAL_SAMPLE_WAIT_3__FLD LPDDR4__DENALI_PHY_842__PHY_DATA_DC_CAL_SAMPLE_WAIT_3

#define LPDDR4__DENALI_PHY_843_READ_MASK                             0xFF3F03FFU
#define LPDDR4__DENALI_PHY_843_WRITE_MASK                            0xFF3F03FFU
#define LPDDR4__DENALI_PHY_843__PHY_DATA_DC_CAL_TIMEOUT_3_MASK       0x000000FFU
#define LPDDR4__DENALI_PHY_843__PHY_DATA_DC_CAL_TIMEOUT_3_SHIFT               0U
#define LPDDR4__DENALI_PHY_843__PHY_DATA_DC_CAL_TIMEOUT_3_WIDTH               8U
#define LPDDR4__PHY_DATA_DC_CAL_TIMEOUT_3__REG DENALI_PHY_843
#define LPDDR4__PHY_DATA_DC_CAL_TIMEOUT_3__FLD LPDDR4__DENALI_PHY_843__PHY_DATA_DC_CAL_TIMEOUT_3

#define LPDDR4__DENALI_PHY_843__PHY_DATA_DC_WEIGHT_3_MASK            0x00000300U
#define LPDDR4__DENALI_PHY_843__PHY_DATA_DC_WEIGHT_3_SHIFT                    8U
#define LPDDR4__DENALI_PHY_843__PHY_DATA_DC_WEIGHT_3_WIDTH                    2U
#define LPDDR4__PHY_DATA_DC_WEIGHT_3__REG DENALI_PHY_843
#define LPDDR4__PHY_DATA_DC_WEIGHT_3__FLD LPDDR4__DENALI_PHY_843__PHY_DATA_DC_WEIGHT_3

#define LPDDR4__DENALI_PHY_843__PHY_DATA_DC_ADJUST_START_3_MASK      0x003F0000U
#define LPDDR4__DENALI_PHY_843__PHY_DATA_DC_ADJUST_START_3_SHIFT             16U
#define LPDDR4__DENALI_PHY_843__PHY_DATA_DC_ADJUST_START_3_WIDTH              6U
#define LPDDR4__PHY_DATA_DC_ADJUST_START_3__REG DENALI_PHY_843
#define LPDDR4__PHY_DATA_DC_ADJUST_START_3__FLD LPDDR4__DENALI_PHY_843__PHY_DATA_DC_ADJUST_START_3

#define LPDDR4__DENALI_PHY_843__PHY_DATA_DC_ADJUST_SAMPLE_CNT_3_MASK 0xFF000000U
#define LPDDR4__DENALI_PHY_843__PHY_DATA_DC_ADJUST_SAMPLE_CNT_3_SHIFT        24U
#define LPDDR4__DENALI_PHY_843__PHY_DATA_DC_ADJUST_SAMPLE_CNT_3_WIDTH         8U
#define LPDDR4__PHY_DATA_DC_ADJUST_SAMPLE_CNT_3__REG DENALI_PHY_843
#define LPDDR4__PHY_DATA_DC_ADJUST_SAMPLE_CNT_3__FLD LPDDR4__DENALI_PHY_843__PHY_DATA_DC_ADJUST_SAMPLE_CNT_3

#define LPDDR4__DENALI_PHY_844_READ_MASK                             0x010101FFU
#define LPDDR4__DENALI_PHY_844_WRITE_MASK                            0x010101FFU
#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_ADJUST_THRSHLD_3_MASK    0x000000FFU
#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_ADJUST_THRSHLD_3_SHIFT            0U
#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_ADJUST_THRSHLD_3_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_ADJUST_THRSHLD_3__REG DENALI_PHY_844
#define LPDDR4__PHY_DATA_DC_ADJUST_THRSHLD_3__FLD LPDDR4__DENALI_PHY_844__PHY_DATA_DC_ADJUST_THRSHLD_3

#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_ADJUST_DIRECT_3_MASK     0x00000100U
#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_ADJUST_DIRECT_3_SHIFT             8U
#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_ADJUST_DIRECT_3_WIDTH             1U
#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_ADJUST_DIRECT_3_WOCLR             0U
#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_ADJUST_DIRECT_3_WOSET             0U
#define LPDDR4__PHY_DATA_DC_ADJUST_DIRECT_3__REG DENALI_PHY_844
#define LPDDR4__PHY_DATA_DC_ADJUST_DIRECT_3__FLD LPDDR4__DENALI_PHY_844__PHY_DATA_DC_ADJUST_DIRECT_3

#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_CAL_POLARITY_3_MASK      0x00010000U
#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_CAL_POLARITY_3_SHIFT             16U
#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_CAL_POLARITY_3_WIDTH              1U
#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_CAL_POLARITY_3_WOCLR              0U
#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_CAL_POLARITY_3_WOSET              0U
#define LPDDR4__PHY_DATA_DC_CAL_POLARITY_3__REG DENALI_PHY_844
#define LPDDR4__PHY_DATA_DC_CAL_POLARITY_3__FLD LPDDR4__DENALI_PHY_844__PHY_DATA_DC_CAL_POLARITY_3

#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_CAL_START_3_MASK         0x01000000U
#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_CAL_START_3_SHIFT                24U
#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_CAL_START_3_WIDTH                 1U
#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_CAL_START_3_WOCLR                 0U
#define LPDDR4__DENALI_PHY_844__PHY_DATA_DC_CAL_START_3_WOSET                 0U
#define LPDDR4__PHY_DATA_DC_CAL_START_3__REG DENALI_PHY_844
#define LPDDR4__PHY_DATA_DC_CAL_START_3__FLD LPDDR4__DENALI_PHY_844__PHY_DATA_DC_CAL_START_3

#define LPDDR4__DENALI_PHY_845_READ_MASK                             0x01010703U
#define LPDDR4__DENALI_PHY_845_WRITE_MASK                            0x01010703U
#define LPDDR4__DENALI_PHY_845__PHY_DATA_DC_SW_RANK_3_MASK           0x00000003U
#define LPDDR4__DENALI_PHY_845__PHY_DATA_DC_SW_RANK_3_SHIFT                   0U
#define LPDDR4__DENALI_PHY_845__PHY_DATA_DC_SW_RANK_3_WIDTH                   2U
#define LPDDR4__PHY_DATA_DC_SW_RANK_3__REG DENALI_PHY_845
#define LPDDR4__PHY_DATA_DC_SW_RANK_3__FLD LPDDR4__DENALI_PHY_845__PHY_DATA_DC_SW_RANK_3

#define LPDDR4__DENALI_PHY_845__PHY_FDBK_PWR_CTRL_3_MASK             0x00000700U
#define LPDDR4__DENALI_PHY_845__PHY_FDBK_PWR_CTRL_3_SHIFT                     8U
#define LPDDR4__DENALI_PHY_845__PHY_FDBK_PWR_CTRL_3_WIDTH                     3U
#define LPDDR4__PHY_FDBK_PWR_CTRL_3__REG DENALI_PHY_845
#define LPDDR4__PHY_FDBK_PWR_CTRL_3__FLD LPDDR4__DENALI_PHY_845__PHY_FDBK_PWR_CTRL_3

#define LPDDR4__DENALI_PHY_845__PHY_SLV_DLY_CTRL_GATE_DISABLE_3_MASK 0x00010000U
#define LPDDR4__DENALI_PHY_845__PHY_SLV_DLY_CTRL_GATE_DISABLE_3_SHIFT        16U
#define LPDDR4__DENALI_PHY_845__PHY_SLV_DLY_CTRL_GATE_DISABLE_3_WIDTH         1U
#define LPDDR4__DENALI_PHY_845__PHY_SLV_DLY_CTRL_GATE_DISABLE_3_WOCLR         0U
#define LPDDR4__DENALI_PHY_845__PHY_SLV_DLY_CTRL_GATE_DISABLE_3_WOSET         0U
#define LPDDR4__PHY_SLV_DLY_CTRL_GATE_DISABLE_3__REG DENALI_PHY_845
#define LPDDR4__PHY_SLV_DLY_CTRL_GATE_DISABLE_3__FLD LPDDR4__DENALI_PHY_845__PHY_SLV_DLY_CTRL_GATE_DISABLE_3

#define LPDDR4__DENALI_PHY_845__PHY_RDPATH_GATE_DISABLE_3_MASK       0x01000000U
#define LPDDR4__DENALI_PHY_845__PHY_RDPATH_GATE_DISABLE_3_SHIFT              24U
#define LPDDR4__DENALI_PHY_845__PHY_RDPATH_GATE_DISABLE_3_WIDTH               1U
#define LPDDR4__DENALI_PHY_845__PHY_RDPATH_GATE_DISABLE_3_WOCLR               0U
#define LPDDR4__DENALI_PHY_845__PHY_RDPATH_GATE_DISABLE_3_WOSET               0U
#define LPDDR4__PHY_RDPATH_GATE_DISABLE_3__REG DENALI_PHY_845
#define LPDDR4__PHY_RDPATH_GATE_DISABLE_3__FLD LPDDR4__DENALI_PHY_845__PHY_RDPATH_GATE_DISABLE_3

#define LPDDR4__DENALI_PHY_846_READ_MASK                             0x00000101U
#define LPDDR4__DENALI_PHY_846_WRITE_MASK                            0x00000101U
#define LPDDR4__DENALI_PHY_846__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_3_MASK 0x00000001U
#define LPDDR4__DENALI_PHY_846__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_3_SHIFT       0U
#define LPDDR4__DENALI_PHY_846__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_3_WIDTH       1U
#define LPDDR4__DENALI_PHY_846__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_3_WOCLR       0U
#define LPDDR4__DENALI_PHY_846__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_3_WOSET       0U
#define LPDDR4__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_3__REG DENALI_PHY_846
#define LPDDR4__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_3__FLD LPDDR4__DENALI_PHY_846__PHY_DCC_RXCAL_CTRL_GATE_DISABLE_3

#define LPDDR4__DENALI_PHY_846__PHY_SLICE_PWR_RDC_DISABLE_3_MASK     0x00000100U
#define LPDDR4__DENALI_PHY_846__PHY_SLICE_PWR_RDC_DISABLE_3_SHIFT             8U
#define LPDDR4__DENALI_PHY_846__PHY_SLICE_PWR_RDC_DISABLE_3_WIDTH             1U
#define LPDDR4__DENALI_PHY_846__PHY_SLICE_PWR_RDC_DISABLE_3_WOCLR             0U
#define LPDDR4__DENALI_PHY_846__PHY_SLICE_PWR_RDC_DISABLE_3_WOSET             0U
#define LPDDR4__PHY_SLICE_PWR_RDC_DISABLE_3__REG DENALI_PHY_846
#define LPDDR4__PHY_SLICE_PWR_RDC_DISABLE_3__FLD LPDDR4__DENALI_PHY_846__PHY_SLICE_PWR_RDC_DISABLE_3

#define LPDDR4__DENALI_PHY_847_READ_MASK                             0x07FFFF07U
#define LPDDR4__DENALI_PHY_847_WRITE_MASK                            0x07FFFF07U
#define LPDDR4__DENALI_PHY_847__PHY_DQ_TSEL_ENABLE_3_MASK            0x00000007U
#define LPDDR4__DENALI_PHY_847__PHY_DQ_TSEL_ENABLE_3_SHIFT                    0U
#define LPDDR4__DENALI_PHY_847__PHY_DQ_TSEL_ENABLE_3_WIDTH                    3U
#define LPDDR4__PHY_DQ_TSEL_ENABLE_3__REG DENALI_PHY_847
#define LPDDR4__PHY_DQ_TSEL_ENABLE_3__FLD LPDDR4__DENALI_PHY_847__PHY_DQ_TSEL_ENABLE_3

#define LPDDR4__DENALI_PHY_847__PHY_DQ_TSEL_SELECT_3_MASK            0x00FFFF00U
#define LPDDR4__DENALI_PHY_847__PHY_DQ_TSEL_SELECT_3_SHIFT                    8U
#define LPDDR4__DENALI_PHY_847__PHY_DQ_TSEL_SELECT_3_WIDTH                   16U
#define LPDDR4__PHY_DQ_TSEL_SELECT_3__REG DENALI_PHY_847
#define LPDDR4__PHY_DQ_TSEL_SELECT_3__FLD LPDDR4__DENALI_PHY_847__PHY_DQ_TSEL_SELECT_3

#define LPDDR4__DENALI_PHY_847__PHY_DQS_TSEL_ENABLE_3_MASK           0x07000000U
#define LPDDR4__DENALI_PHY_847__PHY_DQS_TSEL_ENABLE_3_SHIFT                  24U
#define LPDDR4__DENALI_PHY_847__PHY_DQS_TSEL_ENABLE_3_WIDTH                   3U
#define LPDDR4__PHY_DQS_TSEL_ENABLE_3__REG DENALI_PHY_847
#define LPDDR4__PHY_DQS_TSEL_ENABLE_3__FLD LPDDR4__DENALI_PHY_847__PHY_DQS_TSEL_ENABLE_3

#define LPDDR4__DENALI_PHY_848_READ_MASK                             0x7F03FFFFU
#define LPDDR4__DENALI_PHY_848_WRITE_MASK                            0x7F03FFFFU
#define LPDDR4__DENALI_PHY_848__PHY_DQS_TSEL_SELECT_3_MASK           0x0000FFFFU
#define LPDDR4__DENALI_PHY_848__PHY_DQS_TSEL_SELECT_3_SHIFT                   0U
#define LPDDR4__DENALI_PHY_848__PHY_DQS_TSEL_SELECT_3_WIDTH                  16U
#define LPDDR4__PHY_DQS_TSEL_SELECT_3__REG DENALI_PHY_848
#define LPDDR4__PHY_DQS_TSEL_SELECT_3__FLD LPDDR4__DENALI_PHY_848__PHY_DQS_TSEL_SELECT_3

#define LPDDR4__DENALI_PHY_848__PHY_TWO_CYC_PREAMBLE_3_MASK          0x00030000U
#define LPDDR4__DENALI_PHY_848__PHY_TWO_CYC_PREAMBLE_3_SHIFT                 16U
#define LPDDR4__DENALI_PHY_848__PHY_TWO_CYC_PREAMBLE_3_WIDTH                  2U
#define LPDDR4__PHY_TWO_CYC_PREAMBLE_3__REG DENALI_PHY_848
#define LPDDR4__PHY_TWO_CYC_PREAMBLE_3__FLD LPDDR4__DENALI_PHY_848__PHY_TWO_CYC_PREAMBLE_3

#define LPDDR4__DENALI_PHY_848__PHY_VREF_INITIAL_START_POINT_3_MASK  0x7F000000U
#define LPDDR4__DENALI_PHY_848__PHY_VREF_INITIAL_START_POINT_3_SHIFT         24U
#define LPDDR4__DENALI_PHY_848__PHY_VREF_INITIAL_START_POINT_3_WIDTH          7U
#define LPDDR4__PHY_VREF_INITIAL_START_POINT_3__REG DENALI_PHY_848
#define LPDDR4__PHY_VREF_INITIAL_START_POINT_3__FLD LPDDR4__DENALI_PHY_848__PHY_VREF_INITIAL_START_POINT_3

#define LPDDR4__DENALI_PHY_849_READ_MASK                             0xFF01037FU
#define LPDDR4__DENALI_PHY_849_WRITE_MASK                            0xFF01037FU
#define LPDDR4__DENALI_PHY_849__PHY_VREF_INITIAL_STOP_POINT_3_MASK   0x0000007FU
#define LPDDR4__DENALI_PHY_849__PHY_VREF_INITIAL_STOP_POINT_3_SHIFT           0U
#define LPDDR4__DENALI_PHY_849__PHY_VREF_INITIAL_STOP_POINT_3_WIDTH           7U
#define LPDDR4__PHY_VREF_INITIAL_STOP_POINT_3__REG DENALI_PHY_849
#define LPDDR4__PHY_VREF_INITIAL_STOP_POINT_3__FLD LPDDR4__DENALI_PHY_849__PHY_VREF_INITIAL_STOP_POINT_3

#define LPDDR4__DENALI_PHY_849__PHY_VREF_TRAINING_CTRL_3_MASK        0x00000300U
#define LPDDR4__DENALI_PHY_849__PHY_VREF_TRAINING_CTRL_3_SHIFT                8U
#define LPDDR4__DENALI_PHY_849__PHY_VREF_TRAINING_CTRL_3_WIDTH                2U
#define LPDDR4__PHY_VREF_TRAINING_CTRL_3__REG DENALI_PHY_849
#define LPDDR4__PHY_VREF_TRAINING_CTRL_3__FLD LPDDR4__DENALI_PHY_849__PHY_VREF_TRAINING_CTRL_3

#define LPDDR4__DENALI_PHY_849__PHY_NTP_TRAIN_EN_3_MASK              0x00010000U
#define LPDDR4__DENALI_PHY_849__PHY_NTP_TRAIN_EN_3_SHIFT                     16U
#define LPDDR4__DENALI_PHY_849__PHY_NTP_TRAIN_EN_3_WIDTH                      1U
#define LPDDR4__DENALI_PHY_849__PHY_NTP_TRAIN_EN_3_WOCLR                      0U
#define LPDDR4__DENALI_PHY_849__PHY_NTP_TRAIN_EN_3_WOSET                      0U
#define LPDDR4__PHY_NTP_TRAIN_EN_3__REG DENALI_PHY_849
#define LPDDR4__PHY_NTP_TRAIN_EN_3__FLD LPDDR4__DENALI_PHY_849__PHY_NTP_TRAIN_EN_3

#define LPDDR4__DENALI_PHY_849__PHY_NTP_WDQ_STEP_SIZE_3_MASK         0xFF000000U
#define LPDDR4__DENALI_PHY_849__PHY_NTP_WDQ_STEP_SIZE_3_SHIFT                24U
#define LPDDR4__DENALI_PHY_849__PHY_NTP_WDQ_STEP_SIZE_3_WIDTH                 8U
#define LPDDR4__PHY_NTP_WDQ_STEP_SIZE_3__REG DENALI_PHY_849
#define LPDDR4__PHY_NTP_WDQ_STEP_SIZE_3__FLD LPDDR4__DENALI_PHY_849__PHY_NTP_WDQ_STEP_SIZE_3

#define LPDDR4__DENALI_PHY_850_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_850_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_850__PHY_NTP_WDQ_START_3_MASK             0x000007FFU
#define LPDDR4__DENALI_PHY_850__PHY_NTP_WDQ_START_3_SHIFT                     0U
#define LPDDR4__DENALI_PHY_850__PHY_NTP_WDQ_START_3_WIDTH                    11U
#define LPDDR4__PHY_NTP_WDQ_START_3__REG DENALI_PHY_850
#define LPDDR4__PHY_NTP_WDQ_START_3__FLD LPDDR4__DENALI_PHY_850__PHY_NTP_WDQ_START_3

#define LPDDR4__DENALI_PHY_850__PHY_NTP_WDQ_STOP_3_MASK              0x07FF0000U
#define LPDDR4__DENALI_PHY_850__PHY_NTP_WDQ_STOP_3_SHIFT                     16U
#define LPDDR4__DENALI_PHY_850__PHY_NTP_WDQ_STOP_3_WIDTH                     11U
#define LPDDR4__PHY_NTP_WDQ_STOP_3__REG DENALI_PHY_850
#define LPDDR4__PHY_NTP_WDQ_STOP_3__FLD LPDDR4__DENALI_PHY_850__PHY_NTP_WDQ_STOP_3

#define LPDDR4__DENALI_PHY_851_READ_MASK                             0x0103FFFFU
#define LPDDR4__DENALI_PHY_851_WRITE_MASK                            0x0103FFFFU
#define LPDDR4__DENALI_PHY_851__PHY_NTP_WDQ_BIT_EN_3_MASK            0x000000FFU
#define LPDDR4__DENALI_PHY_851__PHY_NTP_WDQ_BIT_EN_3_SHIFT                    0U
#define LPDDR4__DENALI_PHY_851__PHY_NTP_WDQ_BIT_EN_3_WIDTH                    8U
#define LPDDR4__PHY_NTP_WDQ_BIT_EN_3__REG DENALI_PHY_851
#define LPDDR4__PHY_NTP_WDQ_BIT_EN_3__FLD LPDDR4__DENALI_PHY_851__PHY_NTP_WDQ_BIT_EN_3

#define LPDDR4__DENALI_PHY_851__PHY_WDQLVL_DVW_MIN_3_MASK            0x0003FF00U
#define LPDDR4__DENALI_PHY_851__PHY_WDQLVL_DVW_MIN_3_SHIFT                    8U
#define LPDDR4__DENALI_PHY_851__PHY_WDQLVL_DVW_MIN_3_WIDTH                   10U
#define LPDDR4__PHY_WDQLVL_DVW_MIN_3__REG DENALI_PHY_851
#define LPDDR4__PHY_WDQLVL_DVW_MIN_3__FLD LPDDR4__DENALI_PHY_851__PHY_WDQLVL_DVW_MIN_3

#define LPDDR4__DENALI_PHY_851__PHY_SW_WDQLVL_DVW_MIN_EN_3_MASK      0x01000000U
#define LPDDR4__DENALI_PHY_851__PHY_SW_WDQLVL_DVW_MIN_EN_3_SHIFT             24U
#define LPDDR4__DENALI_PHY_851__PHY_SW_WDQLVL_DVW_MIN_EN_3_WIDTH              1U
#define LPDDR4__DENALI_PHY_851__PHY_SW_WDQLVL_DVW_MIN_EN_3_WOCLR              0U
#define LPDDR4__DENALI_PHY_851__PHY_SW_WDQLVL_DVW_MIN_EN_3_WOSET              0U
#define LPDDR4__PHY_SW_WDQLVL_DVW_MIN_EN_3__REG DENALI_PHY_851
#define LPDDR4__PHY_SW_WDQLVL_DVW_MIN_EN_3__FLD LPDDR4__DENALI_PHY_851__PHY_SW_WDQLVL_DVW_MIN_EN_3

#define LPDDR4__DENALI_PHY_852_READ_MASK                             0x1F1F0F3FU
#define LPDDR4__DENALI_PHY_852_WRITE_MASK                            0x1F1F0F3FU
#define LPDDR4__DENALI_PHY_852__PHY_WDQLVL_PER_START_OFFSET_3_MASK   0x0000003FU
#define LPDDR4__DENALI_PHY_852__PHY_WDQLVL_PER_START_OFFSET_3_SHIFT           0U
#define LPDDR4__DENALI_PHY_852__PHY_WDQLVL_PER_START_OFFSET_3_WIDTH           6U
#define LPDDR4__PHY_WDQLVL_PER_START_OFFSET_3__REG DENALI_PHY_852
#define LPDDR4__PHY_WDQLVL_PER_START_OFFSET_3__FLD LPDDR4__DENALI_PHY_852__PHY_WDQLVL_PER_START_OFFSET_3

#define LPDDR4__DENALI_PHY_852__PHY_FAST_LVL_EN_3_MASK               0x00000F00U
#define LPDDR4__DENALI_PHY_852__PHY_FAST_LVL_EN_3_SHIFT                       8U
#define LPDDR4__DENALI_PHY_852__PHY_FAST_LVL_EN_3_WIDTH                       4U
#define LPDDR4__PHY_FAST_LVL_EN_3__REG DENALI_PHY_852
#define LPDDR4__PHY_FAST_LVL_EN_3__FLD LPDDR4__DENALI_PHY_852__PHY_FAST_LVL_EN_3

#define LPDDR4__DENALI_PHY_852__PHY_PAD_TX_DCD_3_MASK                0x001F0000U
#define LPDDR4__DENALI_PHY_852__PHY_PAD_TX_DCD_3_SHIFT                       16U
#define LPDDR4__DENALI_PHY_852__PHY_PAD_TX_DCD_3_WIDTH                        5U
#define LPDDR4__PHY_PAD_TX_DCD_3__REG DENALI_PHY_852
#define LPDDR4__PHY_PAD_TX_DCD_3__FLD LPDDR4__DENALI_PHY_852__PHY_PAD_TX_DCD_3

#define LPDDR4__DENALI_PHY_852__PHY_PAD_RX_DCD_0_3_MASK              0x1F000000U
#define LPDDR4__DENALI_PHY_852__PHY_PAD_RX_DCD_0_3_SHIFT                     24U
#define LPDDR4__DENALI_PHY_852__PHY_PAD_RX_DCD_0_3_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_0_3__REG DENALI_PHY_852
#define LPDDR4__PHY_PAD_RX_DCD_0_3__FLD LPDDR4__DENALI_PHY_852__PHY_PAD_RX_DCD_0_3

#define LPDDR4__DENALI_PHY_853_READ_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_853_WRITE_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_853__PHY_PAD_RX_DCD_1_3_MASK              0x0000001FU
#define LPDDR4__DENALI_PHY_853__PHY_PAD_RX_DCD_1_3_SHIFT                      0U
#define LPDDR4__DENALI_PHY_853__PHY_PAD_RX_DCD_1_3_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_1_3__REG DENALI_PHY_853
#define LPDDR4__PHY_PAD_RX_DCD_1_3__FLD LPDDR4__DENALI_PHY_853__PHY_PAD_RX_DCD_1_3

#define LPDDR4__DENALI_PHY_853__PHY_PAD_RX_DCD_2_3_MASK              0x00001F00U
#define LPDDR4__DENALI_PHY_853__PHY_PAD_RX_DCD_2_3_SHIFT                      8U
#define LPDDR4__DENALI_PHY_853__PHY_PAD_RX_DCD_2_3_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_2_3__REG DENALI_PHY_853
#define LPDDR4__PHY_PAD_RX_DCD_2_3__FLD LPDDR4__DENALI_PHY_853__PHY_PAD_RX_DCD_2_3

#define LPDDR4__DENALI_PHY_853__PHY_PAD_RX_DCD_3_3_MASK              0x001F0000U
#define LPDDR4__DENALI_PHY_853__PHY_PAD_RX_DCD_3_3_SHIFT                     16U
#define LPDDR4__DENALI_PHY_853__PHY_PAD_RX_DCD_3_3_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_3_3__REG DENALI_PHY_853
#define LPDDR4__PHY_PAD_RX_DCD_3_3__FLD LPDDR4__DENALI_PHY_853__PHY_PAD_RX_DCD_3_3

#define LPDDR4__DENALI_PHY_853__PHY_PAD_RX_DCD_4_3_MASK              0x1F000000U
#define LPDDR4__DENALI_PHY_853__PHY_PAD_RX_DCD_4_3_SHIFT                     24U
#define LPDDR4__DENALI_PHY_853__PHY_PAD_RX_DCD_4_3_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_4_3__REG DENALI_PHY_853
#define LPDDR4__PHY_PAD_RX_DCD_4_3__FLD LPDDR4__DENALI_PHY_853__PHY_PAD_RX_DCD_4_3

#define LPDDR4__DENALI_PHY_854_READ_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_854_WRITE_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_854__PHY_PAD_RX_DCD_5_3_MASK              0x0000001FU
#define LPDDR4__DENALI_PHY_854__PHY_PAD_RX_DCD_5_3_SHIFT                      0U
#define LPDDR4__DENALI_PHY_854__PHY_PAD_RX_DCD_5_3_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_5_3__REG DENALI_PHY_854
#define LPDDR4__PHY_PAD_RX_DCD_5_3__FLD LPDDR4__DENALI_PHY_854__PHY_PAD_RX_DCD_5_3

#define LPDDR4__DENALI_PHY_854__PHY_PAD_RX_DCD_6_3_MASK              0x00001F00U
#define LPDDR4__DENALI_PHY_854__PHY_PAD_RX_DCD_6_3_SHIFT                      8U
#define LPDDR4__DENALI_PHY_854__PHY_PAD_RX_DCD_6_3_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_6_3__REG DENALI_PHY_854
#define LPDDR4__PHY_PAD_RX_DCD_6_3__FLD LPDDR4__DENALI_PHY_854__PHY_PAD_RX_DCD_6_3

#define LPDDR4__DENALI_PHY_854__PHY_PAD_RX_DCD_7_3_MASK              0x001F0000U
#define LPDDR4__DENALI_PHY_854__PHY_PAD_RX_DCD_7_3_SHIFT                     16U
#define LPDDR4__DENALI_PHY_854__PHY_PAD_RX_DCD_7_3_WIDTH                      5U
#define LPDDR4__PHY_PAD_RX_DCD_7_3__REG DENALI_PHY_854
#define LPDDR4__PHY_PAD_RX_DCD_7_3__FLD LPDDR4__DENALI_PHY_854__PHY_PAD_RX_DCD_7_3

#define LPDDR4__DENALI_PHY_854__PHY_PAD_DM_RX_DCD_3_MASK             0x1F000000U
#define LPDDR4__DENALI_PHY_854__PHY_PAD_DM_RX_DCD_3_SHIFT                    24U
#define LPDDR4__DENALI_PHY_854__PHY_PAD_DM_RX_DCD_3_WIDTH                     5U
#define LPDDR4__PHY_PAD_DM_RX_DCD_3__REG DENALI_PHY_854
#define LPDDR4__PHY_PAD_DM_RX_DCD_3__FLD LPDDR4__DENALI_PHY_854__PHY_PAD_DM_RX_DCD_3

#define LPDDR4__DENALI_PHY_855_READ_MASK                             0x007F1F1FU
#define LPDDR4__DENALI_PHY_855_WRITE_MASK                            0x007F1F1FU
#define LPDDR4__DENALI_PHY_855__PHY_PAD_DQS_RX_DCD_3_MASK            0x0000001FU
#define LPDDR4__DENALI_PHY_855__PHY_PAD_DQS_RX_DCD_3_SHIFT                    0U
#define LPDDR4__DENALI_PHY_855__PHY_PAD_DQS_RX_DCD_3_WIDTH                    5U
#define LPDDR4__PHY_PAD_DQS_RX_DCD_3__REG DENALI_PHY_855
#define LPDDR4__PHY_PAD_DQS_RX_DCD_3__FLD LPDDR4__DENALI_PHY_855__PHY_PAD_DQS_RX_DCD_3

#define LPDDR4__DENALI_PHY_855__PHY_PAD_FDBK_RX_DCD_3_MASK           0x00001F00U
#define LPDDR4__DENALI_PHY_855__PHY_PAD_FDBK_RX_DCD_3_SHIFT                   8U
#define LPDDR4__DENALI_PHY_855__PHY_PAD_FDBK_RX_DCD_3_WIDTH                   5U
#define LPDDR4__PHY_PAD_FDBK_RX_DCD_3__REG DENALI_PHY_855
#define LPDDR4__PHY_PAD_FDBK_RX_DCD_3__FLD LPDDR4__DENALI_PHY_855__PHY_PAD_FDBK_RX_DCD_3

#define LPDDR4__DENALI_PHY_855__PHY_PAD_DSLICE_IO_CFG_3_MASK         0x007F0000U
#define LPDDR4__DENALI_PHY_855__PHY_PAD_DSLICE_IO_CFG_3_SHIFT                16U
#define LPDDR4__DENALI_PHY_855__PHY_PAD_DSLICE_IO_CFG_3_WIDTH                 7U
#define LPDDR4__PHY_PAD_DSLICE_IO_CFG_3__REG DENALI_PHY_855
#define LPDDR4__PHY_PAD_DSLICE_IO_CFG_3__FLD LPDDR4__DENALI_PHY_855__PHY_PAD_DSLICE_IO_CFG_3

#define LPDDR4__DENALI_PHY_856_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_856_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_856__PHY_RDDQ0_SLAVE_DELAY_3_MASK         0x000003FFU
#define LPDDR4__DENALI_PHY_856__PHY_RDDQ0_SLAVE_DELAY_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_856__PHY_RDDQ0_SLAVE_DELAY_3_WIDTH                10U
#define LPDDR4__PHY_RDDQ0_SLAVE_DELAY_3__REG DENALI_PHY_856
#define LPDDR4__PHY_RDDQ0_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_856__PHY_RDDQ0_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_856__PHY_RDDQ1_SLAVE_DELAY_3_MASK         0x03FF0000U
#define LPDDR4__DENALI_PHY_856__PHY_RDDQ1_SLAVE_DELAY_3_SHIFT                16U
#define LPDDR4__DENALI_PHY_856__PHY_RDDQ1_SLAVE_DELAY_3_WIDTH                10U
#define LPDDR4__PHY_RDDQ1_SLAVE_DELAY_3__REG DENALI_PHY_856
#define LPDDR4__PHY_RDDQ1_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_856__PHY_RDDQ1_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_857_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_857_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_857__PHY_RDDQ2_SLAVE_DELAY_3_MASK         0x000003FFU
#define LPDDR4__DENALI_PHY_857__PHY_RDDQ2_SLAVE_DELAY_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_857__PHY_RDDQ2_SLAVE_DELAY_3_WIDTH                10U
#define LPDDR4__PHY_RDDQ2_SLAVE_DELAY_3__REG DENALI_PHY_857
#define LPDDR4__PHY_RDDQ2_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_857__PHY_RDDQ2_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_857__PHY_RDDQ3_SLAVE_DELAY_3_MASK         0x03FF0000U
#define LPDDR4__DENALI_PHY_857__PHY_RDDQ3_SLAVE_DELAY_3_SHIFT                16U
#define LPDDR4__DENALI_PHY_857__PHY_RDDQ3_SLAVE_DELAY_3_WIDTH                10U
#define LPDDR4__PHY_RDDQ3_SLAVE_DELAY_3__REG DENALI_PHY_857
#define LPDDR4__PHY_RDDQ3_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_857__PHY_RDDQ3_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_858_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_858_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_858__PHY_RDDQ4_SLAVE_DELAY_3_MASK         0x000003FFU
#define LPDDR4__DENALI_PHY_858__PHY_RDDQ4_SLAVE_DELAY_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_858__PHY_RDDQ4_SLAVE_DELAY_3_WIDTH                10U
#define LPDDR4__PHY_RDDQ4_SLAVE_DELAY_3__REG DENALI_PHY_858
#define LPDDR4__PHY_RDDQ4_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_858__PHY_RDDQ4_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_858__PHY_RDDQ5_SLAVE_DELAY_3_MASK         0x03FF0000U
#define LPDDR4__DENALI_PHY_858__PHY_RDDQ5_SLAVE_DELAY_3_SHIFT                16U
#define LPDDR4__DENALI_PHY_858__PHY_RDDQ5_SLAVE_DELAY_3_WIDTH                10U
#define LPDDR4__PHY_RDDQ5_SLAVE_DELAY_3__REG DENALI_PHY_858
#define LPDDR4__PHY_RDDQ5_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_858__PHY_RDDQ5_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_859_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_859_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_859__PHY_RDDQ6_SLAVE_DELAY_3_MASK         0x000003FFU
#define LPDDR4__DENALI_PHY_859__PHY_RDDQ6_SLAVE_DELAY_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_859__PHY_RDDQ6_SLAVE_DELAY_3_WIDTH                10U
#define LPDDR4__PHY_RDDQ6_SLAVE_DELAY_3__REG DENALI_PHY_859
#define LPDDR4__PHY_RDDQ6_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_859__PHY_RDDQ6_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_859__PHY_RDDQ7_SLAVE_DELAY_3_MASK         0x03FF0000U
#define LPDDR4__DENALI_PHY_859__PHY_RDDQ7_SLAVE_DELAY_3_SHIFT                16U
#define LPDDR4__DENALI_PHY_859__PHY_RDDQ7_SLAVE_DELAY_3_WIDTH                10U
#define LPDDR4__PHY_RDDQ7_SLAVE_DELAY_3__REG DENALI_PHY_859
#define LPDDR4__PHY_RDDQ7_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_859__PHY_RDDQ7_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_860_READ_MASK                             0x1F0703FFU
#define LPDDR4__DENALI_PHY_860_WRITE_MASK                            0x1F0703FFU
#define LPDDR4__DENALI_PHY_860__PHY_RDDM_SLAVE_DELAY_3_MASK          0x000003FFU
#define LPDDR4__DENALI_PHY_860__PHY_RDDM_SLAVE_DELAY_3_SHIFT                  0U
#define LPDDR4__DENALI_PHY_860__PHY_RDDM_SLAVE_DELAY_3_WIDTH                 10U
#define LPDDR4__PHY_RDDM_SLAVE_DELAY_3__REG DENALI_PHY_860
#define LPDDR4__PHY_RDDM_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_860__PHY_RDDM_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_860__PHY_RX_PCLK_CLK_SEL_3_MASK           0x00070000U
#define LPDDR4__DENALI_PHY_860__PHY_RX_PCLK_CLK_SEL_3_SHIFT                  16U
#define LPDDR4__DENALI_PHY_860__PHY_RX_PCLK_CLK_SEL_3_WIDTH                   3U
#define LPDDR4__PHY_RX_PCLK_CLK_SEL_3__REG DENALI_PHY_860
#define LPDDR4__PHY_RX_PCLK_CLK_SEL_3__FLD LPDDR4__DENALI_PHY_860__PHY_RX_PCLK_CLK_SEL_3

#define LPDDR4__DENALI_PHY_860__PHY_RX_CAL_ALL_DLY_3_MASK            0x1F000000U
#define LPDDR4__DENALI_PHY_860__PHY_RX_CAL_ALL_DLY_3_SHIFT                   24U
#define LPDDR4__DENALI_PHY_860__PHY_RX_CAL_ALL_DLY_3_WIDTH                    5U
#define LPDDR4__PHY_RX_CAL_ALL_DLY_3__REG DENALI_PHY_860
#define LPDDR4__PHY_RX_CAL_ALL_DLY_3__FLD LPDDR4__DENALI_PHY_860__PHY_RX_CAL_ALL_DLY_3

#define LPDDR4__DENALI_PHY_861_READ_MASK                             0x00000007U
#define LPDDR4__DENALI_PHY_861_WRITE_MASK                            0x00000007U
#define LPDDR4__DENALI_PHY_861__PHY_DATA_DC_CAL_CLK_SEL_3_MASK       0x00000007U
#define LPDDR4__DENALI_PHY_861__PHY_DATA_DC_CAL_CLK_SEL_3_SHIFT               0U
#define LPDDR4__DENALI_PHY_861__PHY_DATA_DC_CAL_CLK_SEL_3_WIDTH               3U
#define LPDDR4__PHY_DATA_DC_CAL_CLK_SEL_3__REG DENALI_PHY_861
#define LPDDR4__PHY_DATA_DC_CAL_CLK_SEL_3__FLD LPDDR4__DENALI_PHY_861__PHY_DATA_DC_CAL_CLK_SEL_3

#define LPDDR4__DENALI_PHY_862_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_862_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_862__PHY_DQ_OE_TIMING_3_MASK              0x000000FFU
#define LPDDR4__DENALI_PHY_862__PHY_DQ_OE_TIMING_3_SHIFT                      0U
#define LPDDR4__DENALI_PHY_862__PHY_DQ_OE_TIMING_3_WIDTH                      8U
#define LPDDR4__PHY_DQ_OE_TIMING_3__REG DENALI_PHY_862
#define LPDDR4__PHY_DQ_OE_TIMING_3__FLD LPDDR4__DENALI_PHY_862__PHY_DQ_OE_TIMING_3

#define LPDDR4__DENALI_PHY_862__PHY_DQ_TSEL_RD_TIMING_3_MASK         0x0000FF00U
#define LPDDR4__DENALI_PHY_862__PHY_DQ_TSEL_RD_TIMING_3_SHIFT                 8U
#define LPDDR4__DENALI_PHY_862__PHY_DQ_TSEL_RD_TIMING_3_WIDTH                 8U
#define LPDDR4__PHY_DQ_TSEL_RD_TIMING_3__REG DENALI_PHY_862
#define LPDDR4__PHY_DQ_TSEL_RD_TIMING_3__FLD LPDDR4__DENALI_PHY_862__PHY_DQ_TSEL_RD_TIMING_3

#define LPDDR4__DENALI_PHY_862__PHY_DQ_TSEL_WR_TIMING_3_MASK         0x00FF0000U
#define LPDDR4__DENALI_PHY_862__PHY_DQ_TSEL_WR_TIMING_3_SHIFT                16U
#define LPDDR4__DENALI_PHY_862__PHY_DQ_TSEL_WR_TIMING_3_WIDTH                 8U
#define LPDDR4__PHY_DQ_TSEL_WR_TIMING_3__REG DENALI_PHY_862
#define LPDDR4__PHY_DQ_TSEL_WR_TIMING_3__FLD LPDDR4__DENALI_PHY_862__PHY_DQ_TSEL_WR_TIMING_3

#define LPDDR4__DENALI_PHY_862__PHY_DQS_OE_TIMING_3_MASK             0xFF000000U
#define LPDDR4__DENALI_PHY_862__PHY_DQS_OE_TIMING_3_SHIFT                    24U
#define LPDDR4__DENALI_PHY_862__PHY_DQS_OE_TIMING_3_WIDTH                     8U
#define LPDDR4__PHY_DQS_OE_TIMING_3__REG DENALI_PHY_862
#define LPDDR4__PHY_DQS_OE_TIMING_3__FLD LPDDR4__DENALI_PHY_862__PHY_DQS_OE_TIMING_3

#define LPDDR4__DENALI_PHY_863_READ_MASK                             0xFFFFFF0FU
#define LPDDR4__DENALI_PHY_863_WRITE_MASK                            0xFFFFFF0FU
#define LPDDR4__DENALI_PHY_863__PHY_IO_PAD_DELAY_TIMING_3_MASK       0x0000000FU
#define LPDDR4__DENALI_PHY_863__PHY_IO_PAD_DELAY_TIMING_3_SHIFT               0U
#define LPDDR4__DENALI_PHY_863__PHY_IO_PAD_DELAY_TIMING_3_WIDTH               4U
#define LPDDR4__PHY_IO_PAD_DELAY_TIMING_3__REG DENALI_PHY_863
#define LPDDR4__PHY_IO_PAD_DELAY_TIMING_3__FLD LPDDR4__DENALI_PHY_863__PHY_IO_PAD_DELAY_TIMING_3

#define LPDDR4__DENALI_PHY_863__PHY_DQS_TSEL_RD_TIMING_3_MASK        0x0000FF00U
#define LPDDR4__DENALI_PHY_863__PHY_DQS_TSEL_RD_TIMING_3_SHIFT                8U
#define LPDDR4__DENALI_PHY_863__PHY_DQS_TSEL_RD_TIMING_3_WIDTH                8U
#define LPDDR4__PHY_DQS_TSEL_RD_TIMING_3__REG DENALI_PHY_863
#define LPDDR4__PHY_DQS_TSEL_RD_TIMING_3__FLD LPDDR4__DENALI_PHY_863__PHY_DQS_TSEL_RD_TIMING_3

#define LPDDR4__DENALI_PHY_863__PHY_DQS_OE_RD_TIMING_3_MASK          0x00FF0000U
#define LPDDR4__DENALI_PHY_863__PHY_DQS_OE_RD_TIMING_3_SHIFT                 16U
#define LPDDR4__DENALI_PHY_863__PHY_DQS_OE_RD_TIMING_3_WIDTH                  8U
#define LPDDR4__PHY_DQS_OE_RD_TIMING_3__REG DENALI_PHY_863
#define LPDDR4__PHY_DQS_OE_RD_TIMING_3__FLD LPDDR4__DENALI_PHY_863__PHY_DQS_OE_RD_TIMING_3

#define LPDDR4__DENALI_PHY_863__PHY_DQS_TSEL_WR_TIMING_3_MASK        0xFF000000U
#define LPDDR4__DENALI_PHY_863__PHY_DQS_TSEL_WR_TIMING_3_SHIFT               24U
#define LPDDR4__DENALI_PHY_863__PHY_DQS_TSEL_WR_TIMING_3_WIDTH                8U
#define LPDDR4__PHY_DQS_TSEL_WR_TIMING_3__REG DENALI_PHY_863
#define LPDDR4__PHY_DQS_TSEL_WR_TIMING_3__FLD LPDDR4__DENALI_PHY_863__PHY_DQS_TSEL_WR_TIMING_3

#define LPDDR4__DENALI_PHY_864_READ_MASK                             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_864_WRITE_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_864__PHY_VREF_SETTING_TIME_3_MASK         0x0000FFFFU
#define LPDDR4__DENALI_PHY_864__PHY_VREF_SETTING_TIME_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_864__PHY_VREF_SETTING_TIME_3_WIDTH                16U
#define LPDDR4__PHY_VREF_SETTING_TIME_3__REG DENALI_PHY_864
#define LPDDR4__PHY_VREF_SETTING_TIME_3__FLD LPDDR4__DENALI_PHY_864__PHY_VREF_SETTING_TIME_3

#define LPDDR4__DENALI_PHY_864__PHY_PAD_VREF_CTRL_DQ_3_MASK          0x0FFF0000U
#define LPDDR4__DENALI_PHY_864__PHY_PAD_VREF_CTRL_DQ_3_SHIFT                 16U
#define LPDDR4__DENALI_PHY_864__PHY_PAD_VREF_CTRL_DQ_3_WIDTH                 12U
#define LPDDR4__PHY_PAD_VREF_CTRL_DQ_3__REG DENALI_PHY_864
#define LPDDR4__PHY_PAD_VREF_CTRL_DQ_3__FLD LPDDR4__DENALI_PHY_864__PHY_PAD_VREF_CTRL_DQ_3

#define LPDDR4__DENALI_PHY_865_READ_MASK                             0x03FFFF01U
#define LPDDR4__DENALI_PHY_865_WRITE_MASK                            0x03FFFF01U
#define LPDDR4__DENALI_PHY_865__PHY_PER_CS_TRAINING_EN_3_MASK        0x00000001U
#define LPDDR4__DENALI_PHY_865__PHY_PER_CS_TRAINING_EN_3_SHIFT                0U
#define LPDDR4__DENALI_PHY_865__PHY_PER_CS_TRAINING_EN_3_WIDTH                1U
#define LPDDR4__DENALI_PHY_865__PHY_PER_CS_TRAINING_EN_3_WOCLR                0U
#define LPDDR4__DENALI_PHY_865__PHY_PER_CS_TRAINING_EN_3_WOSET                0U
#define LPDDR4__PHY_PER_CS_TRAINING_EN_3__REG DENALI_PHY_865
#define LPDDR4__PHY_PER_CS_TRAINING_EN_3__FLD LPDDR4__DENALI_PHY_865__PHY_PER_CS_TRAINING_EN_3

#define LPDDR4__DENALI_PHY_865__PHY_DQ_IE_TIMING_3_MASK              0x0000FF00U
#define LPDDR4__DENALI_PHY_865__PHY_DQ_IE_TIMING_3_SHIFT                      8U
#define LPDDR4__DENALI_PHY_865__PHY_DQ_IE_TIMING_3_WIDTH                      8U
#define LPDDR4__PHY_DQ_IE_TIMING_3__REG DENALI_PHY_865
#define LPDDR4__PHY_DQ_IE_TIMING_3__FLD LPDDR4__DENALI_PHY_865__PHY_DQ_IE_TIMING_3

#define LPDDR4__DENALI_PHY_865__PHY_DQS_IE_TIMING_3_MASK             0x00FF0000U
#define LPDDR4__DENALI_PHY_865__PHY_DQS_IE_TIMING_3_SHIFT                    16U
#define LPDDR4__DENALI_PHY_865__PHY_DQS_IE_TIMING_3_WIDTH                     8U
#define LPDDR4__PHY_DQS_IE_TIMING_3__REG DENALI_PHY_865
#define LPDDR4__PHY_DQS_IE_TIMING_3__FLD LPDDR4__DENALI_PHY_865__PHY_DQS_IE_TIMING_3

#define LPDDR4__DENALI_PHY_865__PHY_RDDATA_EN_IE_DLY_3_MASK          0x03000000U
#define LPDDR4__DENALI_PHY_865__PHY_RDDATA_EN_IE_DLY_3_SHIFT                 24U
#define LPDDR4__DENALI_PHY_865__PHY_RDDATA_EN_IE_DLY_3_WIDTH                  2U
#define LPDDR4__PHY_RDDATA_EN_IE_DLY_3__REG DENALI_PHY_865
#define LPDDR4__PHY_RDDATA_EN_IE_DLY_3__FLD LPDDR4__DENALI_PHY_865__PHY_RDDATA_EN_IE_DLY_3

#define LPDDR4__DENALI_PHY_866_READ_MASK                             0x1F010303U
#define LPDDR4__DENALI_PHY_866_WRITE_MASK                            0x1F010303U
#define LPDDR4__DENALI_PHY_866__PHY_IE_MODE_3_MASK                   0x00000003U
#define LPDDR4__DENALI_PHY_866__PHY_IE_MODE_3_SHIFT                           0U
#define LPDDR4__DENALI_PHY_866__PHY_IE_MODE_3_WIDTH                           2U
#define LPDDR4__PHY_IE_MODE_3__REG DENALI_PHY_866
#define LPDDR4__PHY_IE_MODE_3__FLD LPDDR4__DENALI_PHY_866__PHY_IE_MODE_3

#define LPDDR4__DENALI_PHY_866__PHY_DBI_MODE_3_MASK                  0x00000300U
#define LPDDR4__DENALI_PHY_866__PHY_DBI_MODE_3_SHIFT                          8U
#define LPDDR4__DENALI_PHY_866__PHY_DBI_MODE_3_WIDTH                          2U
#define LPDDR4__PHY_DBI_MODE_3__REG DENALI_PHY_866
#define LPDDR4__PHY_DBI_MODE_3__FLD LPDDR4__DENALI_PHY_866__PHY_DBI_MODE_3

#define LPDDR4__DENALI_PHY_866__PHY_WDQLVL_IE_ON_3_MASK              0x00010000U
#define LPDDR4__DENALI_PHY_866__PHY_WDQLVL_IE_ON_3_SHIFT                     16U
#define LPDDR4__DENALI_PHY_866__PHY_WDQLVL_IE_ON_3_WIDTH                      1U
#define LPDDR4__DENALI_PHY_866__PHY_WDQLVL_IE_ON_3_WOCLR                      0U
#define LPDDR4__DENALI_PHY_866__PHY_WDQLVL_IE_ON_3_WOSET                      0U
#define LPDDR4__PHY_WDQLVL_IE_ON_3__REG DENALI_PHY_866
#define LPDDR4__PHY_WDQLVL_IE_ON_3__FLD LPDDR4__DENALI_PHY_866__PHY_WDQLVL_IE_ON_3

#define LPDDR4__DENALI_PHY_866__PHY_WDQLVL_RDDATA_EN_DLY_3_MASK      0x1F000000U
#define LPDDR4__DENALI_PHY_866__PHY_WDQLVL_RDDATA_EN_DLY_3_SHIFT             24U
#define LPDDR4__DENALI_PHY_866__PHY_WDQLVL_RDDATA_EN_DLY_3_WIDTH              5U
#define LPDDR4__PHY_WDQLVL_RDDATA_EN_DLY_3__REG DENALI_PHY_866
#define LPDDR4__PHY_WDQLVL_RDDATA_EN_DLY_3__FLD LPDDR4__DENALI_PHY_866__PHY_WDQLVL_RDDATA_EN_DLY_3

#define LPDDR4__DENALI_PHY_867_READ_MASK                             0x0F1F1F1FU
#define LPDDR4__DENALI_PHY_867_WRITE_MASK                            0x0F1F1F1FU
#define LPDDR4__DENALI_PHY_867__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_3_MASK 0x0000001FU
#define LPDDR4__DENALI_PHY_867__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_3_SHIFT         0U
#define LPDDR4__DENALI_PHY_867__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_3_WIDTH         5U
#define LPDDR4__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_3__REG DENALI_PHY_867
#define LPDDR4__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_3__FLD LPDDR4__DENALI_PHY_867__PHY_WDQLVL_RDDATA_EN_TSEL_DLY_3

#define LPDDR4__DENALI_PHY_867__PHY_RDDATA_EN_TSEL_DLY_3_MASK        0x00001F00U
#define LPDDR4__DENALI_PHY_867__PHY_RDDATA_EN_TSEL_DLY_3_SHIFT                8U
#define LPDDR4__DENALI_PHY_867__PHY_RDDATA_EN_TSEL_DLY_3_WIDTH                5U
#define LPDDR4__PHY_RDDATA_EN_TSEL_DLY_3__REG DENALI_PHY_867
#define LPDDR4__PHY_RDDATA_EN_TSEL_DLY_3__FLD LPDDR4__DENALI_PHY_867__PHY_RDDATA_EN_TSEL_DLY_3

#define LPDDR4__DENALI_PHY_867__PHY_RDDATA_EN_OE_DLY_3_MASK          0x001F0000U
#define LPDDR4__DENALI_PHY_867__PHY_RDDATA_EN_OE_DLY_3_SHIFT                 16U
#define LPDDR4__DENALI_PHY_867__PHY_RDDATA_EN_OE_DLY_3_WIDTH                  5U
#define LPDDR4__PHY_RDDATA_EN_OE_DLY_3__REG DENALI_PHY_867
#define LPDDR4__PHY_RDDATA_EN_OE_DLY_3__FLD LPDDR4__DENALI_PHY_867__PHY_RDDATA_EN_OE_DLY_3

#define LPDDR4__DENALI_PHY_867__PHY_SW_MASTER_MODE_3_MASK            0x0F000000U
#define LPDDR4__DENALI_PHY_867__PHY_SW_MASTER_MODE_3_SHIFT                   24U
#define LPDDR4__DENALI_PHY_867__PHY_SW_MASTER_MODE_3_WIDTH                    4U
#define LPDDR4__PHY_SW_MASTER_MODE_3__REG DENALI_PHY_867
#define LPDDR4__PHY_SW_MASTER_MODE_3__FLD LPDDR4__DENALI_PHY_867__PHY_SW_MASTER_MODE_3

#define LPDDR4__DENALI_PHY_868_READ_MASK                             0xFF3F07FFU
#define LPDDR4__DENALI_PHY_868_WRITE_MASK                            0xFF3F07FFU
#define LPDDR4__DENALI_PHY_868__PHY_MASTER_DELAY_START_3_MASK        0x000007FFU
#define LPDDR4__DENALI_PHY_868__PHY_MASTER_DELAY_START_3_SHIFT                0U
#define LPDDR4__DENALI_PHY_868__PHY_MASTER_DELAY_START_3_WIDTH               11U
#define LPDDR4__PHY_MASTER_DELAY_START_3__REG DENALI_PHY_868
#define LPDDR4__PHY_MASTER_DELAY_START_3__FLD LPDDR4__DENALI_PHY_868__PHY_MASTER_DELAY_START_3

#define LPDDR4__DENALI_PHY_868__PHY_MASTER_DELAY_STEP_3_MASK         0x003F0000U
#define LPDDR4__DENALI_PHY_868__PHY_MASTER_DELAY_STEP_3_SHIFT                16U
#define LPDDR4__DENALI_PHY_868__PHY_MASTER_DELAY_STEP_3_WIDTH                 6U
#define LPDDR4__PHY_MASTER_DELAY_STEP_3__REG DENALI_PHY_868
#define LPDDR4__PHY_MASTER_DELAY_STEP_3__FLD LPDDR4__DENALI_PHY_868__PHY_MASTER_DELAY_STEP_3

#define LPDDR4__DENALI_PHY_868__PHY_MASTER_DELAY_WAIT_3_MASK         0xFF000000U
#define LPDDR4__DENALI_PHY_868__PHY_MASTER_DELAY_WAIT_3_SHIFT                24U
#define LPDDR4__DENALI_PHY_868__PHY_MASTER_DELAY_WAIT_3_WIDTH                 8U
#define LPDDR4__PHY_MASTER_DELAY_WAIT_3__REG DENALI_PHY_868
#define LPDDR4__PHY_MASTER_DELAY_WAIT_3__FLD LPDDR4__DENALI_PHY_868__PHY_MASTER_DELAY_WAIT_3

#define LPDDR4__DENALI_PHY_869_READ_MASK                             0x0FFF0FFFU
#define LPDDR4__DENALI_PHY_869_WRITE_MASK                            0x0FFF0FFFU
#define LPDDR4__DENALI_PHY_869__PHY_MASTER_DELAY_HALF_MEASURE_3_MASK 0x000000FFU
#define LPDDR4__DENALI_PHY_869__PHY_MASTER_DELAY_HALF_MEASURE_3_SHIFT         0U
#define LPDDR4__DENALI_PHY_869__PHY_MASTER_DELAY_HALF_MEASURE_3_WIDTH         8U
#define LPDDR4__PHY_MASTER_DELAY_HALF_MEASURE_3__REG DENALI_PHY_869
#define LPDDR4__PHY_MASTER_DELAY_HALF_MEASURE_3__FLD LPDDR4__DENALI_PHY_869__PHY_MASTER_DELAY_HALF_MEASURE_3

#define LPDDR4__DENALI_PHY_869__PHY_RPTR_UPDATE_3_MASK               0x00000F00U
#define LPDDR4__DENALI_PHY_869__PHY_RPTR_UPDATE_3_SHIFT                       8U
#define LPDDR4__DENALI_PHY_869__PHY_RPTR_UPDATE_3_WIDTH                       4U
#define LPDDR4__PHY_RPTR_UPDATE_3__REG DENALI_PHY_869
#define LPDDR4__PHY_RPTR_UPDATE_3__FLD LPDDR4__DENALI_PHY_869__PHY_RPTR_UPDATE_3

#define LPDDR4__DENALI_PHY_869__PHY_WRLVL_DLY_STEP_3_MASK            0x00FF0000U
#define LPDDR4__DENALI_PHY_869__PHY_WRLVL_DLY_STEP_3_SHIFT                   16U
#define LPDDR4__DENALI_PHY_869__PHY_WRLVL_DLY_STEP_3_WIDTH                    8U
#define LPDDR4__PHY_WRLVL_DLY_STEP_3__REG DENALI_PHY_869
#define LPDDR4__PHY_WRLVL_DLY_STEP_3__FLD LPDDR4__DENALI_PHY_869__PHY_WRLVL_DLY_STEP_3

#define LPDDR4__DENALI_PHY_869__PHY_WRLVL_DLY_FINE_STEP_3_MASK       0x0F000000U
#define LPDDR4__DENALI_PHY_869__PHY_WRLVL_DLY_FINE_STEP_3_SHIFT              24U
#define LPDDR4__DENALI_PHY_869__PHY_WRLVL_DLY_FINE_STEP_3_WIDTH               4U
#define LPDDR4__PHY_WRLVL_DLY_FINE_STEP_3__REG DENALI_PHY_869
#define LPDDR4__PHY_WRLVL_DLY_FINE_STEP_3__FLD LPDDR4__DENALI_PHY_869__PHY_WRLVL_DLY_FINE_STEP_3

#define LPDDR4__DENALI_PHY_870_READ_MASK                             0x001F0F3FU
#define LPDDR4__DENALI_PHY_870_WRITE_MASK                            0x001F0F3FU
#define LPDDR4__DENALI_PHY_870__PHY_WRLVL_RESP_WAIT_CNT_3_MASK       0x0000003FU
#define LPDDR4__DENALI_PHY_870__PHY_WRLVL_RESP_WAIT_CNT_3_SHIFT               0U
#define LPDDR4__DENALI_PHY_870__PHY_WRLVL_RESP_WAIT_CNT_3_WIDTH               6U
#define LPDDR4__PHY_WRLVL_RESP_WAIT_CNT_3__REG DENALI_PHY_870
#define LPDDR4__PHY_WRLVL_RESP_WAIT_CNT_3__FLD LPDDR4__DENALI_PHY_870__PHY_WRLVL_RESP_WAIT_CNT_3

#define LPDDR4__DENALI_PHY_870__PHY_GTLVL_DLY_STEP_3_MASK            0x00000F00U
#define LPDDR4__DENALI_PHY_870__PHY_GTLVL_DLY_STEP_3_SHIFT                    8U
#define LPDDR4__DENALI_PHY_870__PHY_GTLVL_DLY_STEP_3_WIDTH                    4U
#define LPDDR4__PHY_GTLVL_DLY_STEP_3__REG DENALI_PHY_870
#define LPDDR4__PHY_GTLVL_DLY_STEP_3__FLD LPDDR4__DENALI_PHY_870__PHY_GTLVL_DLY_STEP_3

#define LPDDR4__DENALI_PHY_870__PHY_GTLVL_RESP_WAIT_CNT_3_MASK       0x001F0000U
#define LPDDR4__DENALI_PHY_870__PHY_GTLVL_RESP_WAIT_CNT_3_SHIFT              16U
#define LPDDR4__DENALI_PHY_870__PHY_GTLVL_RESP_WAIT_CNT_3_WIDTH               5U
#define LPDDR4__PHY_GTLVL_RESP_WAIT_CNT_3__REG DENALI_PHY_870
#define LPDDR4__PHY_GTLVL_RESP_WAIT_CNT_3__FLD LPDDR4__DENALI_PHY_870__PHY_GTLVL_RESP_WAIT_CNT_3

#define LPDDR4__DENALI_PHY_871_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_871_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_871__PHY_GTLVL_BACK_STEP_3_MASK           0x000003FFU
#define LPDDR4__DENALI_PHY_871__PHY_GTLVL_BACK_STEP_3_SHIFT                   0U
#define LPDDR4__DENALI_PHY_871__PHY_GTLVL_BACK_STEP_3_WIDTH                  10U
#define LPDDR4__PHY_GTLVL_BACK_STEP_3__REG DENALI_PHY_871
#define LPDDR4__PHY_GTLVL_BACK_STEP_3__FLD LPDDR4__DENALI_PHY_871__PHY_GTLVL_BACK_STEP_3

#define LPDDR4__DENALI_PHY_871__PHY_GTLVL_FINAL_STEP_3_MASK          0x03FF0000U
#define LPDDR4__DENALI_PHY_871__PHY_GTLVL_FINAL_STEP_3_SHIFT                 16U
#define LPDDR4__DENALI_PHY_871__PHY_GTLVL_FINAL_STEP_3_WIDTH                 10U
#define LPDDR4__PHY_GTLVL_FINAL_STEP_3__REG DENALI_PHY_871
#define LPDDR4__PHY_GTLVL_FINAL_STEP_3__FLD LPDDR4__DENALI_PHY_871__PHY_GTLVL_FINAL_STEP_3

#define LPDDR4__DENALI_PHY_872_READ_MASK                             0x01FF0FFFU
#define LPDDR4__DENALI_PHY_872_WRITE_MASK                            0x01FF0FFFU
#define LPDDR4__DENALI_PHY_872__PHY_WDQLVL_DLY_STEP_3_MASK           0x000000FFU
#define LPDDR4__DENALI_PHY_872__PHY_WDQLVL_DLY_STEP_3_SHIFT                   0U
#define LPDDR4__DENALI_PHY_872__PHY_WDQLVL_DLY_STEP_3_WIDTH                   8U
#define LPDDR4__PHY_WDQLVL_DLY_STEP_3__REG DENALI_PHY_872
#define LPDDR4__PHY_WDQLVL_DLY_STEP_3__FLD LPDDR4__DENALI_PHY_872__PHY_WDQLVL_DLY_STEP_3

#define LPDDR4__DENALI_PHY_872__PHY_WDQLVL_QTR_DLY_STEP_3_MASK       0x00000F00U
#define LPDDR4__DENALI_PHY_872__PHY_WDQLVL_QTR_DLY_STEP_3_SHIFT               8U
#define LPDDR4__DENALI_PHY_872__PHY_WDQLVL_QTR_DLY_STEP_3_WIDTH               4U
#define LPDDR4__PHY_WDQLVL_QTR_DLY_STEP_3__REG DENALI_PHY_872
#define LPDDR4__PHY_WDQLVL_QTR_DLY_STEP_3__FLD LPDDR4__DENALI_PHY_872__PHY_WDQLVL_QTR_DLY_STEP_3

#define LPDDR4__DENALI_PHY_872__PHY_WDQLVL_DM_SEARCH_RANGE_3_MASK    0x01FF0000U
#define LPDDR4__DENALI_PHY_872__PHY_WDQLVL_DM_SEARCH_RANGE_3_SHIFT           16U
#define LPDDR4__DENALI_PHY_872__PHY_WDQLVL_DM_SEARCH_RANGE_3_WIDTH            9U
#define LPDDR4__PHY_WDQLVL_DM_SEARCH_RANGE_3__REG DENALI_PHY_872
#define LPDDR4__PHY_WDQLVL_DM_SEARCH_RANGE_3__FLD LPDDR4__DENALI_PHY_872__PHY_WDQLVL_DM_SEARCH_RANGE_3

#define LPDDR4__DENALI_PHY_873_READ_MASK                             0x00000F01U
#define LPDDR4__DENALI_PHY_873_WRITE_MASK                            0x00000F01U
#define LPDDR4__DENALI_PHY_873__PHY_TOGGLE_PRE_SUPPORT_3_MASK        0x00000001U
#define LPDDR4__DENALI_PHY_873__PHY_TOGGLE_PRE_SUPPORT_3_SHIFT                0U
#define LPDDR4__DENALI_PHY_873__PHY_TOGGLE_PRE_SUPPORT_3_WIDTH                1U
#define LPDDR4__DENALI_PHY_873__PHY_TOGGLE_PRE_SUPPORT_3_WOCLR                0U
#define LPDDR4__DENALI_PHY_873__PHY_TOGGLE_PRE_SUPPORT_3_WOSET                0U
#define LPDDR4__PHY_TOGGLE_PRE_SUPPORT_3__REG DENALI_PHY_873
#define LPDDR4__PHY_TOGGLE_PRE_SUPPORT_3__FLD LPDDR4__DENALI_PHY_873__PHY_TOGGLE_PRE_SUPPORT_3

#define LPDDR4__DENALI_PHY_873__PHY_RDLVL_DLY_STEP_3_MASK            0x00000F00U
#define LPDDR4__DENALI_PHY_873__PHY_RDLVL_DLY_STEP_3_SHIFT                    8U
#define LPDDR4__DENALI_PHY_873__PHY_RDLVL_DLY_STEP_3_WIDTH                    4U
#define LPDDR4__PHY_RDLVL_DLY_STEP_3__REG DENALI_PHY_873
#define LPDDR4__PHY_RDLVL_DLY_STEP_3__FLD LPDDR4__DENALI_PHY_873__PHY_RDLVL_DLY_STEP_3

#define LPDDR4__DENALI_PHY_874_READ_MASK                             0x000003FFU
#define LPDDR4__DENALI_PHY_874_WRITE_MASK                            0x000003FFU
#define LPDDR4__DENALI_PHY_874__PHY_RDLVL_MAX_EDGE_3_MASK            0x000003FFU
#define LPDDR4__DENALI_PHY_874__PHY_RDLVL_MAX_EDGE_3_SHIFT                    0U
#define LPDDR4__DENALI_PHY_874__PHY_RDLVL_MAX_EDGE_3_WIDTH                   10U
#define LPDDR4__PHY_RDLVL_MAX_EDGE_3__REG DENALI_PHY_874
#define LPDDR4__PHY_RDLVL_MAX_EDGE_3__FLD LPDDR4__DENALI_PHY_874__PHY_RDLVL_MAX_EDGE_3

#define LPDDR4__DENALI_PHY_875_READ_MASK                             0x00030703U
#define LPDDR4__DENALI_PHY_875_WRITE_MASK                            0x00030703U
#define LPDDR4__DENALI_PHY_875__PHY_WRPATH_GATE_DISABLE_3_MASK       0x00000003U
#define LPDDR4__DENALI_PHY_875__PHY_WRPATH_GATE_DISABLE_3_SHIFT               0U
#define LPDDR4__DENALI_PHY_875__PHY_WRPATH_GATE_DISABLE_3_WIDTH               2U
#define LPDDR4__PHY_WRPATH_GATE_DISABLE_3__REG DENALI_PHY_875
#define LPDDR4__PHY_WRPATH_GATE_DISABLE_3__FLD LPDDR4__DENALI_PHY_875__PHY_WRPATH_GATE_DISABLE_3

#define LPDDR4__DENALI_PHY_875__PHY_WRPATH_GATE_TIMING_3_MASK        0x00000700U
#define LPDDR4__DENALI_PHY_875__PHY_WRPATH_GATE_TIMING_3_SHIFT                8U
#define LPDDR4__DENALI_PHY_875__PHY_WRPATH_GATE_TIMING_3_WIDTH                3U
#define LPDDR4__PHY_WRPATH_GATE_TIMING_3__REG DENALI_PHY_875
#define LPDDR4__PHY_WRPATH_GATE_TIMING_3__FLD LPDDR4__DENALI_PHY_875__PHY_WRPATH_GATE_TIMING_3

#define LPDDR4__DENALI_PHY_875__PHY_DATA_DC_INIT_DISABLE_3_MASK      0x00030000U
#define LPDDR4__DENALI_PHY_875__PHY_DATA_DC_INIT_DISABLE_3_SHIFT             16U
#define LPDDR4__DENALI_PHY_875__PHY_DATA_DC_INIT_DISABLE_3_WIDTH              2U
#define LPDDR4__PHY_DATA_DC_INIT_DISABLE_3__REG DENALI_PHY_875
#define LPDDR4__PHY_DATA_DC_INIT_DISABLE_3__FLD LPDDR4__DENALI_PHY_875__PHY_DATA_DC_INIT_DISABLE_3

#define LPDDR4__DENALI_PHY_876_READ_MASK                             0x07FF03FFU
#define LPDDR4__DENALI_PHY_876_WRITE_MASK                            0x07FF03FFU
#define LPDDR4__DENALI_PHY_876__PHY_DATA_DC_DQS_INIT_SLV_DELAY_3_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_876__PHY_DATA_DC_DQS_INIT_SLV_DELAY_3_SHIFT        0U
#define LPDDR4__DENALI_PHY_876__PHY_DATA_DC_DQS_INIT_SLV_DELAY_3_WIDTH       10U
#define LPDDR4__PHY_DATA_DC_DQS_INIT_SLV_DELAY_3__REG DENALI_PHY_876
#define LPDDR4__PHY_DATA_DC_DQS_INIT_SLV_DELAY_3__FLD LPDDR4__DENALI_PHY_876__PHY_DATA_DC_DQS_INIT_SLV_DELAY_3

#define LPDDR4__DENALI_PHY_876__PHY_DATA_DC_DQ_INIT_SLV_DELAY_3_MASK 0x07FF0000U
#define LPDDR4__DENALI_PHY_876__PHY_DATA_DC_DQ_INIT_SLV_DELAY_3_SHIFT        16U
#define LPDDR4__DENALI_PHY_876__PHY_DATA_DC_DQ_INIT_SLV_DELAY_3_WIDTH        11U
#define LPDDR4__PHY_DATA_DC_DQ_INIT_SLV_DELAY_3__REG DENALI_PHY_876
#define LPDDR4__PHY_DATA_DC_DQ_INIT_SLV_DELAY_3__FLD LPDDR4__DENALI_PHY_876__PHY_DATA_DC_DQ_INIT_SLV_DELAY_3

#define LPDDR4__DENALI_PHY_877_READ_MASK                             0xFFFF0101U
#define LPDDR4__DENALI_PHY_877_WRITE_MASK                            0xFFFF0101U
#define LPDDR4__DENALI_PHY_877__PHY_DATA_DC_WRLVL_ENABLE_3_MASK      0x00000001U
#define LPDDR4__DENALI_PHY_877__PHY_DATA_DC_WRLVL_ENABLE_3_SHIFT              0U
#define LPDDR4__DENALI_PHY_877__PHY_DATA_DC_WRLVL_ENABLE_3_WIDTH              1U
#define LPDDR4__DENALI_PHY_877__PHY_DATA_DC_WRLVL_ENABLE_3_WOCLR              0U
#define LPDDR4__DENALI_PHY_877__PHY_DATA_DC_WRLVL_ENABLE_3_WOSET              0U
#define LPDDR4__PHY_DATA_DC_WRLVL_ENABLE_3__REG DENALI_PHY_877
#define LPDDR4__PHY_DATA_DC_WRLVL_ENABLE_3__FLD LPDDR4__DENALI_PHY_877__PHY_DATA_DC_WRLVL_ENABLE_3

#define LPDDR4__DENALI_PHY_877__PHY_DATA_DC_WDQLVL_ENABLE_3_MASK     0x00000100U
#define LPDDR4__DENALI_PHY_877__PHY_DATA_DC_WDQLVL_ENABLE_3_SHIFT             8U
#define LPDDR4__DENALI_PHY_877__PHY_DATA_DC_WDQLVL_ENABLE_3_WIDTH             1U
#define LPDDR4__DENALI_PHY_877__PHY_DATA_DC_WDQLVL_ENABLE_3_WOCLR             0U
#define LPDDR4__DENALI_PHY_877__PHY_DATA_DC_WDQLVL_ENABLE_3_WOSET             0U
#define LPDDR4__PHY_DATA_DC_WDQLVL_ENABLE_3__REG DENALI_PHY_877
#define LPDDR4__PHY_DATA_DC_WDQLVL_ENABLE_3__FLD LPDDR4__DENALI_PHY_877__PHY_DATA_DC_WDQLVL_ENABLE_3

#define LPDDR4__DENALI_PHY_877__PHY_DATA_DC_DM_CLK_SE_THRSHLD_3_MASK 0x00FF0000U
#define LPDDR4__DENALI_PHY_877__PHY_DATA_DC_DM_CLK_SE_THRSHLD_3_SHIFT        16U
#define LPDDR4__DENALI_PHY_877__PHY_DATA_DC_DM_CLK_SE_THRSHLD_3_WIDTH         8U
#define LPDDR4__PHY_DATA_DC_DM_CLK_SE_THRSHLD_3__REG DENALI_PHY_877
#define LPDDR4__PHY_DATA_DC_DM_CLK_SE_THRSHLD_3__FLD LPDDR4__DENALI_PHY_877__PHY_DATA_DC_DM_CLK_SE_THRSHLD_3

#define LPDDR4__DENALI_PHY_877__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_3_MASK 0xFF000000U
#define LPDDR4__DENALI_PHY_877__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_3_SHIFT      24U
#define LPDDR4__DENALI_PHY_877__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_3_WIDTH       8U
#define LPDDR4__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_3__REG DENALI_PHY_877
#define LPDDR4__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_3__FLD LPDDR4__DENALI_PHY_877__PHY_DATA_DC_DM_CLK_DIFF_THRSHLD_3

#define LPDDR4__DENALI_PHY_878_READ_MASK                             0x001F7F7FU
#define LPDDR4__DENALI_PHY_878_WRITE_MASK                            0x001F7F7FU
#define LPDDR4__DENALI_PHY_878__PHY_WDQ_OSC_DELTA_3_MASK             0x0000007FU
#define LPDDR4__DENALI_PHY_878__PHY_WDQ_OSC_DELTA_3_SHIFT                     0U
#define LPDDR4__DENALI_PHY_878__PHY_WDQ_OSC_DELTA_3_WIDTH                     7U
#define LPDDR4__PHY_WDQ_OSC_DELTA_3__REG DENALI_PHY_878
#define LPDDR4__PHY_WDQ_OSC_DELTA_3__FLD LPDDR4__DENALI_PHY_878__PHY_WDQ_OSC_DELTA_3

#define LPDDR4__DENALI_PHY_878__PHY_MEAS_DLY_STEP_ENABLE_3_MASK      0x00007F00U
#define LPDDR4__DENALI_PHY_878__PHY_MEAS_DLY_STEP_ENABLE_3_SHIFT              8U
#define LPDDR4__DENALI_PHY_878__PHY_MEAS_DLY_STEP_ENABLE_3_WIDTH              7U
#define LPDDR4__PHY_MEAS_DLY_STEP_ENABLE_3__REG DENALI_PHY_878
#define LPDDR4__PHY_MEAS_DLY_STEP_ENABLE_3__FLD LPDDR4__DENALI_PHY_878__PHY_MEAS_DLY_STEP_ENABLE_3

#define LPDDR4__DENALI_PHY_878__PHY_RDDATA_EN_DLY_3_MASK             0x001F0000U
#define LPDDR4__DENALI_PHY_878__PHY_RDDATA_EN_DLY_3_SHIFT                    16U
#define LPDDR4__DENALI_PHY_878__PHY_RDDATA_EN_DLY_3_WIDTH                     5U
#define LPDDR4__PHY_RDDATA_EN_DLY_3__REG DENALI_PHY_878
#define LPDDR4__PHY_RDDATA_EN_DLY_3__FLD LPDDR4__DENALI_PHY_878__PHY_RDDATA_EN_DLY_3

#define LPDDR4__DENALI_PHY_879_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_879_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_879__PHY_DQ_DM_SWIZZLE0_3_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_879__PHY_DQ_DM_SWIZZLE0_3_SHIFT                    0U
#define LPDDR4__DENALI_PHY_879__PHY_DQ_DM_SWIZZLE0_3_WIDTH                   32U
#define LPDDR4__PHY_DQ_DM_SWIZZLE0_3__REG DENALI_PHY_879
#define LPDDR4__PHY_DQ_DM_SWIZZLE0_3__FLD LPDDR4__DENALI_PHY_879__PHY_DQ_DM_SWIZZLE0_3

#define LPDDR4__DENALI_PHY_880_READ_MASK                             0x0000000FU
#define LPDDR4__DENALI_PHY_880_WRITE_MASK                            0x0000000FU
#define LPDDR4__DENALI_PHY_880__PHY_DQ_DM_SWIZZLE1_3_MASK            0x0000000FU
#define LPDDR4__DENALI_PHY_880__PHY_DQ_DM_SWIZZLE1_3_SHIFT                    0U
#define LPDDR4__DENALI_PHY_880__PHY_DQ_DM_SWIZZLE1_3_WIDTH                    4U
#define LPDDR4__PHY_DQ_DM_SWIZZLE1_3__REG DENALI_PHY_880
#define LPDDR4__PHY_DQ_DM_SWIZZLE1_3__FLD LPDDR4__DENALI_PHY_880__PHY_DQ_DM_SWIZZLE1_3

#define LPDDR4__DENALI_PHY_881_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_881_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_881__PHY_CLK_WRDQ0_SLAVE_DELAY_3_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_881__PHY_CLK_WRDQ0_SLAVE_DELAY_3_SHIFT             0U
#define LPDDR4__DENALI_PHY_881__PHY_CLK_WRDQ0_SLAVE_DELAY_3_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ0_SLAVE_DELAY_3__REG DENALI_PHY_881
#define LPDDR4__PHY_CLK_WRDQ0_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_881__PHY_CLK_WRDQ0_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_881__PHY_CLK_WRDQ1_SLAVE_DELAY_3_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_881__PHY_CLK_WRDQ1_SLAVE_DELAY_3_SHIFT            16U
#define LPDDR4__DENALI_PHY_881__PHY_CLK_WRDQ1_SLAVE_DELAY_3_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ1_SLAVE_DELAY_3__REG DENALI_PHY_881
#define LPDDR4__PHY_CLK_WRDQ1_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_881__PHY_CLK_WRDQ1_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_882_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_882_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_882__PHY_CLK_WRDQ2_SLAVE_DELAY_3_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_882__PHY_CLK_WRDQ2_SLAVE_DELAY_3_SHIFT             0U
#define LPDDR4__DENALI_PHY_882__PHY_CLK_WRDQ2_SLAVE_DELAY_3_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ2_SLAVE_DELAY_3__REG DENALI_PHY_882
#define LPDDR4__PHY_CLK_WRDQ2_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_882__PHY_CLK_WRDQ2_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_882__PHY_CLK_WRDQ3_SLAVE_DELAY_3_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_882__PHY_CLK_WRDQ3_SLAVE_DELAY_3_SHIFT            16U
#define LPDDR4__DENALI_PHY_882__PHY_CLK_WRDQ3_SLAVE_DELAY_3_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ3_SLAVE_DELAY_3__REG DENALI_PHY_882
#define LPDDR4__PHY_CLK_WRDQ3_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_882__PHY_CLK_WRDQ3_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_883_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_883_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_883__PHY_CLK_WRDQ4_SLAVE_DELAY_3_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_883__PHY_CLK_WRDQ4_SLAVE_DELAY_3_SHIFT             0U
#define LPDDR4__DENALI_PHY_883__PHY_CLK_WRDQ4_SLAVE_DELAY_3_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ4_SLAVE_DELAY_3__REG DENALI_PHY_883
#define LPDDR4__PHY_CLK_WRDQ4_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_883__PHY_CLK_WRDQ4_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_883__PHY_CLK_WRDQ5_SLAVE_DELAY_3_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_883__PHY_CLK_WRDQ5_SLAVE_DELAY_3_SHIFT            16U
#define LPDDR4__DENALI_PHY_883__PHY_CLK_WRDQ5_SLAVE_DELAY_3_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ5_SLAVE_DELAY_3__REG DENALI_PHY_883
#define LPDDR4__PHY_CLK_WRDQ5_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_883__PHY_CLK_WRDQ5_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_884_READ_MASK                             0x07FF07FFU
#define LPDDR4__DENALI_PHY_884_WRITE_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_884__PHY_CLK_WRDQ6_SLAVE_DELAY_3_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_884__PHY_CLK_WRDQ6_SLAVE_DELAY_3_SHIFT             0U
#define LPDDR4__DENALI_PHY_884__PHY_CLK_WRDQ6_SLAVE_DELAY_3_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ6_SLAVE_DELAY_3__REG DENALI_PHY_884
#define LPDDR4__PHY_CLK_WRDQ6_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_884__PHY_CLK_WRDQ6_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_884__PHY_CLK_WRDQ7_SLAVE_DELAY_3_MASK     0x07FF0000U
#define LPDDR4__DENALI_PHY_884__PHY_CLK_WRDQ7_SLAVE_DELAY_3_SHIFT            16U
#define LPDDR4__DENALI_PHY_884__PHY_CLK_WRDQ7_SLAVE_DELAY_3_WIDTH            11U
#define LPDDR4__PHY_CLK_WRDQ7_SLAVE_DELAY_3__REG DENALI_PHY_884
#define LPDDR4__PHY_CLK_WRDQ7_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_884__PHY_CLK_WRDQ7_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_885_READ_MASK                             0x03FF07FFU
#define LPDDR4__DENALI_PHY_885_WRITE_MASK                            0x03FF07FFU
#define LPDDR4__DENALI_PHY_885__PHY_CLK_WRDM_SLAVE_DELAY_3_MASK      0x000007FFU
#define LPDDR4__DENALI_PHY_885__PHY_CLK_WRDM_SLAVE_DELAY_3_SHIFT              0U
#define LPDDR4__DENALI_PHY_885__PHY_CLK_WRDM_SLAVE_DELAY_3_WIDTH             11U
#define LPDDR4__PHY_CLK_WRDM_SLAVE_DELAY_3__REG DENALI_PHY_885
#define LPDDR4__PHY_CLK_WRDM_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_885__PHY_CLK_WRDM_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_885__PHY_CLK_WRDQS_SLAVE_DELAY_3_MASK     0x03FF0000U
#define LPDDR4__DENALI_PHY_885__PHY_CLK_WRDQS_SLAVE_DELAY_3_SHIFT            16U
#define LPDDR4__DENALI_PHY_885__PHY_CLK_WRDQS_SLAVE_DELAY_3_WIDTH            10U
#define LPDDR4__PHY_CLK_WRDQS_SLAVE_DELAY_3__REG DENALI_PHY_885
#define LPDDR4__PHY_CLK_WRDQS_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_885__PHY_CLK_WRDQS_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_886_READ_MASK                             0x0003FF03U
#define LPDDR4__DENALI_PHY_886_WRITE_MASK                            0x0003FF03U
#define LPDDR4__DENALI_PHY_886__PHY_WRLVL_THRESHOLD_ADJUST_3_MASK    0x00000003U
#define LPDDR4__DENALI_PHY_886__PHY_WRLVL_THRESHOLD_ADJUST_3_SHIFT            0U
#define LPDDR4__DENALI_PHY_886__PHY_WRLVL_THRESHOLD_ADJUST_3_WIDTH            2U
#define LPDDR4__PHY_WRLVL_THRESHOLD_ADJUST_3__REG DENALI_PHY_886
#define LPDDR4__PHY_WRLVL_THRESHOLD_ADJUST_3__FLD LPDDR4__DENALI_PHY_886__PHY_WRLVL_THRESHOLD_ADJUST_3

#define LPDDR4__DENALI_PHY_886__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_3_MASK 0x0003FF00U
#define LPDDR4__DENALI_PHY_886__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_3_SHIFT        8U
#define LPDDR4__DENALI_PHY_886__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_3_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_3__REG DENALI_PHY_886
#define LPDDR4__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_886__PHY_RDDQS_DQ0_RISE_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_887_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_887_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_887__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_3_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_887__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_3_SHIFT        0U
#define LPDDR4__DENALI_PHY_887__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_3_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_3__REG DENALI_PHY_887
#define LPDDR4__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_887__PHY_RDDQS_DQ0_FALL_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_887__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_3_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_887__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_3_SHIFT       16U
#define LPDDR4__DENALI_PHY_887__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_3_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_3__REG DENALI_PHY_887
#define LPDDR4__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_887__PHY_RDDQS_DQ1_RISE_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_888_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_888_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_888__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_3_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_888__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_3_SHIFT        0U
#define LPDDR4__DENALI_PHY_888__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_3_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_3__REG DENALI_PHY_888
#define LPDDR4__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_888__PHY_RDDQS_DQ1_FALL_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_888__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_3_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_888__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_3_SHIFT       16U
#define LPDDR4__DENALI_PHY_888__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_3_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_3__REG DENALI_PHY_888
#define LPDDR4__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_888__PHY_RDDQS_DQ2_RISE_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_889_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_889_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_889__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_3_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_889__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_3_SHIFT        0U
#define LPDDR4__DENALI_PHY_889__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_3_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_3__REG DENALI_PHY_889
#define LPDDR4__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_889__PHY_RDDQS_DQ2_FALL_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_889__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_3_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_889__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_3_SHIFT       16U
#define LPDDR4__DENALI_PHY_889__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_3_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_3__REG DENALI_PHY_889
#define LPDDR4__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_889__PHY_RDDQS_DQ3_RISE_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_890_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_890_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_890__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_3_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_890__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_3_SHIFT        0U
#define LPDDR4__DENALI_PHY_890__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_3_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_3__REG DENALI_PHY_890
#define LPDDR4__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_890__PHY_RDDQS_DQ3_FALL_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_890__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_3_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_890__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_3_SHIFT       16U
#define LPDDR4__DENALI_PHY_890__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_3_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_3__REG DENALI_PHY_890
#define LPDDR4__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_890__PHY_RDDQS_DQ4_RISE_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_891_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_891_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_891__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_3_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_891__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_3_SHIFT        0U
#define LPDDR4__DENALI_PHY_891__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_3_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_3__REG DENALI_PHY_891
#define LPDDR4__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_891__PHY_RDDQS_DQ4_FALL_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_891__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_3_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_891__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_3_SHIFT       16U
#define LPDDR4__DENALI_PHY_891__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_3_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_3__REG DENALI_PHY_891
#define LPDDR4__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_891__PHY_RDDQS_DQ5_RISE_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_892_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_892_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_892__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_3_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_892__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_3_SHIFT        0U
#define LPDDR4__DENALI_PHY_892__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_3_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_3__REG DENALI_PHY_892
#define LPDDR4__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_892__PHY_RDDQS_DQ5_FALL_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_892__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_3_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_892__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_3_SHIFT       16U
#define LPDDR4__DENALI_PHY_892__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_3_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_3__REG DENALI_PHY_892
#define LPDDR4__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_892__PHY_RDDQS_DQ6_RISE_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_893_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_893_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_893__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_3_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_893__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_3_SHIFT        0U
#define LPDDR4__DENALI_PHY_893__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_3_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_3__REG DENALI_PHY_893
#define LPDDR4__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_893__PHY_RDDQS_DQ6_FALL_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_893__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_3_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_893__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_3_SHIFT       16U
#define LPDDR4__DENALI_PHY_893__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_3_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_3__REG DENALI_PHY_893
#define LPDDR4__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_893__PHY_RDDQS_DQ7_RISE_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_894_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_894_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_894__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_3_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_894__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_3_SHIFT        0U
#define LPDDR4__DENALI_PHY_894__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_3_WIDTH       10U
#define LPDDR4__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_3__REG DENALI_PHY_894
#define LPDDR4__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_894__PHY_RDDQS_DQ7_FALL_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_894__PHY_RDDQS_DM_RISE_SLAVE_DELAY_3_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_894__PHY_RDDQS_DM_RISE_SLAVE_DELAY_3_SHIFT        16U
#define LPDDR4__DENALI_PHY_894__PHY_RDDQS_DM_RISE_SLAVE_DELAY_3_WIDTH        10U
#define LPDDR4__PHY_RDDQS_DM_RISE_SLAVE_DELAY_3__REG DENALI_PHY_894
#define LPDDR4__PHY_RDDQS_DM_RISE_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_894__PHY_RDDQS_DM_RISE_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_895_READ_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PHY_895_WRITE_MASK                            0x03FF03FFU
#define LPDDR4__DENALI_PHY_895__PHY_RDDQS_DM_FALL_SLAVE_DELAY_3_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_895__PHY_RDDQS_DM_FALL_SLAVE_DELAY_3_SHIFT         0U
#define LPDDR4__DENALI_PHY_895__PHY_RDDQS_DM_FALL_SLAVE_DELAY_3_WIDTH        10U
#define LPDDR4__PHY_RDDQS_DM_FALL_SLAVE_DELAY_3__REG DENALI_PHY_895
#define LPDDR4__PHY_RDDQS_DM_FALL_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_895__PHY_RDDQS_DM_FALL_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_895__PHY_RDDQS_GATE_SLAVE_DELAY_3_MASK    0x03FF0000U
#define LPDDR4__DENALI_PHY_895__PHY_RDDQS_GATE_SLAVE_DELAY_3_SHIFT           16U
#define LPDDR4__DENALI_PHY_895__PHY_RDDQS_GATE_SLAVE_DELAY_3_WIDTH           10U
#define LPDDR4__PHY_RDDQS_GATE_SLAVE_DELAY_3__REG DENALI_PHY_895
#define LPDDR4__PHY_RDDQS_GATE_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_895__PHY_RDDQS_GATE_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_896_READ_MASK                             0x03FF070FU
#define LPDDR4__DENALI_PHY_896_WRITE_MASK                            0x03FF070FU
#define LPDDR4__DENALI_PHY_896__PHY_RDDQS_LATENCY_ADJUST_3_MASK      0x0000000FU
#define LPDDR4__DENALI_PHY_896__PHY_RDDQS_LATENCY_ADJUST_3_SHIFT              0U
#define LPDDR4__DENALI_PHY_896__PHY_RDDQS_LATENCY_ADJUST_3_WIDTH              4U
#define LPDDR4__PHY_RDDQS_LATENCY_ADJUST_3__REG DENALI_PHY_896
#define LPDDR4__PHY_RDDQS_LATENCY_ADJUST_3__FLD LPDDR4__DENALI_PHY_896__PHY_RDDQS_LATENCY_ADJUST_3

#define LPDDR4__DENALI_PHY_896__PHY_WRITE_PATH_LAT_ADD_3_MASK        0x00000700U
#define LPDDR4__DENALI_PHY_896__PHY_WRITE_PATH_LAT_ADD_3_SHIFT                8U
#define LPDDR4__DENALI_PHY_896__PHY_WRITE_PATH_LAT_ADD_3_WIDTH                3U
#define LPDDR4__PHY_WRITE_PATH_LAT_ADD_3__REG DENALI_PHY_896
#define LPDDR4__PHY_WRITE_PATH_LAT_ADD_3__FLD LPDDR4__DENALI_PHY_896__PHY_WRITE_PATH_LAT_ADD_3

#define LPDDR4__DENALI_PHY_896__PHY_WRLVL_DELAY_EARLY_THRESHOLD_3_MASK 0x03FF0000U
#define LPDDR4__DENALI_PHY_896__PHY_WRLVL_DELAY_EARLY_THRESHOLD_3_SHIFT      16U
#define LPDDR4__DENALI_PHY_896__PHY_WRLVL_DELAY_EARLY_THRESHOLD_3_WIDTH      10U
#define LPDDR4__PHY_WRLVL_DELAY_EARLY_THRESHOLD_3__REG DENALI_PHY_896
#define LPDDR4__PHY_WRLVL_DELAY_EARLY_THRESHOLD_3__FLD LPDDR4__DENALI_PHY_896__PHY_WRLVL_DELAY_EARLY_THRESHOLD_3

#define LPDDR4__DENALI_PHY_897_READ_MASK                             0x000103FFU
#define LPDDR4__DENALI_PHY_897_WRITE_MASK                            0x000103FFU
#define LPDDR4__DENALI_PHY_897__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_3_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_897__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_3_SHIFT      0U
#define LPDDR4__DENALI_PHY_897__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_3_WIDTH     10U
#define LPDDR4__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_3__REG DENALI_PHY_897
#define LPDDR4__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_3__FLD LPDDR4__DENALI_PHY_897__PHY_WRLVL_DELAY_PERIOD_THRESHOLD_3

#define LPDDR4__DENALI_PHY_897__PHY_WRLVL_EARLY_FORCE_ZERO_3_MASK    0x00010000U
#define LPDDR4__DENALI_PHY_897__PHY_WRLVL_EARLY_FORCE_ZERO_3_SHIFT           16U
#define LPDDR4__DENALI_PHY_897__PHY_WRLVL_EARLY_FORCE_ZERO_3_WIDTH            1U
#define LPDDR4__DENALI_PHY_897__PHY_WRLVL_EARLY_FORCE_ZERO_3_WOCLR            0U
#define LPDDR4__DENALI_PHY_897__PHY_WRLVL_EARLY_FORCE_ZERO_3_WOSET            0U
#define LPDDR4__PHY_WRLVL_EARLY_FORCE_ZERO_3__REG DENALI_PHY_897
#define LPDDR4__PHY_WRLVL_EARLY_FORCE_ZERO_3__FLD LPDDR4__DENALI_PHY_897__PHY_WRLVL_EARLY_FORCE_ZERO_3

#define LPDDR4__DENALI_PHY_898_READ_MASK                             0x000F03FFU
#define LPDDR4__DENALI_PHY_898_WRITE_MASK                            0x000F03FFU
#define LPDDR4__DENALI_PHY_898__PHY_GTLVL_RDDQS_SLV_DLY_START_3_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_898__PHY_GTLVL_RDDQS_SLV_DLY_START_3_SHIFT         0U
#define LPDDR4__DENALI_PHY_898__PHY_GTLVL_RDDQS_SLV_DLY_START_3_WIDTH        10U
#define LPDDR4__PHY_GTLVL_RDDQS_SLV_DLY_START_3__REG DENALI_PHY_898
#define LPDDR4__PHY_GTLVL_RDDQS_SLV_DLY_START_3__FLD LPDDR4__DENALI_PHY_898__PHY_GTLVL_RDDQS_SLV_DLY_START_3

#define LPDDR4__DENALI_PHY_898__PHY_GTLVL_LAT_ADJ_START_3_MASK       0x000F0000U
#define LPDDR4__DENALI_PHY_898__PHY_GTLVL_LAT_ADJ_START_3_SHIFT              16U
#define LPDDR4__DENALI_PHY_898__PHY_GTLVL_LAT_ADJ_START_3_WIDTH               4U
#define LPDDR4__PHY_GTLVL_LAT_ADJ_START_3__REG DENALI_PHY_898
#define LPDDR4__PHY_GTLVL_LAT_ADJ_START_3__FLD LPDDR4__DENALI_PHY_898__PHY_GTLVL_LAT_ADJ_START_3

#define LPDDR4__DENALI_PHY_899_READ_MASK                             0x010F07FFU
#define LPDDR4__DENALI_PHY_899_WRITE_MASK                            0x010F07FFU
#define LPDDR4__DENALI_PHY_899__PHY_WDQLVL_DQDM_SLV_DLY_START_3_MASK 0x000007FFU
#define LPDDR4__DENALI_PHY_899__PHY_WDQLVL_DQDM_SLV_DLY_START_3_SHIFT         0U
#define LPDDR4__DENALI_PHY_899__PHY_WDQLVL_DQDM_SLV_DLY_START_3_WIDTH        11U
#define LPDDR4__PHY_WDQLVL_DQDM_SLV_DLY_START_3__REG DENALI_PHY_899
#define LPDDR4__PHY_WDQLVL_DQDM_SLV_DLY_START_3__FLD LPDDR4__DENALI_PHY_899__PHY_WDQLVL_DQDM_SLV_DLY_START_3

#define LPDDR4__DENALI_PHY_899__PHY_NTP_WRLAT_START_3_MASK           0x000F0000U
#define LPDDR4__DENALI_PHY_899__PHY_NTP_WRLAT_START_3_SHIFT                  16U
#define LPDDR4__DENALI_PHY_899__PHY_NTP_WRLAT_START_3_WIDTH                   4U
#define LPDDR4__PHY_NTP_WRLAT_START_3__REG DENALI_PHY_899
#define LPDDR4__PHY_NTP_WRLAT_START_3__FLD LPDDR4__DENALI_PHY_899__PHY_NTP_WRLAT_START_3

#define LPDDR4__DENALI_PHY_899__PHY_NTP_PASS_3_MASK                  0x01000000U
#define LPDDR4__DENALI_PHY_899__PHY_NTP_PASS_3_SHIFT                         24U
#define LPDDR4__DENALI_PHY_899__PHY_NTP_PASS_3_WIDTH                          1U
#define LPDDR4__DENALI_PHY_899__PHY_NTP_PASS_3_WOCLR                          0U
#define LPDDR4__DENALI_PHY_899__PHY_NTP_PASS_3_WOSET                          0U
#define LPDDR4__PHY_NTP_PASS_3__REG DENALI_PHY_899
#define LPDDR4__PHY_NTP_PASS_3__FLD LPDDR4__DENALI_PHY_899__PHY_NTP_PASS_3

#define LPDDR4__DENALI_PHY_900_READ_MASK                             0x000003FFU
#define LPDDR4__DENALI_PHY_900_WRITE_MASK                            0x000003FFU
#define LPDDR4__DENALI_PHY_900__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_3_MASK 0x000003FFU
#define LPDDR4__DENALI_PHY_900__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_3_SHIFT      0U
#define LPDDR4__DENALI_PHY_900__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_3_WIDTH     10U
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_3__REG DENALI_PHY_900
#define LPDDR4__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_3__FLD LPDDR4__DENALI_PHY_900__PHY_RDLVL_RDDQS_DQ_SLV_DLY_START_3

#define LPDDR4__DENALI_PHY_901_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_901_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_901__PHY_DATA_DC_DQS_CLK_ADJUST_3_MASK    0x000000FFU
#define LPDDR4__DENALI_PHY_901__PHY_DATA_DC_DQS_CLK_ADJUST_3_SHIFT            0U
#define LPDDR4__DENALI_PHY_901__PHY_DATA_DC_DQS_CLK_ADJUST_3_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQS_CLK_ADJUST_3__REG DENALI_PHY_901
#define LPDDR4__PHY_DATA_DC_DQS_CLK_ADJUST_3__FLD LPDDR4__DENALI_PHY_901__PHY_DATA_DC_DQS_CLK_ADJUST_3

#define LPDDR4__DENALI_PHY_901__PHY_DATA_DC_DQ0_CLK_ADJUST_3_MASK    0x0000FF00U
#define LPDDR4__DENALI_PHY_901__PHY_DATA_DC_DQ0_CLK_ADJUST_3_SHIFT            8U
#define LPDDR4__DENALI_PHY_901__PHY_DATA_DC_DQ0_CLK_ADJUST_3_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ0_CLK_ADJUST_3__REG DENALI_PHY_901
#define LPDDR4__PHY_DATA_DC_DQ0_CLK_ADJUST_3__FLD LPDDR4__DENALI_PHY_901__PHY_DATA_DC_DQ0_CLK_ADJUST_3

#define LPDDR4__DENALI_PHY_901__PHY_DATA_DC_DQ1_CLK_ADJUST_3_MASK    0x00FF0000U
#define LPDDR4__DENALI_PHY_901__PHY_DATA_DC_DQ1_CLK_ADJUST_3_SHIFT           16U
#define LPDDR4__DENALI_PHY_901__PHY_DATA_DC_DQ1_CLK_ADJUST_3_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ1_CLK_ADJUST_3__REG DENALI_PHY_901
#define LPDDR4__PHY_DATA_DC_DQ1_CLK_ADJUST_3__FLD LPDDR4__DENALI_PHY_901__PHY_DATA_DC_DQ1_CLK_ADJUST_3

#define LPDDR4__DENALI_PHY_901__PHY_DATA_DC_DQ2_CLK_ADJUST_3_MASK    0xFF000000U
#define LPDDR4__DENALI_PHY_901__PHY_DATA_DC_DQ2_CLK_ADJUST_3_SHIFT           24U
#define LPDDR4__DENALI_PHY_901__PHY_DATA_DC_DQ2_CLK_ADJUST_3_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ2_CLK_ADJUST_3__REG DENALI_PHY_901
#define LPDDR4__PHY_DATA_DC_DQ2_CLK_ADJUST_3__FLD LPDDR4__DENALI_PHY_901__PHY_DATA_DC_DQ2_CLK_ADJUST_3

#define LPDDR4__DENALI_PHY_902_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_902_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_902__PHY_DATA_DC_DQ3_CLK_ADJUST_3_MASK    0x000000FFU
#define LPDDR4__DENALI_PHY_902__PHY_DATA_DC_DQ3_CLK_ADJUST_3_SHIFT            0U
#define LPDDR4__DENALI_PHY_902__PHY_DATA_DC_DQ3_CLK_ADJUST_3_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ3_CLK_ADJUST_3__REG DENALI_PHY_902
#define LPDDR4__PHY_DATA_DC_DQ3_CLK_ADJUST_3__FLD LPDDR4__DENALI_PHY_902__PHY_DATA_DC_DQ3_CLK_ADJUST_3

#define LPDDR4__DENALI_PHY_902__PHY_DATA_DC_DQ4_CLK_ADJUST_3_MASK    0x0000FF00U
#define LPDDR4__DENALI_PHY_902__PHY_DATA_DC_DQ4_CLK_ADJUST_3_SHIFT            8U
#define LPDDR4__DENALI_PHY_902__PHY_DATA_DC_DQ4_CLK_ADJUST_3_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ4_CLK_ADJUST_3__REG DENALI_PHY_902
#define LPDDR4__PHY_DATA_DC_DQ4_CLK_ADJUST_3__FLD LPDDR4__DENALI_PHY_902__PHY_DATA_DC_DQ4_CLK_ADJUST_3

#define LPDDR4__DENALI_PHY_902__PHY_DATA_DC_DQ5_CLK_ADJUST_3_MASK    0x00FF0000U
#define LPDDR4__DENALI_PHY_902__PHY_DATA_DC_DQ5_CLK_ADJUST_3_SHIFT           16U
#define LPDDR4__DENALI_PHY_902__PHY_DATA_DC_DQ5_CLK_ADJUST_3_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ5_CLK_ADJUST_3__REG DENALI_PHY_902
#define LPDDR4__PHY_DATA_DC_DQ5_CLK_ADJUST_3__FLD LPDDR4__DENALI_PHY_902__PHY_DATA_DC_DQ5_CLK_ADJUST_3

#define LPDDR4__DENALI_PHY_902__PHY_DATA_DC_DQ6_CLK_ADJUST_3_MASK    0xFF000000U
#define LPDDR4__DENALI_PHY_902__PHY_DATA_DC_DQ6_CLK_ADJUST_3_SHIFT           24U
#define LPDDR4__DENALI_PHY_902__PHY_DATA_DC_DQ6_CLK_ADJUST_3_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ6_CLK_ADJUST_3__REG DENALI_PHY_902
#define LPDDR4__PHY_DATA_DC_DQ6_CLK_ADJUST_3__FLD LPDDR4__DENALI_PHY_902__PHY_DATA_DC_DQ6_CLK_ADJUST_3

#define LPDDR4__DENALI_PHY_903_READ_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_903_WRITE_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_903__PHY_DATA_DC_DQ7_CLK_ADJUST_3_MASK    0x000000FFU
#define LPDDR4__DENALI_PHY_903__PHY_DATA_DC_DQ7_CLK_ADJUST_3_SHIFT            0U
#define LPDDR4__DENALI_PHY_903__PHY_DATA_DC_DQ7_CLK_ADJUST_3_WIDTH            8U
#define LPDDR4__PHY_DATA_DC_DQ7_CLK_ADJUST_3__REG DENALI_PHY_903
#define LPDDR4__PHY_DATA_DC_DQ7_CLK_ADJUST_3__FLD LPDDR4__DENALI_PHY_903__PHY_DATA_DC_DQ7_CLK_ADJUST_3

#define LPDDR4__DENALI_PHY_903__PHY_DATA_DC_DM_CLK_ADJUST_3_MASK     0x0000FF00U
#define LPDDR4__DENALI_PHY_903__PHY_DATA_DC_DM_CLK_ADJUST_3_SHIFT             8U
#define LPDDR4__DENALI_PHY_903__PHY_DATA_DC_DM_CLK_ADJUST_3_WIDTH             8U
#define LPDDR4__PHY_DATA_DC_DM_CLK_ADJUST_3__REG DENALI_PHY_903
#define LPDDR4__PHY_DATA_DC_DM_CLK_ADJUST_3__FLD LPDDR4__DENALI_PHY_903__PHY_DATA_DC_DM_CLK_ADJUST_3

#define LPDDR4__DENALI_PHY_903__PHY_DSLICE_PAD_BOOSTPN_SETTING_3_MASK 0xFFFF0000U
#define LPDDR4__DENALI_PHY_903__PHY_DSLICE_PAD_BOOSTPN_SETTING_3_SHIFT       16U
#define LPDDR4__DENALI_PHY_903__PHY_DSLICE_PAD_BOOSTPN_SETTING_3_WIDTH       16U
#define LPDDR4__PHY_DSLICE_PAD_BOOSTPN_SETTING_3__REG DENALI_PHY_903
#define LPDDR4__PHY_DSLICE_PAD_BOOSTPN_SETTING_3__FLD LPDDR4__DENALI_PHY_903__PHY_DSLICE_PAD_BOOSTPN_SETTING_3

#define LPDDR4__DENALI_PHY_904_READ_MASK                             0x0003033FU
#define LPDDR4__DENALI_PHY_904_WRITE_MASK                            0x0003033FU
#define LPDDR4__DENALI_PHY_904__PHY_DSLICE_PAD_RX_CTLE_SETTING_3_MASK 0x0000003FU
#define LPDDR4__DENALI_PHY_904__PHY_DSLICE_PAD_RX_CTLE_SETTING_3_SHIFT        0U
#define LPDDR4__DENALI_PHY_904__PHY_DSLICE_PAD_RX_CTLE_SETTING_3_WIDTH        6U
#define LPDDR4__PHY_DSLICE_PAD_RX_CTLE_SETTING_3__REG DENALI_PHY_904
#define LPDDR4__PHY_DSLICE_PAD_RX_CTLE_SETTING_3__FLD LPDDR4__DENALI_PHY_904__PHY_DSLICE_PAD_RX_CTLE_SETTING_3

#define LPDDR4__DENALI_PHY_904__PHY_DQ_FFE_3_MASK                    0x00000300U
#define LPDDR4__DENALI_PHY_904__PHY_DQ_FFE_3_SHIFT                            8U
#define LPDDR4__DENALI_PHY_904__PHY_DQ_FFE_3_WIDTH                            2U
#define LPDDR4__PHY_DQ_FFE_3__REG DENALI_PHY_904
#define LPDDR4__PHY_DQ_FFE_3__FLD LPDDR4__DENALI_PHY_904__PHY_DQ_FFE_3

#define LPDDR4__DENALI_PHY_904__PHY_DQS_FFE_3_MASK                   0x00030000U
#define LPDDR4__DENALI_PHY_904__PHY_DQS_FFE_3_SHIFT                          16U
#define LPDDR4__DENALI_PHY_904__PHY_DQS_FFE_3_WIDTH                           2U
#define LPDDR4__PHY_DQS_FFE_3__REG DENALI_PHY_904
#define LPDDR4__PHY_DQS_FFE_3__FLD LPDDR4__DENALI_PHY_904__PHY_DQS_FFE_3

#endif /* REG_LPDDR4_DATA_SLICE_3_MACROS_H_ */

/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */

