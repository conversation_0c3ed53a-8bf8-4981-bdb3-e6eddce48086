%%{
    let module = system.modules['/drivers/epwm/epwm'];
    let common   = system.getScript("/common");
    let cpuName = system.getScript('/common').getSelfSysCfgCoreName();
%%}
/*
 * EPWM
 */
#include <drivers/epwm.h>
#include <drivers/soc.h>

/* EPWM Instance Macros */
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let config = module.getInstanceConfig(instance);
    % if (["am263x"].includes(common.getSocName())) {
#define `instance.$name`_BASE_ADDR (`config.baseAddr[instance.epwmGroup.slice(-1)]`)
    % }
    % else {
#define `instance.$name.toUpperCase()`_BASE_ADDR (`config.baseAddr`)
#define `instance.$name.toUpperCase()`_FCLK (`config.funcClk`)
#define `instance.$name.toUpperCase()`_INTR (`config.intrNum`)
    % if(config.eventId != undefined) {
#define `instance.$name.toUpperCase()`_EVENT_ID (`config.eventId`)
    % }
    % else {
#define `instance.$name.toUpperCase()`_EVENT_ID HWIP_INVALID_EVENT_ID
    % }
#define `instance.$name.toUpperCase()`_TRIP_INTR (`config.tripIntrNum`)
#define `instance.$name.toUpperCase()`_INTR_IS_PULSE (TRUE)
    % }
% }
#define CONFIG_EPWM_NUM_INSTANCES (`module.$instances.length`U)
