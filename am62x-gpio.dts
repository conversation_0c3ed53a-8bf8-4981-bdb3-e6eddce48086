/*
 * AM62x GPIO设备树配置示例
 * 
 * 展示如何在Linux设备树中配置AM62x GPIO控制器
 */

/ {
    model = "Texas Instruments AM62x SoC";
    compatible = "ti,am62x";
    
    /* 主域GPIO控制器配置 */
    main_gpio0: gpio@600000 {
        compatible = "ti,am62x-gpio";
        reg = <0x0 0x600000 0x0 0x100>;
        gpio-controller;
        #gpio-cells = <2>;
        interrupt-controller;
        #interrupt-cells = <2>;
        interrupts = <GIC_SPI 84 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&k3_clks 77 0>;
        clock-names = "gpio";
        ngpios = <87>;
        ti,ngpio-base = <0>;
        power-domains = <&k3_pds 77 TI_SCI_PD_EXCLUSIVE>;
        
        /* GPIO引脚配置示例 */
        pinctrl-names = "default";
        pinctrl-0 = <&main_gpio0_pins_default>;
        
        /* 状态指示 */
        status = "okay";
    };
    
    main_gpio1: gpio@601000 {
        compatible = "ti,am62x-gpio";
        reg = <0x0 0x601000 0x0 0x100>;
        gpio-controller;
        #gpio-cells = <2>;
        interrupt-controller;
        #interrupt-cells = <2>;
        interrupts = <GIC_SPI 85 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&k3_clks 78 0>;
        clock-names = "gpio";
        ngpios = <88>;
        ti,ngpio-base = <87>;
        power-domains = <&k3_pds 78 TI_SCI_PD_EXCLUSIVE>;
        status = "okay";
    };
    
    /* MCU域GPIO控制器配置 */
    mcu_gpio0: gpio@4201000 {
        compatible = "ti,am62x-mcu-gpio";
        reg = <0x0 0x4201000 0x0 0x100>;
        gpio-controller;
        #gpio-cells = <2>;
        interrupt-controller;
        #interrupt-cells = <2>;
        interrupts = <GIC_SPI 182 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&k3_clks 79 0>;
        clock-names = "gpio";
        ngpios = <24>;
        ti,ngpio-base = <175>;
        power-domains = <&k3_pds 79 TI_SCI_PD_EXCLUSIVE>;
        status = "okay";
    };
    
    /* 引脚复用配置 */
    main_pmx0: pinctrl@f4000 {
        compatible = "pinctrl-single";
        reg = <0x0 0xf4000 0x0 0x2ac>;
        #pinctrl-cells = <1>;
        pinctrl-single,register-width = <32>;
        pinctrl-single,function-mask = <0xffffffff>;
        
        /* GPIO引脚配置 */
        main_gpio0_pins_default: main-gpio0-pins-default {
            pinctrl-single,pins = <
                /* GPIO0_0 - 输出模式，用于LED控制 */
                AM62X_IOPAD(0x01a4, PIN_OUTPUT, 7) /* (B20) MCASP0_ACLKX.GPIO0_0 */
                /* GPIO0_1 - 输入模式，用于按键检测 */
                AM62X_IOPAD(0x01a8, PIN_INPUT, 7)  /* (D20) MCASP0_AFSX.GPIO0_1 */
                /* GPIO0_2 - 输出模式，用于继电器控制 */
                AM62X_IOPAD(0x01ac, PIN_OUTPUT, 7) /* (E20) MCASP0_AXR0.GPIO0_2 */
            >;
        };
        
        main_gpio1_pins_default: main-gpio1-pins-default {
            pinctrl-single,pins = <
                /* GPIO1_0 - 输入模式，带上拉电阻 */
                AM62X_IOPAD(0x01b0, PIN_INPUT_PULLUP, 7) /* (A20) MCASP0_AXR1.GPIO1_0 */
                /* GPIO1_1 - 输出模式，开漏输出 */
                AM62X_IOPAD(0x01b4, PIN_OUTPUT_PULLUP, 7) /* (B21) MCASP0_AXR2.GPIO1_1 */
            >;
        };
        
        mcu_gpio0_pins_default: mcu-gpio0-pins-default {
            pinctrl-single,pins = <
                /* MCU_GPIO0_0 - 输出模式，用于电源控制 */
                AM62X_MCU_IOPAD(0x0004, PIN_OUTPUT, 7) /* (B22) MCU_SPI0_CS1.MCU_GPIO0_0 */
                /* MCU_GPIO0_1 - 输入模式，用于状态检测 */
                AM62X_MCU_IOPAD(0x0008, PIN_INPUT, 7)  /* (A22) MCU_SPI0_CLK.MCU_GPIO0_1 */
            >;
        };
    };
    
    /* GPIO使用示例 */
    leds {
        compatible = "gpio-leds";
        
        led0 {
            label = "am62x:green:user0";
            gpios = <&main_gpio0 0 GPIO_ACTIVE_HIGH>;
            default-state = "off";
            linux,default-trigger = "heartbeat";
        };
        
        led1 {
            label = "am62x:red:user1";
            gpios = <&main_gpio0 2 GPIO_ACTIVE_HIGH>;
            default-state = "off";
            linux,default-trigger = "none";
        };
        
        power_led {
            label = "am62x:blue:power";
            gpios = <&mcu_gpio0 0 GPIO_ACTIVE_HIGH>;
            default-state = "on";
            linux,default-trigger = "default-on";
        };
    };
    
    gpio_keys {
        compatible = "gpio-keys";
        autorepeat;
        
        user_button {
            label = "User Button";
            linux,code = <KEY_ENTER>;
            gpios = <&main_gpio0 1 GPIO_ACTIVE_LOW>;
            debounce-interval = <50>;
            wakeup-source;
        };
        
        reset_button {
            label = "Reset Button";
            linux,code = <KEY_ESC>;
            gpios = <&main_gpio1 0 GPIO_ACTIVE_LOW>;
            debounce-interval = <100>;
        };
    };
    
    /* GPIO控制的继电器 */
    relay_control {
        compatible = "gpio-relay";
        relay-gpios = <&main_gpio1 1 GPIO_ACTIVE_HIGH>;
        relay-delay-ms = <100>;
        status = "okay";
    };
    
    /* GPIO控制的电源管理 */
    power_control {
        compatible = "gpio-poweroff";
        gpios = <&mcu_gpio0 1 GPIO_ACTIVE_HIGH>;
        input;
        timeout-ms = <3000>;
    };
};

/* GPIO引脚定义宏 */
#define AM62X_IOPAD(pa, val, muxmode) (((pa) & 0x1fff)) ((val) | (muxmode))
#define AM62X_MCU_IOPAD(pa, val, muxmode) (((pa) & 0x1fff)) ((val) | (muxmode))

/* 引脚配置值定义 */
#define PIN_OUTPUT              (0 << 17)
#define PIN_INPUT               (1 << 17)
#define PIN_INPUT_PULLUP        ((1 << 17) | (1 << 16))
#define PIN_INPUT_PULLDOWN      ((1 << 17) | (0 << 16))
#define PIN_OUTPUT_PULLUP       ((0 << 17) | (1 << 16))
#define PIN_OUTPUT_PULLDOWN     ((0 << 17) | (0 << 16))

/* GPIO激活电平定义 */
#define GPIO_ACTIVE_HIGH        0
#define GPIO_ACTIVE_LOW         1

/* 按键码定义 */
#define KEY_ENTER               28
#define KEY_ESC                 1

/* 中断类型定义 */
#define IRQ_TYPE_LEVEL_HIGH     4
#define IRQ_TYPE_LEVEL_LOW      8
#define IRQ_TYPE_EDGE_RISING    1
#define IRQ_TYPE_EDGE_FALLING   2
#define IRQ_TYPE_EDGE_BOTH      3

/* SoC集成器定义 */
#define GIC_SPI                 0

/* 电源域和时钟定义 */
#define TI_SCI_PD_EXCLUSIVE     0
