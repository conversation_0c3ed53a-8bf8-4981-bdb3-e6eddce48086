
{
	"epwm" : [
		// Time Base Submodule
		{
			"name" :  "epwmTimebase_emulationMode",
			"driverlibFunction" : "EPWM_setEmulationMode",
			"driverlibFunctionArg" : {
				"base" : "",
				"emulationMode" : "this"
			},
			"register": "TBCTL.FREE_SOFT",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTimebase_clockDiv",
			"driverlibFunction" : "EPWM_setClockPrescaler",
			"driverlibFunctionArg" : {
				"base" : "",
				"prescaler" : "this",
				"highSpeedPrescaler" : ""
			},
			"register": "TBCTL.CLKDIV",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTimebase_hsClockDiv",
			"driverlibFunction" : "EPWM_setClockPrescaler",
			"driverlibFunctionArg" : {
				"base" : "",
				"prescaler" : "",
				"highSpeedPrescaler" : "this"
			},
			"register": "TBCTL.HSPCLKDIV",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTimebase_period",
			"driverlibFunction" : "EPWM_setTimeBasePeriod",
			"driverlibFunctionArg" : {
				"base" : "",
				"periodCount" : "this"
			},
			"register": "TBPRD",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTimebase_periodLink",
			"driverlibFunction" : "EPWM_setupEPWMLinks",
			"driverlibFunctionArg" : {
				"base" : "",
				"epwmLink" : "this",
				"linkComp" : "EPWM_LINK_TBPRD"
			},
			"register": "EPWMXLINK.TBPRDLINK",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTimebase_periodGld",
			"driverlibFunction" : "EPWM_enableGlobalLoadRegisters",
			"driverlibFunctionAlt" : "EPWM_disableGlobalLoadRegisters",
			"driverlibFunctionArg" : {
				"base" : "",
				"loadRegister" : "EPWM_GL_REGISTER_TBPRD_TBPRDHR"
			},
			"register": "GLDCFG.TBPRD_TBPRDHR",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTimebase_periodLoadMode",
			"driverlibFunction" : "EPWM_setPeriodLoadMode",
			"driverlibFunctionArg" : {
				"base" : "",
				"loadMode" : "this"
			},
			"register": "TBCTL.PRDLD",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTimebase_counterValue",
			"driverlibFunction" : "EPWM_setTimeBaseCounter",
			"driverlibFunctionArg" : {
				"base" : "",
				"loadMode" : "this"
			},
			"register": "TBCTL.TBCTR",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTimebase_counterMode",
			"driverlibFunction" : "EPWM_setTimeBaseCounterMode",
			"driverlibFunctionArg" : {
				"base" : "",
				"counterMode" : "this"
			},
			"register": "TBCTL.CTRMODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTimebase_counterModeAfterSync",
			"driverlibFunction" : "EPWM_setCountModeAfterSync",
			"driverlibFunctionArg" : {
				"base" : "",
				"mode" : "this"
			},
			"register": "TBCTL.PHSDIR",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmTimebase_phaseEnable",
			"driverlibFunction" : "EPWM_enablePhaseShiftLoad",
			"driverlibFunctionAlt" : "EPWM_disablePhaseShiftLoad",
			"driverlibFunctionArg" : {
		        "base" : ""
			},
			"register": "TBCTL.PHSEN",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTimebase_phaseShift",
		    "driverlibFunction" : "EPWM_setPhaseShift",
			"driverlibFunctionArg" : {
				"base" : "",
				"phaseCount" : "this"
			},
			"register": "TBPHS.TBPHS",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTimebase_forceSyncPulse",
		    "driverlibFunction" : "EPWM_forceSyncPulse",
			"driverlibFunctionAlt" : "",
			"driverlibFunctionArg" : {
		        "base" : ""
			},
			"register": "TBCTL.SWFSYNC",
			"devices" : [
                "am263x"
			]
		},

        // Counter Compare Submodule

        // CMPA
        {
			"name" :  "epwmCounterCompare_cmpA",
		    "driverlibFunction" : "EPWM_setCounterCompareValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"compModule" : "EPWM_COUNTER_COMPARE_A",
				"compCount": "this"
			},
			"register": "CMPA.CMPA",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmCounterCompare_cmpAGld",
			"driverlibFunction" : "EPWM_enableGlobalLoadRegisters",
			"driverlibFunctionAlt" : "EPWM_disableGlobalLoadRegisters",
			"driverlibFunctionArg" : {
				"base" : "",
				"loadRegister" : "EPWM_GL_REGISTER_CMPA_CMPAHR",
			},
			"register": "GLDCFG.CMPA_CMPAHR",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmCounterCompare_enableShadowLoadModeCMPA",
		    "driverlibFunction" : "",
			"driverlibFunctionAlt" : "EPWM_disableCounterCompareShadowLoadMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "compModule" : "EPWM_COUNTER_COMPARE_A"
			},
			"register": "CMPCTL.SHDWAMODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmCounterCompare_shadowLoadModeCMPA",
			"driverlibFunction" : "EPWM_setCounterCompareShadowLoadMode",
			"driverlibFunctionArg" : {
				"base" : "",
				"compModule" : "EPWM_COUNTER_COMPARE_A",
				"loadMode" : "this"
			},
			"register": "CMPCTL.LOADASYNC",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmCounterCompare_cmpALink",
			"driverlibFunction" : "EPWM_setupEPWMLinks",
			"driverlibFunctionArg" : {
				"base" : "",
				"epwmLink" : "this",
				"linkComp" : "EPWM_LINK_COMP_A"
			},
			"register": "EPWMXLINK.CMPALINK",
			"devices" : [
                "am263x"
			]
		},

		// CMPB
        {
			"name" :  "epwmCounterCompare_cmpB",
		    "driverlibFunction" : "EPWM_setCounterCompareValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"compModule" : "EPWM_COUNTER_COMPARE_B",
				"compCount": "this"
			},
			"register": "CMPB.CMPB",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmCounterCompare_cmpBGld",
			"driverlibFunction" : "EPWM_enableGlobalLoadRegisters",
			"driverlibFunctionAlt" : "EPWM_disableGlobalLoadRegisters",
			"driverlibFunctionArg" : {
				"base" : "",
				"loadRegister" : "EPWM_GL_REGISTER_CMPB_CMPBHR",
			},
			"register": "GLDCFG.CMPB_CMPBHR",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmCounterCompare_enableShadowLoadModeCMPB",
		    "driverlibFunction" : "",
			"driverlibFunctionAlt" : "EPWM_disableCounterCompareShadowLoadMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "compModule" : "EPWM_COUNTER_COMPARE_B"
			},
			"register": "CMPCTL.SHDWBMODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmCounterCompare_shadowLoadModeCMPB",
			"driverlibFunction" : "EPWM_setCounterCompareShadowLoadMode",
			"driverlibFunctionArg" : {
				"base" : "",
				"compModule" : "EPWM_COUNTER_COMPARE_B",
				"loadMode" : "this"
			},
			"register": "CMPCTL.LOADBSYNC",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmCounterCompare_cmpBLink",
			"driverlibFunction" : "EPWM_setupEPWMLinks",
			"driverlibFunctionArg" : {
				"base" : "",
				"epwmLink" : "this",
				"linkComp" : "EPWM_LINK_COMP_B"
			},
			"register": "EPWMXLINK.CMPBLINK",
			"devices" : [
                "am263x"
			]
		},

		// CMPC
        {
			"name" :  "epwmCounterCompare_cmpC",
		    "driverlibFunction" : "EPWM_setCounterCompareValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"compModule" : "EPWM_COUNTER_COMPARE_C",
				"compCount": "this"
			},
			"register": "CMPC.CMPC",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmCounterCompare_cmpCGld",
			"driverlibFunction" : "EPWM_enableGlobalLoadRegisters",
			"driverlibFunctionAlt" : "EPWM_disableGlobalLoadRegisters",
			"driverlibFunctionArg" : {
				"base" : "",
				"loadRegister" : "EPWM_GL_REGISTER_CMPC",
			},
			"register": "GLDCFG.CMPC_CMPCHR",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmCounterCompare_enableShadowLoadModeCMPC",
		    "driverlibFunction" : "",
			"driverlibFunctionAlt" : "EPWM_disableCounterCompareShadowLoadMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "compModule" : "EPWM_COUNTER_COMPARE_C"
			},
			"register": "CMPCTL.SHDWAMODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmCounterCompare_shadowLoadModeCMPC",
			"driverlibFunction" : "EPWM_setCounterCompareShadowLoadMode",
			"driverlibFunctionArg" : {
				"base" : "",
				"compModule" : "EPWM_COUNTER_COMPARE_C",
				"loadMode" : "this"
			},
			"register": "CMPCTL.LOADCSYNC",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmCounterCompare_cmpCLink",
			"driverlibFunction" : "EPWM_setupEPWMLinks",
			"driverlibFunctionArg" : {
				"base" : "",
				"epwmLink" : "this",
				"linkComp" : "EPWM_LINK_COMP_C"
			},
			"register": "EPWMXLINK.CMPCLINK",
			"devices" : [
                "am263x"
			]
		},

		// CMPD
        {
			"name" :  "epwmCounterCompare_cmpD",
		    "driverlibFunction" : "EPWM_setCounterCompareValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"compModule" : "EPWM_COUNTER_COMPARE_D",
				"compCount": "this"
			},
			"register": "CMPD.CMPD",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmCounterCompare_cmpDGld",
			"driverlibFunction" : "EPWM_enableGlobalLoadRegisters",
			"driverlibFunctionAlt" : "EPWM_disableGlobalLoadRegisters",
			"driverlibFunctionArg" : {
				"base" : "",
				"loadRegister" : "EPWM_GL_REGISTER_CMPD",
			},
			"register": "GLDCFG.CMPD_CMPDHR",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmCounterCompare_enableShadowLoadModeCMPD",
		    "driverlibFunction" : "",
			"driverlibFunctionAlt" : "EPWM_disableCounterCompareShadowLoadMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "compModule" : "EPWM_COUNTER_COMPARE_D"
			},
			"register": "CMPCTL.SHDWDMODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmCounterCompare_shadowLoadModeCMPD",
			"driverlibFunction" : "EPWM_setCounterCompareShadowLoadMode",
			"driverlibFunctionArg" : {
				"base" : "",
				"compModule" : "EPWM_COUNTER_COMPARE_D",
				"loadMode" : "this"
			},
			"register": "CMPCTL.LOADDSYNC",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmCounterCompare_cmpDLink",
			"driverlibFunction" : "EPWM_setupEPWMLinks",
			"driverlibFunctionArg" : {
				"base" : "",
				"epwmLink" : "this",
				"linkComp" : "EPWM_LINK_COMP_D"
			},
			"register": "EPWMXLINK.CMPDLINK",
			"devices" : [
                "am263x"
			]
		},

		// Action Qualifier
		{
			"name" :  "epwmActionQualifier_continousSwForceReloadModeGld",
			"driverlibFunction" : "EPWM_enableGlobalLoadRegisters",
			"driverlibFunctionAlt" : "EPWM_disableGlobalLoadRegisters",
			"driverlibFunctionArg" : {
				"base" : "",
				"loadRegister" : "EPWM_GL_REGISTER_AQCSFRC",
			},
			"register": "GLDCFG.AQCSFRC",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_continousSwForceReloadMode",
			"driverlibFunction" : "EPWM_setActionQualifierContSWForceShadowMode",
			"driverlibFunctionArg" : {
				"base" : "",
				"mode" : "this",
			},
			"register": "AQSFRC.RLDCSF",
			"devices" : [
                "am263x"
			]
		},

		// EPWM A Output
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_A_gld",
			"driverlibFunction" : "EPWM_enableGlobalLoadRegisters",
			"driverlibFunctionAlt" : "EPWM_disableGlobalLoadRegisters",
			"driverlibFunctionArg" : {
				"base" : "",
				"loadRegister" : "EPWM_GL_REGISTER_AQCTLA_AQCTLA2",
			},
			"register": "GLDCFG.ACQCTLA_AQCTLA2",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_A_shadowMode",
			"driverlibFunction" : "",
			"driverlibFunctionAlt" : "EPWM_disableActionQualifierShadowLoadMode",
			"driverlibFunctionArg" : {
				"base" : "",
				"aqModule": "EPWM_ACTION_QUALIFIER_A",
			},
			"register": "AQCTL.SHDWAQAMODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_A_shadowEvent",
			"driverlibFunction" : "EPWM_setActionQualifierShadowLoadMode",
			"driverlibFunctionArg" : {
				"base" : "",
				"aqModule": "EPWM_ACTION_QUALIFIER_A",
				"loadMode" : "this",
			},
			"register": "ACQTL.LDAQASYNC",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_A_t1Source",
			"driverlibFunction" : "EPWM_setActionQualifierT1TriggerSource",
			"driverlibFunctionArg" : {
				"base" : "",
				"trigger": "this",
			},
			"register": "AQTSRCSEL.T1SEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_A_t2Source",
			"driverlibFunction" : "EPWM_setActionQualifierT2TriggerSource",
			"driverlibFunctionArg" : {
				"base" : "",
				"trigger": "this",
			},
			"register": "AQTSRCSEL.T2SEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_A_onetimeSwForceAction",
			"driverlibFunction" : "EPWM_setActionQualifierSWAction",
			"driverlibFunctionArg" : {
				"base" : "",
				"epwmOutput": "EPWM_AQ_OUTPUT_A",
				"output" : "this",
			},
			"register": "AQSFRC.ACTSFA",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_A_continuousSwForceAction",
			"driverlibFunction" : "EPWM_setActionQualifierContSWForceAction",
			"driverlibFunctionArg" : {
				"base" : "",
				"epwmOutput": "EPWM_AQ_OUTPUT_A",
				"output" : "this",
			},
			"register": "AQSFRC.CSFA",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_ZERO",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_ZERO"
			},
			"register": "AQCTLA.ZRO",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_PERIOD",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_PERIOD"
			},
			"register": "AQCTLA.PRD",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_UP_CMPA",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_UP_CMPA"
			},
			"register": "AQCTLA.CAU",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_DOWN_CMPA",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_DOWN_CMPA"
			},
			"register": "AQCTLA.CAD",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_UP_CMPB",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_UP_CMPB"
			},
			"register": "AQCTLA.CBU",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_DOWN_CMPB",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_DOWN_CMPB"
			},
			"register": "AQCTLA.CBD",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_T1_COUNT_UP",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_T1_COUNT_UP"
			},
			"register": "AQCTLA2.T1U",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_T1_COUNT_DOWN",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_T1_COUNT_DOWN"
			},
			"register": "AQCTLA2.T1D",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_T2_COUNT_UP",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_T2_COUNT_UP"
			},
			"register": "AQCTLA2.T2U",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_A_ON_T2_COUNT_DOWN",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_T2_COUNT_DOWN"
			},
			"register": "AQCTLA2.T2D",
			"devices" : [
                "am263x"
			]
		},

		// EPWM B Output
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_B_gld",
			"driverlibFunction" : "EPWM_enableGlobalLoadRegisters",
			"driverlibFunctionAlt" : "EPWM_disableGlobalLoadRegisters",
			"driverlibFunctionArg" : {
				"base" : "",
				"loadRegister" : "EPWM_GL_REGISTER_AQCTLB_AQCTLB2",
			},
			"register": "GLDCFG.ACQCTLB_AQCTLB2",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_B_shadowMode",
			"driverlibFunction" : "",
			"driverlibFunctionAlt" : "EPWM_disableActionQualifierShadowLoadMode",
			"driverlibFunctionArg" : {
				"base" : "",
				"aqModule": "EPWM_ACTION_QUALIFIER_B",
			},
			"register": "AQCTL.SHDWAQBMODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_B_shadowEvent",
			"driverlibFunction" : "EPWM_setActionQualifierShadowLoadMode",
			"driverlibFunctionArg" : {
				"base" : "",
				"aqModule": "EPWM_ACTION_QUALIFIER_B",
				"loadMode" : "this",
			},
			"register": "ACQTL.LDAQBSYNC",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_B_t1Source",
			"driverlibFunction" : "EPWM_setActionQualifierT1TriggerSource",
			"driverlibFunctionArg" : {
				"base" : "",
				"trigger": "this",
			},
			"register": "AQTSRCSEL.T1SEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_B_t2Source",
			"driverlibFunction" : "EPWM_setActionQualifierT2TriggerSource",
			"driverlibFunctionArg" : {
				"base" : "",
				"trigger": "this",
			},
			"register": "AQTSRCSEL.T2SEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_B_onetimeSwForceAction",
			"driverlibFunction" : "EPWM_setActionQualifierSWAction",
			"driverlibFunctionArg" : {
				"base" : "",
				"epwmOutput": "EPWM_AQ_OUTPUT_B",
				"output" : "this",
			},
			"register": "AQSFRC.ACTSFB",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_B_continuousSwForceAction",
			"driverlibFunction" : "EPWM_setActionQualifierContSWForceAction",
			"driverlibFunctionArg" : {
				"base" : "",
				"epwmOutput": "EPWM_AQ_OUTPUT_B",
				"output" : "this",
			},
			"register": "AQSFRC.CSFB",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_ZERO",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_ZERO"
			},
			"register": "AQCTLB.ZRO",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_PERIOD",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_PERIOD"
			},
			"register": "AQCTLB.PRD",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_UP_CMPA",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_UP_CMPA"
			},
			"register": "AQCTLB.CAU",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_DOWN_CMPA",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_DOWN_CMPA"
			},
			"register": "AQCTLB.CAD",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_UP_CMPB",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_UP_CMPB"
			},
			"register": "AQCTLB.CBU",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_DOWN_CMPB",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_DOWN_CMPB"
			},
			"register": "AQCTLB.CBD",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_B_ON_T1_COUNT_UP",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_T1_COUNT_UP"
			},
			"register": "AQCTLB2.T1U",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_B_ON_T1_COUNT_DOWN",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_T1_COUNT_DOWN"
			},
			"register": "AQCTLB2.T1D",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_B_ON_T2_COUNT_UP",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_T2_COUNT_UP"
			},
			"register": "AQCTLB2.T2U",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmActionQualifier_EPWM_AQ_OUTPUT_B_ON_T2_COUNT_DOWN",
			"driverlibFunction" : "EPWM_setActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_T2_COUNT_DOWN"
			},
			"register": "AQCTLB2.T2D",
			"devices" : [
                "am263x"
			]
		},

		// Trip Zone Submodule
		{
			"name" :  "epwmTripZone_useAdvancedEPWMTripZoneActions",
			"driverlibFunction" : "EPWM_enableTripZoneAdvAction",
			"driverlibFunctionAlt" : "EPWM_disableTripZoneAdvAction",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "TZCTL2.ETZE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ADV_ACTION_EVENT_TZB_D",
			"driverlibFunction" : "EPWM_setTripZoneAdvAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzAdvEvent" : "EPWM_TZ_ADV_ACTION_EVENT_TZB_D",
		        "tzAdvAction" : "this",
			},
			"register": "TZCTL2.TZBD",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ADV_ACTION_EVENT_TZB_U",
			"driverlibFunction" : "EPWM_setTripZoneAdvAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzAdvEvent" : "EPWM_TZ_ADV_ACTION_EVENT_TZB_U",
		        "tzAdvAction" : "this",
			},
			"register": "TZCTL2.TZBU",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ADV_ACTION_EVENT_TZA_D",
			"driverlibFunction" : "EPWM_setTripZoneAdvAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzAdvEvent" : "EPWM_TZ_ADV_ACTION_EVENT_TZA_D",
		        "tzAdvAction" : "this",
			},
			"register": "TZCTL2.TZAD",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ADV_ACTION_EVENT_TZA_U",
			"driverlibFunction" : "EPWM_setTripZoneAdvAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzAdvEvent" : "EPWM_TZ_ADV_ACTION_EVENT_TZA_U",
		        "tzAdvAction" : "this",
			},
			"register": "TZCTL2.TZAU",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ADV_ACTION_EVENT_DCxEVT1_U_A",
			"driverlibFunction" : "EPWM_setTripZoneAdvDigitalCompareActionA",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzAdvDCEvent" : "EPWM_TZ_ADV_ACTION_EVENT_DCxEVT1_U",
		        "tzAdvDCAction" : "this",
			},
			"register": "TZCTLDCA.DCAEVT1U",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ADV_ACTION_EVENT_DCxEVT1_D_A",
			"driverlibFunction" : "EPWM_setTripZoneAdvDigitalCompareActionA",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzAdvDCEvent" : "EPWM_TZ_ADV_ACTION_EVENT_DCxEVT1_D",
		        "tzAdvDCAction" : "this",
			},
			"register": "TZCTLDCA.DCAEVT1D",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ADV_ACTION_EVENT_DCxEVT2_U_A",
			"driverlibFunction" : "EPWM_setTripZoneAdvDigitalCompareActionA",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzAdvDCEvent" : "EPWM_TZ_ADV_ACTION_EVENT_DCxEVT2_U",
		        "tzAdvDCAction" : "this",
			},
			"register": "TZCTLDCA.DCAEVT2U",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ADV_ACTION_EVENT_DCxEVT2_D_A",
			"driverlibFunction" : "EPWM_setTripZoneAdvDigitalCompareActionA",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzAdvDCEvent" : "EPWM_TZ_ADV_ACTION_EVENT_DCxEVT2_D",
		        "tzAdvDCAction" : "this",
			},
			"register": "TZCTLDCA.DCAEVT2D",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ADV_ACTION_EVENT_DCxEVT1_U_B",
			"driverlibFunction" : "EPWM_setTripZoneAdvDigitalCompareActionB",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzAdvDCEvent" : "EPWM_TZ_ADV_ACTION_EVENT_DCxEVT1_U",
		        "tzAdvDCAction" : "this",
			},
			"register": "TZCTLDCB.DCBEVT1U",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ADV_ACTION_EVENT_DCxEVT1_D_B",
			"driverlibFunction" : "EPWM_setTripZoneAdvDigitalCompareActionB",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzAdvDCEvent" : "EPWM_TZ_ADV_ACTION_EVENT_DCxEVT1_D",
		        "tzAdvDCAction" : "this",
			},
			"register": "TZCTLDCB.DCBEVT1D",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ADV_ACTION_EVENT_DCxEVT2_U_B",
			"driverlibFunction" : "EPWM_setTripZoneAdvDigitalCompareActionB",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzAdvDCEvent" : "EPWM_TZ_ADV_ACTION_EVENT_DCxEVT2_U",
		        "tzAdvDCAction" : "this",
			},
			"register": "TZCTLDCB.DCBEVT2U",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ADV_ACTION_EVENT_DCxEVT2_D_B",
			"driverlibFunction" : "EPWM_setTripZoneAdvDigitalCompareActionB",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzAdvDCEvent" : "EPWM_TZ_ADV_ACTION_EVENT_DCxEVT2_D",
		        "tzAdvDCAction" : "this",
			},
			"register": "TZCTLDCB.DCBEVT2D",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ACTION_EVENT_TZA",
			"driverlibFunction" : "EPWM_setTripZoneAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzEvent" : "EPWM_TZ_ACTION_EVENT_TZA",
		        "tzAction" : "this",
			},
			"register": "TZCTL.TZA",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ACTION_EVENT_TZB",
			"driverlibFunction" : "EPWM_setTripZoneAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzEvent" : "EPWM_TZ_ACTION_EVENT_TZB",
		        "tzAction" : "this",
			},
			"register": "TZCTL.TZB",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ACTION_EVENT_DCAEVT1",
			"driverlibFunction" : "EPWM_setTripZoneAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzEvent" : "EPWM_TZ_ACTION_EVENT_DCAEVT1",
		        "tzAction" : "this",
			},
			"register": "TZCTL.DCAEVT1",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ACTION_EVENT_DCAEVT2",
			"driverlibFunction" : "EPWM_setTripZoneAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzEvent" : "EPWM_TZ_ACTION_EVENT_DCAEVT2",
		        "tzAction" : "this",
			},
			"register": "TZCTL.DCAEVT2",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ACTION_EVENT_DCBEVT1",
			"driverlibFunction" : "EPWM_setTripZoneAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzEvent" : "EPWM_TZ_ACTION_EVENT_DCBEVT1",
		        "tzAction" : "this",
			},
			"register": "TZCTL.DCBEVT1",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_EPWM_TZ_ACTION_EVENT_DCBEVT2",
			"driverlibFunction" : "EPWM_setTripZoneAction",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzEvent" : "EPWM_TZ_ACTION_EVENT_DCBEVT2",
		        "tzAction" : "this",
			},
			"register": "TZCTL.DCBEVT2",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_oneShotSource",
			"driverlibFunction" : "EPWM_enableTripZoneSignals",
			"driverlibFunctionAlt": "EPWM_disableTripZoneSignals",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzSignal" : "this",
			},
			"register": "TZSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_cbcSource",
			"driverlibFunction" : "EPWM_enableTripZoneSignals",
			"driverlibFunctionAlt": "EPWM_disableTripZoneSignals",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzSignal" : "this",
			},
			"register": "TZSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_cbcPulse",
			"driverlibFunction" : "EPWM_selectCycleByCycleTripZoneClearEvent",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "clearEvent" : "this",
			},
			"register": "TZCLR.CBPULSE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTripZone_tzInterruptSource",
			"driverlibFunction" : "EPWM_enableTripZoneInterrupt",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tzInterrupt" : "this",
			},
			"register": "TZEINT",
			"devices" : [
                "am263x"
			]
		},


		// EPWM Digital Compare
		{
			"name" :  "epwmDigitalCompare_dcFilterInput",
			"driverlibFunction" : "EPWM_setDigitalCompareFilterInput",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "filterInput" : "this",
			},
			"register": "DCFCTL.SRCSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_useBlankingWindow",
			"driverlibFunction" : "EPWM_enableDigitalCompareBlankingWindow",
			"driverlibFunctionAlt" : "EPWM_disableDigitalCompareBlankingWindow",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "DCFCTL.BLANKE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_blankingWindowEvent",
			"driverlibFunction" : "EPWM_setDigitalCompareBlankingEvent",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "blankingPulse" : "this",
                "mixedSource": "this",
			},
			"register": "DCFCTL.PULSESEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_blankingWindowOffset",
			"driverlibFunction" : "EPWM_setDigitalCompareWindowOffset",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "windowOffsetCount" : "this",
			},
			"register": "DCFOFFSET.DCFOFFSET",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_blankingWindowLength",
			"driverlibFunction" : "EPWM_setDigitalCompareWindowLength",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "windowLengthCount" : "this",
			},
			"register": "DCFWINDOW.DCFWINDOW",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_invertBlankingWindow",
			"driverlibFunction" : "EPWM_enableDigitalCompareWindowInverseMode",
			"driverlibFunctionAlt" : "EPWM_disableDigitalCompareWindowInverseMode",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "DCFCTL.BLANKINV",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_useDCCounterCapture",
			"driverlibFunction" : "EPWM_enableDigitalCompareCounterCapture",
			"driverlibFunctionAlt" : "EPWM_disableDigitalCompareCounterCapture",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "DCCAPCTL.CAPE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_dCCounterCaptureShadow",
			"driverlibFunction" : "EPWM_setDigitalCompareCounterShadowMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "enableShadowMode": "this",
			},
			"register": "DCCAPCTL.SHDWMODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_useEdgeFilter",
			"driverlibFunction" : "EPWM_enableDigitalCompareEdgeFilter",
			"driverlibFunctionAlt" : "EPWM_disableDigitalCompareEdgeFilter",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "DCFCTL.EDGEFILTSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_edgeFilterMode",
			"driverlibFunction" : "EPWM_setDigitalCompareEdgeFilterMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "edgeMode": "this",
			},
			"register": "DCFCTL.EDGEMODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_edgeFilterEdgeCount",
			"driverlibFunction" : "EPWM_setDigitalCompareEdgeFilterEdgeCount",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "edgeCount": "this",
			},
			"register": "DCFCTL.EDGECOUNT",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_enableValleyCapture",
			"driverlibFunction" : "EPWM_enableValleyCapture",
			"driverlibFunctionAlt" : "EPWM_disableValleyCapture",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "VCAPCTL.VCAPE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_valleyCaptureSource",
			"driverlibFunction" : "EPWM_setValleyTriggerSource",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "trigger": "this",
			},
			"register": "VCAPCTL.TRIGSEL",
			"devices" : [
                "am263x"
			]
		},
				{
			"name" :  "epwmDigitalCompare_startValleyCapture",
			"driverlibFunction" : "EPWM_startValleyCapture",
			"driverlibFunctionAlt" : "",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "VCAPCTL.VCAPSTART",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_startValleyCaptureTriggerCount",
			"driverlibFunction" : "EPWM_setValleyTriggerEdgeCounts",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "startCount": "this",
		        "stopCount": "",
			},
			"register": "VCNTCFG.STARTEDGE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_stopValleyCaptureTriggerCount",
			"driverlibFunction" : "EPWM_setValleyTriggerEdgeCounts",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "startCount": "",
		        "stopCount": "this",
			},
			"register": "VCNTCFG.STOPEDGE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_enableEdgeFilterDelay",
			"driverlibFunction" : "EPWM_enableValleyHWDelay",
			"driverlibFunctionAlt" : "EPWM_disableValleyHWDelay",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "VCAPCTL.EDGEFILTDLYSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_SWVDELVAL",
			"driverlibFunction" : "EPWM_setValleySWDelayValue",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "delayOffsetValue": "this",
			},
			"register": "SWDELVAL.SWDELVAL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_valleyDelayDivider",
			"driverlibFunction" : "EPWM_setValleyDelayDivider",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "delayMode": "this",
			},
			"register": "VCAPCTL.VDELAYDIV",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_TYPE_DCAH",
			"driverlibFunction" : "EPWM_selectDigitalCompareTripInput",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tripSource": "this",
		        "dcType": "EPWM_DC_TYPE_DCAH",
			},
			"register": "DCTRIPSEL.DCAHCOMPSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_TYPE_DCAH_combinationInputConfig",
			"driverlibFunction" : "EPWM_enableDigitalCompareTripCombinationInput",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tripInput": "this",
		        "dcType": "EPWM_DC_TYPE_DCAH",
			},
			"register": "DCAHTRIPSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_TYPE_DCAL",
			"driverlibFunction" : "EPWM_selectDigitalCompareTripInput",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tripSource": "this",
		        "dcType": "EPWM_DC_TYPE_DCAL",
			},
			"register": "DCTRIPSEL.DCALCOMPSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_TYPE_DCAL_combinationInputConfig",
			"driverlibFunction" : "EPWM_enableDigitalCompareTripCombinationInput",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tripInput": "this",
		        "dcType": "EPWM_DC_TYPE_DCAL",
			},
			"register": "DCALTRIPSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_TZ_DC_OUTPUT_A1",
			"driverlibFunction" : "EPWM_setTripZoneDigitalCompareEventCondition",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcType": "EPWM_TZ_DC_OUTPUT_A1",
		        "dcEvent":"this",
			},
			"register": "TZDCSEL.DCAEVT1",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_TZ_DC_OUTPUT_A2",
			"driverlibFunction" : "EPWM_setTripZoneDigitalCompareEventCondition",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcType": "EPWM_TZ_DC_OUTPUT_A2",
		        "dcEvent":"this",
			},
			"register": "TZDCSEL.DCAEVT2",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_A_EPWM_DC_EVENT_1_adcTrig",
			"driverlibFunction" : "EPWM_enableDigitalCompareADCTrigger",
			"driverlibFunctionAlt" : "EPWM_disableDigitalCompareADCTrigger",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_A",
			},
			"register": "DCACTL.EVT1SOCE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_A_EPWM_DC_EVENT_1_syncTrig",
			"driverlibFunction" : "EPWM_enableDigitalCompareSyncEvent",
			"driverlibFunctionAlt" : "",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_A",
			},
			"register": "DCACTL.EVT1SYNCE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_A_EPWM_DC_EVENT_1_eventSync",
			"driverlibFunction" : "EPWM_setDigitalCompareEventSyncMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_A",
		        "dcEvent": "EPWM_DC_EVENT_1",
		        "syncMode": "this",
			},
			"register": "DCACTL.EVT1FRCSYNCSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_A_EPWM_DC_EVENT_1_eventSource",
			"driverlibFunction" : "EPWM_setDigitalCompareEventSource",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_A",
		        "dcEvent": "EPWM_DC_EVENT_1",
		        "dcEventSource": "this",
			},
			"register": "DCACTL.EVT1SRCSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_A_EPWM_DC_EVENT_2_eventSync",
			"driverlibFunction" : "EPWM_setDigitalCompareEventSyncMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_A",
		        "dcEvent": "EPWM_DC_EVENT_2",
		        "syncMode": "this",
			},
			"register": "DCACTL.EVT2FRCSYNCSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_A_EPWM_DC_EVENT_2_eventSource",
			"driverlibFunction" : "EPWM_setDigitalCompareEventSource",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_A",
		        "dcEvent": "EPWM_DC_EVENT_2",
		        "dcEventSource": "this",
			},
			"register": "DCACTL.EVT2SRCSEL",
			"devices" : [
                "am263x"
			]
		},
				{
			"name" :  "epwmDigitalCompare_EPWM_DC_TYPE_DCBH",
			"driverlibFunction" : "EPWM_selectDigitalCompareTripInput",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tripSource": "this",
		        "dcType": "EPWM_DC_TYPE_DCBH",
			},
			"register": "DCTRIPSEL.DCBHCOMPSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_TYPE_DCBH_combinationInputConfig",
			"driverlibFunction" : "EPWM_enableDigitalCompareTripCombinationInput",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tripInput": "this",
		        "dcType": "EPWM_DC_TYPE_DCBH",
			},
			"register": "DCBHTRIPSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_TYPE_DCBL",
			"driverlibFunction" : "EPWM_selectDigitalCompareTripInput",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tripSource": "this",
		        "dcType": "EPWM_DC_TYPE_DCBL",
			},
			"register": "DCBLTRIPSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_TYPE_DCBL_combinationInputConfig",
			"driverlibFunction" : "EPWM_enableDigitalCompareTripCombinationInput",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "tripInput": "this",
		        "dcType": "EPWM_DC_TYPE_DCBL",
			},
			"register": "TZDCSEL.DCBEVT2",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_TZ_DC_OUTPUT_B1",
			"driverlibFunction" : "EPWM_setTripZoneDigitalCompareEventCondition",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcType": "EPWM_TZ_DC_OUTPUT_B1",
		        "dcEvent":"this",
			},
			"register": "TZDCSEL.DCBEVT1",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_TZ_DC_OUTPUT_B2",
			"driverlibFunction" : "EPWM_setTripZoneDigitalCompareEventCondition",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcType": "EPWM_TZ_DC_OUTPUT_B2",
		        "dcEvent":"this",
			},
			"register": "TZDCSEL.DCBEVT2",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_B_EPWM_DC_EVENT_1_adcTrig",
			"driverlibFunction" : "EPWM_enableDigitalCompareADCTrigger",
			"driverlibFunctionAlt" : "EPWM_disableDigitalCompareADCTrigger",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_B",
			},
			"register": "DCBCTL.EVT1SOCE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_B_EPWM_DC_EVENT_1_syncTrig",
			"driverlibFunction" : "EPWM_enableDigitalCompareSyncEvent",
			"driverlibFunctionAlt" : "",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_B",
			},
			"register": "DCBCTL.EVT1SYNCE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_B_EPWM_DC_EVENT_1_eventSync",
			"driverlibFunction" : "EPWM_setDigitalCompareEventSyncMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_B",
		        "dcEvent": "EPWM_DC_EVENT_1",
		        "syncMode": "this",
			},
			"register": "DCBCTL.EVT1FRCSYNCSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_B_EPWM_DC_EVENT_1_eventSource",
			"driverlibFunction" : "EPWM_setDigitalCompareEventSource",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_B",
		        "dcEvent": "EPWM_DC_EVENT_1",
		        "dcEventSource": "this",
			},
			"register": "DCBCTL.EVT1SRCSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_B_EPWM_DC_EVENT_2_eventSync",
			"driverlibFunction" : "EPWM_setDigitalCompareEventSyncMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_B",
		        "dcEvent": "EPWM_DC_EVENT_2",
		        "syncMode": "this",
			},
			"register": "DCBCTL.EVT2FRCSYNCSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_B_EPWM_DC_EVENT_2_eventSource",
			"driverlibFunction" : "EPWM_setDigitalCompareEventSource",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_B",
		        "dcEvent": "EPWM_DC_EVENT_2",
		        "dcEventSource": "this",
			},
			"register": "DCBCTL.EVT2SRCSEL",
			"devices" : [
                "am263x"
			]
		},

		// EPWM Dead-Band
		{
			"name" :  "epwmDeadband_inputRED",
			"driverlibFunction" : "EPWM_setRisingEdgeDeadBandDelayInput",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "input" : "this",
			},
			"register": "DBCTL.IN_MODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_inputFED",
			"driverlibFunction" : "EPWM_setFallingEdgeDeadBandDelayInput",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "input" : "this",
			},
			"register": "DBCTL.IN_MODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_polarityRED",
			"driverlibFunction" : "EPWM_setDeadBandDelayPolarity",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "delayMode" : "EPWM_DB_RED",
		        "polarity": "this",
			},
			"register": "DBCTL.POLSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_polarityFED",
			"driverlibFunction" : "EPWM_setDeadBandDelayPolarity",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "delayMode" : "EPWM_DB_FED",
		        "polarity": "this",
			},
			"register": "DBCTL.POLSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_enableRED",
			"driverlibFunction" : "EPWM_setDeadBandDelayMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "delayMode" : "EPWM_DB_RED",
		        "enableDelayMode": "this",
			},
			"register": "DBCTL.OUT_MODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_delayRED",
			"driverlibFunction" : "EPWM_setRisingEdgeDelayCount",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "redCount" : "this",
			},
			"register": "DBRED.DBRED",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_enableFED",
			"driverlibFunction" : "EPWM_setDeadBandDelayMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "delayMode" : "EPWM_DB_FED",
		        "enableDelayMode": "this",
			},
			"register": "DBCTL.OUT_MODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_delayFED",
			"driverlibFunction" : "EPWM_setFallingEdgeDelayCount",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "fedCount" : "this",
			},
			"register": "DBFED.DBFED",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_outputSwapOutA",
			"driverlibFunction" : "EPWM_setDeadBandOutputSwapMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "output" : "EPWM_DB_OUTPUT_A",
		        "enableSwapMode": "this",
			},
			"register": "DBCTL.OUTSWAP",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_outputSwapOutB",
			"driverlibFunction" : "EPWM_setDeadBandOutputSwapMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "output" : "EPWM_DB_OUTPUT_B",
		        "enableSwapMode": "this",
			},
			"register": "DBCTL.OUTSWAP",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_dbControlGld",
			"driverlibFunction" : "EPWM_enableGlobalLoadRegisters",
			"driverlibFunctionAlt": "",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "loadRegister" : "EPWM_GL_REGISTER_DBCTL",
			},
			"register": "GLDCFG.DBCTL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_controlShadowMode",
			"driverlibFunction" : "",
			"driverlibFunctionAlt" : "EPWM_disableDeadBandControlShadowLoadMode",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "DBCTL2.SHDWBCTLMODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_controlShadowLoadEvent",
			"driverlibFunction" : "EPWM_setDeadBandControlShadowLoadMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "loadMode" : "this",
			},
			"register": "DBCTL2.LOADDBCTLMODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_redGld",
			"driverlibFunction" : "EPWM_enableGlobalLoadRegisters",
			"driverlibFunctionAlt":"",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "loadRegister" : "EPWM_GL_REGISTER_DBRED_DBREDHR",
			},
			"register": "GLDCFG.DBRED_DBREDHR",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_redShadowMode",
			"driverlibFunction" : "",
			"driverlibFunctionAlt" : "EPWM_disableRisingEdgeDelayCountShadowLoadMode",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "DBCTL.SHDWDBREDMODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_redShadowLoadEvent",
			"driverlibFunction" : "EPWM_setRisingEdgeDelayCountShadowLoadMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "loadMode" : "this",
			},
			"register": "epwmDeadband_DBCTL.LOADREDMODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_fedGld",
			"driverlibFunction" : "EPWM_enableGlobalLoadRegisters",
			"driverlibFunctionAlt":"",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "loadRegister" : "EPWM_GL_REGISTER_DBFED_DBFEDHR",
			},
			"register": "GLDCFG.DBFED_DBFEDHR",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_fedShadowMode",
			"driverlibFunction" : "",
			"driverlibFunctionAlt": "EPWM_disableFallingEdgeDelayCountShadowLoadMode",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "DBCTL.SHDWDBFEDMODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_fedShadowLoadEvent",
			"driverlibFunction" : "EPWM_setFallingEdgeDelayCountShadowLoadMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "loadMode" : "this",
			},
			"register": "DBCTL.LOADFEDMODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDeadband_deadbandCounterClockRate",
			"driverlibFunction" : "EPWM_setDeadBandCounterClock",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "clockMode" : "this",
			},
			"register": "DBCTL.HALFCYCLE",
			"devices" : [
                "am263x"
			]
		},

		// EPWM Chopper
		{
			"name" :  "epwmChopper_useChopper",
			"driverlibFunction" : "EPWM_enableChopper",
			"driverlibFunctionAlt" : "EPWM_disableChopper",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "PCCTL.CHPEN",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmChopper_chopperDuty",
			"driverlibFunction" : "EPWM_setChopperDutyCycle",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dutyCycleCount" : "this",
			},
			"register": "PCCTL.CHPDUTY",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmChopper_chopperFreq",
			"driverlibFunction" : "EPWM_setChopperFreq",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "freqDiv" : "this",
			},
			"register": "PCCTL.CHPFREQ",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmChopper_chopperFirstPulseWidth",
			"driverlibFunction" : "EPWM_setChopperFirstPulseWidth",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "firstPulseWidth" : "this",
			},
			"register": "PCCTL.OSHTWTH",
			"devices" : [
                "am263x"
			]
		},

		// EPWM Event Trigger
		{
			"name" :  "epwmEventTrigger_enableInterrupt",
			"driverlibFunction" : "EPWM_enableInterrupt",
			"driverlibFunctionAlt" : "EPWM_disableInterrupt",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "ETSEL.INTEN",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmEventTrigger_interruptSource",
			"driverlibFunction" : "EPWM_setInterruptSource",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "interruptSource": "this",
                "mixedSource": "this",
			},
			"register": "ETSEL.INTSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmEventTrigger_interruptEventCount",
			"driverlibFunction" : "EPWM_setInterruptEventCount",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "eventCount": "this",
			},
			"register": "ETINTPS.INTPRD2",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmEventTrigger_interruptEventCountInitEnable",
			"driverlibFunction" : "EPWM_enableInterruptEventCountInit",
			"driverlibFunctionAlt": "EPWM_disableInterruptEventCountInit",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "ETCNTINITCTL.INITINITEN",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmEventTrigger_interruptEventCountInitValue",
			"driverlibFunction" : "EPWM_setInterruptEventCountInitValue",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "eventCount": "this",
			},
			"register": "ETCNTINIT.INTINIT",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmEventTrigger_interruptEventCountInitForce",
			"driverlibFunction" : "EPWM_forceInterruptEventCountInit",
			"driverlibFunctionAlt" : "",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "ETCNTINITCTL.INTINITFRC",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmEventTrigger_EPWM_SOC_A_triggerEnable",
			"driverlibFunction" : "EPWM_enableADCTrigger",
			"driverlibFunctionAlt": "EPWM_disableADCTrigger",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "adcSOCType": "EPWM_SOC_A",
			},
			"register": "ETSEL.SOCAEN",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmEventTrigger_EPWM_SOC_A_triggerSource",
			"driverlibFunction" : "EPWM_setADCTriggerSource",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "adcSOCType": "EPWM_SOC_A",
		        "socSource": "this",
                "mixedSource": "this",
			},
			"register": "ETSEL.SOCASEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmEventTrigger_EPWM_SOC_A_triggerEventPrescalar",
			"driverlibFunction" : "EPWM_setADCTriggerEventPrescale",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "adcSOCType" : "EPWM_SOC_A",
		        "preScaleCount" : "this",
			},
			"register": "ETSOCPS.SOCAPRD2",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmEventTrigger_EPWM_SOC_A_triggerEventCountInitEnable",
			"driverlibFunction" : "EPWM_enableADCTriggerEventCountInit",
			"driverlibFunctionAlt": "EPWM_disableADCTriggerEventCountInit",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "adcSOCType":"EPWM_SOC_A"
			},
			"register": "ETCNTINITCTL.SOCAINITEN",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmEventTrigger_EPWM_SOC_A_triggerEventCountInitValue",
			"driverlibFunction" : "EPWM_setADCTriggerEventCountInitValue",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "adcSOCType":"EPWM_SOC_A",
		        "eventCount":"this",
			},
			"register": "ETCNTINIT.SOCAINIT",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmEventTrigger_EPWM_SOC_A_triggerEventCountInitForce",
			"driverlibFunction" : "EPWM_forceADCTriggerEventCountInit",
			"driverlibFunctionAlt" : "",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "adcSOCType":"EPWM_SOC_A",
			},
			"register": "ETCNTINITCTL.SOCAINITFRC",
			"devices" : [
                "am263x"
			]
		},
				{
			"name" :  "epwmEventTrigger_EPWM_SOC_B_triggerEnable",
			"driverlibFunction" : "EPWM_enableADCTrigger",
			"driverlibFunctionAlt": "EPWM_disableADCTrigger",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "adcSOCType": "EPWM_SOC_B",
			},
			"register": "ETSEL.SOCBEN",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmEventTrigger_EPWM_SOC_B_triggerSource",
			"driverlibFunction" : "EPWM_setADCTriggerSource",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "adcSOCType": "EPWM_SOC_B",
		        "socSource": "this",
                "mixedSource": "this",
			},
			"register": "ETSEL.SOCBSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmEventTrigger_EPWM_SOC_B_triggerEventPrescalar",
			"driverlibFunction" : "EPWM_setADCTriggerEventPrescale",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "adcSOCType" : "EPWM_SOC_B",
		        "preScaleCount" : "this",
			},
			"register": "ETSOCPS.SOCBPRD2",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmEventTrigger_EPWM_SOC_B_triggerEventCountInitEnable",
			"driverlibFunction" : "EPWM_enableADCTriggerEventCountInit",
			"driverlibFunctionAlt": "EPWM_disableADCTriggerEventCountInit",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "adcSOCType":"EPWM_SOC_B"
			},
			"register": "ETCNTINITCTL.SOCBINITEN",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmEventTrigger_EPWM_SOC_B_triggerEventCountInitValue",
			"driverlibFunction" : "EPWM_setADCTriggerEventCountInitValue",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "adcSOCType":"EPWM_SOC_B",
		        "eventCount":"this",
			},
			"register": "ETCNTINIT.SOCBINIT",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmEventTrigger_EPWM_SOC_B_triggerEventCountInitForce",
			"driverlibFunction" : "EPWM_forceADCTriggerEventCountInit",
			"driverlibFunctionAlt" : "",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "adcSOCType":"EPWM_SOC_B",
			},
			"register": "ETCNTINITCTL.SOCBINITFRC",
			"devices" : [
                "am263x"
			]
		},

        //EPWM XCMP Mode Operation

        {
			"name" :  "epwmXCMP_enableMode",
			"driverlibFunction" : "EPWM_enableXCMPMode",
			"driverlibFunctionAlt" : "EPWM_disableXCMPMode",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "XCMPCTL1.XCMPEN",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP_SplitCheck",
			"driverlibFunction" : "EPWM_enableSplitXCMP",
			"driverlibFunctionAlt" : "EPWM_disableSplitXCMP",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "XCMPCTL1.XCMPSPLIT",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP_No_Split",
			"driverlibFunction" : "EPWM_allocAXCMP",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "alloctype" : "this",
			},
			"register": "XCMPCTL1.XCMPA_ALLOC",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP_Split_A",
			"driverlibFunction" : "EPWM_allocAXCMP",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "alloctype" : "this",
			},
			"register": "XCMPCTL1.XCMPA_ALLOC",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP_Split_B",
			"driverlibFunction" : "EPWM_allocBXCMP",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "alloctype" : "this",
			},
			"register": "XCMPCTL1.XCMPB_ALLOC",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP1_Active",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP1_ACTIVE",
				"xcmpvalue": "this"
			},
			"register": "XCMP1_ACTIVE.XCMP1_ACTIVE",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP2_Active",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP2_ACTIVE",
				"xcmpvalue": "this"
			},
			"register": "XCMP2_ACTIVE.XCMP2_ACTIVE",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP3_Active",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP3_ACTIVE",
				"xcmpvalue": "this"
			},
			"register": "XCMP3_ACTIVE.XCMP3_ACTIVE",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP4_Active",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP4_ACTIVE",
				"xcmpvalue": "this"
			},
			"register": "XCMP4_ACTIVE.XCMP4_ACTIVE",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP5_Active",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP5_ACTIVE",
				"xcmpvalue": "this"
			},
			"register": "XCMP5_ACTIVE.XCMP5_ACTIVE",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP6_Active",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP6_ACTIVE",
				"xcmpvalue": "this"
			},
			"register": "XCMP6_ACTIVE.XCMP6_ACTIVE",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP7_Active",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP7_ACTIVE",
				"xcmpvalue": "this"
			},
			"register": "XCMP7_ACTIVE.XCMP7_ACTIVE",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP8_Active",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP8_ACTIVE",
				"xcmpvalue": "this"
			},
			"register": "XCMP8_ACTIVE.XCMP8_ACTIVE",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP1_Shdw1",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP1_SHADOW1",
				"xcmpvalue": "this"
			},
			"register": "XCMP1_SHDW1.XCMP1_SHDW1",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP2_Shdw1",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP2_SHADOW1",
				"xcmpvalue": "this"
			},
			"register": "XCMP2_SHDW1.XCMP2_SHDW1",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP3_Shdw1",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP3_SHADOW1",
				"xcmpvalue": "this"
			},
			"register": "XCMP3_SHDW1.XCMP3_SHDW1",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP4_Shdw1",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP4_SHADOW1",
				"xcmpvalue": "this"
			},
			"register": "XCMP4_SHDW1.XCMP4_SHDW1",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP5_Shdw1",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP5_SHADOW1",
				"xcmpvalue": "this"
			},
			"register": "XCMP5_SHDW1.XCMP5_SHDW1",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP6_Shdw1",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP6_SHADOW1",
				"xcmpvalue": "this"
			},
			"register": "XCMP6_SHDW1.XCMP6_SHDW1",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP7_Shdw1",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP7_SHADOW1",
				"xcmpvalue": "this"
			},
			"register": "XCMP7_SHDW1.XCMP7_SHDW1",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP8_Shdw1",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP8_SHADOW1",
				"xcmpvalue": "this"
			},
			"register": "XCMP8_SHDW1.XCMP8_SHDW1",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP1_Shdw2",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP1_SHADOW2",
				"xcmpvalue": "this"
			},
			"register": "XCMP1_SHDW2.XCMP1_SHDW2",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP2_Shdw2",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP2_SHADOW2",
				"xcmpvalue": "this"
			},
			"register": "XCMP2_SHDW2.XCMP2_SHDW2",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP3_Shdw2",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP3_SHADOW2",
				"xcmpvalue": "this"
			},
			"register": "XCMP3_SHDW2.XCMP3_SHDW2",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP4_Shdw2",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP4_SHADOW2",
				"xcmpvalue": "this"
			},
			"register": "XCMP4_SHDW2.XCMP4_SHDW2",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP5_Shdw2",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP5_SHADOW2",
				"xcmpvalue": "this"
			},
			"register": "XCMP5_SHDW2.XCMP5_SHDW2",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP6_Shdw2",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP6_SHADOW2",
				"xcmpvalue": "this"
			},
			"register": "XCMP6_SHDW2.XCMP6_SHDW2",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP7_Shdw2",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP7_SHADOW2",
				"xcmpvalue": "this"
			},
			"register": "XCMP7_SHDW2.XCMP7_SHDW2",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP8_Shdw2",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP8_SHADOW2",
				"xcmpvalue": "this"
			},
			"register": "XCMP8_SHDW2.XCMP8_SHDW2",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP1_Shdw3",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP1_SHADOW3",
				"xcmpvalue": "this"
			},
			"register": "XCMP1_SHDW3.XCMP1_SHDW3",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP2_Shdw3",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP2_SHADOW3",
				"xcmpvalue": "this"
			},
			"register": "XCMP2_SHDW3.XCMP2_SHDW3",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP3_Shdw3",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP3_SHADOW3",
				"xcmpvalue": "this"
			},
			"register": "XCMP3_SHDW3.XCMP3_SHDW3",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP4_Shdw3",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP4_SHADOW3",
				"xcmpvalue": "this"
			},
			"register": "XCMP4_SHDW3.XCMP4_SHDW3",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP5_Shdw3",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP5_SHADOW3",
				"xcmpvalue": "this"
			},
			"register": "XCMP5_SHDW3.XCMP5_SHDW3",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP6_Shdw3",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP6_SHADOW3",
				"xcmpvalue": "this"
			},
			"register": "XCMP6_SHDW3.XCMP6_SHDW3",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP7_Shdw3",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP7_SHADOW3",
				"xcmpvalue": "this"
			},
			"register": "XCMP7_SHDW3.XCMP7_SHDW3",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXCMP8_Shdw3",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XCMP8_SHADOW3",
				"xcmpvalue": "this"
			},
			"register": "XCMP8_SHDW3.XCMP8_SHDW3",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXTBPRD_Active",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XTBPRD_ACTIVE",
				"xcmpvalue": "this"
			},
			"register": "XTBPRD_ACTIVE.XTBPRD_ACTIVE",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXTBPRD_Shdw1",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XTBPRD_SHADOW1",
				"xcmpvalue": "this"
			},
			"register": "XTBPRD_SHDW1.XTBPRD_SHDW1",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXTBPRD_Shdw2",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XTBPRD_SHADOW2",
				"xcmpvalue": "this"
			},
			"register": "XTBPRD_SHDW2.XTBPRD_SHDW2",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXTBPRD_Shdw3",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XTBPRD_SHADOW3",
				"xcmpvalue": "this"
			},
			"register": "XTBPRD_SHDW3.XTBPRD_SHDW3",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXMinMax_Active",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XMINMAX_ACTIVE",
				"xcmpvalue": "this"
			},
			"register": "XMINMAX_ACTIVE.XMINMAX_ACTIVE",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXMinMax_Shdw1",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XMINMAX_SHADOW1",
				"xcmpvalue": "this"
			},
			"register": "XMINMAX_SHDW1.XMINMAX_SHDW1",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXMinMax_Shdw2",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XMINMAX_SHADOW2",
				"xcmpvalue": "this"
			},
			"register": "XMINMAX_SHDW2.XMINMAX_SHDW2",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXMinMax_Shdw3",
		    "driverlibFunction" : "EPWM_setXCMPRegValue",
			"driverlibFunctionArg" : {
				"base" : "",
				"xcmpReg" : "EPWM_XMINMAX_SHADOW3",
				"xcmpvalue": "this"
			},
			"register": "XMINMAX_SHDW3.XMINMAX_SHDW3",
			"devices" : [
                "am263x"
			]
		},

        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP1_ACTIVE",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_ACTIVE",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP1"
			},
			"register": "XAQCTLA_ACTIVE.XCMP1",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP2_ACTIVE",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_ACTIVE",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP2"
			},
			"register": "XAQCTLA_ACTIVE.XCMP2",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP3_ACTIVE",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_ACTIVE",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP3"
			},
			"register": "XAQCTLA_ACTIVE.XCMP3",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP4_ACTIVE",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_ACTIVE",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP4"
			},
			"register": "XAQCTLA_ACTIVE.XCMP4",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP5_ACTIVE",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_ACTIVE",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP5"
			},
			"register": "XAQCTLA_ACTIVE.XCMP5",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP6_ACTIVE",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_ACTIVE",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP6"
			},
			"register": "XAQCTLA_ACTIVE.XCMP6",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP7_ACTIVE",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_ACTIVE",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP7"
			},
			"register": "XAQCTLA_ACTIVE.XCMP7",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP8_ACTIVE",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_ACTIVE",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP8"
			},
			"register": "XAQCTLA_ACTIVE.XCMP8",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_XCMP5_ACTIVE",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_ACTIVE",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP5"
			},
			"register": "XAQCTLB_ACTIVE.XCMP5",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_XCMP6_ACTIVE",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_ACTIVE",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP6"
			},
			"register": "XAQCTLB_ACTIVE.XCMP6",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_XCMP7_ACTIVE",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_ACTIVE",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP7"
			},
			"register": "XAQCTLB_ACTIVE.XCMP7",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_XCMP8_ACTIVE",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_ACTIVE",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP8"
			},
			"register": "XAQCTLB_ACTIVE.XCMP8",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP1_SHADOW_1",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW1",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP1"
			},
			"register": "XAQCTLA_SHDW1.XCMP1",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP2_SHADOW_1",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW1",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP2"
			},
			"register": "XAQCTLA_SHDW1.XCMP2",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP3_SHADOW_1",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW1",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP3"
			},
			"register": "XAQCTLA_SHDW1.XCMP3",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP4_SHADOW_1",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW1",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP4"
			},
			"register": "XAQCTLA_SHDW1.XCMP4",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP5_SHADOW_1",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW1",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP5"
			},
			"register": "XAQCTLA_SHDW1.XCMP5",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP6_SHADOW_1",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW1",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP6"
			},
			"register": "XAQCTLA_SHDW1.XCMP6",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP7_SHADOW_1",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW1",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP7"
			},
			"register": "XAQCTLA_SHDW1.XCMP7",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP8_SHADOW_1",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW1",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP8"
			},
			"register": "XAQCTLA_SHDW1.XCMP8",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_XCMP5_SHADOW_1",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW1",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP5"
			},
			"register": "XAQCTLB_SHDW1.XCMP5",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_XCMP6_SHADOW_1",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW1",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP6"
			},
			"register": "XAQCTLB_SHDW1.XCMP6",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_XCMP7_SHADOW_1",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW1",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP7"
			},
			"register": "XAQCTLB_SHDW1.XCMP7",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_XCMP8_SHADOW_1",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW1",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP8"
			},
			"register": "XAQCTLB_SHDW1.XCMP8",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP1_SHADOW_2",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW2",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP1"
			},
			"register": "XAQCTLA_SHDW2.XCMP1",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP2_SHADOW_2",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW2",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP2"
			},
			"register": "XAQCTLA_SHDW2.XCMP2",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP3_SHADOW_2",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW2",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP3"
			},
			"register": "XAQCTLA_SHDW2.XCMP3",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP4_SHADOW_2",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW2",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP4"
			},
			"register": "XAQCTLA_SHDW2.XCMP4",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP5_SHADOW_2",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW2",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP5"
			},
			"register": "XAQCTLA_SHDW2.XCMP5",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP6_SHADOW_2",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW2",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP6"
			},
			"register": "XAQCTLA_SHDW2.XCMP6",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP7_SHADOW_2",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW2",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP7"
			},
			"register": "XAQCTLA_SHDW2.XCMP7",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP8_SHADOW_2",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW2",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP8"
			},
			"register": "XAQCTLA_SHDW2.XCMP8",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_XCMP5_SHADOW_2",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW2",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP5"
			},
			"register": "XAQCTLB_SHDW2.XCMP5",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_XCMP6_SHADOW_2",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW2",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP6"
			},
			"register": "XAQCTLB_SHDW2.XCMP6",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_XCMP7_SHADOW_2",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW2",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP7"
			},
			"register": "XAQCTLB_SHDW2.XCMP7",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_XCMP8_SHADOW_2",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW2",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP8"
			},
			"register": "XAQCTLB_SHDW2.XCMP8",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP1_SHADOW_3",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW3",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP1"
			},
			"register": "XAQCTLA_SHDW3.XCMP1",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP2_SHADOW_3",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW3",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP2"
			},
			"register": "XAQCTLA_SHDW3.XCMP2",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP3_SHADOW_3",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW3",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP3"
			},
			"register": "XAQCTLA_SHDW3.XCMP3",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP4_SHADOW_3",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW3",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP4"
			},
			"register": "XAQCTLA_SHDW3.XCMP4",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP5_SHADOW_3",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW3",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP5"
			},
			"register": "XAQCTLA_SHDW3.XCMP5",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP6_SHADOW_3",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW3",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP6"
			},
			"register": "XAQCTLA_SHDW3.XCMP6",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP7_SHADOW_3",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW3",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP7"
			},
			"register": "XAQCTLA_SHDW3.XCMP7",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_A_ON_TIMEBASE_XCMP8_SHADOW_3",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW3",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_A",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP8"
			},
			"register": "XAQCTLA_SHDW3.XCMP8",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_XCMP5_SHADOW_3",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW3",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP5"
			},
			"register": "XAQCTLB_SHDW3.XCMP5",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_XCMP6_SHADOW_3",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW3",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP6"
			},
			"register": "XAQCTLB_SHDW3.XCMP6",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_XCMP7_SHADOW_3",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW3",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP7"
			},
			"register": "XAQCTLB_SHDW3.XCMP7",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXAQCTL_EPWM_AQ_OUTPUT_B_ON_TIMEBASE_XCMP8_SHADOW_3",
			"driverlibFunction" : "EPWM_setXCMPActionQualifierAction",
			"driverlibFunctionArg" : {
		        "base" : "",
                "shadowset" : "EPWM_XCMP_SHADOW3",
		        "epwmOutput" : "EPWM_AQ_OUTPUT_B",
		        "output" : "this",
		        "event" : "EPWM_AQ_OUTPUT_ON_TIMEBASE_XCMP8"
			},
			"register": "XAQCTLB_SHDW3.XCMP8",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXLOADCTL_Loadmode",
			"driverlibFunction" : "EPWM_setXCMPLoadMode",
			"driverlibFunctionArg" : {
		        "base" : "",
                "mode" : "this"
			},
			"register": "XLOADCTL.LOADMODE",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXLOADCTL_Shadowlevel",
			"driverlibFunction" : "EPWM_setXCMPShadowLevel",
			"driverlibFunctionArg" : {
		        "base" : "",
                "level" : "this"
			},
			"register": "XLOADCTL.SHDWLEVEL",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXLOADCTL_Loadonce",
			"driverlibFunction" : "EPWM_setXCMPShadowBufPtrLoadOnce",
			"driverlibFunctionArg" : {
		        "base" : "",
                "level" : "this"
			},
			"register": "XLOADCTL.SHDWBUFPTR_LOADONCE",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXLOADCTL_RepeatBuf2",
			"driverlibFunction" : "EPWM_setXCMPShadowRepeatBufxCount",
			"driverlibFunctionArg" : {
		        "base" : "",
                "bufferset" : "EPWM_XCMP_SHADOW2",
                "count" : "this"
			},
			"register": "XLOADCTL.RPTBUF2PRD",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmXLOADCTL_RepeatBuf3",
			"driverlibFunction" : "EPWM_setXCMPShadowRepeatBufxCount",
			"driverlibFunctionArg" : {
		        "base" : "",
                "bufferset" : "EPWM_XCMP_SHADOW3",
                "count" : "this"
			},
			"register": "XLOADCTL.RPTBUF3PRD",
			"devices" : [
                "am263x"
			]
		},

        //Diode Emulation

        {
			"name" :  "epwmDE_enableMode",
			"driverlibFunction" : "EPWM_enableDiodeEmulationMode",
			"driverlibFunctionAlt": "EPWM_disableDiodeEmulationMode",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "DECTL.ENABLE",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmDE_selectMode",
			"driverlibFunction" : "EPWM_setDiodeEmulationMode",
			"driverlibFunctionArg" : {
		        "base" : "",
                "mode": "this",
			},
			"register": "DECTL.MODE",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmDE_reEntryDelay",
			"driverlibFunction" : "EPWM_setDiodeEmulationReentryDelay",
			"driverlibFunctionArg" : {
		        "base" : "",
                "delay": "this",
			},
			"register": "DECTL.REENTRYDLY",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmDE_TripL",
			"driverlibFunction" : "EPWM_configureDiodeEmulationTripSources",
			"driverlibFunctionArg" : {
		        "base" : "",
                "source" : "EPWM_DE_TRIPL",
                "tripLorH": "this",
			},
			"register": "DECOMPSEL.TRIPL",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmDE_TripH",
			"driverlibFunction" : "EPWM_configureDiodeEmulationTripSources",
			"driverlibFunctionArg" : {
		        "base" : "",
                "source" : "EPWM_DE_TRIPH",
                "tripLorH": "this",
			},
			"register": "DECOMPSEL.TRIPH",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmDE_PWMA",
			"driverlibFunction" : "EPWM_selectDiodeEmulationPWMsignal",
			"driverlibFunctionArg" : {
		        "base" : "",
                "channel" : "EPWM_DE_CHANNEL_A",
                "signal": "this",
			},
			"register": "DEACTCTL.PWMA",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmDE_TripSelA",
			"driverlibFunction" : "EPWM_selectDiodeEmulationTripSignal",
			"driverlibFunctionArg" : {
		        "base" : "",
                "channel" : "EPWM_DE_CHANNEL_A",
                "signal": "this",
			},
			"register": "DEACTCTL.TRIPSELA",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmDE_PWMB",
			"driverlibFunction" : "EPWM_selectDiodeEmulationPWMsignal",
			"driverlibFunctionArg" : {
		        "base" : "",
                "channel" : "EPWM_DE_CHANNEL_B",
                "signal": "this",
			},
			"register": "DEACTCTL.PWMB",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmDE_TripSelB",
			"driverlibFunction" : "EPWM_selectDiodeEmulationTripSignal",
			"driverlibFunctionArg" : {
		        "base" : "",
                "channel" : "EPWM_DE_CHANNEL_B",
                "signal": "this",
			},
			"register": "DEACTCTL.TRIPSELB",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmDE_TripEnable",
			"driverlibFunction" : "EPWM_bypassDiodeEmulationLogic",
            "driverlibFunctionAlt": "EPWM_nobypassDiodeEmulationLogic",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "DEACTCTL.TRIPENABLE",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmDE_Frc",
			"driverlibFunction" : "EPWM_forceDiodeEmulationActive",
            "driverlibFunctionAlt" : "",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "DEFRC.DEACTIVE",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmDE_TripMonitorMode",
			"driverlibFunction" : "EPWM_enableDiodeEmulationMonitorModeControl",
            "driverlibFunctionAlt": "EPWM_disableDiodeEmulationMonitorModeControl",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "DEMONCTL.ENABLE",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmDE_Threshold",
			"driverlibFunction" : "EPWM_setDiodeEmulationMonitorCounterThreshold",
			"driverlibFunctionArg" : {
		        "base" : "",
                "threshold" : "this",
			},
			"register": "DEMONTHRES.THRESHOLD",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmDE_DecrementStep",
			"driverlibFunction" : "EPWM_setDiodeEmulationMonitorModeStep",
			"driverlibFunctionArg" : {
		        "base" : "",
                "direction" : "EPWM_DE_COUNT_DOWN",
                "stepsize" : "this",
			},
			"register": "DEMONSTEP.DECSTEP",
			"devices" : [
                "am263x"
			]
		},
        {
			"name" :  "epwmDE_IncrementStep",
			"driverlibFunction" : "EPWM_setDiodeEmulationMonitorModeStep",
			"driverlibFunctionArg" : {
		        "base" : "",
                "direction" : "EPWM_DE_COUNT_UP",
                "stepsize" : "this",
			},
			"register": "DEMONSTEP.INCSTEP",
			"devices" : [
                "am263x"
			]
		},

        // HRPWM functions
		{
			"name" :  "hrpwm_autoConv",
			"driverlibFunction" : "HRPWM_enableAutoConversion",
			"driverlibFunctionAlt" : "HRPWM_disableAutoConversion",
			"driverlibFunctionArg" : {
		        "base" : "HRCNFG.AUTOCONV"
			},
			"register": "",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_controlModeA",
			"driverlibFunction" : "HRPWM_setMEPControlMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "channel": "HRPWM_CHANNEL_A",
		        "mepCtrlMode": "this",
			},
			"register": "HRCNFG.CTLMODE",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_controlModeB",
			"driverlibFunction" : "HRPWM_setMEPControlMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "channel": "HRPWM_CHANNEL_B",
		        "mepCtrlMode": "this",
			},
			"register": "HRCNFG.CTLMODEB",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_tbphsHR",
			"driverlibFunction" : "HRPWM_setHiResPhaseShift",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "hrPhaseCount": "this",
			},
			"register": "TBPHS.TBPHSHR",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_syncSource",
			"driverlibFunction" : "HRPWM_setSyncPulseSource",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "syncPulseSource": "this",
			},
			"register": "HRPCTL.PWMSYNCSEL",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_phaseLoadEnable",
			"driverlibFunction" : "HRPWM_enablePhaseShiftLoad",
			"driverlibFunctionAlt" : "HRPWM_disablePhaseShiftLoad",
			"driverlibFunctionArg" : {
		        "base" : ""
			},
			"register": "HRPCTL.TBPHSHRLOADE",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_edgeModeA",
			"driverlibFunction" : "HRPWM_setMEPEdgeSelect",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "channel": "HRPWM_CHANNEL_A",
		        "mepEdgeMode": "this",
			},
			"register": "HRCNFG.EDGMODE",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_edgeModeB",
			"driverlibFunction" : "HRPWM_setMEPEdgeSelect",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "channel": "HRPWM_CHANNEL_B",
		        "mepEdgeMode": "this",
			},
			"register": "HRCNFG.EDGMODEB",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_cmpaHR",
			"driverlibFunction" : "HRPWM_setHiResCounterCompareValue",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "compModule": "HRPWM_COUNTER_COMPARE_A",
		        "hrCompCount": "this",
			},
			"register": "CMPA.CMPAHR",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_cmpbHR",
			"driverlibFunction" : "HRPWM_setHiResCounterCompareValue",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "compModule": "HRPWM_COUNTER_COMPARE_B",
		        "hrCompCount": "this",
			},
			"register": "CMPB.CMPBHR",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_HRLoadA",
			"driverlibFunction" : "HRPWM_setCounterCompareShadowLoadEvent",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "channel": "HRPWM_CHANNEL_A",
		        "loadEvent": "this",
			},
			"register": "HRCNFG.HRLOAD",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_HRLoadB",
			"driverlibFunction" : "HRPWM_setCounterCompareShadowLoadEvent",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "channel": "HRPWM_CHANNEL_B",
		        "loadEvent": "this",
			},
			"register": "HRCNFG.HRLOADB",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_periodEnable",
			"driverlibFunction" : "HRPWM_enablePeriodControl",
			"driverlibFunctionAlt" : "HRPWM_disablePeriodControl",
			"driverlibFunctionArg" : {
		        "base" : ""
			},
			"register": "HRPCTL.HRPE",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_tbprdHR",
			"driverlibFunction" : "HRPWM_setHiResTimeBasePeriod",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "hrPeriodCount": "this",
			},
			"register": "TBPRDHR",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_edgeModeDB",
			"driverlibFunction" : "HRPWM_setDeadbandMEPEdgeSelect",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "mepDBEdge": "this",
			},
			"register": "HRCNFG2.EDGMODEDB",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_DBredHR",
			"driverlibFunction" : "HRPWM_setHiResRisingEdgeDelay",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "hrRedCount": "this",
			},
			"register": "DBREDHR",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_controlModeDBA",
			"driverlibFunction" : "HRPWM_setRisingEdgeDelayLoadMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "loadEvent": "this",
			},
			"register": "HRCNFG2.CTLMODEDBRED",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_DBfedHR",
			"driverlibFunction" : "HRPWM_setHiResFallingEdgeDelayOnly",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "hrFedCount": "this",
			},
			"register": "DBFEDHR",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_controlModeDBB",
			"driverlibFunction" : "HRPWM_setFallingEdgeDelayLoadMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "loadEvent": "this",
			},
			"register": "HRCNFG2.CTLMODEDBFED",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_swapDBOutputs",
			"driverlibFunction" : "HRPWM_setOutputSwapMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "enableOutputSwap": "this",
			},
			"register": "HRCNFG.SWAPAB",
			"devices" : [
				"am263x"
			]
		},
		{
			"name" :  "hrpwm_DBBOutput",
			"driverlibFunction" : "HRPWM_setChannelBOutputPath",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "outputOnB": "this",
			},
			"register": "HRCNFG.SELOUTB",
			"devices" : [
				"am263x"
			]
		},

		// EPWM Global Loading
		{
			"name" :  "epwmGlobalLoad_gld",
			"driverlibFunction" : "EPWM_enableGlobalLoad",
			"driverlibFunctionAlt": "EPWM_disableGlobalLoad",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "GLDCTL.GLD",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmGlobalLoad_gldMode",
			"driverlibFunction" : "EPWM_setGlobalLoadTrigger",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "loadTrigger":"this",
			},
			"register": "GLDCTL.GLDMODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmGlobalLoad_gldPeriod",
			"driverlibFunction" : "EPWM_setGlobalLoadEventPrescale",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "prescalePulseCount":"this",
			},
			"register": "GLDCTL.GLDPRD",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmGlobalLoad_enableOneShot",
			"driverlibFunction" : "EPWM_enableGlobalLoadOneShotMode",
			"driverlibFunctionAlt":"EPWM_disableGlobalLoadOneShotMode",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "GLDCTL.OSHTMODE",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmGlobalLoad_oneShotMode",
			"driverlibFunction" : "EPWM_setGlobalLoadOneShotLatch",
			"driverlibFunctionAlt":"",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "GLDCTL2.OSHTLD",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmGlobalLoad_oneShotForce",
			"driverlibFunction" : "EPWM_forceGlobalLoadOneShotEvent",
			"driverlibFunctionAlt":"",
			"driverlibFunctionArg" : {
		        "base" : "",
			},
			"register": "GLDCTL2.GFRCLD",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmGlobalLoad_globalePWMLoadLink",
			"driverlibFunction" : "EPWM_setupEPWMLinks",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "epwmLink":"this",
		        "linkComp":"EPWM_LINK_GLDCTL2"

			},
			"register": "EPWMXLINK.GLDCTL2LINK",
			"devices" : [
                "am263x"
			]
		},
		// F28004x forward has lock
		{
			"name" :  "epwmLock",
			"driverlibFunction" : "EPWM_lockRegisters",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "registerGroup":"this"
			},
			"register": "EPWMLOCK",
			"devices" : [
                "am263x"
			]
		},

		// New sync scheme for F2838x forward
		{
			"name" :  "epwmTimebase_syncInPulseSource",
			"driverlibFunction" : "EPWM_setSyncInPulseSource",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "source":"this"
			},
			"register": "EPWMSYNCINSEL.SEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTimebase_syncOutPulseMode",
			"driverlibFunction" : "EPWM_enableSyncOutPulseSource",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "source":"this"
			},
			"register": "EPWMSYNCOUTEN",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmTimebase_oneShotSyncOutTrigger",
			"driverlibFunction" : "EPWM_setOneShotSyncOutTrigger",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "trigger":"this"
			},
			"register": "TBCTL3.OSSFRCEN",
			"devices" : [
                "am263x"
			]
		},

		// New CBC latch in DC module for F2838x forward
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_A_EPWM_DC_EVENT_1_latchMode",
			"driverlibFunction" : "EPWM_setDigitalCompareCBCLatchMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_A",
		        "dcEvent": "EPWM_DC_EVENT_1",
		        "latchMode": "EPWM_DC_CBC_LATCH_DISABLED",
			},
			"register": "DCACTL.EVT1LATSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_A_EPWM_DC_EVENT_1_latchClearEvent",
			"driverlibFunction" : "EPWM_selectDigitalCompareCBCLatchClearEvent",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_A",
		        "dcEvent": "EPWM_DC_EVENT_1",
		        "clearEvent": "EPWM_DC_CBC_LATCH_CLR_CNTR_ZERO",
			},
			"register": "DCACTL.EVT1LATCLRSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_A_EPWM_DC_EVENT_2_latchMode",
			"driverlibFunction" : "EPWM_setDigitalCompareCBCLatchMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_A",
		        "dcEvent": "EPWM_DC_EVENT_2",
		        "latchMode": "EPWM_DC_CBC_LATCH_DISABLED",
			},
			"register": "DCACTL.EVT2LATSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_A_EPWM_DC_EVENT_2_latchClearEvent",
			"driverlibFunction" : "EPWM_selectDigitalCompareCBCLatchClearEvent",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_A",
		        "dcEvent": "EPWM_DC_EVENT_2",
		        "clearEvent": "EPWM_DC_CBC_LATCH_CLR_CNTR_ZERO",
			},
			"register": "DCACTL.EVT2LATCLRSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_B_EPWM_DC_EVENT_1_latchMode",
			"driverlibFunction" : "EPWM_setDigitalCompareCBCLatchMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_B",
		        "dcEvent": "EPWM_DC_EVENT_1",
		        "latchMode": "EPWM_DC_CBC_LATCH_DISABLED",
			},
			"register": "DCBCTL.EVT1LATSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_B_EPWM_DC_EVENT_1_latchClearEvent",
			"driverlibFunction" : "EPWM_selectDigitalCompareCBCLatchClearEvent",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_B",
		        "dcEvent": "EPWM_DC_EVENT_1",
		        "clearEvent": "EPWM_DC_CBC_LATCH_CLR_CNTR_ZERO",
			},
			"register": "DCBCTL.EVT1LATCLRSEL",
			"devices" : [
                "am263x"
			]
		},


		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_B_EPWM_DC_EVENT_2_latchMode",
			"driverlibFunction" : "EPWM_setDigitalCompareCBCLatchMode",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_B",
		        "dcEvent": "EPWM_DC_EVENT_2",
		        "latchMode": "EPWM_DC_CBC_LATCH_DISABLED",
			},
			"register": "DCBCTL.EVT2LATSEL",
			"devices" : [
                "am263x"
			]
		},
		{
			"name" :  "epwmDigitalCompare_EPWM_DC_MODULE_B_EPWM_DC_EVENT_2_latchClearEvent",
			"driverlibFunction" : "EPWM_selectDigitalCompareCBCLatchClearEvent",
			"driverlibFunctionArg" : {
		        "base" : "",
		        "dcModule": "EPWM_DC_MODULE_B",
		        "dcEvent": "EPWM_DC_EVENT_2",
		        "clearEvent": "EPWM_DC_CBC_LATCH_CLR_CNTR_ZERO",
			},
			"register": "DCBCTL.EVT2LATCLRSEL",
			"devices" : [
                "am263x"
			]
		},

	]
}