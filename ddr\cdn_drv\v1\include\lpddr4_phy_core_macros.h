/* parasoft-begin-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" parasoft-begin-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */

/**********************************************************************
 * Copyright (C) 2012-2022 Cadence Design Systems, Inc.
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 
 * 1. Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THIS FILE IS AUTOMATICALLY GENERATED, DO NOT EDIT
 *
 **********************************************************************/

#ifndef REG_LPDDR4_PHY_CORE_MACROS_H_
#define REG_LPDDR4_PHY_CORE_MACROS_H_

#define LPDDR4__DENALI_PHY_1792_READ_MASK                            0x00000003U
#define LPDDR4__DENALI_PHY_1792_WRITE_MASK                           0x00000003U
#define LPDDR4__DENALI_PHY_1792__PHY_FREQ_SEL_MASK                   0x00000003U
#define LPDDR4__DENALI_PHY_1792__PHY_FREQ_SEL_SHIFT                           0U
#define LPDDR4__DENALI_PHY_1792__PHY_FREQ_SEL_WIDTH                           2U
#define LPDDR4__PHY_FREQ_SEL__REG DENALI_PHY_1792
#define LPDDR4__PHY_FREQ_SEL__FLD LPDDR4__DENALI_PHY_1792__PHY_FREQ_SEL

#define LPDDR4__DENALI_PHY_1793_READ_MASK                            0x1F030101U
#define LPDDR4__DENALI_PHY_1793_WRITE_MASK                           0x1F030101U
#define LPDDR4__DENALI_PHY_1793__PHY_FREQ_SEL_FROM_REGIF_MASK        0x00000001U
#define LPDDR4__DENALI_PHY_1793__PHY_FREQ_SEL_FROM_REGIF_SHIFT                0U
#define LPDDR4__DENALI_PHY_1793__PHY_FREQ_SEL_FROM_REGIF_WIDTH                1U
#define LPDDR4__DENALI_PHY_1793__PHY_FREQ_SEL_FROM_REGIF_WOCLR                0U
#define LPDDR4__DENALI_PHY_1793__PHY_FREQ_SEL_FROM_REGIF_WOSET                0U
#define LPDDR4__PHY_FREQ_SEL_FROM_REGIF__REG DENALI_PHY_1793
#define LPDDR4__PHY_FREQ_SEL_FROM_REGIF__FLD LPDDR4__DENALI_PHY_1793__PHY_FREQ_SEL_FROM_REGIF

#define LPDDR4__DENALI_PHY_1793__PHY_FREQ_SEL_MULTICAST_EN_MASK      0x00000100U
#define LPDDR4__DENALI_PHY_1793__PHY_FREQ_SEL_MULTICAST_EN_SHIFT              8U
#define LPDDR4__DENALI_PHY_1793__PHY_FREQ_SEL_MULTICAST_EN_WIDTH              1U
#define LPDDR4__DENALI_PHY_1793__PHY_FREQ_SEL_MULTICAST_EN_WOCLR              0U
#define LPDDR4__DENALI_PHY_1793__PHY_FREQ_SEL_MULTICAST_EN_WOSET              0U
#define LPDDR4__PHY_FREQ_SEL_MULTICAST_EN__REG DENALI_PHY_1793
#define LPDDR4__PHY_FREQ_SEL_MULTICAST_EN__FLD LPDDR4__DENALI_PHY_1793__PHY_FREQ_SEL_MULTICAST_EN

#define LPDDR4__DENALI_PHY_1793__PHY_FREQ_SEL_INDEX_MASK             0x00030000U
#define LPDDR4__DENALI_PHY_1793__PHY_FREQ_SEL_INDEX_SHIFT                    16U
#define LPDDR4__DENALI_PHY_1793__PHY_FREQ_SEL_INDEX_WIDTH                     2U
#define LPDDR4__PHY_FREQ_SEL_INDEX__REG DENALI_PHY_1793
#define LPDDR4__PHY_FREQ_SEL_INDEX__FLD LPDDR4__DENALI_PHY_1793__PHY_FREQ_SEL_INDEX

#define LPDDR4__DENALI_PHY_1793__PHY_SW_GRP0_SHIFT_0_MASK            0x1F000000U
#define LPDDR4__DENALI_PHY_1793__PHY_SW_GRP0_SHIFT_0_SHIFT                   24U
#define LPDDR4__DENALI_PHY_1793__PHY_SW_GRP0_SHIFT_0_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP0_SHIFT_0__REG DENALI_PHY_1793
#define LPDDR4__PHY_SW_GRP0_SHIFT_0__FLD LPDDR4__DENALI_PHY_1793__PHY_SW_GRP0_SHIFT_0

#define LPDDR4__DENALI_PHY_1794_READ_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_1794_WRITE_MASK                           0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_1794__PHY_SW_GRP1_SHIFT_0_MASK            0x0000001FU
#define LPDDR4__DENALI_PHY_1794__PHY_SW_GRP1_SHIFT_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1794__PHY_SW_GRP1_SHIFT_0_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP1_SHIFT_0__REG DENALI_PHY_1794
#define LPDDR4__PHY_SW_GRP1_SHIFT_0__FLD LPDDR4__DENALI_PHY_1794__PHY_SW_GRP1_SHIFT_0

#define LPDDR4__DENALI_PHY_1794__PHY_SW_GRP2_SHIFT_0_MASK            0x00001F00U
#define LPDDR4__DENALI_PHY_1794__PHY_SW_GRP2_SHIFT_0_SHIFT                    8U
#define LPDDR4__DENALI_PHY_1794__PHY_SW_GRP2_SHIFT_0_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP2_SHIFT_0__REG DENALI_PHY_1794
#define LPDDR4__PHY_SW_GRP2_SHIFT_0__FLD LPDDR4__DENALI_PHY_1794__PHY_SW_GRP2_SHIFT_0

#define LPDDR4__DENALI_PHY_1794__PHY_SW_GRP3_SHIFT_0_MASK            0x001F0000U
#define LPDDR4__DENALI_PHY_1794__PHY_SW_GRP3_SHIFT_0_SHIFT                   16U
#define LPDDR4__DENALI_PHY_1794__PHY_SW_GRP3_SHIFT_0_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP3_SHIFT_0__REG DENALI_PHY_1794
#define LPDDR4__PHY_SW_GRP3_SHIFT_0__FLD LPDDR4__DENALI_PHY_1794__PHY_SW_GRP3_SHIFT_0

#define LPDDR4__DENALI_PHY_1794__PHY_SW_GRP0_SHIFT_1_MASK            0x1F000000U
#define LPDDR4__DENALI_PHY_1794__PHY_SW_GRP0_SHIFT_1_SHIFT                   24U
#define LPDDR4__DENALI_PHY_1794__PHY_SW_GRP0_SHIFT_1_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP0_SHIFT_1__REG DENALI_PHY_1794
#define LPDDR4__PHY_SW_GRP0_SHIFT_1__FLD LPDDR4__DENALI_PHY_1794__PHY_SW_GRP0_SHIFT_1

#define LPDDR4__DENALI_PHY_1795_READ_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_1795_WRITE_MASK                           0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_1795__PHY_SW_GRP1_SHIFT_1_MASK            0x0000001FU
#define LPDDR4__DENALI_PHY_1795__PHY_SW_GRP1_SHIFT_1_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1795__PHY_SW_GRP1_SHIFT_1_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP1_SHIFT_1__REG DENALI_PHY_1795
#define LPDDR4__PHY_SW_GRP1_SHIFT_1__FLD LPDDR4__DENALI_PHY_1795__PHY_SW_GRP1_SHIFT_1

#define LPDDR4__DENALI_PHY_1795__PHY_SW_GRP2_SHIFT_1_MASK            0x00001F00U
#define LPDDR4__DENALI_PHY_1795__PHY_SW_GRP2_SHIFT_1_SHIFT                    8U
#define LPDDR4__DENALI_PHY_1795__PHY_SW_GRP2_SHIFT_1_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP2_SHIFT_1__REG DENALI_PHY_1795
#define LPDDR4__PHY_SW_GRP2_SHIFT_1__FLD LPDDR4__DENALI_PHY_1795__PHY_SW_GRP2_SHIFT_1

#define LPDDR4__DENALI_PHY_1795__PHY_SW_GRP3_SHIFT_1_MASK            0x001F0000U
#define LPDDR4__DENALI_PHY_1795__PHY_SW_GRP3_SHIFT_1_SHIFT                   16U
#define LPDDR4__DENALI_PHY_1795__PHY_SW_GRP3_SHIFT_1_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP3_SHIFT_1__REG DENALI_PHY_1795
#define LPDDR4__PHY_SW_GRP3_SHIFT_1__FLD LPDDR4__DENALI_PHY_1795__PHY_SW_GRP3_SHIFT_1

#define LPDDR4__DENALI_PHY_1795__PHY_SW_GRP0_SHIFT_2_MASK            0x1F000000U
#define LPDDR4__DENALI_PHY_1795__PHY_SW_GRP0_SHIFT_2_SHIFT                   24U
#define LPDDR4__DENALI_PHY_1795__PHY_SW_GRP0_SHIFT_2_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP0_SHIFT_2__REG DENALI_PHY_1795
#define LPDDR4__PHY_SW_GRP0_SHIFT_2__FLD LPDDR4__DENALI_PHY_1795__PHY_SW_GRP0_SHIFT_2

#define LPDDR4__DENALI_PHY_1796_READ_MASK                            0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_1796_WRITE_MASK                           0x1F1F1F1FU
#define LPDDR4__DENALI_PHY_1796__PHY_SW_GRP1_SHIFT_2_MASK            0x0000001FU
#define LPDDR4__DENALI_PHY_1796__PHY_SW_GRP1_SHIFT_2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1796__PHY_SW_GRP1_SHIFT_2_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP1_SHIFT_2__REG DENALI_PHY_1796
#define LPDDR4__PHY_SW_GRP1_SHIFT_2__FLD LPDDR4__DENALI_PHY_1796__PHY_SW_GRP1_SHIFT_2

#define LPDDR4__DENALI_PHY_1796__PHY_SW_GRP2_SHIFT_2_MASK            0x00001F00U
#define LPDDR4__DENALI_PHY_1796__PHY_SW_GRP2_SHIFT_2_SHIFT                    8U
#define LPDDR4__DENALI_PHY_1796__PHY_SW_GRP2_SHIFT_2_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP2_SHIFT_2__REG DENALI_PHY_1796
#define LPDDR4__PHY_SW_GRP2_SHIFT_2__FLD LPDDR4__DENALI_PHY_1796__PHY_SW_GRP2_SHIFT_2

#define LPDDR4__DENALI_PHY_1796__PHY_SW_GRP3_SHIFT_2_MASK            0x001F0000U
#define LPDDR4__DENALI_PHY_1796__PHY_SW_GRP3_SHIFT_2_SHIFT                   16U
#define LPDDR4__DENALI_PHY_1796__PHY_SW_GRP3_SHIFT_2_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP3_SHIFT_2__REG DENALI_PHY_1796
#define LPDDR4__PHY_SW_GRP3_SHIFT_2__FLD LPDDR4__DENALI_PHY_1796__PHY_SW_GRP3_SHIFT_2

#define LPDDR4__DENALI_PHY_1796__PHY_SW_GRP0_SHIFT_3_MASK            0x1F000000U
#define LPDDR4__DENALI_PHY_1796__PHY_SW_GRP0_SHIFT_3_SHIFT                   24U
#define LPDDR4__DENALI_PHY_1796__PHY_SW_GRP0_SHIFT_3_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP0_SHIFT_3__REG DENALI_PHY_1796
#define LPDDR4__PHY_SW_GRP0_SHIFT_3__FLD LPDDR4__DENALI_PHY_1796__PHY_SW_GRP0_SHIFT_3

#define LPDDR4__DENALI_PHY_1797_READ_MASK                            0x001F1F1FU
#define LPDDR4__DENALI_PHY_1797_WRITE_MASK                           0x001F1F1FU
#define LPDDR4__DENALI_PHY_1797__PHY_SW_GRP1_SHIFT_3_MASK            0x0000001FU
#define LPDDR4__DENALI_PHY_1797__PHY_SW_GRP1_SHIFT_3_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1797__PHY_SW_GRP1_SHIFT_3_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP1_SHIFT_3__REG DENALI_PHY_1797
#define LPDDR4__PHY_SW_GRP1_SHIFT_3__FLD LPDDR4__DENALI_PHY_1797__PHY_SW_GRP1_SHIFT_3

#define LPDDR4__DENALI_PHY_1797__PHY_SW_GRP2_SHIFT_3_MASK            0x00001F00U
#define LPDDR4__DENALI_PHY_1797__PHY_SW_GRP2_SHIFT_3_SHIFT                    8U
#define LPDDR4__DENALI_PHY_1797__PHY_SW_GRP2_SHIFT_3_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP2_SHIFT_3__REG DENALI_PHY_1797
#define LPDDR4__PHY_SW_GRP2_SHIFT_3__FLD LPDDR4__DENALI_PHY_1797__PHY_SW_GRP2_SHIFT_3

#define LPDDR4__DENALI_PHY_1797__PHY_SW_GRP3_SHIFT_3_MASK            0x001F0000U
#define LPDDR4__DENALI_PHY_1797__PHY_SW_GRP3_SHIFT_3_SHIFT                   16U
#define LPDDR4__DENALI_PHY_1797__PHY_SW_GRP3_SHIFT_3_WIDTH                    5U
#define LPDDR4__PHY_SW_GRP3_SHIFT_3__REG DENALI_PHY_1797
#define LPDDR4__PHY_SW_GRP3_SHIFT_3__FLD LPDDR4__DENALI_PHY_1797__PHY_SW_GRP3_SHIFT_3

#define LPDDR4__DENALI_PHY_1798_READ_MASK                            0x011F07FFU
#define LPDDR4__DENALI_PHY_1798_WRITE_MASK                           0x011F07FFU
#define LPDDR4__DENALI_PHY_1798__PHY_GRP_BYPASS_SLAVE_DELAY_MASK     0x000007FFU
#define LPDDR4__DENALI_PHY_1798__PHY_GRP_BYPASS_SLAVE_DELAY_SHIFT             0U
#define LPDDR4__DENALI_PHY_1798__PHY_GRP_BYPASS_SLAVE_DELAY_WIDTH            11U
#define LPDDR4__PHY_GRP_BYPASS_SLAVE_DELAY__REG DENALI_PHY_1798
#define LPDDR4__PHY_GRP_BYPASS_SLAVE_DELAY__FLD LPDDR4__DENALI_PHY_1798__PHY_GRP_BYPASS_SLAVE_DELAY

#define LPDDR4__DENALI_PHY_1798__PHY_SW_GRP_BYPASS_SHIFT_MASK        0x001F0000U
#define LPDDR4__DENALI_PHY_1798__PHY_SW_GRP_BYPASS_SHIFT_SHIFT               16U
#define LPDDR4__DENALI_PHY_1798__PHY_SW_GRP_BYPASS_SHIFT_WIDTH                5U
#define LPDDR4__PHY_SW_GRP_BYPASS_SHIFT__REG DENALI_PHY_1798
#define LPDDR4__PHY_SW_GRP_BYPASS_SHIFT__FLD LPDDR4__DENALI_PHY_1798__PHY_SW_GRP_BYPASS_SHIFT

#define LPDDR4__DENALI_PHY_1798__PHY_GRP_BYPASS_OVERRIDE_MASK        0x01000000U
#define LPDDR4__DENALI_PHY_1798__PHY_GRP_BYPASS_OVERRIDE_SHIFT               24U
#define LPDDR4__DENALI_PHY_1798__PHY_GRP_BYPASS_OVERRIDE_WIDTH                1U
#define LPDDR4__DENALI_PHY_1798__PHY_GRP_BYPASS_OVERRIDE_WOCLR                0U
#define LPDDR4__DENALI_PHY_1798__PHY_GRP_BYPASS_OVERRIDE_WOSET                0U
#define LPDDR4__PHY_GRP_BYPASS_OVERRIDE__REG DENALI_PHY_1798
#define LPDDR4__PHY_GRP_BYPASS_OVERRIDE__FLD LPDDR4__DENALI_PHY_1798__PHY_GRP_BYPASS_OVERRIDE

#define LPDDR4__DENALI_PHY_1799_READ_MASK                            0x07FF0100U
#define LPDDR4__DENALI_PHY_1799_WRITE_MASK                           0x07FF0100U
#define LPDDR4__DENALI_PHY_1799__SC_PHY_MANUAL_UPDATE_MASK           0x00000001U
#define LPDDR4__DENALI_PHY_1799__SC_PHY_MANUAL_UPDATE_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1799__SC_PHY_MANUAL_UPDATE_WIDTH                   1U
#define LPDDR4__DENALI_PHY_1799__SC_PHY_MANUAL_UPDATE_WOCLR                   0U
#define LPDDR4__DENALI_PHY_1799__SC_PHY_MANUAL_UPDATE_WOSET                   0U
#define LPDDR4__SC_PHY_MANUAL_UPDATE__REG DENALI_PHY_1799
#define LPDDR4__SC_PHY_MANUAL_UPDATE__FLD LPDDR4__DENALI_PHY_1799__SC_PHY_MANUAL_UPDATE

#define LPDDR4__DENALI_PHY_1799__PHY_MANUAL_UPDATE_PHYUPD_ENABLE_MASK 0x00000100U
#define LPDDR4__DENALI_PHY_1799__PHY_MANUAL_UPDATE_PHYUPD_ENABLE_SHIFT        8U
#define LPDDR4__DENALI_PHY_1799__PHY_MANUAL_UPDATE_PHYUPD_ENABLE_WIDTH        1U
#define LPDDR4__DENALI_PHY_1799__PHY_MANUAL_UPDATE_PHYUPD_ENABLE_WOCLR        0U
#define LPDDR4__DENALI_PHY_1799__PHY_MANUAL_UPDATE_PHYUPD_ENABLE_WOSET        0U
#define LPDDR4__PHY_MANUAL_UPDATE_PHYUPD_ENABLE__REG DENALI_PHY_1799
#define LPDDR4__PHY_MANUAL_UPDATE_PHYUPD_ENABLE__FLD LPDDR4__DENALI_PHY_1799__PHY_MANUAL_UPDATE_PHYUPD_ENABLE

#define LPDDR4__DENALI_PHY_1799__PHY_CSLVL_START_MASK                0x07FF0000U
#define LPDDR4__DENALI_PHY_1799__PHY_CSLVL_START_SHIFT                       16U
#define LPDDR4__DENALI_PHY_1799__PHY_CSLVL_START_WIDTH                       11U
#define LPDDR4__PHY_CSLVL_START__REG DENALI_PHY_1799
#define LPDDR4__PHY_CSLVL_START__FLD LPDDR4__DENALI_PHY_1799__PHY_CSLVL_START

#define LPDDR4__DENALI_PHY_1800_READ_MASK                            0x000107FFU
#define LPDDR4__DENALI_PHY_1800_WRITE_MASK                           0x000107FFU
#define LPDDR4__DENALI_PHY_1800__PHY_CSLVL_COARSE_DLY_MASK           0x000007FFU
#define LPDDR4__DENALI_PHY_1800__PHY_CSLVL_COARSE_DLY_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1800__PHY_CSLVL_COARSE_DLY_WIDTH                  11U
#define LPDDR4__PHY_CSLVL_COARSE_DLY__REG DENALI_PHY_1800
#define LPDDR4__PHY_CSLVL_COARSE_DLY__FLD LPDDR4__DENALI_PHY_1800__PHY_CSLVL_COARSE_DLY

#define LPDDR4__DENALI_PHY_1800__PHY_CSLVL_DEBUG_MODE_MASK           0x00010000U
#define LPDDR4__DENALI_PHY_1800__PHY_CSLVL_DEBUG_MODE_SHIFT                  16U
#define LPDDR4__DENALI_PHY_1800__PHY_CSLVL_DEBUG_MODE_WIDTH                   1U
#define LPDDR4__DENALI_PHY_1800__PHY_CSLVL_DEBUG_MODE_WOCLR                   0U
#define LPDDR4__DENALI_PHY_1800__PHY_CSLVL_DEBUG_MODE_WOSET                   0U
#define LPDDR4__PHY_CSLVL_DEBUG_MODE__REG DENALI_PHY_1800
#define LPDDR4__PHY_CSLVL_DEBUG_MODE__FLD LPDDR4__DENALI_PHY_1800__PHY_CSLVL_DEBUG_MODE

#define LPDDR4__DENALI_PHY_1800__SC_PHY_CSLVL_DEBUG_CONT_MASK        0x01000000U
#define LPDDR4__DENALI_PHY_1800__SC_PHY_CSLVL_DEBUG_CONT_SHIFT               24U
#define LPDDR4__DENALI_PHY_1800__SC_PHY_CSLVL_DEBUG_CONT_WIDTH                1U
#define LPDDR4__DENALI_PHY_1800__SC_PHY_CSLVL_DEBUG_CONT_WOCLR                0U
#define LPDDR4__DENALI_PHY_1800__SC_PHY_CSLVL_DEBUG_CONT_WOSET                0U
#define LPDDR4__SC_PHY_CSLVL_DEBUG_CONT__REG DENALI_PHY_1800
#define LPDDR4__SC_PHY_CSLVL_DEBUG_CONT__FLD LPDDR4__DENALI_PHY_1800__SC_PHY_CSLVL_DEBUG_CONT

#define LPDDR4__DENALI_PHY_1801__SC_PHY_CSLVL_ERROR_CLR_MASK         0x00000001U
#define LPDDR4__DENALI_PHY_1801__SC_PHY_CSLVL_ERROR_CLR_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1801__SC_PHY_CSLVL_ERROR_CLR_WIDTH                 1U
#define LPDDR4__DENALI_PHY_1801__SC_PHY_CSLVL_ERROR_CLR_WOCLR                 0U
#define LPDDR4__DENALI_PHY_1801__SC_PHY_CSLVL_ERROR_CLR_WOSET                 0U
#define LPDDR4__SC_PHY_CSLVL_ERROR_CLR__REG DENALI_PHY_1801
#define LPDDR4__SC_PHY_CSLVL_ERROR_CLR__FLD LPDDR4__DENALI_PHY_1801__SC_PHY_CSLVL_ERROR_CLR

#define LPDDR4__DENALI_PHY_1802_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1802_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1802__PHY_CSLVL_OBS0_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1802__PHY_CSLVL_OBS0_SHIFT                         0U
#define LPDDR4__DENALI_PHY_1802__PHY_CSLVL_OBS0_WIDTH                        32U
#define LPDDR4__PHY_CSLVL_OBS0__REG DENALI_PHY_1802
#define LPDDR4__PHY_CSLVL_OBS0__FLD LPDDR4__DENALI_PHY_1802__PHY_CSLVL_OBS0

#define LPDDR4__DENALI_PHY_1803_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1803_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1803__PHY_CSLVL_OBS1_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1803__PHY_CSLVL_OBS1_SHIFT                         0U
#define LPDDR4__DENALI_PHY_1803__PHY_CSLVL_OBS1_WIDTH                        32U
#define LPDDR4__PHY_CSLVL_OBS1__REG DENALI_PHY_1803
#define LPDDR4__PHY_CSLVL_OBS1__FLD LPDDR4__DENALI_PHY_1803__PHY_CSLVL_OBS1

#define LPDDR4__DENALI_PHY_1804_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1804_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1804__PHY_CSLVL_OBS2_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1804__PHY_CSLVL_OBS2_SHIFT                         0U
#define LPDDR4__DENALI_PHY_1804__PHY_CSLVL_OBS2_WIDTH                        32U
#define LPDDR4__PHY_CSLVL_OBS2__REG DENALI_PHY_1804
#define LPDDR4__PHY_CSLVL_OBS2__FLD LPDDR4__DENALI_PHY_1804__PHY_CSLVL_OBS2

#define LPDDR4__DENALI_PHY_1805_READ_MASK                            0x0101FF01U
#define LPDDR4__DENALI_PHY_1805_WRITE_MASK                           0x0101FF01U
#define LPDDR4__DENALI_PHY_1805__PHY_CSLVL_ENABLE_MASK               0x00000001U
#define LPDDR4__DENALI_PHY_1805__PHY_CSLVL_ENABLE_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1805__PHY_CSLVL_ENABLE_WIDTH                       1U
#define LPDDR4__DENALI_PHY_1805__PHY_CSLVL_ENABLE_WOCLR                       0U
#define LPDDR4__DENALI_PHY_1805__PHY_CSLVL_ENABLE_WOSET                       0U
#define LPDDR4__PHY_CSLVL_ENABLE__REG DENALI_PHY_1805
#define LPDDR4__PHY_CSLVL_ENABLE__FLD LPDDR4__DENALI_PHY_1805__PHY_CSLVL_ENABLE

#define LPDDR4__DENALI_PHY_1805__PHY_CSLVL_PERIODIC_START_OFFSET_MASK 0x0001FF00U
#define LPDDR4__DENALI_PHY_1805__PHY_CSLVL_PERIODIC_START_OFFSET_SHIFT        8U
#define LPDDR4__DENALI_PHY_1805__PHY_CSLVL_PERIODIC_START_OFFSET_WIDTH        9U
#define LPDDR4__PHY_CSLVL_PERIODIC_START_OFFSET__REG DENALI_PHY_1805
#define LPDDR4__PHY_CSLVL_PERIODIC_START_OFFSET__FLD LPDDR4__DENALI_PHY_1805__PHY_CSLVL_PERIODIC_START_OFFSET

#define LPDDR4__DENALI_PHY_1805__PHY_LP4_BOOT_DISABLE_MASK           0x01000000U
#define LPDDR4__DENALI_PHY_1805__PHY_LP4_BOOT_DISABLE_SHIFT                  24U
#define LPDDR4__DENALI_PHY_1805__PHY_LP4_BOOT_DISABLE_WIDTH                   1U
#define LPDDR4__DENALI_PHY_1805__PHY_LP4_BOOT_DISABLE_WOCLR                   0U
#define LPDDR4__DENALI_PHY_1805__PHY_LP4_BOOT_DISABLE_WOSET                   0U
#define LPDDR4__PHY_LP4_BOOT_DISABLE__REG DENALI_PHY_1805
#define LPDDR4__PHY_LP4_BOOT_DISABLE__FLD LPDDR4__DENALI_PHY_1805__PHY_LP4_BOOT_DISABLE

#define LPDDR4__DENALI_PHY_1806_READ_MASK                            0x0007FF0FU
#define LPDDR4__DENALI_PHY_1806_WRITE_MASK                           0x0007FF0FU
#define LPDDR4__DENALI_PHY_1806__PHY_CSLVL_CS_MAP_MASK               0x0000000FU
#define LPDDR4__DENALI_PHY_1806__PHY_CSLVL_CS_MAP_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1806__PHY_CSLVL_CS_MAP_WIDTH                       4U
#define LPDDR4__PHY_CSLVL_CS_MAP__REG DENALI_PHY_1806
#define LPDDR4__PHY_CSLVL_CS_MAP__FLD LPDDR4__DENALI_PHY_1806__PHY_CSLVL_CS_MAP

#define LPDDR4__DENALI_PHY_1806__PHY_CSLVL_QTR_MASK                  0x0007FF00U
#define LPDDR4__DENALI_PHY_1806__PHY_CSLVL_QTR_SHIFT                          8U
#define LPDDR4__DENALI_PHY_1806__PHY_CSLVL_QTR_WIDTH                         11U
#define LPDDR4__PHY_CSLVL_QTR__REG DENALI_PHY_1806
#define LPDDR4__PHY_CSLVL_QTR__FLD LPDDR4__DENALI_PHY_1806__PHY_CSLVL_QTR

#define LPDDR4__DENALI_PHY_1807_READ_MASK                            0xFF0F07FFU
#define LPDDR4__DENALI_PHY_1807_WRITE_MASK                           0xFF0F07FFU
#define LPDDR4__DENALI_PHY_1807__PHY_CSLVL_COARSE_CHK_MASK           0x000007FFU
#define LPDDR4__DENALI_PHY_1807__PHY_CSLVL_COARSE_CHK_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1807__PHY_CSLVL_COARSE_CHK_WIDTH                  11U
#define LPDDR4__PHY_CSLVL_COARSE_CHK__REG DENALI_PHY_1807
#define LPDDR4__PHY_CSLVL_COARSE_CHK__FLD LPDDR4__DENALI_PHY_1807__PHY_CSLVL_COARSE_CHK

#define LPDDR4__DENALI_PHY_1807__PHY_CSLVL_COARSE_CAPTURE_CNT_MASK   0x000F0000U
#define LPDDR4__DENALI_PHY_1807__PHY_CSLVL_COARSE_CAPTURE_CNT_SHIFT          16U
#define LPDDR4__DENALI_PHY_1807__PHY_CSLVL_COARSE_CAPTURE_CNT_WIDTH           4U
#define LPDDR4__PHY_CSLVL_COARSE_CAPTURE_CNT__REG DENALI_PHY_1807
#define LPDDR4__PHY_CSLVL_COARSE_CAPTURE_CNT__FLD LPDDR4__DENALI_PHY_1807__PHY_CSLVL_COARSE_CAPTURE_CNT

#define LPDDR4__DENALI_PHY_1807__PHY_CALVL_CS_MAP_MASK               0xFF000000U
#define LPDDR4__DENALI_PHY_1807__PHY_CALVL_CS_MAP_SHIFT                      24U
#define LPDDR4__DENALI_PHY_1807__PHY_CALVL_CS_MAP_WIDTH                       8U
#define LPDDR4__PHY_CALVL_CS_MAP__REG DENALI_PHY_1807
#define LPDDR4__PHY_CALVL_CS_MAP__FLD LPDDR4__DENALI_PHY_1807__PHY_CALVL_CS_MAP

#define LPDDR4__DENALI_PHY_1808_READ_MASK                            0x01030007U
#define LPDDR4__DENALI_PHY_1808_WRITE_MASK                           0x01030007U
#define LPDDR4__DENALI_PHY_1808__PHY_ADRCTL_SLAVE_LOOP_CNT_UPDATE_MASK 0x00000007U
#define LPDDR4__DENALI_PHY_1808__PHY_ADRCTL_SLAVE_LOOP_CNT_UPDATE_SHIFT       0U
#define LPDDR4__DENALI_PHY_1808__PHY_ADRCTL_SLAVE_LOOP_CNT_UPDATE_WIDTH       3U
#define LPDDR4__PHY_ADRCTL_SLAVE_LOOP_CNT_UPDATE__REG DENALI_PHY_1808
#define LPDDR4__PHY_ADRCTL_SLAVE_LOOP_CNT_UPDATE__FLD LPDDR4__DENALI_PHY_1808__PHY_ADRCTL_SLAVE_LOOP_CNT_UPDATE

#define LPDDR4__DENALI_PHY_1808__PHY_ADRCTL_SNAP_OBS_REGS_MASK       0x00000100U
#define LPDDR4__DENALI_PHY_1808__PHY_ADRCTL_SNAP_OBS_REGS_SHIFT               8U
#define LPDDR4__DENALI_PHY_1808__PHY_ADRCTL_SNAP_OBS_REGS_WIDTH               1U
#define LPDDR4__DENALI_PHY_1808__PHY_ADRCTL_SNAP_OBS_REGS_WOCLR               0U
#define LPDDR4__DENALI_PHY_1808__PHY_ADRCTL_SNAP_OBS_REGS_WOSET               0U
#define LPDDR4__PHY_ADRCTL_SNAP_OBS_REGS__REG DENALI_PHY_1808
#define LPDDR4__PHY_ADRCTL_SNAP_OBS_REGS__FLD LPDDR4__DENALI_PHY_1808__PHY_ADRCTL_SNAP_OBS_REGS

#define LPDDR4__DENALI_PHY_1808__PHY_DFI_PHYUPD_TYPE_MASK            0x00030000U
#define LPDDR4__DENALI_PHY_1808__PHY_DFI_PHYUPD_TYPE_SHIFT                   16U
#define LPDDR4__DENALI_PHY_1808__PHY_DFI_PHYUPD_TYPE_WIDTH                    2U
#define LPDDR4__PHY_DFI_PHYUPD_TYPE__REG DENALI_PHY_1808
#define LPDDR4__PHY_DFI_PHYUPD_TYPE__FLD LPDDR4__DENALI_PHY_1808__PHY_DFI_PHYUPD_TYPE

#define LPDDR4__DENALI_PHY_1808__PHY_ADRCTL_LPDDR_MASK               0x01000000U
#define LPDDR4__DENALI_PHY_1808__PHY_ADRCTL_LPDDR_SHIFT                      24U
#define LPDDR4__DENALI_PHY_1808__PHY_ADRCTL_LPDDR_WIDTH                       1U
#define LPDDR4__DENALI_PHY_1808__PHY_ADRCTL_LPDDR_WOCLR                       0U
#define LPDDR4__DENALI_PHY_1808__PHY_ADRCTL_LPDDR_WOSET                       0U
#define LPDDR4__PHY_ADRCTL_LPDDR__REG DENALI_PHY_1808
#define LPDDR4__PHY_ADRCTL_LPDDR__FLD LPDDR4__DENALI_PHY_1808__PHY_ADRCTL_LPDDR

#define LPDDR4__DENALI_PHY_1809_READ_MASK                            0xFFFF0101U
#define LPDDR4__DENALI_PHY_1809_WRITE_MASK                           0xFFFF0101U
#define LPDDR4__DENALI_PHY_1809__PHY_LP4_ACTIVE_MASK                 0x00000001U
#define LPDDR4__DENALI_PHY_1809__PHY_LP4_ACTIVE_SHIFT                         0U
#define LPDDR4__DENALI_PHY_1809__PHY_LP4_ACTIVE_WIDTH                         1U
#define LPDDR4__DENALI_PHY_1809__PHY_LP4_ACTIVE_WOCLR                         0U
#define LPDDR4__DENALI_PHY_1809__PHY_LP4_ACTIVE_WOSET                         0U
#define LPDDR4__PHY_LP4_ACTIVE__REG DENALI_PHY_1809
#define LPDDR4__PHY_LP4_ACTIVE__FLD LPDDR4__DENALI_PHY_1809__PHY_LP4_ACTIVE

#define LPDDR4__DENALI_PHY_1809__PHY_LPDDR3_CS_MASK                  0x00000100U
#define LPDDR4__DENALI_PHY_1809__PHY_LPDDR3_CS_SHIFT                          8U
#define LPDDR4__DENALI_PHY_1809__PHY_LPDDR3_CS_WIDTH                          1U
#define LPDDR4__DENALI_PHY_1809__PHY_LPDDR3_CS_WOCLR                          0U
#define LPDDR4__DENALI_PHY_1809__PHY_LPDDR3_CS_WOSET                          0U
#define LPDDR4__PHY_LPDDR3_CS__REG DENALI_PHY_1809
#define LPDDR4__PHY_LPDDR3_CS__FLD LPDDR4__DENALI_PHY_1809__PHY_LPDDR3_CS

#define LPDDR4__DENALI_PHY_1809__PHY_CLK_DC_CAL_SAMPLE_WAIT_MASK     0x00FF0000U
#define LPDDR4__DENALI_PHY_1809__PHY_CLK_DC_CAL_SAMPLE_WAIT_SHIFT            16U
#define LPDDR4__DENALI_PHY_1809__PHY_CLK_DC_CAL_SAMPLE_WAIT_WIDTH             8U
#define LPDDR4__PHY_CLK_DC_CAL_SAMPLE_WAIT__REG DENALI_PHY_1809
#define LPDDR4__PHY_CLK_DC_CAL_SAMPLE_WAIT__FLD LPDDR4__DENALI_PHY_1809__PHY_CLK_DC_CAL_SAMPLE_WAIT

#define LPDDR4__DENALI_PHY_1809__PHY_CLK_DC_CAL_TIMEOUT_MASK         0xFF000000U
#define LPDDR4__DENALI_PHY_1809__PHY_CLK_DC_CAL_TIMEOUT_SHIFT                24U
#define LPDDR4__DENALI_PHY_1809__PHY_CLK_DC_CAL_TIMEOUT_WIDTH                 8U
#define LPDDR4__PHY_CLK_DC_CAL_TIMEOUT__REG DENALI_PHY_1809
#define LPDDR4__PHY_CLK_DC_CAL_TIMEOUT__FLD LPDDR4__DENALI_PHY_1809__PHY_CLK_DC_CAL_TIMEOUT

#define LPDDR4__DENALI_PHY_1810_READ_MASK                            0xFF3F0103U
#define LPDDR4__DENALI_PHY_1810_WRITE_MASK                           0xFF3F0103U
#define LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_WEIGHT_MASK              0x00000003U
#define LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_WEIGHT_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_WEIGHT_WIDTH                      2U
#define LPDDR4__PHY_CLK_DC_WEIGHT__REG DENALI_PHY_1810
#define LPDDR4__PHY_CLK_DC_WEIGHT__FLD LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_WEIGHT

#define LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_FREQ_CHG_ADJ_MASK        0x00000100U
#define LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_FREQ_CHG_ADJ_SHIFT                8U
#define LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_FREQ_CHG_ADJ_WIDTH                1U
#define LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_FREQ_CHG_ADJ_WOCLR                0U
#define LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_FREQ_CHG_ADJ_WOSET                0U
#define LPDDR4__PHY_CLK_DC_FREQ_CHG_ADJ__REG DENALI_PHY_1810
#define LPDDR4__PHY_CLK_DC_FREQ_CHG_ADJ__FLD LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_FREQ_CHG_ADJ

#define LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_ADJUST_START_MASK        0x003F0000U
#define LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_ADJUST_START_SHIFT               16U
#define LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_ADJUST_START_WIDTH                6U
#define LPDDR4__PHY_CLK_DC_ADJUST_START__REG DENALI_PHY_1810
#define LPDDR4__PHY_CLK_DC_ADJUST_START__FLD LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_ADJUST_START

#define LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_ADJUST_SAMPLE_CNT_MASK   0xFF000000U
#define LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_ADJUST_SAMPLE_CNT_SHIFT          24U
#define LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_ADJUST_SAMPLE_CNT_WIDTH           8U
#define LPDDR4__PHY_CLK_DC_ADJUST_SAMPLE_CNT__REG DENALI_PHY_1810
#define LPDDR4__PHY_CLK_DC_ADJUST_SAMPLE_CNT__FLD LPDDR4__DENALI_PHY_1810__PHY_CLK_DC_ADJUST_SAMPLE_CNT

#define LPDDR4__DENALI_PHY_1811_READ_MASK                            0x010101FFU
#define LPDDR4__DENALI_PHY_1811_WRITE_MASK                           0x010101FFU
#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_ADJUST_THRSHLD_MASK      0x000000FFU
#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_ADJUST_THRSHLD_SHIFT              0U
#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_ADJUST_THRSHLD_WIDTH              8U
#define LPDDR4__PHY_CLK_DC_ADJUST_THRSHLD__REG DENALI_PHY_1811
#define LPDDR4__PHY_CLK_DC_ADJUST_THRSHLD__FLD LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_ADJUST_THRSHLD

#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_ADJUST_DIRECT_MASK       0x00000100U
#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_ADJUST_DIRECT_SHIFT               8U
#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_ADJUST_DIRECT_WIDTH               1U
#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_ADJUST_DIRECT_WOCLR               0U
#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_ADJUST_DIRECT_WOSET               0U
#define LPDDR4__PHY_CLK_DC_ADJUST_DIRECT__REG DENALI_PHY_1811
#define LPDDR4__PHY_CLK_DC_ADJUST_DIRECT__FLD LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_ADJUST_DIRECT

#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_CAL_POLARITY_MASK        0x00010000U
#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_CAL_POLARITY_SHIFT               16U
#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_CAL_POLARITY_WIDTH                1U
#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_CAL_POLARITY_WOCLR                0U
#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_CAL_POLARITY_WOSET                0U
#define LPDDR4__PHY_CLK_DC_CAL_POLARITY__REG DENALI_PHY_1811
#define LPDDR4__PHY_CLK_DC_CAL_POLARITY__FLD LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_CAL_POLARITY

#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_CAL_START_MASK           0x01000000U
#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_CAL_START_SHIFT                  24U
#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_CAL_START_WIDTH                   1U
#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_CAL_START_WOCLR                   0U
#define LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_CAL_START_WOSET                   0U
#define LPDDR4__PHY_CLK_DC_CAL_START__REG DENALI_PHY_1811
#define LPDDR4__PHY_CLK_DC_CAL_START__FLD LPDDR4__DENALI_PHY_1811__PHY_CLK_DC_CAL_START

#define LPDDR4__DENALI_PHY_1812_READ_MASK                            0x0F0F0100U
#define LPDDR4__DENALI_PHY_1812_WRITE_MASK                           0x0F0F0100U
#define LPDDR4__DENALI_PHY_1812__SC_PHY_UPDATE_CLK_CAL_VALUES_MASK   0x00000001U
#define LPDDR4__DENALI_PHY_1812__SC_PHY_UPDATE_CLK_CAL_VALUES_SHIFT           0U
#define LPDDR4__DENALI_PHY_1812__SC_PHY_UPDATE_CLK_CAL_VALUES_WIDTH           1U
#define LPDDR4__DENALI_PHY_1812__SC_PHY_UPDATE_CLK_CAL_VALUES_WOCLR           0U
#define LPDDR4__DENALI_PHY_1812__SC_PHY_UPDATE_CLK_CAL_VALUES_WOSET           0U
#define LPDDR4__SC_PHY_UPDATE_CLK_CAL_VALUES__REG DENALI_PHY_1812
#define LPDDR4__SC_PHY_UPDATE_CLK_CAL_VALUES__FLD LPDDR4__DENALI_PHY_1812__SC_PHY_UPDATE_CLK_CAL_VALUES

#define LPDDR4__DENALI_PHY_1812__PHY_CONTINUOUS_CLK_CAL_UPDATE_MASK  0x00000100U
#define LPDDR4__DENALI_PHY_1812__PHY_CONTINUOUS_CLK_CAL_UPDATE_SHIFT          8U
#define LPDDR4__DENALI_PHY_1812__PHY_CONTINUOUS_CLK_CAL_UPDATE_WIDTH          1U
#define LPDDR4__DENALI_PHY_1812__PHY_CONTINUOUS_CLK_CAL_UPDATE_WOCLR          0U
#define LPDDR4__DENALI_PHY_1812__PHY_CONTINUOUS_CLK_CAL_UPDATE_WOSET          0U
#define LPDDR4__PHY_CONTINUOUS_CLK_CAL_UPDATE__REG DENALI_PHY_1812
#define LPDDR4__PHY_CONTINUOUS_CLK_CAL_UPDATE__FLD LPDDR4__DENALI_PHY_1812__PHY_CONTINUOUS_CLK_CAL_UPDATE

#define LPDDR4__DENALI_PHY_1812__PHY_SW_TXIO_CTRL_0_MASK             0x000F0000U
#define LPDDR4__DENALI_PHY_1812__PHY_SW_TXIO_CTRL_0_SHIFT                    16U
#define LPDDR4__DENALI_PHY_1812__PHY_SW_TXIO_CTRL_0_WIDTH                     4U
#define LPDDR4__PHY_SW_TXIO_CTRL_0__REG DENALI_PHY_1812
#define LPDDR4__PHY_SW_TXIO_CTRL_0__FLD LPDDR4__DENALI_PHY_1812__PHY_SW_TXIO_CTRL_0

#define LPDDR4__DENALI_PHY_1812__PHY_SW_TXIO_CTRL_1_MASK             0x0F000000U
#define LPDDR4__DENALI_PHY_1812__PHY_SW_TXIO_CTRL_1_SHIFT                    24U
#define LPDDR4__DENALI_PHY_1812__PHY_SW_TXIO_CTRL_1_WIDTH                     4U
#define LPDDR4__PHY_SW_TXIO_CTRL_1__REG DENALI_PHY_1812
#define LPDDR4__PHY_SW_TXIO_CTRL_1__FLD LPDDR4__DENALI_PHY_1812__PHY_SW_TXIO_CTRL_1

#define LPDDR4__DENALI_PHY_1813_READ_MASK                            0x0F010F0FU
#define LPDDR4__DENALI_PHY_1813_WRITE_MASK                           0x0F010F0FU
#define LPDDR4__DENALI_PHY_1813__PHY_SW_TXIO_CTRL_2_MASK             0x0000000FU
#define LPDDR4__DENALI_PHY_1813__PHY_SW_TXIO_CTRL_2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1813__PHY_SW_TXIO_CTRL_2_WIDTH                     4U
#define LPDDR4__PHY_SW_TXIO_CTRL_2__REG DENALI_PHY_1813
#define LPDDR4__PHY_SW_TXIO_CTRL_2__FLD LPDDR4__DENALI_PHY_1813__PHY_SW_TXIO_CTRL_2

#define LPDDR4__DENALI_PHY_1813__PHY_SW_TXIO_CTRL_3_MASK             0x00000F00U
#define LPDDR4__DENALI_PHY_1813__PHY_SW_TXIO_CTRL_3_SHIFT                     8U
#define LPDDR4__DENALI_PHY_1813__PHY_SW_TXIO_CTRL_3_WIDTH                     4U
#define LPDDR4__PHY_SW_TXIO_CTRL_3__REG DENALI_PHY_1813
#define LPDDR4__PHY_SW_TXIO_CTRL_3__FLD LPDDR4__DENALI_PHY_1813__PHY_SW_TXIO_CTRL_3

#define LPDDR4__DENALI_PHY_1813__PHY_MEMCLK_SW_TXIO_CTRL_MASK        0x00010000U
#define LPDDR4__DENALI_PHY_1813__PHY_MEMCLK_SW_TXIO_CTRL_SHIFT               16U
#define LPDDR4__DENALI_PHY_1813__PHY_MEMCLK_SW_TXIO_CTRL_WIDTH                1U
#define LPDDR4__DENALI_PHY_1813__PHY_MEMCLK_SW_TXIO_CTRL_WOCLR                0U
#define LPDDR4__DENALI_PHY_1813__PHY_MEMCLK_SW_TXIO_CTRL_WOSET                0U
#define LPDDR4__PHY_MEMCLK_SW_TXIO_CTRL__REG DENALI_PHY_1813
#define LPDDR4__PHY_MEMCLK_SW_TXIO_CTRL__FLD LPDDR4__DENALI_PHY_1813__PHY_MEMCLK_SW_TXIO_CTRL

#define LPDDR4__DENALI_PHY_1813__PHY_ADRCTL_SW_TXPWR_CTRL_0_MASK     0x0F000000U
#define LPDDR4__DENALI_PHY_1813__PHY_ADRCTL_SW_TXPWR_CTRL_0_SHIFT            24U
#define LPDDR4__DENALI_PHY_1813__PHY_ADRCTL_SW_TXPWR_CTRL_0_WIDTH             4U
#define LPDDR4__PHY_ADRCTL_SW_TXPWR_CTRL_0__REG DENALI_PHY_1813
#define LPDDR4__PHY_ADRCTL_SW_TXPWR_CTRL_0__FLD LPDDR4__DENALI_PHY_1813__PHY_ADRCTL_SW_TXPWR_CTRL_0

#define LPDDR4__DENALI_PHY_1814_READ_MASK                            0x010F0F0FU
#define LPDDR4__DENALI_PHY_1814_WRITE_MASK                           0x010F0F0FU
#define LPDDR4__DENALI_PHY_1814__PHY_ADRCTL_SW_TXPWR_CTRL_1_MASK     0x0000000FU
#define LPDDR4__DENALI_PHY_1814__PHY_ADRCTL_SW_TXPWR_CTRL_1_SHIFT             0U
#define LPDDR4__DENALI_PHY_1814__PHY_ADRCTL_SW_TXPWR_CTRL_1_WIDTH             4U
#define LPDDR4__PHY_ADRCTL_SW_TXPWR_CTRL_1__REG DENALI_PHY_1814
#define LPDDR4__PHY_ADRCTL_SW_TXPWR_CTRL_1__FLD LPDDR4__DENALI_PHY_1814__PHY_ADRCTL_SW_TXPWR_CTRL_1

#define LPDDR4__DENALI_PHY_1814__PHY_ADRCTL_SW_TXPWR_CTRL_2_MASK     0x00000F00U
#define LPDDR4__DENALI_PHY_1814__PHY_ADRCTL_SW_TXPWR_CTRL_2_SHIFT             8U
#define LPDDR4__DENALI_PHY_1814__PHY_ADRCTL_SW_TXPWR_CTRL_2_WIDTH             4U
#define LPDDR4__PHY_ADRCTL_SW_TXPWR_CTRL_2__REG DENALI_PHY_1814
#define LPDDR4__PHY_ADRCTL_SW_TXPWR_CTRL_2__FLD LPDDR4__DENALI_PHY_1814__PHY_ADRCTL_SW_TXPWR_CTRL_2

#define LPDDR4__DENALI_PHY_1814__PHY_ADRCTL_SW_TXPWR_CTRL_3_MASK     0x000F0000U
#define LPDDR4__DENALI_PHY_1814__PHY_ADRCTL_SW_TXPWR_CTRL_3_SHIFT            16U
#define LPDDR4__DENALI_PHY_1814__PHY_ADRCTL_SW_TXPWR_CTRL_3_WIDTH             4U
#define LPDDR4__PHY_ADRCTL_SW_TXPWR_CTRL_3__REG DENALI_PHY_1814
#define LPDDR4__PHY_ADRCTL_SW_TXPWR_CTRL_3__FLD LPDDR4__DENALI_PHY_1814__PHY_ADRCTL_SW_TXPWR_CTRL_3

#define LPDDR4__DENALI_PHY_1814__PHY_MEMCLK_SW_TXPWR_CTRL_MASK       0x01000000U
#define LPDDR4__DENALI_PHY_1814__PHY_MEMCLK_SW_TXPWR_CTRL_SHIFT              24U
#define LPDDR4__DENALI_PHY_1814__PHY_MEMCLK_SW_TXPWR_CTRL_WIDTH               1U
#define LPDDR4__DENALI_PHY_1814__PHY_MEMCLK_SW_TXPWR_CTRL_WOCLR               0U
#define LPDDR4__DENALI_PHY_1814__PHY_MEMCLK_SW_TXPWR_CTRL_WOSET               0U
#define LPDDR4__PHY_MEMCLK_SW_TXPWR_CTRL__REG DENALI_PHY_1814
#define LPDDR4__PHY_MEMCLK_SW_TXPWR_CTRL__FLD LPDDR4__DENALI_PHY_1814__PHY_MEMCLK_SW_TXPWR_CTRL

#define LPDDR4__DENALI_PHY_1815_READ_MASK                            0xFFFF0101U
#define LPDDR4__DENALI_PHY_1815_WRITE_MASK                           0xFFFF0101U
#define LPDDR4__DENALI_PHY_1815__PHY_TOP_STATIC_TOG_DISABLE_MASK     0x00000001U
#define LPDDR4__DENALI_PHY_1815__PHY_TOP_STATIC_TOG_DISABLE_SHIFT             0U
#define LPDDR4__DENALI_PHY_1815__PHY_TOP_STATIC_TOG_DISABLE_WIDTH             1U
#define LPDDR4__DENALI_PHY_1815__PHY_TOP_STATIC_TOG_DISABLE_WOCLR             0U
#define LPDDR4__DENALI_PHY_1815__PHY_TOP_STATIC_TOG_DISABLE_WOSET             0U
#define LPDDR4__PHY_TOP_STATIC_TOG_DISABLE__REG DENALI_PHY_1815
#define LPDDR4__PHY_TOP_STATIC_TOG_DISABLE__FLD LPDDR4__DENALI_PHY_1815__PHY_TOP_STATIC_TOG_DISABLE

#define LPDDR4__DENALI_PHY_1815__PHY_BYTE_DISABLE_STATIC_TOG_DISABLE_MASK 0x00000100U
#define LPDDR4__DENALI_PHY_1815__PHY_BYTE_DISABLE_STATIC_TOG_DISABLE_SHIFT    8U
#define LPDDR4__DENALI_PHY_1815__PHY_BYTE_DISABLE_STATIC_TOG_DISABLE_WIDTH    1U
#define LPDDR4__DENALI_PHY_1815__PHY_BYTE_DISABLE_STATIC_TOG_DISABLE_WOCLR    0U
#define LPDDR4__DENALI_PHY_1815__PHY_BYTE_DISABLE_STATIC_TOG_DISABLE_WOSET    0U
#define LPDDR4__PHY_BYTE_DISABLE_STATIC_TOG_DISABLE__REG DENALI_PHY_1815
#define LPDDR4__PHY_BYTE_DISABLE_STATIC_TOG_DISABLE__FLD LPDDR4__DENALI_PHY_1815__PHY_BYTE_DISABLE_STATIC_TOG_DISABLE

#define LPDDR4__DENALI_PHY_1815__PHY_STATIC_TOG_CONTROL_MASK         0xFFFF0000U
#define LPDDR4__DENALI_PHY_1815__PHY_STATIC_TOG_CONTROL_SHIFT                16U
#define LPDDR4__DENALI_PHY_1815__PHY_STATIC_TOG_CONTROL_WIDTH                16U
#define LPDDR4__PHY_STATIC_TOG_CONTROL__REG DENALI_PHY_1815
#define LPDDR4__PHY_STATIC_TOG_CONTROL__FLD LPDDR4__DENALI_PHY_1815__PHY_STATIC_TOG_CONTROL

#define LPDDR4__DENALI_PHY_1816_READ_MASK                            0x0001010FU
#define LPDDR4__DENALI_PHY_1816_WRITE_MASK                           0x0001010FU
#define LPDDR4__DENALI_PHY_1816__PHY_ADRCTL_STATIC_TOG_DISABLE_MASK  0x0000000FU
#define LPDDR4__DENALI_PHY_1816__PHY_ADRCTL_STATIC_TOG_DISABLE_SHIFT          0U
#define LPDDR4__DENALI_PHY_1816__PHY_ADRCTL_STATIC_TOG_DISABLE_WIDTH          4U
#define LPDDR4__PHY_ADRCTL_STATIC_TOG_DISABLE__REG DENALI_PHY_1816
#define LPDDR4__PHY_ADRCTL_STATIC_TOG_DISABLE__FLD LPDDR4__DENALI_PHY_1816__PHY_ADRCTL_STATIC_TOG_DISABLE

#define LPDDR4__DENALI_PHY_1816__PHY_MEMCLK_STATIC_TOG_DISABLE_MASK  0x00000100U
#define LPDDR4__DENALI_PHY_1816__PHY_MEMCLK_STATIC_TOG_DISABLE_SHIFT          8U
#define LPDDR4__DENALI_PHY_1816__PHY_MEMCLK_STATIC_TOG_DISABLE_WIDTH          1U
#define LPDDR4__DENALI_PHY_1816__PHY_MEMCLK_STATIC_TOG_DISABLE_WOCLR          0U
#define LPDDR4__DENALI_PHY_1816__PHY_MEMCLK_STATIC_TOG_DISABLE_WOSET          0U
#define LPDDR4__PHY_MEMCLK_STATIC_TOG_DISABLE__REG DENALI_PHY_1816
#define LPDDR4__PHY_MEMCLK_STATIC_TOG_DISABLE__FLD LPDDR4__DENALI_PHY_1816__PHY_MEMCLK_STATIC_TOG_DISABLE

#define LPDDR4__DENALI_PHY_1816__PHY_LP4_BOOT_PLL_BYPASS_MASK        0x00010000U
#define LPDDR4__DENALI_PHY_1816__PHY_LP4_BOOT_PLL_BYPASS_SHIFT               16U
#define LPDDR4__DENALI_PHY_1816__PHY_LP4_BOOT_PLL_BYPASS_WIDTH                1U
#define LPDDR4__DENALI_PHY_1816__PHY_LP4_BOOT_PLL_BYPASS_WOCLR                0U
#define LPDDR4__DENALI_PHY_1816__PHY_LP4_BOOT_PLL_BYPASS_WOSET                0U
#define LPDDR4__PHY_LP4_BOOT_PLL_BYPASS__REG DENALI_PHY_1816
#define LPDDR4__PHY_LP4_BOOT_PLL_BYPASS__FLD LPDDR4__DENALI_PHY_1816__PHY_LP4_BOOT_PLL_BYPASS

#define LPDDR4__DENALI_PHY_1817_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1817_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1817__PHY_CLK_SWITCH_OBS_MASK             0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1817__PHY_CLK_SWITCH_OBS_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1817__PHY_CLK_SWITCH_OBS_WIDTH                    32U
#define LPDDR4__PHY_CLK_SWITCH_OBS__REG DENALI_PHY_1817
#define LPDDR4__PHY_CLK_SWITCH_OBS__FLD LPDDR4__DENALI_PHY_1817__PHY_CLK_SWITCH_OBS

#define LPDDR4__DENALI_PHY_1818_READ_MASK                            0x0000FFFFU
#define LPDDR4__DENALI_PHY_1818_WRITE_MASK                           0x0000FFFFU
#define LPDDR4__DENALI_PHY_1818__PHY_PLL_WAIT_MASK                   0x0000FFFFU
#define LPDDR4__DENALI_PHY_1818__PHY_PLL_WAIT_SHIFT                           0U
#define LPDDR4__DENALI_PHY_1818__PHY_PLL_WAIT_WIDTH                          16U
#define LPDDR4__PHY_PLL_WAIT__REG DENALI_PHY_1818
#define LPDDR4__PHY_PLL_WAIT__FLD LPDDR4__DENALI_PHY_1818__PHY_PLL_WAIT

#define LPDDR4__DENALI_PHY_1819_READ_MASK                            0x00000001U
#define LPDDR4__DENALI_PHY_1819_WRITE_MASK                           0x00000001U
#define LPDDR4__DENALI_PHY_1819__PHY_SW_PLL_BYPASS_MASK              0x00000001U
#define LPDDR4__DENALI_PHY_1819__PHY_SW_PLL_BYPASS_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1819__PHY_SW_PLL_BYPASS_WIDTH                      1U
#define LPDDR4__DENALI_PHY_1819__PHY_SW_PLL_BYPASS_WOCLR                      0U
#define LPDDR4__DENALI_PHY_1819__PHY_SW_PLL_BYPASS_WOSET                      0U
#define LPDDR4__PHY_SW_PLL_BYPASS__REG DENALI_PHY_1819
#define LPDDR4__PHY_SW_PLL_BYPASS__FLD LPDDR4__DENALI_PHY_1819__PHY_SW_PLL_BYPASS

#define LPDDR4__DENALI_PHY_1820_READ_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_1820_WRITE_MASK                           0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_1820__PHY_SET_DFI_INPUT_0_MASK            0x0000000FU
#define LPDDR4__DENALI_PHY_1820__PHY_SET_DFI_INPUT_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1820__PHY_SET_DFI_INPUT_0_WIDTH                    4U
#define LPDDR4__PHY_SET_DFI_INPUT_0__REG DENALI_PHY_1820
#define LPDDR4__PHY_SET_DFI_INPUT_0__FLD LPDDR4__DENALI_PHY_1820__PHY_SET_DFI_INPUT_0

#define LPDDR4__DENALI_PHY_1820__PHY_SET_DFI_INPUT_1_MASK            0x00000F00U
#define LPDDR4__DENALI_PHY_1820__PHY_SET_DFI_INPUT_1_SHIFT                    8U
#define LPDDR4__DENALI_PHY_1820__PHY_SET_DFI_INPUT_1_WIDTH                    4U
#define LPDDR4__PHY_SET_DFI_INPUT_1__REG DENALI_PHY_1820
#define LPDDR4__PHY_SET_DFI_INPUT_1__FLD LPDDR4__DENALI_PHY_1820__PHY_SET_DFI_INPUT_1

#define LPDDR4__DENALI_PHY_1820__PHY_SET_DFI_INPUT_2_MASK            0x000F0000U
#define LPDDR4__DENALI_PHY_1820__PHY_SET_DFI_INPUT_2_SHIFT                   16U
#define LPDDR4__DENALI_PHY_1820__PHY_SET_DFI_INPUT_2_WIDTH                    4U
#define LPDDR4__PHY_SET_DFI_INPUT_2__REG DENALI_PHY_1820
#define LPDDR4__PHY_SET_DFI_INPUT_2__FLD LPDDR4__DENALI_PHY_1820__PHY_SET_DFI_INPUT_2

#define LPDDR4__DENALI_PHY_1820__PHY_SET_DFI_INPUT_3_MASK            0x0F000000U
#define LPDDR4__DENALI_PHY_1820__PHY_SET_DFI_INPUT_3_SHIFT                   24U
#define LPDDR4__DENALI_PHY_1820__PHY_SET_DFI_INPUT_3_WIDTH                    4U
#define LPDDR4__PHY_SET_DFI_INPUT_3__REG DENALI_PHY_1820
#define LPDDR4__PHY_SET_DFI_INPUT_3__FLD LPDDR4__DENALI_PHY_1820__PHY_SET_DFI_INPUT_3

#define LPDDR4__DENALI_PHY_1821_READ_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_1821_WRITE_MASK                           0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_1821__PHY_CS_ACS_ALLOCATION_BIT0_0_MASK   0x0000000FU
#define LPDDR4__DENALI_PHY_1821__PHY_CS_ACS_ALLOCATION_BIT0_0_SHIFT           0U
#define LPDDR4__DENALI_PHY_1821__PHY_CS_ACS_ALLOCATION_BIT0_0_WIDTH           4U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT0_0__REG DENALI_PHY_1821
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT0_0__FLD LPDDR4__DENALI_PHY_1821__PHY_CS_ACS_ALLOCATION_BIT0_0

#define LPDDR4__DENALI_PHY_1821__PHY_CS_ACS_ALLOCATION_BIT1_0_MASK   0x00000F00U
#define LPDDR4__DENALI_PHY_1821__PHY_CS_ACS_ALLOCATION_BIT1_0_SHIFT           8U
#define LPDDR4__DENALI_PHY_1821__PHY_CS_ACS_ALLOCATION_BIT1_0_WIDTH           4U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT1_0__REG DENALI_PHY_1821
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT1_0__FLD LPDDR4__DENALI_PHY_1821__PHY_CS_ACS_ALLOCATION_BIT1_0

#define LPDDR4__DENALI_PHY_1821__PHY_CS_ACS_ALLOCATION_BIT2_0_MASK   0x000F0000U
#define LPDDR4__DENALI_PHY_1821__PHY_CS_ACS_ALLOCATION_BIT2_0_SHIFT          16U
#define LPDDR4__DENALI_PHY_1821__PHY_CS_ACS_ALLOCATION_BIT2_0_WIDTH           4U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT2_0__REG DENALI_PHY_1821
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT2_0__FLD LPDDR4__DENALI_PHY_1821__PHY_CS_ACS_ALLOCATION_BIT2_0

#define LPDDR4__DENALI_PHY_1821__PHY_CS_ACS_ALLOCATION_BIT3_0_MASK   0x0F000000U
#define LPDDR4__DENALI_PHY_1821__PHY_CS_ACS_ALLOCATION_BIT3_0_SHIFT          24U
#define LPDDR4__DENALI_PHY_1821__PHY_CS_ACS_ALLOCATION_BIT3_0_WIDTH           4U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT3_0__REG DENALI_PHY_1821
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT3_0__FLD LPDDR4__DENALI_PHY_1821__PHY_CS_ACS_ALLOCATION_BIT3_0

#define LPDDR4__DENALI_PHY_1822_READ_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_1822_WRITE_MASK                           0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_1822__PHY_CS_ACS_ALLOCATION_BIT0_1_MASK   0x0000000FU
#define LPDDR4__DENALI_PHY_1822__PHY_CS_ACS_ALLOCATION_BIT0_1_SHIFT           0U
#define LPDDR4__DENALI_PHY_1822__PHY_CS_ACS_ALLOCATION_BIT0_1_WIDTH           4U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT0_1__REG DENALI_PHY_1822
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT0_1__FLD LPDDR4__DENALI_PHY_1822__PHY_CS_ACS_ALLOCATION_BIT0_1

#define LPDDR4__DENALI_PHY_1822__PHY_CS_ACS_ALLOCATION_BIT1_1_MASK   0x00000F00U
#define LPDDR4__DENALI_PHY_1822__PHY_CS_ACS_ALLOCATION_BIT1_1_SHIFT           8U
#define LPDDR4__DENALI_PHY_1822__PHY_CS_ACS_ALLOCATION_BIT1_1_WIDTH           4U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT1_1__REG DENALI_PHY_1822
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT1_1__FLD LPDDR4__DENALI_PHY_1822__PHY_CS_ACS_ALLOCATION_BIT1_1

#define LPDDR4__DENALI_PHY_1822__PHY_CS_ACS_ALLOCATION_BIT2_1_MASK   0x000F0000U
#define LPDDR4__DENALI_PHY_1822__PHY_CS_ACS_ALLOCATION_BIT2_1_SHIFT          16U
#define LPDDR4__DENALI_PHY_1822__PHY_CS_ACS_ALLOCATION_BIT2_1_WIDTH           4U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT2_1__REG DENALI_PHY_1822
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT2_1__FLD LPDDR4__DENALI_PHY_1822__PHY_CS_ACS_ALLOCATION_BIT2_1

#define LPDDR4__DENALI_PHY_1822__PHY_CS_ACS_ALLOCATION_BIT3_1_MASK   0x0F000000U
#define LPDDR4__DENALI_PHY_1822__PHY_CS_ACS_ALLOCATION_BIT3_1_SHIFT          24U
#define LPDDR4__DENALI_PHY_1822__PHY_CS_ACS_ALLOCATION_BIT3_1_WIDTH           4U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT3_1__REG DENALI_PHY_1822
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT3_1__FLD LPDDR4__DENALI_PHY_1822__PHY_CS_ACS_ALLOCATION_BIT3_1

#define LPDDR4__DENALI_PHY_1823_READ_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_1823_WRITE_MASK                           0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_1823__PHY_CS_ACS_ALLOCATION_BIT0_2_MASK   0x0000000FU
#define LPDDR4__DENALI_PHY_1823__PHY_CS_ACS_ALLOCATION_BIT0_2_SHIFT           0U
#define LPDDR4__DENALI_PHY_1823__PHY_CS_ACS_ALLOCATION_BIT0_2_WIDTH           4U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT0_2__REG DENALI_PHY_1823
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT0_2__FLD LPDDR4__DENALI_PHY_1823__PHY_CS_ACS_ALLOCATION_BIT0_2

#define LPDDR4__DENALI_PHY_1823__PHY_CS_ACS_ALLOCATION_BIT1_2_MASK   0x00000F00U
#define LPDDR4__DENALI_PHY_1823__PHY_CS_ACS_ALLOCATION_BIT1_2_SHIFT           8U
#define LPDDR4__DENALI_PHY_1823__PHY_CS_ACS_ALLOCATION_BIT1_2_WIDTH           4U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT1_2__REG DENALI_PHY_1823
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT1_2__FLD LPDDR4__DENALI_PHY_1823__PHY_CS_ACS_ALLOCATION_BIT1_2

#define LPDDR4__DENALI_PHY_1823__PHY_CS_ACS_ALLOCATION_BIT2_2_MASK   0x000F0000U
#define LPDDR4__DENALI_PHY_1823__PHY_CS_ACS_ALLOCATION_BIT2_2_SHIFT          16U
#define LPDDR4__DENALI_PHY_1823__PHY_CS_ACS_ALLOCATION_BIT2_2_WIDTH           4U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT2_2__REG DENALI_PHY_1823
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT2_2__FLD LPDDR4__DENALI_PHY_1823__PHY_CS_ACS_ALLOCATION_BIT2_2

#define LPDDR4__DENALI_PHY_1823__PHY_CS_ACS_ALLOCATION_BIT3_2_MASK   0x0F000000U
#define LPDDR4__DENALI_PHY_1823__PHY_CS_ACS_ALLOCATION_BIT3_2_SHIFT          24U
#define LPDDR4__DENALI_PHY_1823__PHY_CS_ACS_ALLOCATION_BIT3_2_WIDTH           4U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT3_2__REG DENALI_PHY_1823
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT3_2__FLD LPDDR4__DENALI_PHY_1823__PHY_CS_ACS_ALLOCATION_BIT3_2

#define LPDDR4__DENALI_PHY_1824_READ_MASK                            0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_1824_WRITE_MASK                           0x0F0F0F0FU
#define LPDDR4__DENALI_PHY_1824__PHY_CS_ACS_ALLOCATION_BIT0_3_MASK   0x0000000FU
#define LPDDR4__DENALI_PHY_1824__PHY_CS_ACS_ALLOCATION_BIT0_3_SHIFT           0U
#define LPDDR4__DENALI_PHY_1824__PHY_CS_ACS_ALLOCATION_BIT0_3_WIDTH           4U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT0_3__REG DENALI_PHY_1824
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT0_3__FLD LPDDR4__DENALI_PHY_1824__PHY_CS_ACS_ALLOCATION_BIT0_3

#define LPDDR4__DENALI_PHY_1824__PHY_CS_ACS_ALLOCATION_BIT1_3_MASK   0x00000F00U
#define LPDDR4__DENALI_PHY_1824__PHY_CS_ACS_ALLOCATION_BIT1_3_SHIFT           8U
#define LPDDR4__DENALI_PHY_1824__PHY_CS_ACS_ALLOCATION_BIT1_3_WIDTH           4U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT1_3__REG DENALI_PHY_1824
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT1_3__FLD LPDDR4__DENALI_PHY_1824__PHY_CS_ACS_ALLOCATION_BIT1_3

#define LPDDR4__DENALI_PHY_1824__PHY_CS_ACS_ALLOCATION_BIT2_3_MASK   0x000F0000U
#define LPDDR4__DENALI_PHY_1824__PHY_CS_ACS_ALLOCATION_BIT2_3_SHIFT          16U
#define LPDDR4__DENALI_PHY_1824__PHY_CS_ACS_ALLOCATION_BIT2_3_WIDTH           4U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT2_3__REG DENALI_PHY_1824
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT2_3__FLD LPDDR4__DENALI_PHY_1824__PHY_CS_ACS_ALLOCATION_BIT2_3

#define LPDDR4__DENALI_PHY_1824__PHY_CS_ACS_ALLOCATION_BIT3_3_MASK   0x0F000000U
#define LPDDR4__DENALI_PHY_1824__PHY_CS_ACS_ALLOCATION_BIT3_3_SHIFT          24U
#define LPDDR4__DENALI_PHY_1824__PHY_CS_ACS_ALLOCATION_BIT3_3_WIDTH           4U
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT3_3__REG DENALI_PHY_1824
#define LPDDR4__PHY_CS_ACS_ALLOCATION_BIT3_3__FLD LPDDR4__DENALI_PHY_1824__PHY_CS_ACS_ALLOCATION_BIT3_3

#define LPDDR4__DENALI_PHY_1825_READ_MASK                            0x00FF01FFU
#define LPDDR4__DENALI_PHY_1825_WRITE_MASK                           0x00FF01FFU
#define LPDDR4__DENALI_PHY_1825__PHY_CLK_DC_ADJUST_0_MASK            0x000000FFU
#define LPDDR4__DENALI_PHY_1825__PHY_CLK_DC_ADJUST_0_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1825__PHY_CLK_DC_ADJUST_0_WIDTH                    8U
#define LPDDR4__PHY_CLK_DC_ADJUST_0__REG DENALI_PHY_1825
#define LPDDR4__PHY_CLK_DC_ADJUST_0__FLD LPDDR4__DENALI_PHY_1825__PHY_CLK_DC_ADJUST_0

#define LPDDR4__DENALI_PHY_1825__PHY_CLK_DC_INIT_DISABLE_MASK        0x00000100U
#define LPDDR4__DENALI_PHY_1825__PHY_CLK_DC_INIT_DISABLE_SHIFT                8U
#define LPDDR4__DENALI_PHY_1825__PHY_CLK_DC_INIT_DISABLE_WIDTH                1U
#define LPDDR4__DENALI_PHY_1825__PHY_CLK_DC_INIT_DISABLE_WOCLR                0U
#define LPDDR4__DENALI_PHY_1825__PHY_CLK_DC_INIT_DISABLE_WOSET                0U
#define LPDDR4__PHY_CLK_DC_INIT_DISABLE__REG DENALI_PHY_1825
#define LPDDR4__PHY_CLK_DC_INIT_DISABLE__FLD LPDDR4__DENALI_PHY_1825__PHY_CLK_DC_INIT_DISABLE

#define LPDDR4__DENALI_PHY_1825__PHY_CLK_DC_DM_THRSHLD_MASK          0x00FF0000U
#define LPDDR4__DENALI_PHY_1825__PHY_CLK_DC_DM_THRSHLD_SHIFT                 16U
#define LPDDR4__DENALI_PHY_1825__PHY_CLK_DC_DM_THRSHLD_WIDTH                  8U
#define LPDDR4__PHY_CLK_DC_DM_THRSHLD__REG DENALI_PHY_1825
#define LPDDR4__PHY_CLK_DC_DM_THRSHLD__FLD LPDDR4__DENALI_PHY_1825__PHY_CLK_DC_DM_THRSHLD

#define LPDDR4__DENALI_PHY_1826_READ_MASK                            0xFFFF1FFFU
#define LPDDR4__DENALI_PHY_1826_WRITE_MASK                           0xFFFF1FFFU
#define LPDDR4__DENALI_PHY_1826__PHY_LP4_BOOT_PLL_CTRL_MASK          0x00001FFFU
#define LPDDR4__DENALI_PHY_1826__PHY_LP4_BOOT_PLL_CTRL_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1826__PHY_LP4_BOOT_PLL_CTRL_WIDTH                 13U
#define LPDDR4__PHY_LP4_BOOT_PLL_CTRL__REG DENALI_PHY_1826
#define LPDDR4__PHY_LP4_BOOT_PLL_CTRL__FLD LPDDR4__DENALI_PHY_1826__PHY_LP4_BOOT_PLL_CTRL

#define LPDDR4__DENALI_PHY_1826__PHY_PLL_CTRL_OVERRIDE_MASK          0xFFFF0000U
#define LPDDR4__DENALI_PHY_1826__PHY_PLL_CTRL_OVERRIDE_SHIFT                 16U
#define LPDDR4__DENALI_PHY_1826__PHY_PLL_CTRL_OVERRIDE_WIDTH                 16U
#define LPDDR4__PHY_PLL_CTRL_OVERRIDE__REG DENALI_PHY_1826
#define LPDDR4__PHY_PLL_CTRL_OVERRIDE__FLD LPDDR4__DENALI_PHY_1826__PHY_PLL_CTRL_OVERRIDE

#define LPDDR4__DENALI_PHY_1827_READ_MASK                            0x0000FF01U
#define LPDDR4__DENALI_PHY_1827_WRITE_MASK                           0x0000FF01U
#define LPDDR4__DENALI_PHY_1827__PHY_USE_PLL_DSKEWCALLOCK_MASK       0x00000001U
#define LPDDR4__DENALI_PHY_1827__PHY_USE_PLL_DSKEWCALLOCK_SHIFT               0U
#define LPDDR4__DENALI_PHY_1827__PHY_USE_PLL_DSKEWCALLOCK_WIDTH               1U
#define LPDDR4__DENALI_PHY_1827__PHY_USE_PLL_DSKEWCALLOCK_WOCLR               0U
#define LPDDR4__DENALI_PHY_1827__PHY_USE_PLL_DSKEWCALLOCK_WOSET               0U
#define LPDDR4__PHY_USE_PLL_DSKEWCALLOCK__REG DENALI_PHY_1827
#define LPDDR4__PHY_USE_PLL_DSKEWCALLOCK__FLD LPDDR4__DENALI_PHY_1827__PHY_USE_PLL_DSKEWCALLOCK

#define LPDDR4__DENALI_PHY_1827__PHY_PLL_SPO_CAL_CTRL_MASK           0x0000FF00U
#define LPDDR4__DENALI_PHY_1827__PHY_PLL_SPO_CAL_CTRL_SHIFT                   8U
#define LPDDR4__DENALI_PHY_1827__PHY_PLL_SPO_CAL_CTRL_WIDTH                   8U
#define LPDDR4__PHY_PLL_SPO_CAL_CTRL__REG DENALI_PHY_1827
#define LPDDR4__PHY_PLL_SPO_CAL_CTRL__FLD LPDDR4__DENALI_PHY_1827__PHY_PLL_SPO_CAL_CTRL

#define LPDDR4__DENALI_PHY_1827__SC_PHY_PLL_SPO_CAL_SNAP_OBS_MASK    0x00030000U
#define LPDDR4__DENALI_PHY_1827__SC_PHY_PLL_SPO_CAL_SNAP_OBS_SHIFT           16U
#define LPDDR4__DENALI_PHY_1827__SC_PHY_PLL_SPO_CAL_SNAP_OBS_WIDTH            2U
#define LPDDR4__SC_PHY_PLL_SPO_CAL_SNAP_OBS__REG DENALI_PHY_1827
#define LPDDR4__SC_PHY_PLL_SPO_CAL_SNAP_OBS__FLD LPDDR4__DENALI_PHY_1827__SC_PHY_PLL_SPO_CAL_SNAP_OBS

#define LPDDR4__DENALI_PHY_1828_READ_MASK                            0x0000FFFFU
#define LPDDR4__DENALI_PHY_1828_WRITE_MASK                           0x0000FFFFU
#define LPDDR4__DENALI_PHY_1828__PHY_PLL_OBS_0_MASK                  0x0000FFFFU
#define LPDDR4__DENALI_PHY_1828__PHY_PLL_OBS_0_SHIFT                          0U
#define LPDDR4__DENALI_PHY_1828__PHY_PLL_OBS_0_WIDTH                         16U
#define LPDDR4__PHY_PLL_OBS_0__REG DENALI_PHY_1828
#define LPDDR4__PHY_PLL_OBS_0__FLD LPDDR4__DENALI_PHY_1828__PHY_PLL_OBS_0

#define LPDDR4__DENALI_PHY_1829_READ_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_PHY_1829_WRITE_MASK                           0x0001FFFFU
#define LPDDR4__DENALI_PHY_1829__PHY_PLL_SPO_CAL_OBS_0_MASK          0x0001FFFFU
#define LPDDR4__DENALI_PHY_1829__PHY_PLL_SPO_CAL_OBS_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1829__PHY_PLL_SPO_CAL_OBS_0_WIDTH                 17U
#define LPDDR4__PHY_PLL_SPO_CAL_OBS_0__REG DENALI_PHY_1829
#define LPDDR4__PHY_PLL_SPO_CAL_OBS_0__FLD LPDDR4__DENALI_PHY_1829__PHY_PLL_SPO_CAL_OBS_0

#define LPDDR4__DENALI_PHY_1830_READ_MASK                            0x0FFF0FFFU
#define LPDDR4__DENALI_PHY_1830_WRITE_MASK                           0x0FFF0FFFU
#define LPDDR4__DENALI_PHY_1830__PHY_PLL_DESKEWCALIN_0_MASK          0x00000FFFU
#define LPDDR4__DENALI_PHY_1830__PHY_PLL_DESKEWCALIN_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1830__PHY_PLL_DESKEWCALIN_0_WIDTH                 12U
#define LPDDR4__PHY_PLL_DESKEWCALIN_0__REG DENALI_PHY_1830
#define LPDDR4__PHY_PLL_DESKEWCALIN_0__FLD LPDDR4__DENALI_PHY_1830__PHY_PLL_DESKEWCALIN_0

#define LPDDR4__DENALI_PHY_1830__PHY_LP4_BOOT_PLL_DESKEWCALIN_0_MASK 0x0FFF0000U
#define LPDDR4__DENALI_PHY_1830__PHY_LP4_BOOT_PLL_DESKEWCALIN_0_SHIFT        16U
#define LPDDR4__DENALI_PHY_1830__PHY_LP4_BOOT_PLL_DESKEWCALIN_0_WIDTH        12U
#define LPDDR4__PHY_LP4_BOOT_PLL_DESKEWCALIN_0__REG DENALI_PHY_1830
#define LPDDR4__PHY_LP4_BOOT_PLL_DESKEWCALIN_0__FLD LPDDR4__DENALI_PHY_1830__PHY_LP4_BOOT_PLL_DESKEWCALIN_0

#define LPDDR4__DENALI_PHY_1831_READ_MASK                            0x0000FFFFU
#define LPDDR4__DENALI_PHY_1831_WRITE_MASK                           0x0000FFFFU
#define LPDDR4__DENALI_PHY_1831__PHY_PLL_OBS_1_MASK                  0x0000FFFFU
#define LPDDR4__DENALI_PHY_1831__PHY_PLL_OBS_1_SHIFT                          0U
#define LPDDR4__DENALI_PHY_1831__PHY_PLL_OBS_1_WIDTH                         16U
#define LPDDR4__PHY_PLL_OBS_1__REG DENALI_PHY_1831
#define LPDDR4__PHY_PLL_OBS_1__FLD LPDDR4__DENALI_PHY_1831__PHY_PLL_OBS_1

#define LPDDR4__DENALI_PHY_1832_READ_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_PHY_1832_WRITE_MASK                           0x0001FFFFU
#define LPDDR4__DENALI_PHY_1832__PHY_PLL_SPO_CAL_OBS_1_MASK          0x0001FFFFU
#define LPDDR4__DENALI_PHY_1832__PHY_PLL_SPO_CAL_OBS_1_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1832__PHY_PLL_SPO_CAL_OBS_1_WIDTH                 17U
#define LPDDR4__PHY_PLL_SPO_CAL_OBS_1__REG DENALI_PHY_1832
#define LPDDR4__PHY_PLL_SPO_CAL_OBS_1__FLD LPDDR4__DENALI_PHY_1832__PHY_PLL_SPO_CAL_OBS_1

#define LPDDR4__DENALI_PHY_1833_READ_MASK                            0x0FFF0FFFU
#define LPDDR4__DENALI_PHY_1833_WRITE_MASK                           0x0FFF0FFFU
#define LPDDR4__DENALI_PHY_1833__PHY_PLL_DESKEWCALIN_1_MASK          0x00000FFFU
#define LPDDR4__DENALI_PHY_1833__PHY_PLL_DESKEWCALIN_1_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1833__PHY_PLL_DESKEWCALIN_1_WIDTH                 12U
#define LPDDR4__PHY_PLL_DESKEWCALIN_1__REG DENALI_PHY_1833
#define LPDDR4__PHY_PLL_DESKEWCALIN_1__FLD LPDDR4__DENALI_PHY_1833__PHY_PLL_DESKEWCALIN_1

#define LPDDR4__DENALI_PHY_1833__PHY_LP4_BOOT_PLL_DESKEWCALIN_1_MASK 0x0FFF0000U
#define LPDDR4__DENALI_PHY_1833__PHY_LP4_BOOT_PLL_DESKEWCALIN_1_SHIFT        16U
#define LPDDR4__DENALI_PHY_1833__PHY_LP4_BOOT_PLL_DESKEWCALIN_1_WIDTH        12U
#define LPDDR4__PHY_LP4_BOOT_PLL_DESKEWCALIN_1__REG DENALI_PHY_1833
#define LPDDR4__PHY_LP4_BOOT_PLL_DESKEWCALIN_1__FLD LPDDR4__DENALI_PHY_1833__PHY_LP4_BOOT_PLL_DESKEWCALIN_1

#define LPDDR4__DENALI_PHY_1834_READ_MASK                            0x0F010101U
#define LPDDR4__DENALI_PHY_1834_WRITE_MASK                           0x0F010101U
#define LPDDR4__DENALI_PHY_1834__PHY_PLL_TESTOUT_SEL_MASK            0x00000001U
#define LPDDR4__DENALI_PHY_1834__PHY_PLL_TESTOUT_SEL_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1834__PHY_PLL_TESTOUT_SEL_WIDTH                    1U
#define LPDDR4__DENALI_PHY_1834__PHY_PLL_TESTOUT_SEL_WOCLR                    0U
#define LPDDR4__DENALI_PHY_1834__PHY_PLL_TESTOUT_SEL_WOSET                    0U
#define LPDDR4__PHY_PLL_TESTOUT_SEL__REG DENALI_PHY_1834
#define LPDDR4__PHY_PLL_TESTOUT_SEL__FLD LPDDR4__DENALI_PHY_1834__PHY_PLL_TESTOUT_SEL

#define LPDDR4__DENALI_PHY_1834__PHY_PLL_REFOUT_SEL_MASK             0x00000100U
#define LPDDR4__DENALI_PHY_1834__PHY_PLL_REFOUT_SEL_SHIFT                     8U
#define LPDDR4__DENALI_PHY_1834__PHY_PLL_REFOUT_SEL_WIDTH                     1U
#define LPDDR4__DENALI_PHY_1834__PHY_PLL_REFOUT_SEL_WOCLR                     0U
#define LPDDR4__DENALI_PHY_1834__PHY_PLL_REFOUT_SEL_WOSET                     0U
#define LPDDR4__PHY_PLL_REFOUT_SEL__REG DENALI_PHY_1834
#define LPDDR4__PHY_PLL_REFOUT_SEL__FLD LPDDR4__DENALI_PHY_1834__PHY_PLL_REFOUT_SEL

#define LPDDR4__DENALI_PHY_1834__PHY_LP4_BOOT_LOW_FREQ_SEL_MASK      0x00010000U
#define LPDDR4__DENALI_PHY_1834__PHY_LP4_BOOT_LOW_FREQ_SEL_SHIFT             16U
#define LPDDR4__DENALI_PHY_1834__PHY_LP4_BOOT_LOW_FREQ_SEL_WIDTH              1U
#define LPDDR4__DENALI_PHY_1834__PHY_LP4_BOOT_LOW_FREQ_SEL_WOCLR              0U
#define LPDDR4__DENALI_PHY_1834__PHY_LP4_BOOT_LOW_FREQ_SEL_WOSET              0U
#define LPDDR4__PHY_LP4_BOOT_LOW_FREQ_SEL__REG DENALI_PHY_1834
#define LPDDR4__PHY_LP4_BOOT_LOW_FREQ_SEL__FLD LPDDR4__DENALI_PHY_1834__PHY_LP4_BOOT_LOW_FREQ_SEL

#define LPDDR4__DENALI_PHY_1834__PHY_TCKSRE_WAIT_MASK                0x0F000000U
#define LPDDR4__DENALI_PHY_1834__PHY_TCKSRE_WAIT_SHIFT                       24U
#define LPDDR4__DENALI_PHY_1834__PHY_TCKSRE_WAIT_WIDTH                        4U
#define LPDDR4__PHY_TCKSRE_WAIT__REG DENALI_PHY_1834
#define LPDDR4__PHY_TCKSRE_WAIT__FLD LPDDR4__DENALI_PHY_1834__PHY_TCKSRE_WAIT

#define LPDDR4__DENALI_PHY_1835_READ_MASK                            0x03FF01FFU
#define LPDDR4__DENALI_PHY_1835_WRITE_MASK                           0x03FF01FFU
#define LPDDR4__DENALI_PHY_1835__PHY_LP_WAKEUP_MASK                  0x000000FFU
#define LPDDR4__DENALI_PHY_1835__PHY_LP_WAKEUP_SHIFT                          0U
#define LPDDR4__DENALI_PHY_1835__PHY_LP_WAKEUP_WIDTH                          8U
#define LPDDR4__PHY_LP_WAKEUP__REG DENALI_PHY_1835
#define LPDDR4__PHY_LP_WAKEUP__FLD LPDDR4__DENALI_PHY_1835__PHY_LP_WAKEUP

#define LPDDR4__DENALI_PHY_1835__PHY_LS_IDLE_EN_MASK                 0x00000100U
#define LPDDR4__DENALI_PHY_1835__PHY_LS_IDLE_EN_SHIFT                         8U
#define LPDDR4__DENALI_PHY_1835__PHY_LS_IDLE_EN_WIDTH                         1U
#define LPDDR4__DENALI_PHY_1835__PHY_LS_IDLE_EN_WOCLR                         0U
#define LPDDR4__DENALI_PHY_1835__PHY_LS_IDLE_EN_WOSET                         0U
#define LPDDR4__PHY_LS_IDLE_EN__REG DENALI_PHY_1835
#define LPDDR4__PHY_LS_IDLE_EN__FLD LPDDR4__DENALI_PHY_1835__PHY_LS_IDLE_EN

#define LPDDR4__DENALI_PHY_1835__PHY_LP_CTRLUPD_CNTR_CFG_MASK        0x03FF0000U
#define LPDDR4__DENALI_PHY_1835__PHY_LP_CTRLUPD_CNTR_CFG_SHIFT               16U
#define LPDDR4__DENALI_PHY_1835__PHY_LP_CTRLUPD_CNTR_CFG_WIDTH               10U
#define LPDDR4__PHY_LP_CTRLUPD_CNTR_CFG__REG DENALI_PHY_1835
#define LPDDR4__PHY_LP_CTRLUPD_CNTR_CFG__FLD LPDDR4__DENALI_PHY_1835__PHY_LP_CTRLUPD_CNTR_CFG

#define LPDDR4__DENALI_PHY_1836_READ_MASK                            0x0101FFFFU
#define LPDDR4__DENALI_PHY_1836_WRITE_MASK                           0x0101FFFFU
#define LPDDR4__DENALI_PHY_1836__PHY_DS_EXIT_CTRL_MASK               0x0001FFFFU
#define LPDDR4__DENALI_PHY_1836__PHY_DS_EXIT_CTRL_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1836__PHY_DS_EXIT_CTRL_WIDTH                      17U
#define LPDDR4__PHY_DS_EXIT_CTRL__REG DENALI_PHY_1836
#define LPDDR4__PHY_DS_EXIT_CTRL__FLD LPDDR4__DENALI_PHY_1836__PHY_DS_EXIT_CTRL

#define LPDDR4__DENALI_PHY_1836__PHY_TDFI_PHY_WRDELAY_MASK           0x01000000U
#define LPDDR4__DENALI_PHY_1836__PHY_TDFI_PHY_WRDELAY_SHIFT                  24U
#define LPDDR4__DENALI_PHY_1836__PHY_TDFI_PHY_WRDELAY_WIDTH                   1U
#define LPDDR4__DENALI_PHY_1836__PHY_TDFI_PHY_WRDELAY_WOCLR                   0U
#define LPDDR4__DENALI_PHY_1836__PHY_TDFI_PHY_WRDELAY_WOSET                   0U
#define LPDDR4__PHY_TDFI_PHY_WRDELAY__REG DENALI_PHY_1836
#define LPDDR4__PHY_TDFI_PHY_WRDELAY__FLD LPDDR4__DENALI_PHY_1836__PHY_TDFI_PHY_WRDELAY

#define LPDDR4__DENALI_PHY_1837_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1837_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1837__PHY_PAD_FDBK_TERM_MASK              0x0003FFFFU
#define LPDDR4__DENALI_PHY_1837__PHY_PAD_FDBK_TERM_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1837__PHY_PAD_FDBK_TERM_WIDTH                     18U
#define LPDDR4__PHY_PAD_FDBK_TERM__REG DENALI_PHY_1837
#define LPDDR4__PHY_PAD_FDBK_TERM__FLD LPDDR4__DENALI_PHY_1837__PHY_PAD_FDBK_TERM

#define LPDDR4__DENALI_PHY_1838_READ_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_PHY_1838_WRITE_MASK                           0x0001FFFFU
#define LPDDR4__DENALI_PHY_1838__PHY_PAD_DATA_TERM_MASK              0x0001FFFFU
#define LPDDR4__DENALI_PHY_1838__PHY_PAD_DATA_TERM_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1838__PHY_PAD_DATA_TERM_WIDTH                     17U
#define LPDDR4__PHY_PAD_DATA_TERM__REG DENALI_PHY_1838
#define LPDDR4__PHY_PAD_DATA_TERM__FLD LPDDR4__DENALI_PHY_1838__PHY_PAD_DATA_TERM

#define LPDDR4__DENALI_PHY_1839_READ_MASK                            0x0001FFFFU
#define LPDDR4__DENALI_PHY_1839_WRITE_MASK                           0x0001FFFFU
#define LPDDR4__DENALI_PHY_1839__PHY_PAD_DQS_TERM_MASK               0x0001FFFFU
#define LPDDR4__DENALI_PHY_1839__PHY_PAD_DQS_TERM_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1839__PHY_PAD_DQS_TERM_WIDTH                      17U
#define LPDDR4__PHY_PAD_DQS_TERM__REG DENALI_PHY_1839
#define LPDDR4__PHY_PAD_DQS_TERM__FLD LPDDR4__DENALI_PHY_1839__PHY_PAD_DQS_TERM

#define LPDDR4__DENALI_PHY_1840_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1840_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1840__PHY_PAD_ADDR_TERM_MASK              0x0003FFFFU
#define LPDDR4__DENALI_PHY_1840__PHY_PAD_ADDR_TERM_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1840__PHY_PAD_ADDR_TERM_WIDTH                     18U
#define LPDDR4__PHY_PAD_ADDR_TERM__REG DENALI_PHY_1840
#define LPDDR4__PHY_PAD_ADDR_TERM__FLD LPDDR4__DENALI_PHY_1840__PHY_PAD_ADDR_TERM

#define LPDDR4__DENALI_PHY_1841_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1841_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1841__PHY_PAD_CLK_TERM_MASK               0x0003FFFFU
#define LPDDR4__DENALI_PHY_1841__PHY_PAD_CLK_TERM_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1841__PHY_PAD_CLK_TERM_WIDTH                      18U
#define LPDDR4__PHY_PAD_CLK_TERM__REG DENALI_PHY_1841
#define LPDDR4__PHY_PAD_CLK_TERM__FLD LPDDR4__DENALI_PHY_1841__PHY_PAD_CLK_TERM

#define LPDDR4__DENALI_PHY_1842_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1842_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1842__PHY_PAD_ERR_TERM_MASK               0x0003FFFFU
#define LPDDR4__DENALI_PHY_1842__PHY_PAD_ERR_TERM_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1842__PHY_PAD_ERR_TERM_WIDTH                      18U
#define LPDDR4__PHY_PAD_ERR_TERM__REG DENALI_PHY_1842
#define LPDDR4__PHY_PAD_ERR_TERM__FLD LPDDR4__DENALI_PHY_1842__PHY_PAD_ERR_TERM

#define LPDDR4__DENALI_PHY_1843_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1843_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1843__PHY_PAD_CKE_TERM_MASK               0x0003FFFFU
#define LPDDR4__DENALI_PHY_1843__PHY_PAD_CKE_TERM_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1843__PHY_PAD_CKE_TERM_WIDTH                      18U
#define LPDDR4__PHY_PAD_CKE_TERM__REG DENALI_PHY_1843
#define LPDDR4__PHY_PAD_CKE_TERM__FLD LPDDR4__DENALI_PHY_1843__PHY_PAD_CKE_TERM

#define LPDDR4__DENALI_PHY_1844_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1844_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1844__PHY_PAD_RST_TERM_MASK               0x0003FFFFU
#define LPDDR4__DENALI_PHY_1844__PHY_PAD_RST_TERM_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1844__PHY_PAD_RST_TERM_WIDTH                      18U
#define LPDDR4__PHY_PAD_RST_TERM__REG DENALI_PHY_1844
#define LPDDR4__PHY_PAD_RST_TERM__FLD LPDDR4__DENALI_PHY_1844__PHY_PAD_RST_TERM

#define LPDDR4__DENALI_PHY_1845_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1845_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1845__PHY_PAD_CS_TERM_MASK                0x0003FFFFU
#define LPDDR4__DENALI_PHY_1845__PHY_PAD_CS_TERM_SHIFT                        0U
#define LPDDR4__DENALI_PHY_1845__PHY_PAD_CS_TERM_WIDTH                       18U
#define LPDDR4__PHY_PAD_CS_TERM__REG DENALI_PHY_1845
#define LPDDR4__PHY_PAD_CS_TERM__FLD LPDDR4__DENALI_PHY_1845__PHY_PAD_CS_TERM

#define LPDDR4__DENALI_PHY_1846_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1846_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1846__PHY_PAD_ODT_TERM_MASK               0x0003FFFFU
#define LPDDR4__DENALI_PHY_1846__PHY_PAD_ODT_TERM_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1846__PHY_PAD_ODT_TERM_WIDTH                      18U
#define LPDDR4__PHY_PAD_ODT_TERM__REG DENALI_PHY_1846
#define LPDDR4__PHY_PAD_ODT_TERM__FLD LPDDR4__DENALI_PHY_1846__PHY_PAD_ODT_TERM

#define LPDDR4__DENALI_PHY_1847_READ_MASK                            0x1FFF03FFU
#define LPDDR4__DENALI_PHY_1847_WRITE_MASK                           0x1FFF03FFU
#define LPDDR4__DENALI_PHY_1847__PHY_ADRCTL_RX_CAL_MASK              0x000003FFU
#define LPDDR4__DENALI_PHY_1847__PHY_ADRCTL_RX_CAL_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1847__PHY_ADRCTL_RX_CAL_WIDTH                     10U
#define LPDDR4__PHY_ADRCTL_RX_CAL__REG DENALI_PHY_1847
#define LPDDR4__PHY_ADRCTL_RX_CAL__FLD LPDDR4__DENALI_PHY_1847__PHY_ADRCTL_RX_CAL

#define LPDDR4__DENALI_PHY_1847__PHY_ADRCTL_LP3_RX_CAL_MASK          0x1FFF0000U
#define LPDDR4__DENALI_PHY_1847__PHY_ADRCTL_LP3_RX_CAL_SHIFT                 16U
#define LPDDR4__DENALI_PHY_1847__PHY_ADRCTL_LP3_RX_CAL_WIDTH                 13U
#define LPDDR4__PHY_ADRCTL_LP3_RX_CAL__REG DENALI_PHY_1847
#define LPDDR4__PHY_ADRCTL_LP3_RX_CAL__FLD LPDDR4__DENALI_PHY_1847__PHY_ADRCTL_LP3_RX_CAL

#define LPDDR4__DENALI_PHY_1848_READ_MASK                            0x00001FFFU
#define LPDDR4__DENALI_PHY_1848_WRITE_MASK                           0x00001FFFU
#define LPDDR4__DENALI_PHY_1848__PHY_CAL_MODE_0_MASK                 0x00001FFFU
#define LPDDR4__DENALI_PHY_1848__PHY_CAL_MODE_0_SHIFT                         0U
#define LPDDR4__DENALI_PHY_1848__PHY_CAL_MODE_0_WIDTH                        13U
#define LPDDR4__PHY_CAL_MODE_0__REG DENALI_PHY_1848
#define LPDDR4__PHY_CAL_MODE_0__FLD LPDDR4__DENALI_PHY_1848__PHY_CAL_MODE_0

#define LPDDR4__DENALI_PHY_1848__PHY_CAL_CLEAR_0_MASK                0x00010000U
#define LPDDR4__DENALI_PHY_1848__PHY_CAL_CLEAR_0_SHIFT                       16U
#define LPDDR4__DENALI_PHY_1848__PHY_CAL_CLEAR_0_WIDTH                        1U
#define LPDDR4__DENALI_PHY_1848__PHY_CAL_CLEAR_0_WOCLR                        0U
#define LPDDR4__DENALI_PHY_1848__PHY_CAL_CLEAR_0_WOSET                        0U
#define LPDDR4__PHY_CAL_CLEAR_0__REG DENALI_PHY_1848
#define LPDDR4__PHY_CAL_CLEAR_0__FLD LPDDR4__DENALI_PHY_1848__PHY_CAL_CLEAR_0

#define LPDDR4__DENALI_PHY_1848__PHY_CAL_START_0_MASK                0x01000000U
#define LPDDR4__DENALI_PHY_1848__PHY_CAL_START_0_SHIFT                       24U
#define LPDDR4__DENALI_PHY_1848__PHY_CAL_START_0_WIDTH                        1U
#define LPDDR4__DENALI_PHY_1848__PHY_CAL_START_0_WOCLR                        0U
#define LPDDR4__DENALI_PHY_1848__PHY_CAL_START_0_WOSET                        0U
#define LPDDR4__PHY_CAL_START_0__REG DENALI_PHY_1848
#define LPDDR4__PHY_CAL_START_0__FLD LPDDR4__DENALI_PHY_1848__PHY_CAL_START_0

#define LPDDR4__DENALI_PHY_1849_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1849_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1849__PHY_CAL_INTERVAL_COUNT_0_MASK       0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1849__PHY_CAL_INTERVAL_COUNT_0_SHIFT               0U
#define LPDDR4__DENALI_PHY_1849__PHY_CAL_INTERVAL_COUNT_0_WIDTH              32U
#define LPDDR4__PHY_CAL_INTERVAL_COUNT_0__REG DENALI_PHY_1849
#define LPDDR4__PHY_CAL_INTERVAL_COUNT_0__FLD LPDDR4__DENALI_PHY_1849__PHY_CAL_INTERVAL_COUNT_0

#define LPDDR4__DENALI_PHY_1850_READ_MASK                            0x000007FFU
#define LPDDR4__DENALI_PHY_1850_WRITE_MASK                           0x000007FFU
#define LPDDR4__DENALI_PHY_1850__PHY_CAL_SAMPLE_WAIT_0_MASK          0x000000FFU
#define LPDDR4__DENALI_PHY_1850__PHY_CAL_SAMPLE_WAIT_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1850__PHY_CAL_SAMPLE_WAIT_0_WIDTH                  8U
#define LPDDR4__PHY_CAL_SAMPLE_WAIT_0__REG DENALI_PHY_1850
#define LPDDR4__PHY_CAL_SAMPLE_WAIT_0__FLD LPDDR4__DENALI_PHY_1850__PHY_CAL_SAMPLE_WAIT_0

#define LPDDR4__DENALI_PHY_1850__PHY_LP4_BOOT_CAL_CLK_SELECT_0_MASK  0x00000700U
#define LPDDR4__DENALI_PHY_1850__PHY_LP4_BOOT_CAL_CLK_SELECT_0_SHIFT          8U
#define LPDDR4__DENALI_PHY_1850__PHY_LP4_BOOT_CAL_CLK_SELECT_0_WIDTH          3U
#define LPDDR4__PHY_LP4_BOOT_CAL_CLK_SELECT_0__REG DENALI_PHY_1850
#define LPDDR4__PHY_LP4_BOOT_CAL_CLK_SELECT_0__FLD LPDDR4__DENALI_PHY_1850__PHY_LP4_BOOT_CAL_CLK_SELECT_0

#define LPDDR4__DENALI_PHY_1851_READ_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1851_WRITE_MASK                           0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1851__PHY_CAL_RESULT_OBS_0_MASK           0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1851__PHY_CAL_RESULT_OBS_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1851__PHY_CAL_RESULT_OBS_0_WIDTH                  24U
#define LPDDR4__PHY_CAL_RESULT_OBS_0__REG DENALI_PHY_1851
#define LPDDR4__PHY_CAL_RESULT_OBS_0__FLD LPDDR4__DENALI_PHY_1851__PHY_CAL_RESULT_OBS_0

#define LPDDR4__DENALI_PHY_1852_READ_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1852_WRITE_MASK                           0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1852__PHY_CAL_RESULT2_OBS_0_MASK          0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1852__PHY_CAL_RESULT2_OBS_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1852__PHY_CAL_RESULT2_OBS_0_WIDTH                 24U
#define LPDDR4__PHY_CAL_RESULT2_OBS_0__REG DENALI_PHY_1852
#define LPDDR4__PHY_CAL_RESULT2_OBS_0__FLD LPDDR4__DENALI_PHY_1852__PHY_CAL_RESULT2_OBS_0

#define LPDDR4__DENALI_PHY_1853_READ_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1853_WRITE_MASK                           0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1853__PHY_CAL_RESULT4_OBS_0_MASK          0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1853__PHY_CAL_RESULT4_OBS_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1853__PHY_CAL_RESULT4_OBS_0_WIDTH                 24U
#define LPDDR4__PHY_CAL_RESULT4_OBS_0__REG DENALI_PHY_1853
#define LPDDR4__PHY_CAL_RESULT4_OBS_0__FLD LPDDR4__DENALI_PHY_1853__PHY_CAL_RESULT4_OBS_0

#define LPDDR4__DENALI_PHY_1854_READ_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1854_WRITE_MASK                           0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1854__PHY_CAL_RESULT5_OBS_0_MASK          0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1854__PHY_CAL_RESULT5_OBS_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1854__PHY_CAL_RESULT5_OBS_0_WIDTH                 24U
#define LPDDR4__PHY_CAL_RESULT5_OBS_0__REG DENALI_PHY_1854
#define LPDDR4__PHY_CAL_RESULT5_OBS_0__FLD LPDDR4__DENALI_PHY_1854__PHY_CAL_RESULT5_OBS_0

#define LPDDR4__DENALI_PHY_1855_READ_MASK                            0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1855_WRITE_MASK                           0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1855__PHY_CAL_RESULT6_OBS_0_MASK          0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1855__PHY_CAL_RESULT6_OBS_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1855__PHY_CAL_RESULT6_OBS_0_WIDTH                 24U
#define LPDDR4__PHY_CAL_RESULT6_OBS_0__REG DENALI_PHY_1855
#define LPDDR4__PHY_CAL_RESULT6_OBS_0__FLD LPDDR4__DENALI_PHY_1855__PHY_CAL_RESULT6_OBS_0

#define LPDDR4__DENALI_PHY_1856_READ_MASK                            0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_1856_WRITE_MASK                           0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_1856__PHY_CAL_RESULT7_OBS_0_MASK          0x00FFFFFFU
#define LPDDR4__DENALI_PHY_1856__PHY_CAL_RESULT7_OBS_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1856__PHY_CAL_RESULT7_OBS_0_WIDTH                 24U
#define LPDDR4__PHY_CAL_RESULT7_OBS_0__REG DENALI_PHY_1856
#define LPDDR4__PHY_CAL_RESULT7_OBS_0__FLD LPDDR4__DENALI_PHY_1856__PHY_CAL_RESULT7_OBS_0

#define LPDDR4__DENALI_PHY_1856__PHY_CAL_CPTR_CNT_0_MASK             0x7F000000U
#define LPDDR4__DENALI_PHY_1856__PHY_CAL_CPTR_CNT_0_SHIFT                    24U
#define LPDDR4__DENALI_PHY_1856__PHY_CAL_CPTR_CNT_0_WIDTH                     7U
#define LPDDR4__PHY_CAL_CPTR_CNT_0__REG DENALI_PHY_1856
#define LPDDR4__PHY_CAL_CPTR_CNT_0__FLD LPDDR4__DENALI_PHY_1856__PHY_CAL_CPTR_CNT_0

#define LPDDR4__DENALI_PHY_1857_READ_MASK                            0x01FFFFFFU
#define LPDDR4__DENALI_PHY_1857_WRITE_MASK                           0x01FFFFFFU
#define LPDDR4__DENALI_PHY_1857__PHY_CAL_PU_FINE_ADJ_0_MASK          0x000000FFU
#define LPDDR4__DENALI_PHY_1857__PHY_CAL_PU_FINE_ADJ_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1857__PHY_CAL_PU_FINE_ADJ_0_WIDTH                  8U
#define LPDDR4__PHY_CAL_PU_FINE_ADJ_0__REG DENALI_PHY_1857
#define LPDDR4__PHY_CAL_PU_FINE_ADJ_0__FLD LPDDR4__DENALI_PHY_1857__PHY_CAL_PU_FINE_ADJ_0

#define LPDDR4__DENALI_PHY_1857__PHY_CAL_PD_FINE_ADJ_0_MASK          0x0000FF00U
#define LPDDR4__DENALI_PHY_1857__PHY_CAL_PD_FINE_ADJ_0_SHIFT                  8U
#define LPDDR4__DENALI_PHY_1857__PHY_CAL_PD_FINE_ADJ_0_WIDTH                  8U
#define LPDDR4__PHY_CAL_PD_FINE_ADJ_0__REG DENALI_PHY_1857
#define LPDDR4__PHY_CAL_PD_FINE_ADJ_0__FLD LPDDR4__DENALI_PHY_1857__PHY_CAL_PD_FINE_ADJ_0

#define LPDDR4__DENALI_PHY_1857__PHY_CAL_RCV_FINE_ADJ_0_MASK         0x00FF0000U
#define LPDDR4__DENALI_PHY_1857__PHY_CAL_RCV_FINE_ADJ_0_SHIFT                16U
#define LPDDR4__DENALI_PHY_1857__PHY_CAL_RCV_FINE_ADJ_0_WIDTH                 8U
#define LPDDR4__PHY_CAL_RCV_FINE_ADJ_0__REG DENALI_PHY_1857
#define LPDDR4__PHY_CAL_RCV_FINE_ADJ_0__FLD LPDDR4__DENALI_PHY_1857__PHY_CAL_RCV_FINE_ADJ_0

#define LPDDR4__DENALI_PHY_1857__PHY_CAL_DBG_CFG_0_MASK              0x01000000U
#define LPDDR4__DENALI_PHY_1857__PHY_CAL_DBG_CFG_0_SHIFT                     24U
#define LPDDR4__DENALI_PHY_1857__PHY_CAL_DBG_CFG_0_WIDTH                      1U
#define LPDDR4__DENALI_PHY_1857__PHY_CAL_DBG_CFG_0_WOCLR                      0U
#define LPDDR4__DENALI_PHY_1857__PHY_CAL_DBG_CFG_0_WOSET                      0U
#define LPDDR4__PHY_CAL_DBG_CFG_0__REG DENALI_PHY_1857
#define LPDDR4__PHY_CAL_DBG_CFG_0__FLD LPDDR4__DENALI_PHY_1857__PHY_CAL_DBG_CFG_0

#define LPDDR4__DENALI_PHY_1858__SC_PHY_PAD_DBG_CONT_0_MASK          0x00000001U
#define LPDDR4__DENALI_PHY_1858__SC_PHY_PAD_DBG_CONT_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1858__SC_PHY_PAD_DBG_CONT_0_WIDTH                  1U
#define LPDDR4__DENALI_PHY_1858__SC_PHY_PAD_DBG_CONT_0_WOCLR                  0U
#define LPDDR4__DENALI_PHY_1858__SC_PHY_PAD_DBG_CONT_0_WOSET                  0U
#define LPDDR4__SC_PHY_PAD_DBG_CONT_0__REG DENALI_PHY_1858
#define LPDDR4__SC_PHY_PAD_DBG_CONT_0__FLD LPDDR4__DENALI_PHY_1858__SC_PHY_PAD_DBG_CONT_0

#define LPDDR4__DENALI_PHY_1859_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1859_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1859__PHY_CAL_RESULT3_OBS_0_MASK          0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1859__PHY_CAL_RESULT3_OBS_0_SHIFT                  0U
#define LPDDR4__DENALI_PHY_1859__PHY_CAL_RESULT3_OBS_0_WIDTH                 32U
#define LPDDR4__PHY_CAL_RESULT3_OBS_0__REG DENALI_PHY_1859
#define LPDDR4__PHY_CAL_RESULT3_OBS_0__FLD LPDDR4__DENALI_PHY_1859__PHY_CAL_RESULT3_OBS_0

#define LPDDR4__DENALI_PHY_1860_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1860_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1860__PHY_ADRCTL_PVT_MAP_0_MASK           0x000000FFU
#define LPDDR4__DENALI_PHY_1860__PHY_ADRCTL_PVT_MAP_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1860__PHY_ADRCTL_PVT_MAP_0_WIDTH                   8U
#define LPDDR4__PHY_ADRCTL_PVT_MAP_0__REG DENALI_PHY_1860
#define LPDDR4__PHY_ADRCTL_PVT_MAP_0__FLD LPDDR4__DENALI_PHY_1860__PHY_ADRCTL_PVT_MAP_0

#define LPDDR4__DENALI_PHY_1860__PHY_CAL_SLOPE_ADJ_0_MASK            0x0FFFFF00U
#define LPDDR4__DENALI_PHY_1860__PHY_CAL_SLOPE_ADJ_0_SHIFT                    8U
#define LPDDR4__DENALI_PHY_1860__PHY_CAL_SLOPE_ADJ_0_WIDTH                   20U
#define LPDDR4__PHY_CAL_SLOPE_ADJ_0__REG DENALI_PHY_1860
#define LPDDR4__PHY_CAL_SLOPE_ADJ_0__FLD LPDDR4__DENALI_PHY_1860__PHY_CAL_SLOPE_ADJ_0

#define LPDDR4__DENALI_PHY_1861_READ_MASK                            0x000FFFFFU
#define LPDDR4__DENALI_PHY_1861_WRITE_MASK                           0x000FFFFFU
#define LPDDR4__DENALI_PHY_1861__PHY_CAL_SLOPE_ADJ_PASS2_0_MASK      0x000FFFFFU
#define LPDDR4__DENALI_PHY_1861__PHY_CAL_SLOPE_ADJ_PASS2_0_SHIFT              0U
#define LPDDR4__DENALI_PHY_1861__PHY_CAL_SLOPE_ADJ_PASS2_0_WIDTH             20U
#define LPDDR4__PHY_CAL_SLOPE_ADJ_PASS2_0__REG DENALI_PHY_1861
#define LPDDR4__PHY_CAL_SLOPE_ADJ_PASS2_0__FLD LPDDR4__DENALI_PHY_1861__PHY_CAL_SLOPE_ADJ_PASS2_0

#define LPDDR4__DENALI_PHY_1862_READ_MASK                            0x01FFFFFFU
#define LPDDR4__DENALI_PHY_1862_WRITE_MASK                           0x01FFFFFFU
#define LPDDR4__DENALI_PHY_1862__PHY_CAL_TWO_PASS_CFG_0_MASK         0x01FFFFFFU
#define LPDDR4__DENALI_PHY_1862__PHY_CAL_TWO_PASS_CFG_0_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1862__PHY_CAL_TWO_PASS_CFG_0_WIDTH                25U
#define LPDDR4__PHY_CAL_TWO_PASS_CFG_0__REG DENALI_PHY_1862
#define LPDDR4__PHY_CAL_TWO_PASS_CFG_0__FLD LPDDR4__DENALI_PHY_1862__PHY_CAL_TWO_PASS_CFG_0

#define LPDDR4__DENALI_PHY_1863_READ_MASK                            0x3F7FFFFFU
#define LPDDR4__DENALI_PHY_1863_WRITE_MASK                           0x3F7FFFFFU
#define LPDDR4__DENALI_PHY_1863__PHY_CAL_SW_CAL_CFG_0_MASK           0x007FFFFFU
#define LPDDR4__DENALI_PHY_1863__PHY_CAL_SW_CAL_CFG_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1863__PHY_CAL_SW_CAL_CFG_0_WIDTH                  23U
#define LPDDR4__PHY_CAL_SW_CAL_CFG_0__REG DENALI_PHY_1863
#define LPDDR4__PHY_CAL_SW_CAL_CFG_0__FLD LPDDR4__DENALI_PHY_1863__PHY_CAL_SW_CAL_CFG_0

#define LPDDR4__DENALI_PHY_1863__PHY_CAL_RANGE_PASS1_PU_MAX_DELTA_0_MASK 0x3F000000U
#define LPDDR4__DENALI_PHY_1863__PHY_CAL_RANGE_PASS1_PU_MAX_DELTA_0_SHIFT    24U
#define LPDDR4__DENALI_PHY_1863__PHY_CAL_RANGE_PASS1_PU_MAX_DELTA_0_WIDTH     6U
#define LPDDR4__PHY_CAL_RANGE_PASS1_PU_MAX_DELTA_0__REG DENALI_PHY_1863
#define LPDDR4__PHY_CAL_RANGE_PASS1_PU_MAX_DELTA_0__FLD LPDDR4__DENALI_PHY_1863__PHY_CAL_RANGE_PASS1_PU_MAX_DELTA_0

#define LPDDR4__DENALI_PHY_1864_READ_MASK                            0x3F3F1F3FU
#define LPDDR4__DENALI_PHY_1864_WRITE_MASK                           0x3F3F1F3FU
#define LPDDR4__DENALI_PHY_1864__PHY_CAL_RANGE_PASS1_PD_MAX_DELTA_0_MASK 0x0000003FU
#define LPDDR4__DENALI_PHY_1864__PHY_CAL_RANGE_PASS1_PD_MAX_DELTA_0_SHIFT     0U
#define LPDDR4__DENALI_PHY_1864__PHY_CAL_RANGE_PASS1_PD_MAX_DELTA_0_WIDTH     6U
#define LPDDR4__PHY_CAL_RANGE_PASS1_PD_MAX_DELTA_0__REG DENALI_PHY_1864
#define LPDDR4__PHY_CAL_RANGE_PASS1_PD_MAX_DELTA_0__FLD LPDDR4__DENALI_PHY_1864__PHY_CAL_RANGE_PASS1_PD_MAX_DELTA_0

#define LPDDR4__DENALI_PHY_1864__PHY_CAL_RANGE_PASS1_RX_MAX_DELTA_0_MASK 0x00001F00U
#define LPDDR4__DENALI_PHY_1864__PHY_CAL_RANGE_PASS1_RX_MAX_DELTA_0_SHIFT     8U
#define LPDDR4__DENALI_PHY_1864__PHY_CAL_RANGE_PASS1_RX_MAX_DELTA_0_WIDTH     5U
#define LPDDR4__PHY_CAL_RANGE_PASS1_RX_MAX_DELTA_0__REG DENALI_PHY_1864
#define LPDDR4__PHY_CAL_RANGE_PASS1_RX_MAX_DELTA_0__FLD LPDDR4__DENALI_PHY_1864__PHY_CAL_RANGE_PASS1_RX_MAX_DELTA_0

#define LPDDR4__DENALI_PHY_1864__PHY_CAL_RANGE_PASS2_PU_MAX_DELTA_0_MASK 0x003F0000U
#define LPDDR4__DENALI_PHY_1864__PHY_CAL_RANGE_PASS2_PU_MAX_DELTA_0_SHIFT    16U
#define LPDDR4__DENALI_PHY_1864__PHY_CAL_RANGE_PASS2_PU_MAX_DELTA_0_WIDTH     6U
#define LPDDR4__PHY_CAL_RANGE_PASS2_PU_MAX_DELTA_0__REG DENALI_PHY_1864
#define LPDDR4__PHY_CAL_RANGE_PASS2_PU_MAX_DELTA_0__FLD LPDDR4__DENALI_PHY_1864__PHY_CAL_RANGE_PASS2_PU_MAX_DELTA_0

#define LPDDR4__DENALI_PHY_1864__PHY_CAL_RANGE_PASS2_PD_MAX_DELTA_0_MASK 0x3F000000U
#define LPDDR4__DENALI_PHY_1864__PHY_CAL_RANGE_PASS2_PD_MAX_DELTA_0_SHIFT    24U
#define LPDDR4__DENALI_PHY_1864__PHY_CAL_RANGE_PASS2_PD_MAX_DELTA_0_WIDTH     6U
#define LPDDR4__PHY_CAL_RANGE_PASS2_PD_MAX_DELTA_0__REG DENALI_PHY_1864
#define LPDDR4__PHY_CAL_RANGE_PASS2_PD_MAX_DELTA_0__FLD LPDDR4__DENALI_PHY_1864__PHY_CAL_RANGE_PASS2_PD_MAX_DELTA_0

#define LPDDR4__DENALI_PHY_1865_READ_MASK                            0x1F3F3F1FU
#define LPDDR4__DENALI_PHY_1865_WRITE_MASK                           0x1F3F3F1FU
#define LPDDR4__DENALI_PHY_1865__PHY_CAL_RANGE_PASS2_RX_MAX_DELTA_0_MASK 0x0000001FU
#define LPDDR4__DENALI_PHY_1865__PHY_CAL_RANGE_PASS2_RX_MAX_DELTA_0_SHIFT     0U
#define LPDDR4__DENALI_PHY_1865__PHY_CAL_RANGE_PASS2_RX_MAX_DELTA_0_WIDTH     5U
#define LPDDR4__PHY_CAL_RANGE_PASS2_RX_MAX_DELTA_0__REG DENALI_PHY_1865
#define LPDDR4__PHY_CAL_RANGE_PASS2_RX_MAX_DELTA_0__FLD LPDDR4__DENALI_PHY_1865__PHY_CAL_RANGE_PASS2_RX_MAX_DELTA_0

#define LPDDR4__DENALI_PHY_1865__PHY_CAL_RANGE_PASS1_PU_MIN_DELTA_0_MASK 0x00003F00U
#define LPDDR4__DENALI_PHY_1865__PHY_CAL_RANGE_PASS1_PU_MIN_DELTA_0_SHIFT     8U
#define LPDDR4__DENALI_PHY_1865__PHY_CAL_RANGE_PASS1_PU_MIN_DELTA_0_WIDTH     6U
#define LPDDR4__PHY_CAL_RANGE_PASS1_PU_MIN_DELTA_0__REG DENALI_PHY_1865
#define LPDDR4__PHY_CAL_RANGE_PASS1_PU_MIN_DELTA_0__FLD LPDDR4__DENALI_PHY_1865__PHY_CAL_RANGE_PASS1_PU_MIN_DELTA_0

#define LPDDR4__DENALI_PHY_1865__PHY_CAL_RANGE_PASS1_PD_MIN_DELTA_0_MASK 0x003F0000U
#define LPDDR4__DENALI_PHY_1865__PHY_CAL_RANGE_PASS1_PD_MIN_DELTA_0_SHIFT    16U
#define LPDDR4__DENALI_PHY_1865__PHY_CAL_RANGE_PASS1_PD_MIN_DELTA_0_WIDTH     6U
#define LPDDR4__PHY_CAL_RANGE_PASS1_PD_MIN_DELTA_0__REG DENALI_PHY_1865
#define LPDDR4__PHY_CAL_RANGE_PASS1_PD_MIN_DELTA_0__FLD LPDDR4__DENALI_PHY_1865__PHY_CAL_RANGE_PASS1_PD_MIN_DELTA_0

#define LPDDR4__DENALI_PHY_1865__PHY_CAL_RANGE_PASS1_RX_MIN_DELTA_0_MASK 0x1F000000U
#define LPDDR4__DENALI_PHY_1865__PHY_CAL_RANGE_PASS1_RX_MIN_DELTA_0_SHIFT    24U
#define LPDDR4__DENALI_PHY_1865__PHY_CAL_RANGE_PASS1_RX_MIN_DELTA_0_WIDTH     5U
#define LPDDR4__PHY_CAL_RANGE_PASS1_RX_MIN_DELTA_0__REG DENALI_PHY_1865
#define LPDDR4__PHY_CAL_RANGE_PASS1_RX_MIN_DELTA_0__FLD LPDDR4__DENALI_PHY_1865__PHY_CAL_RANGE_PASS1_RX_MIN_DELTA_0

#define LPDDR4__DENALI_PHY_1866_READ_MASK                            0x001F3F3FU
#define LPDDR4__DENALI_PHY_1866_WRITE_MASK                           0x001F3F3FU
#define LPDDR4__DENALI_PHY_1866__PHY_CAL_RANGE_PASS2_PU_MIN_DELTA_0_MASK 0x0000003FU
#define LPDDR4__DENALI_PHY_1866__PHY_CAL_RANGE_PASS2_PU_MIN_DELTA_0_SHIFT     0U
#define LPDDR4__DENALI_PHY_1866__PHY_CAL_RANGE_PASS2_PU_MIN_DELTA_0_WIDTH     6U
#define LPDDR4__PHY_CAL_RANGE_PASS2_PU_MIN_DELTA_0__REG DENALI_PHY_1866
#define LPDDR4__PHY_CAL_RANGE_PASS2_PU_MIN_DELTA_0__FLD LPDDR4__DENALI_PHY_1866__PHY_CAL_RANGE_PASS2_PU_MIN_DELTA_0

#define LPDDR4__DENALI_PHY_1866__PHY_CAL_RANGE_PASS2_PD_MIN_DELTA_0_MASK 0x00003F00U
#define LPDDR4__DENALI_PHY_1866__PHY_CAL_RANGE_PASS2_PD_MIN_DELTA_0_SHIFT     8U
#define LPDDR4__DENALI_PHY_1866__PHY_CAL_RANGE_PASS2_PD_MIN_DELTA_0_WIDTH     6U
#define LPDDR4__PHY_CAL_RANGE_PASS2_PD_MIN_DELTA_0__REG DENALI_PHY_1866
#define LPDDR4__PHY_CAL_RANGE_PASS2_PD_MIN_DELTA_0__FLD LPDDR4__DENALI_PHY_1866__PHY_CAL_RANGE_PASS2_PD_MIN_DELTA_0

#define LPDDR4__DENALI_PHY_1866__PHY_CAL_RANGE_PASS2_RX_MIN_DELTA_0_MASK 0x001F0000U
#define LPDDR4__DENALI_PHY_1866__PHY_CAL_RANGE_PASS2_RX_MIN_DELTA_0_SHIFT    16U
#define LPDDR4__DENALI_PHY_1866__PHY_CAL_RANGE_PASS2_RX_MIN_DELTA_0_WIDTH     5U
#define LPDDR4__PHY_CAL_RANGE_PASS2_RX_MIN_DELTA_0__REG DENALI_PHY_1866
#define LPDDR4__PHY_CAL_RANGE_PASS2_RX_MIN_DELTA_0__FLD LPDDR4__DENALI_PHY_1866__PHY_CAL_RANGE_PASS2_RX_MIN_DELTA_0

#define LPDDR4__DENALI_PHY_1867_READ_MASK                            0x0000FFFFU
#define LPDDR4__DENALI_PHY_1867_WRITE_MASK                           0x0000FFFFU
#define LPDDR4__DENALI_PHY_1867__PHY_PAD_ATB_CTRL_MASK               0x0000FFFFU
#define LPDDR4__DENALI_PHY_1867__PHY_PAD_ATB_CTRL_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1867__PHY_PAD_ATB_CTRL_WIDTH                      16U
#define LPDDR4__PHY_PAD_ATB_CTRL__REG DENALI_PHY_1867
#define LPDDR4__PHY_PAD_ATB_CTRL__FLD LPDDR4__DENALI_PHY_1867__PHY_PAD_ATB_CTRL

#define LPDDR4__DENALI_PHY_1867__PHY_ADRCTL_MANUAL_UPDATE_MASK       0x00010000U
#define LPDDR4__DENALI_PHY_1867__PHY_ADRCTL_MANUAL_UPDATE_SHIFT              16U
#define LPDDR4__DENALI_PHY_1867__PHY_ADRCTL_MANUAL_UPDATE_WIDTH               1U
#define LPDDR4__DENALI_PHY_1867__PHY_ADRCTL_MANUAL_UPDATE_WOCLR               0U
#define LPDDR4__DENALI_PHY_1867__PHY_ADRCTL_MANUAL_UPDATE_WOSET               0U
#define LPDDR4__PHY_ADRCTL_MANUAL_UPDATE__REG DENALI_PHY_1867
#define LPDDR4__PHY_ADRCTL_MANUAL_UPDATE__FLD LPDDR4__DENALI_PHY_1867__PHY_ADRCTL_MANUAL_UPDATE

#define LPDDR4__DENALI_PHY_1867__PHY_AC_LPBK_ERR_CLEAR_MASK          0x01000000U
#define LPDDR4__DENALI_PHY_1867__PHY_AC_LPBK_ERR_CLEAR_SHIFT                 24U
#define LPDDR4__DENALI_PHY_1867__PHY_AC_LPBK_ERR_CLEAR_WIDTH                  1U
#define LPDDR4__DENALI_PHY_1867__PHY_AC_LPBK_ERR_CLEAR_WOCLR                  0U
#define LPDDR4__DENALI_PHY_1867__PHY_AC_LPBK_ERR_CLEAR_WOSET                  0U
#define LPDDR4__PHY_AC_LPBK_ERR_CLEAR__REG DENALI_PHY_1867
#define LPDDR4__PHY_AC_LPBK_ERR_CLEAR__FLD LPDDR4__DENALI_PHY_1867__PHY_AC_LPBK_ERR_CLEAR

#define LPDDR4__DENALI_PHY_1868_READ_MASK                            0x01FF0F03U
#define LPDDR4__DENALI_PHY_1868_WRITE_MASK                           0x01FF0F03U
#define LPDDR4__DENALI_PHY_1868__PHY_AC_LPBK_OBS_SELECT_MASK         0x00000003U
#define LPDDR4__DENALI_PHY_1868__PHY_AC_LPBK_OBS_SELECT_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1868__PHY_AC_LPBK_OBS_SELECT_WIDTH                 2U
#define LPDDR4__PHY_AC_LPBK_OBS_SELECT__REG DENALI_PHY_1868
#define LPDDR4__PHY_AC_LPBK_OBS_SELECT__FLD LPDDR4__DENALI_PHY_1868__PHY_AC_LPBK_OBS_SELECT

#define LPDDR4__DENALI_PHY_1868__PHY_AC_LPBK_ENABLE_MASK             0x00000F00U
#define LPDDR4__DENALI_PHY_1868__PHY_AC_LPBK_ENABLE_SHIFT                     8U
#define LPDDR4__DENALI_PHY_1868__PHY_AC_LPBK_ENABLE_WIDTH                     4U
#define LPDDR4__PHY_AC_LPBK_ENABLE__REG DENALI_PHY_1868
#define LPDDR4__PHY_AC_LPBK_ENABLE__FLD LPDDR4__DENALI_PHY_1868__PHY_AC_LPBK_ENABLE

#define LPDDR4__DENALI_PHY_1868__PHY_AC_LPBK_CONTROL_MASK            0x01FF0000U
#define LPDDR4__DENALI_PHY_1868__PHY_AC_LPBK_CONTROL_SHIFT                   16U
#define LPDDR4__DENALI_PHY_1868__PHY_AC_LPBK_CONTROL_WIDTH                    9U
#define LPDDR4__PHY_AC_LPBK_CONTROL__REG DENALI_PHY_1868
#define LPDDR4__PHY_AC_LPBK_CONTROL__FLD LPDDR4__DENALI_PHY_1868__PHY_AC_LPBK_CONTROL

#define LPDDR4__DENALI_PHY_1869_READ_MASK                            0x00000F7FU
#define LPDDR4__DENALI_PHY_1869_WRITE_MASK                           0x00000F7FU
#define LPDDR4__DENALI_PHY_1869__PHY_AC_PRBS_PATTERN_START_MASK      0x0000007FU
#define LPDDR4__DENALI_PHY_1869__PHY_AC_PRBS_PATTERN_START_SHIFT              0U
#define LPDDR4__DENALI_PHY_1869__PHY_AC_PRBS_PATTERN_START_WIDTH              7U
#define LPDDR4__PHY_AC_PRBS_PATTERN_START__REG DENALI_PHY_1869
#define LPDDR4__PHY_AC_PRBS_PATTERN_START__FLD LPDDR4__DENALI_PHY_1869__PHY_AC_PRBS_PATTERN_START

#define LPDDR4__DENALI_PHY_1869__PHY_AC_PRBS_PATTERN_MASK_MASK       0x00000F00U
#define LPDDR4__DENALI_PHY_1869__PHY_AC_PRBS_PATTERN_MASK_SHIFT               8U
#define LPDDR4__DENALI_PHY_1869__PHY_AC_PRBS_PATTERN_MASK_WIDTH               4U
#define LPDDR4__PHY_AC_PRBS_PATTERN_MASK__REG DENALI_PHY_1869
#define LPDDR4__PHY_AC_PRBS_PATTERN_MASK__FLD LPDDR4__DENALI_PHY_1869__PHY_AC_PRBS_PATTERN_MASK

#define LPDDR4__DENALI_PHY_1870_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1870_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1870__PHY_AC_LPBK_RESULT_OBS_MASK         0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1870__PHY_AC_LPBK_RESULT_OBS_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1870__PHY_AC_LPBK_RESULT_OBS_WIDTH                32U
#define LPDDR4__PHY_AC_LPBK_RESULT_OBS__REG DENALI_PHY_1870
#define LPDDR4__PHY_AC_LPBK_RESULT_OBS__FLD LPDDR4__DENALI_PHY_1870__PHY_AC_LPBK_RESULT_OBS

#define LPDDR4__DENALI_PHY_1871_READ_MASK                            0x003F0101U
#define LPDDR4__DENALI_PHY_1871_WRITE_MASK                           0x003F0101U
#define LPDDR4__DENALI_PHY_1871__PHY_AC_CLK_LPBK_OBS_SELECT_MASK     0x00000001U
#define LPDDR4__DENALI_PHY_1871__PHY_AC_CLK_LPBK_OBS_SELECT_SHIFT             0U
#define LPDDR4__DENALI_PHY_1871__PHY_AC_CLK_LPBK_OBS_SELECT_WIDTH             1U
#define LPDDR4__DENALI_PHY_1871__PHY_AC_CLK_LPBK_OBS_SELECT_WOCLR             0U
#define LPDDR4__DENALI_PHY_1871__PHY_AC_CLK_LPBK_OBS_SELECT_WOSET             0U
#define LPDDR4__PHY_AC_CLK_LPBK_OBS_SELECT__REG DENALI_PHY_1871
#define LPDDR4__PHY_AC_CLK_LPBK_OBS_SELECT__FLD LPDDR4__DENALI_PHY_1871__PHY_AC_CLK_LPBK_OBS_SELECT

#define LPDDR4__DENALI_PHY_1871__PHY_AC_CLK_LPBK_ENABLE_MASK         0x00000100U
#define LPDDR4__DENALI_PHY_1871__PHY_AC_CLK_LPBK_ENABLE_SHIFT                 8U
#define LPDDR4__DENALI_PHY_1871__PHY_AC_CLK_LPBK_ENABLE_WIDTH                 1U
#define LPDDR4__DENALI_PHY_1871__PHY_AC_CLK_LPBK_ENABLE_WOCLR                 0U
#define LPDDR4__DENALI_PHY_1871__PHY_AC_CLK_LPBK_ENABLE_WOSET                 0U
#define LPDDR4__PHY_AC_CLK_LPBK_ENABLE__REG DENALI_PHY_1871
#define LPDDR4__PHY_AC_CLK_LPBK_ENABLE__FLD LPDDR4__DENALI_PHY_1871__PHY_AC_CLK_LPBK_ENABLE

#define LPDDR4__DENALI_PHY_1871__PHY_AC_CLK_LPBK_CONTROL_MASK        0x003F0000U
#define LPDDR4__DENALI_PHY_1871__PHY_AC_CLK_LPBK_CONTROL_SHIFT               16U
#define LPDDR4__DENALI_PHY_1871__PHY_AC_CLK_LPBK_CONTROL_WIDTH                6U
#define LPDDR4__PHY_AC_CLK_LPBK_CONTROL__REG DENALI_PHY_1871
#define LPDDR4__PHY_AC_CLK_LPBK_CONTROL__FLD LPDDR4__DENALI_PHY_1871__PHY_AC_CLK_LPBK_CONTROL

#define LPDDR4__DENALI_PHY_1872_READ_MASK                            0x0101FFFFU
#define LPDDR4__DENALI_PHY_1872_WRITE_MASK                           0x0101FFFFU
#define LPDDR4__DENALI_PHY_1872__PHY_AC_CLK_LPBK_RESULT_OBS_MASK     0x0000FFFFU
#define LPDDR4__DENALI_PHY_1872__PHY_AC_CLK_LPBK_RESULT_OBS_SHIFT             0U
#define LPDDR4__DENALI_PHY_1872__PHY_AC_CLK_LPBK_RESULT_OBS_WIDTH            16U
#define LPDDR4__PHY_AC_CLK_LPBK_RESULT_OBS__REG DENALI_PHY_1872
#define LPDDR4__PHY_AC_CLK_LPBK_RESULT_OBS__FLD LPDDR4__DENALI_PHY_1872__PHY_AC_CLK_LPBK_RESULT_OBS

#define LPDDR4__DENALI_PHY_1872__PHY_AC_PWR_RDC_DISABLE_MASK         0x00010000U
#define LPDDR4__DENALI_PHY_1872__PHY_AC_PWR_RDC_DISABLE_SHIFT                16U
#define LPDDR4__DENALI_PHY_1872__PHY_AC_PWR_RDC_DISABLE_WIDTH                 1U
#define LPDDR4__DENALI_PHY_1872__PHY_AC_PWR_RDC_DISABLE_WOCLR                 0U
#define LPDDR4__DENALI_PHY_1872__PHY_AC_PWR_RDC_DISABLE_WOSET                 0U
#define LPDDR4__PHY_AC_PWR_RDC_DISABLE__REG DENALI_PHY_1872
#define LPDDR4__PHY_AC_PWR_RDC_DISABLE__FLD LPDDR4__DENALI_PHY_1872__PHY_AC_PWR_RDC_DISABLE

#define LPDDR4__DENALI_PHY_1872__PHY_TOP_PWR_RDC_DISABLE_MASK        0x01000000U
#define LPDDR4__DENALI_PHY_1872__PHY_TOP_PWR_RDC_DISABLE_SHIFT               24U
#define LPDDR4__DENALI_PHY_1872__PHY_TOP_PWR_RDC_DISABLE_WIDTH                1U
#define LPDDR4__DENALI_PHY_1872__PHY_TOP_PWR_RDC_DISABLE_WOCLR                0U
#define LPDDR4__DENALI_PHY_1872__PHY_TOP_PWR_RDC_DISABLE_WOSET                0U
#define LPDDR4__PHY_TOP_PWR_RDC_DISABLE__REG DENALI_PHY_1872
#define LPDDR4__PHY_TOP_PWR_RDC_DISABLE__FLD LPDDR4__DENALI_PHY_1872__PHY_TOP_PWR_RDC_DISABLE

#define LPDDR4__DENALI_PHY_1873_READ_MASK                            0x00000001U
#define LPDDR4__DENALI_PHY_1873_WRITE_MASK                           0x00000001U
#define LPDDR4__DENALI_PHY_1873__PHY_AC_SLV_DLY_CTRL_GATE_DISABLE_MASK 0x00000001U
#define LPDDR4__DENALI_PHY_1873__PHY_AC_SLV_DLY_CTRL_GATE_DISABLE_SHIFT       0U
#define LPDDR4__DENALI_PHY_1873__PHY_AC_SLV_DLY_CTRL_GATE_DISABLE_WIDTH       1U
#define LPDDR4__DENALI_PHY_1873__PHY_AC_SLV_DLY_CTRL_GATE_DISABLE_WOCLR       0U
#define LPDDR4__DENALI_PHY_1873__PHY_AC_SLV_DLY_CTRL_GATE_DISABLE_WOSET       0U
#define LPDDR4__PHY_AC_SLV_DLY_CTRL_GATE_DISABLE__REG DENALI_PHY_1873
#define LPDDR4__PHY_AC_SLV_DLY_CTRL_GATE_DISABLE__FLD LPDDR4__DENALI_PHY_1873__PHY_AC_SLV_DLY_CTRL_GATE_DISABLE

#define LPDDR4__DENALI_PHY_1874_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1874_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1874__PHY_DATA_BYTE_ORDER_SEL_MASK        0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1874__PHY_DATA_BYTE_ORDER_SEL_SHIFT                0U
#define LPDDR4__DENALI_PHY_1874__PHY_DATA_BYTE_ORDER_SEL_WIDTH               32U
#define LPDDR4__PHY_DATA_BYTE_ORDER_SEL__REG DENALI_PHY_1874
#define LPDDR4__PHY_DATA_BYTE_ORDER_SEL__FLD LPDDR4__DENALI_PHY_1874__PHY_DATA_BYTE_ORDER_SEL

#define LPDDR4__DENALI_PHY_1875_READ_MASK                            0x071F01FFU
#define LPDDR4__DENALI_PHY_1875_WRITE_MASK                           0x071F01FFU
#define LPDDR4__DENALI_PHY_1875__PHY_DATA_BYTE_ORDER_SEL_HIGH_MASK   0x000000FFU
#define LPDDR4__DENALI_PHY_1875__PHY_DATA_BYTE_ORDER_SEL_HIGH_SHIFT           0U
#define LPDDR4__DENALI_PHY_1875__PHY_DATA_BYTE_ORDER_SEL_HIGH_WIDTH           8U
#define LPDDR4__PHY_DATA_BYTE_ORDER_SEL_HIGH__REG DENALI_PHY_1875
#define LPDDR4__PHY_DATA_BYTE_ORDER_SEL_HIGH__FLD LPDDR4__DENALI_PHY_1875__PHY_DATA_BYTE_ORDER_SEL_HIGH

#define LPDDR4__DENALI_PHY_1875__PHY_LPDDR4_CONNECT_MASK             0x00000100U
#define LPDDR4__DENALI_PHY_1875__PHY_LPDDR4_CONNECT_SHIFT                     8U
#define LPDDR4__DENALI_PHY_1875__PHY_LPDDR4_CONNECT_WIDTH                     1U
#define LPDDR4__DENALI_PHY_1875__PHY_LPDDR4_CONNECT_WOCLR                     0U
#define LPDDR4__DENALI_PHY_1875__PHY_LPDDR4_CONNECT_WOSET                     0U
#define LPDDR4__PHY_LPDDR4_CONNECT__REG DENALI_PHY_1875
#define LPDDR4__PHY_LPDDR4_CONNECT__FLD LPDDR4__DENALI_PHY_1875__PHY_LPDDR4_CONNECT

#define LPDDR4__DENALI_PHY_1875__PHY_CALVL_DEVICE_MAP_MASK           0x001F0000U
#define LPDDR4__DENALI_PHY_1875__PHY_CALVL_DEVICE_MAP_SHIFT                  16U
#define LPDDR4__DENALI_PHY_1875__PHY_CALVL_DEVICE_MAP_WIDTH                   5U
#define LPDDR4__PHY_CALVL_DEVICE_MAP__REG DENALI_PHY_1875
#define LPDDR4__PHY_CALVL_DEVICE_MAP__FLD LPDDR4__DENALI_PHY_1875__PHY_CALVL_DEVICE_MAP

#define LPDDR4__DENALI_PHY_1875__PHY_ADR_DISABLE_MASK                0x07000000U
#define LPDDR4__DENALI_PHY_1875__PHY_ADR_DISABLE_SHIFT                       24U
#define LPDDR4__DENALI_PHY_1875__PHY_ADR_DISABLE_WIDTH                        3U
#define LPDDR4__PHY_ADR_DISABLE__REG DENALI_PHY_1875
#define LPDDR4__PHY_ADR_DISABLE__FLD LPDDR4__DENALI_PHY_1875__PHY_ADR_DISABLE

#define LPDDR4__DENALI_PHY_1876_READ_MASK                            0x03030303U
#define LPDDR4__DENALI_PHY_1876_WRITE_MASK                           0x03030303U
#define LPDDR4__DENALI_PHY_1876__PHY_ADRCTL_MSTR_DLY_ENC_SEL_0_MASK  0x00000003U
#define LPDDR4__DENALI_PHY_1876__PHY_ADRCTL_MSTR_DLY_ENC_SEL_0_SHIFT          0U
#define LPDDR4__DENALI_PHY_1876__PHY_ADRCTL_MSTR_DLY_ENC_SEL_0_WIDTH          2U
#define LPDDR4__PHY_ADRCTL_MSTR_DLY_ENC_SEL_0__REG DENALI_PHY_1876
#define LPDDR4__PHY_ADRCTL_MSTR_DLY_ENC_SEL_0__FLD LPDDR4__DENALI_PHY_1876__PHY_ADRCTL_MSTR_DLY_ENC_SEL_0

#define LPDDR4__DENALI_PHY_1876__PHY_ADRCTL_MSTR_DLY_ENC_SEL_1_MASK  0x00000300U
#define LPDDR4__DENALI_PHY_1876__PHY_ADRCTL_MSTR_DLY_ENC_SEL_1_SHIFT          8U
#define LPDDR4__DENALI_PHY_1876__PHY_ADRCTL_MSTR_DLY_ENC_SEL_1_WIDTH          2U
#define LPDDR4__PHY_ADRCTL_MSTR_DLY_ENC_SEL_1__REG DENALI_PHY_1876
#define LPDDR4__PHY_ADRCTL_MSTR_DLY_ENC_SEL_1__FLD LPDDR4__DENALI_PHY_1876__PHY_ADRCTL_MSTR_DLY_ENC_SEL_1

#define LPDDR4__DENALI_PHY_1876__PHY_ADRCTL_MSTR_DLY_ENC_SEL_2_MASK  0x00030000U
#define LPDDR4__DENALI_PHY_1876__PHY_ADRCTL_MSTR_DLY_ENC_SEL_2_SHIFT         16U
#define LPDDR4__DENALI_PHY_1876__PHY_ADRCTL_MSTR_DLY_ENC_SEL_2_WIDTH          2U
#define LPDDR4__PHY_ADRCTL_MSTR_DLY_ENC_SEL_2__REG DENALI_PHY_1876
#define LPDDR4__PHY_ADRCTL_MSTR_DLY_ENC_SEL_2__FLD LPDDR4__DENALI_PHY_1876__PHY_ADRCTL_MSTR_DLY_ENC_SEL_2

#define LPDDR4__DENALI_PHY_1876__PHY_ADRCTL_MSTR_DLY_ENC_SEL_3_MASK  0x03000000U
#define LPDDR4__DENALI_PHY_1876__PHY_ADRCTL_MSTR_DLY_ENC_SEL_3_SHIFT         24U
#define LPDDR4__DENALI_PHY_1876__PHY_ADRCTL_MSTR_DLY_ENC_SEL_3_WIDTH          2U
#define LPDDR4__PHY_ADRCTL_MSTR_DLY_ENC_SEL_3__REG DENALI_PHY_1876
#define LPDDR4__PHY_ADRCTL_MSTR_DLY_ENC_SEL_3__FLD LPDDR4__DENALI_PHY_1876__PHY_ADRCTL_MSTR_DLY_ENC_SEL_3

#define LPDDR4__DENALI_PHY_1877_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1877_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1877__PHY_DDL_AC_ENABLE_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1877__PHY_DDL_AC_ENABLE_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1877__PHY_DDL_AC_ENABLE_WIDTH                     32U
#define LPDDR4__PHY_DDL_AC_ENABLE__REG DENALI_PHY_1877
#define LPDDR4__PHY_DDL_AC_ENABLE__FLD LPDDR4__DENALI_PHY_1877__PHY_DDL_AC_ENABLE

#define LPDDR4__DENALI_PHY_1878_READ_MASK                            0x03FFFFFFU
#define LPDDR4__DENALI_PHY_1878_WRITE_MASK                           0x03FFFFFFU
#define LPDDR4__DENALI_PHY_1878__PHY_DDL_AC_MODE_MASK                0x03FFFFFFU
#define LPDDR4__DENALI_PHY_1878__PHY_DDL_AC_MODE_SHIFT                        0U
#define LPDDR4__DENALI_PHY_1878__PHY_DDL_AC_MODE_WIDTH                       26U
#define LPDDR4__PHY_DDL_AC_MODE__REG DENALI_PHY_1878
#define LPDDR4__PHY_DDL_AC_MODE__FLD LPDDR4__DENALI_PHY_1878__PHY_DDL_AC_MODE

#define LPDDR4__DENALI_PHY_1879_READ_MASK                            0x00FF073FU
#define LPDDR4__DENALI_PHY_1879_WRITE_MASK                           0x00FF073FU
#define LPDDR4__DENALI_PHY_1879__PHY_DDL_AC_MASK_MASK                0x0000003FU
#define LPDDR4__DENALI_PHY_1879__PHY_DDL_AC_MASK_SHIFT                        0U
#define LPDDR4__DENALI_PHY_1879__PHY_DDL_AC_MASK_WIDTH                        6U
#define LPDDR4__PHY_DDL_AC_MASK__REG DENALI_PHY_1879
#define LPDDR4__PHY_DDL_AC_MASK__FLD LPDDR4__DENALI_PHY_1879__PHY_DDL_AC_MASK

#define LPDDR4__DENALI_PHY_1879__PHY_INIT_UPDATE_CONFIG_MASK         0x00000700U
#define LPDDR4__DENALI_PHY_1879__PHY_INIT_UPDATE_CONFIG_SHIFT                 8U
#define LPDDR4__DENALI_PHY_1879__PHY_INIT_UPDATE_CONFIG_WIDTH                 3U
#define LPDDR4__PHY_INIT_UPDATE_CONFIG__REG DENALI_PHY_1879
#define LPDDR4__PHY_INIT_UPDATE_CONFIG__FLD LPDDR4__DENALI_PHY_1879__PHY_INIT_UPDATE_CONFIG

#define LPDDR4__DENALI_PHY_1879__PHY_DDL_TRACK_UPD_THRESHOLD_AC_MASK 0x00FF0000U
#define LPDDR4__DENALI_PHY_1879__PHY_DDL_TRACK_UPD_THRESHOLD_AC_SHIFT        16U
#define LPDDR4__DENALI_PHY_1879__PHY_DDL_TRACK_UPD_THRESHOLD_AC_WIDTH         8U
#define LPDDR4__PHY_DDL_TRACK_UPD_THRESHOLD_AC__REG DENALI_PHY_1879
#define LPDDR4__PHY_DDL_TRACK_UPD_THRESHOLD_AC__FLD LPDDR4__DENALI_PHY_1879__PHY_DDL_TRACK_UPD_THRESHOLD_AC

#define LPDDR4__DENALI_PHY_1880_READ_MASK                            0x0707FFFFU
#define LPDDR4__DENALI_PHY_1880_WRITE_MASK                           0x0707FFFFU
#define LPDDR4__DENALI_PHY_1880__PHY_CA_PARITY_ERR_PULSE_MIN_MASK    0x0000FFFFU
#define LPDDR4__DENALI_PHY_1880__PHY_CA_PARITY_ERR_PULSE_MIN_SHIFT            0U
#define LPDDR4__DENALI_PHY_1880__PHY_CA_PARITY_ERR_PULSE_MIN_WIDTH           16U
#define LPDDR4__PHY_CA_PARITY_ERR_PULSE_MIN__REG DENALI_PHY_1880
#define LPDDR4__PHY_CA_PARITY_ERR_PULSE_MIN__FLD LPDDR4__DENALI_PHY_1880__PHY_CA_PARITY_ERR_PULSE_MIN

#define LPDDR4__DENALI_PHY_1880__PHY_ERR_MASK_EN_MASK                0x00070000U
#define LPDDR4__DENALI_PHY_1880__PHY_ERR_MASK_EN_SHIFT                       16U
#define LPDDR4__DENALI_PHY_1880__PHY_ERR_MASK_EN_WIDTH                        3U
#define LPDDR4__PHY_ERR_MASK_EN__REG DENALI_PHY_1880
#define LPDDR4__PHY_ERR_MASK_EN__FLD LPDDR4__DENALI_PHY_1880__PHY_ERR_MASK_EN

#define LPDDR4__DENALI_PHY_1880__PHY_ERR_STATUS_MASK                 0x07000000U
#define LPDDR4__DENALI_PHY_1880__PHY_ERR_STATUS_SHIFT                        24U
#define LPDDR4__DENALI_PHY_1880__PHY_ERR_STATUS_WIDTH                         3U
#define LPDDR4__PHY_ERR_STATUS__REG DENALI_PHY_1880
#define LPDDR4__PHY_ERR_STATUS__FLD LPDDR4__DENALI_PHY_1880__PHY_ERR_STATUS

#define LPDDR4__DENALI_PHY_1881_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1881_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1881__PHY_DS0_DQS_ERR_COUNTER_MASK        0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1881__PHY_DS0_DQS_ERR_COUNTER_SHIFT                0U
#define LPDDR4__DENALI_PHY_1881__PHY_DS0_DQS_ERR_COUNTER_WIDTH               32U
#define LPDDR4__PHY_DS0_DQS_ERR_COUNTER__REG DENALI_PHY_1881
#define LPDDR4__PHY_DS0_DQS_ERR_COUNTER__FLD LPDDR4__DENALI_PHY_1881__PHY_DS0_DQS_ERR_COUNTER

#define LPDDR4__DENALI_PHY_1882_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1882_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1882__PHY_DS1_DQS_ERR_COUNTER_MASK        0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1882__PHY_DS1_DQS_ERR_COUNTER_SHIFT                0U
#define LPDDR4__DENALI_PHY_1882__PHY_DS1_DQS_ERR_COUNTER_WIDTH               32U
#define LPDDR4__PHY_DS1_DQS_ERR_COUNTER__REG DENALI_PHY_1882
#define LPDDR4__PHY_DS1_DQS_ERR_COUNTER__FLD LPDDR4__DENALI_PHY_1882__PHY_DS1_DQS_ERR_COUNTER

#define LPDDR4__DENALI_PHY_1883_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1883_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1883__PHY_DS2_DQS_ERR_COUNTER_MASK        0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1883__PHY_DS2_DQS_ERR_COUNTER_SHIFT                0U
#define LPDDR4__DENALI_PHY_1883__PHY_DS2_DQS_ERR_COUNTER_WIDTH               32U
#define LPDDR4__PHY_DS2_DQS_ERR_COUNTER__REG DENALI_PHY_1883
#define LPDDR4__PHY_DS2_DQS_ERR_COUNTER__FLD LPDDR4__DENALI_PHY_1883__PHY_DS2_DQS_ERR_COUNTER

#define LPDDR4__DENALI_PHY_1884_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1884_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1884__PHY_DS3_DQS_ERR_COUNTER_MASK        0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1884__PHY_DS3_DQS_ERR_COUNTER_SHIFT                0U
#define LPDDR4__DENALI_PHY_1884__PHY_DS3_DQS_ERR_COUNTER_WIDTH               32U
#define LPDDR4__PHY_DS3_DQS_ERR_COUNTER__REG DENALI_PHY_1884
#define LPDDR4__PHY_DS3_DQS_ERR_COUNTER__FLD LPDDR4__DENALI_PHY_1884__PHY_DS3_DQS_ERR_COUNTER

#define LPDDR4__DENALI_PHY_1885_READ_MASK                            0x0F0FFF03U
#define LPDDR4__DENALI_PHY_1885_WRITE_MASK                           0x0F0FFF03U
#define LPDDR4__DENALI_PHY_1885__PHY_DLL_RST_EN_MASK                 0x00000003U
#define LPDDR4__DENALI_PHY_1885__PHY_DLL_RST_EN_SHIFT                         0U
#define LPDDR4__DENALI_PHY_1885__PHY_DLL_RST_EN_WIDTH                         2U
#define LPDDR4__PHY_DLL_RST_EN__REG DENALI_PHY_1885
#define LPDDR4__PHY_DLL_RST_EN__FLD LPDDR4__DENALI_PHY_1885__PHY_DLL_RST_EN

#define LPDDR4__DENALI_PHY_1885__PHY_AC_INIT_COMPLETE_OBS_MASK       0x000FFF00U
#define LPDDR4__DENALI_PHY_1885__PHY_AC_INIT_COMPLETE_OBS_SHIFT               8U
#define LPDDR4__DENALI_PHY_1885__PHY_AC_INIT_COMPLETE_OBS_WIDTH              12U
#define LPDDR4__PHY_AC_INIT_COMPLETE_OBS__REG DENALI_PHY_1885
#define LPDDR4__PHY_AC_INIT_COMPLETE_OBS__FLD LPDDR4__DENALI_PHY_1885__PHY_AC_INIT_COMPLETE_OBS

#define LPDDR4__DENALI_PHY_1885__PHY_DS_INIT_COMPLETE_OBS_MASK       0x0F000000U
#define LPDDR4__DENALI_PHY_1885__PHY_DS_INIT_COMPLETE_OBS_SHIFT              24U
#define LPDDR4__DENALI_PHY_1885__PHY_DS_INIT_COMPLETE_OBS_WIDTH               4U
#define LPDDR4__PHY_DS_INIT_COMPLETE_OBS__REG DENALI_PHY_1885
#define LPDDR4__PHY_DS_INIT_COMPLETE_OBS__FLD LPDDR4__DENALI_PHY_1885__PHY_DS_INIT_COMPLETE_OBS

#define LPDDR4__DENALI_PHY_1886_READ_MASK                            0x1F010101U
#define LPDDR4__DENALI_PHY_1886_WRITE_MASK                           0x1F010101U
#define LPDDR4__DENALI_PHY_1886__PHY_UPDATE_MASK_MASK                0x00000001U
#define LPDDR4__DENALI_PHY_1886__PHY_UPDATE_MASK_SHIFT                        0U
#define LPDDR4__DENALI_PHY_1886__PHY_UPDATE_MASK_WIDTH                        1U
#define LPDDR4__DENALI_PHY_1886__PHY_UPDATE_MASK_WOCLR                        0U
#define LPDDR4__DENALI_PHY_1886__PHY_UPDATE_MASK_WOSET                        0U
#define LPDDR4__PHY_UPDATE_MASK__REG DENALI_PHY_1886
#define LPDDR4__PHY_UPDATE_MASK__FLD LPDDR4__DENALI_PHY_1886__PHY_UPDATE_MASK

#define LPDDR4__DENALI_PHY_1886__PHY_ERR_IE_MASK                     0x00000100U
#define LPDDR4__DENALI_PHY_1886__PHY_ERR_IE_SHIFT                             8U
#define LPDDR4__DENALI_PHY_1886__PHY_ERR_IE_WIDTH                             1U
#define LPDDR4__DENALI_PHY_1886__PHY_ERR_IE_WOCLR                             0U
#define LPDDR4__DENALI_PHY_1886__PHY_ERR_IE_WOSET                             0U
#define LPDDR4__PHY_ERR_IE__REG DENALI_PHY_1886
#define LPDDR4__PHY_ERR_IE__FLD LPDDR4__DENALI_PHY_1886__PHY_ERR_IE

#define LPDDR4__DENALI_PHY_1886__PHY_AC_DCC_RXCAL_CTRL_GATE_DISABLE_MASK 0x00010000U
#define LPDDR4__DENALI_PHY_1886__PHY_AC_DCC_RXCAL_CTRL_GATE_DISABLE_SHIFT    16U
#define LPDDR4__DENALI_PHY_1886__PHY_AC_DCC_RXCAL_CTRL_GATE_DISABLE_WIDTH     1U
#define LPDDR4__DENALI_PHY_1886__PHY_AC_DCC_RXCAL_CTRL_GATE_DISABLE_WOCLR     0U
#define LPDDR4__DENALI_PHY_1886__PHY_AC_DCC_RXCAL_CTRL_GATE_DISABLE_WOSET     0U
#define LPDDR4__PHY_AC_DCC_RXCAL_CTRL_GATE_DISABLE__REG DENALI_PHY_1886
#define LPDDR4__PHY_AC_DCC_RXCAL_CTRL_GATE_DISABLE__FLD LPDDR4__DENALI_PHY_1886__PHY_AC_DCC_RXCAL_CTRL_GATE_DISABLE

#define LPDDR4__DENALI_PHY_1886__PHY_GRP_SLV_DLY_ENC_OBS_SELECT_MASK 0x1F000000U
#define LPDDR4__DENALI_PHY_1886__PHY_GRP_SLV_DLY_ENC_OBS_SELECT_SHIFT        24U
#define LPDDR4__DENALI_PHY_1886__PHY_GRP_SLV_DLY_ENC_OBS_SELECT_WIDTH         5U
#define LPDDR4__PHY_GRP_SLV_DLY_ENC_OBS_SELECT__REG DENALI_PHY_1886
#define LPDDR4__PHY_GRP_SLV_DLY_ENC_OBS_SELECT__FLD LPDDR4__DENALI_PHY_1886__PHY_GRP_SLV_DLY_ENC_OBS_SELECT

#define LPDDR4__DENALI_PHY_1887_READ_MASK                            0x0707FF0FU
#define LPDDR4__DENALI_PHY_1887_WRITE_MASK                           0x0707FF0FU
#define LPDDR4__DENALI_PHY_1887__PHY_GRP_SHIFT_OBS_SELECT_MASK       0x0000000FU
#define LPDDR4__DENALI_PHY_1887__PHY_GRP_SHIFT_OBS_SELECT_SHIFT               0U
#define LPDDR4__DENALI_PHY_1887__PHY_GRP_SHIFT_OBS_SELECT_WIDTH               4U
#define LPDDR4__PHY_GRP_SHIFT_OBS_SELECT__REG DENALI_PHY_1887
#define LPDDR4__PHY_GRP_SHIFT_OBS_SELECT__FLD LPDDR4__DENALI_PHY_1887__PHY_GRP_SHIFT_OBS_SELECT

#define LPDDR4__DENALI_PHY_1887__PHY_GRP_SLV_DLY_ENC_OBS_MASK        0x0007FF00U
#define LPDDR4__DENALI_PHY_1887__PHY_GRP_SLV_DLY_ENC_OBS_SHIFT                8U
#define LPDDR4__DENALI_PHY_1887__PHY_GRP_SLV_DLY_ENC_OBS_WIDTH               11U
#define LPDDR4__PHY_GRP_SLV_DLY_ENC_OBS__REG DENALI_PHY_1887
#define LPDDR4__PHY_GRP_SLV_DLY_ENC_OBS__FLD LPDDR4__DENALI_PHY_1887__PHY_GRP_SLV_DLY_ENC_OBS

#define LPDDR4__DENALI_PHY_1887__PHY_GRP_SHIFT_OBS_MASK              0x07000000U
#define LPDDR4__DENALI_PHY_1887__PHY_GRP_SHIFT_OBS_SHIFT                     24U
#define LPDDR4__DENALI_PHY_1887__PHY_GRP_SHIFT_OBS_WIDTH                      3U
#define LPDDR4__PHY_GRP_SHIFT_OBS__REG DENALI_PHY_1887
#define LPDDR4__PHY_GRP_SHIFT_OBS__FLD LPDDR4__DENALI_PHY_1887__PHY_GRP_SHIFT_OBS

#define LPDDR4__DENALI_PHY_1888_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1888_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1888__PHY_PAD_CAL_IO_CFG_0_MASK           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1888__PHY_PAD_CAL_IO_CFG_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1888__PHY_PAD_CAL_IO_CFG_0_WIDTH                  18U
#define LPDDR4__PHY_PAD_CAL_IO_CFG_0__REG DENALI_PHY_1888
#define LPDDR4__PHY_PAD_CAL_IO_CFG_0__FLD LPDDR4__DENALI_PHY_1888__PHY_PAD_CAL_IO_CFG_0

#define LPDDR4__DENALI_PHY_1889_READ_MASK                            0x0703FFFFU
#define LPDDR4__DENALI_PHY_1889_WRITE_MASK                           0x0703FFFFU
#define LPDDR4__DENALI_PHY_1889__PHY_PAD_ACS_IO_CFG_MASK             0x0003FFFFU
#define LPDDR4__DENALI_PHY_1889__PHY_PAD_ACS_IO_CFG_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1889__PHY_PAD_ACS_IO_CFG_WIDTH                    18U
#define LPDDR4__PHY_PAD_ACS_IO_CFG__REG DENALI_PHY_1889
#define LPDDR4__PHY_PAD_ACS_IO_CFG__FLD LPDDR4__DENALI_PHY_1889__PHY_PAD_ACS_IO_CFG

#define LPDDR4__DENALI_PHY_1889__PHY_PAD_ACS_RX_PCLK_CLK_SEL_MASK    0x07000000U
#define LPDDR4__DENALI_PHY_1889__PHY_PAD_ACS_RX_PCLK_CLK_SEL_SHIFT           24U
#define LPDDR4__DENALI_PHY_1889__PHY_PAD_ACS_RX_PCLK_CLK_SEL_WIDTH            3U
#define LPDDR4__PHY_PAD_ACS_RX_PCLK_CLK_SEL__REG DENALI_PHY_1889
#define LPDDR4__PHY_PAD_ACS_RX_PCLK_CLK_SEL__FLD LPDDR4__DENALI_PHY_1889__PHY_PAD_ACS_RX_PCLK_CLK_SEL

#define LPDDR4__DENALI_PHY_1890_READ_MASK                            0x00000001U
#define LPDDR4__DENALI_PHY_1890_WRITE_MASK                           0x00000001U
#define LPDDR4__DENALI_PHY_1890__PHY_PLL_BYPASS_MASK                 0x00000001U
#define LPDDR4__DENALI_PHY_1890__PHY_PLL_BYPASS_SHIFT                         0U
#define LPDDR4__DENALI_PHY_1890__PHY_PLL_BYPASS_WIDTH                         1U
#define LPDDR4__DENALI_PHY_1890__PHY_PLL_BYPASS_WOCLR                         0U
#define LPDDR4__DENALI_PHY_1890__PHY_PLL_BYPASS_WOSET                         0U
#define LPDDR4__PHY_PLL_BYPASS__REG DENALI_PHY_1890
#define LPDDR4__PHY_PLL_BYPASS__FLD LPDDR4__DENALI_PHY_1890__PHY_PLL_BYPASS

#define LPDDR4__DENALI_PHY_1891_READ_MASK                            0x00011FFFU
#define LPDDR4__DENALI_PHY_1891_WRITE_MASK                           0x00011FFFU
#define LPDDR4__DENALI_PHY_1891__PHY_PLL_CTRL_MASK                   0x00001FFFU
#define LPDDR4__DENALI_PHY_1891__PHY_PLL_CTRL_SHIFT                           0U
#define LPDDR4__DENALI_PHY_1891__PHY_PLL_CTRL_WIDTH                          13U
#define LPDDR4__PHY_PLL_CTRL__REG DENALI_PHY_1891
#define LPDDR4__PHY_PLL_CTRL__FLD LPDDR4__DENALI_PHY_1891__PHY_PLL_CTRL

#define LPDDR4__DENALI_PHY_1891__PHY_LOW_FREQ_SEL_MASK               0x00010000U
#define LPDDR4__DENALI_PHY_1891__PHY_LOW_FREQ_SEL_SHIFT                      16U
#define LPDDR4__DENALI_PHY_1891__PHY_LOW_FREQ_SEL_WIDTH                       1U
#define LPDDR4__DENALI_PHY_1891__PHY_LOW_FREQ_SEL_WOCLR                       0U
#define LPDDR4__DENALI_PHY_1891__PHY_LOW_FREQ_SEL_WOSET                       0U
#define LPDDR4__PHY_LOW_FREQ_SEL__REG DENALI_PHY_1891
#define LPDDR4__PHY_LOW_FREQ_SEL__FLD LPDDR4__DENALI_PHY_1891__PHY_LOW_FREQ_SEL

#define LPDDR4__DENALI_PHY_1892_READ_MASK                            0x0F0F0FFFU
#define LPDDR4__DENALI_PHY_1892_WRITE_MASK                           0x0F0F0FFFU
#define LPDDR4__DENALI_PHY_1892__PHY_PAD_VREF_CTRL_AC_MASK           0x00000FFFU
#define LPDDR4__DENALI_PHY_1892__PHY_PAD_VREF_CTRL_AC_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1892__PHY_PAD_VREF_CTRL_AC_WIDTH                  12U
#define LPDDR4__PHY_PAD_VREF_CTRL_AC__REG DENALI_PHY_1892
#define LPDDR4__PHY_PAD_VREF_CTRL_AC__FLD LPDDR4__DENALI_PHY_1892__PHY_PAD_VREF_CTRL_AC

#define LPDDR4__DENALI_PHY_1892__PHY_CSLVL_CAPTURE_CNT_MASK          0x000F0000U
#define LPDDR4__DENALI_PHY_1892__PHY_CSLVL_CAPTURE_CNT_SHIFT                 16U
#define LPDDR4__DENALI_PHY_1892__PHY_CSLVL_CAPTURE_CNT_WIDTH                  4U
#define LPDDR4__PHY_CSLVL_CAPTURE_CNT__REG DENALI_PHY_1892
#define LPDDR4__PHY_CSLVL_CAPTURE_CNT__FLD LPDDR4__DENALI_PHY_1892__PHY_CSLVL_CAPTURE_CNT

#define LPDDR4__DENALI_PHY_1892__PHY_CSLVL_DLY_STEP_MASK             0x0F000000U
#define LPDDR4__DENALI_PHY_1892__PHY_CSLVL_DLY_STEP_SHIFT                    24U
#define LPDDR4__DENALI_PHY_1892__PHY_CSLVL_DLY_STEP_WIDTH                     4U
#define LPDDR4__PHY_CSLVL_DLY_STEP__REG DENALI_PHY_1892
#define LPDDR4__PHY_CSLVL_DLY_STEP__FLD LPDDR4__DENALI_PHY_1892__PHY_CSLVL_DLY_STEP

#define LPDDR4__DENALI_PHY_1893_READ_MASK                            0x010103FFU
#define LPDDR4__DENALI_PHY_1893_WRITE_MASK                           0x010103FFU
#define LPDDR4__DENALI_PHY_1893__PHY_SW_CSLVL_DVW_MIN_MASK           0x000003FFU
#define LPDDR4__DENALI_PHY_1893__PHY_SW_CSLVL_DVW_MIN_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1893__PHY_SW_CSLVL_DVW_MIN_WIDTH                  10U
#define LPDDR4__PHY_SW_CSLVL_DVW_MIN__REG DENALI_PHY_1893
#define LPDDR4__PHY_SW_CSLVL_DVW_MIN__FLD LPDDR4__DENALI_PHY_1893__PHY_SW_CSLVL_DVW_MIN

#define LPDDR4__DENALI_PHY_1893__PHY_SW_CSLVL_DVW_MIN_EN_MASK        0x00010000U
#define LPDDR4__DENALI_PHY_1893__PHY_SW_CSLVL_DVW_MIN_EN_SHIFT               16U
#define LPDDR4__DENALI_PHY_1893__PHY_SW_CSLVL_DVW_MIN_EN_WIDTH                1U
#define LPDDR4__DENALI_PHY_1893__PHY_SW_CSLVL_DVW_MIN_EN_WOCLR                0U
#define LPDDR4__DENALI_PHY_1893__PHY_SW_CSLVL_DVW_MIN_EN_WOSET                0U
#define LPDDR4__PHY_SW_CSLVL_DVW_MIN_EN__REG DENALI_PHY_1893
#define LPDDR4__PHY_SW_CSLVL_DVW_MIN_EN__FLD LPDDR4__DENALI_PHY_1893__PHY_SW_CSLVL_DVW_MIN_EN

#define LPDDR4__DENALI_PHY_1893__PHY_LVL_MEAS_DLY_STEP_ENABLE_MASK   0x01000000U
#define LPDDR4__DENALI_PHY_1893__PHY_LVL_MEAS_DLY_STEP_ENABLE_SHIFT          24U
#define LPDDR4__DENALI_PHY_1893__PHY_LVL_MEAS_DLY_STEP_ENABLE_WIDTH           1U
#define LPDDR4__DENALI_PHY_1893__PHY_LVL_MEAS_DLY_STEP_ENABLE_WOCLR           0U
#define LPDDR4__DENALI_PHY_1893__PHY_LVL_MEAS_DLY_STEP_ENABLE_WOSET           0U
#define LPDDR4__PHY_LVL_MEAS_DLY_STEP_ENABLE__REG DENALI_PHY_1893
#define LPDDR4__PHY_LVL_MEAS_DLY_STEP_ENABLE__FLD LPDDR4__DENALI_PHY_1893__PHY_LVL_MEAS_DLY_STEP_ENABLE

#define LPDDR4__DENALI_PHY_1894_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1894_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1894__PHY_GRP0_SLAVE_DELAY_0_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1894__PHY_GRP0_SLAVE_DELAY_0_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1894__PHY_GRP0_SLAVE_DELAY_0_WIDTH                11U
#define LPDDR4__PHY_GRP0_SLAVE_DELAY_0__REG DENALI_PHY_1894
#define LPDDR4__PHY_GRP0_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_1894__PHY_GRP0_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_1894__PHY_GRP1_SLAVE_DELAY_0_MASK         0x07FF0000U
#define LPDDR4__DENALI_PHY_1894__PHY_GRP1_SLAVE_DELAY_0_SHIFT                16U
#define LPDDR4__DENALI_PHY_1894__PHY_GRP1_SLAVE_DELAY_0_WIDTH                11U
#define LPDDR4__PHY_GRP1_SLAVE_DELAY_0__REG DENALI_PHY_1894
#define LPDDR4__PHY_GRP1_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_1894__PHY_GRP1_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_1895_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1895_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1895__PHY_GRP2_SLAVE_DELAY_0_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1895__PHY_GRP2_SLAVE_DELAY_0_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1895__PHY_GRP2_SLAVE_DELAY_0_WIDTH                11U
#define LPDDR4__PHY_GRP2_SLAVE_DELAY_0__REG DENALI_PHY_1895
#define LPDDR4__PHY_GRP2_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_1895__PHY_GRP2_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_1895__PHY_GRP3_SLAVE_DELAY_0_MASK         0x07FF0000U
#define LPDDR4__DENALI_PHY_1895__PHY_GRP3_SLAVE_DELAY_0_SHIFT                16U
#define LPDDR4__DENALI_PHY_1895__PHY_GRP3_SLAVE_DELAY_0_WIDTH                11U
#define LPDDR4__PHY_GRP3_SLAVE_DELAY_0__REG DENALI_PHY_1895
#define LPDDR4__PHY_GRP3_SLAVE_DELAY_0__FLD LPDDR4__DENALI_PHY_1895__PHY_GRP3_SLAVE_DELAY_0

#define LPDDR4__DENALI_PHY_1896_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1896_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1896__PHY_GRP0_SLAVE_DELAY_1_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1896__PHY_GRP0_SLAVE_DELAY_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1896__PHY_GRP0_SLAVE_DELAY_1_WIDTH                11U
#define LPDDR4__PHY_GRP0_SLAVE_DELAY_1__REG DENALI_PHY_1896
#define LPDDR4__PHY_GRP0_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_1896__PHY_GRP0_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_1896__PHY_GRP1_SLAVE_DELAY_1_MASK         0x07FF0000U
#define LPDDR4__DENALI_PHY_1896__PHY_GRP1_SLAVE_DELAY_1_SHIFT                16U
#define LPDDR4__DENALI_PHY_1896__PHY_GRP1_SLAVE_DELAY_1_WIDTH                11U
#define LPDDR4__PHY_GRP1_SLAVE_DELAY_1__REG DENALI_PHY_1896
#define LPDDR4__PHY_GRP1_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_1896__PHY_GRP1_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_1897_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1897_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1897__PHY_GRP2_SLAVE_DELAY_1_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1897__PHY_GRP2_SLAVE_DELAY_1_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1897__PHY_GRP2_SLAVE_DELAY_1_WIDTH                11U
#define LPDDR4__PHY_GRP2_SLAVE_DELAY_1__REG DENALI_PHY_1897
#define LPDDR4__PHY_GRP2_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_1897__PHY_GRP2_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_1897__PHY_GRP3_SLAVE_DELAY_1_MASK         0x07FF0000U
#define LPDDR4__DENALI_PHY_1897__PHY_GRP3_SLAVE_DELAY_1_SHIFT                16U
#define LPDDR4__DENALI_PHY_1897__PHY_GRP3_SLAVE_DELAY_1_WIDTH                11U
#define LPDDR4__PHY_GRP3_SLAVE_DELAY_1__REG DENALI_PHY_1897
#define LPDDR4__PHY_GRP3_SLAVE_DELAY_1__FLD LPDDR4__DENALI_PHY_1897__PHY_GRP3_SLAVE_DELAY_1

#define LPDDR4__DENALI_PHY_1898_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1898_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1898__PHY_GRP0_SLAVE_DELAY_2_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1898__PHY_GRP0_SLAVE_DELAY_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1898__PHY_GRP0_SLAVE_DELAY_2_WIDTH                11U
#define LPDDR4__PHY_GRP0_SLAVE_DELAY_2__REG DENALI_PHY_1898
#define LPDDR4__PHY_GRP0_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1898__PHY_GRP0_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1898__PHY_GRP1_SLAVE_DELAY_2_MASK         0x07FF0000U
#define LPDDR4__DENALI_PHY_1898__PHY_GRP1_SLAVE_DELAY_2_SHIFT                16U
#define LPDDR4__DENALI_PHY_1898__PHY_GRP1_SLAVE_DELAY_2_WIDTH                11U
#define LPDDR4__PHY_GRP1_SLAVE_DELAY_2__REG DENALI_PHY_1898
#define LPDDR4__PHY_GRP1_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1898__PHY_GRP1_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1899_READ_MASK                            0x07FF07FFU
#define LPDDR4__DENALI_PHY_1899_WRITE_MASK                           0x07FF07FFU
#define LPDDR4__DENALI_PHY_1899__PHY_GRP2_SLAVE_DELAY_2_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1899__PHY_GRP2_SLAVE_DELAY_2_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1899__PHY_GRP2_SLAVE_DELAY_2_WIDTH                11U
#define LPDDR4__PHY_GRP2_SLAVE_DELAY_2__REG DENALI_PHY_1899
#define LPDDR4__PHY_GRP2_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1899__PHY_GRP2_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1899__PHY_GRP3_SLAVE_DELAY_2_MASK         0x07FF0000U
#define LPDDR4__DENALI_PHY_1899__PHY_GRP3_SLAVE_DELAY_2_SHIFT                16U
#define LPDDR4__DENALI_PHY_1899__PHY_GRP3_SLAVE_DELAY_2_WIDTH                11U
#define LPDDR4__PHY_GRP3_SLAVE_DELAY_2__REG DENALI_PHY_1899
#define LPDDR4__PHY_GRP3_SLAVE_DELAY_2__FLD LPDDR4__DENALI_PHY_1899__PHY_GRP3_SLAVE_DELAY_2

#define LPDDR4__DENALI_PHY_1900_READ_MASK                            0x000007FFU
#define LPDDR4__DENALI_PHY_1900_WRITE_MASK                           0x000007FFU
#define LPDDR4__DENALI_PHY_1900__PHY_GRP0_SLAVE_DELAY_3_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1900__PHY_GRP0_SLAVE_DELAY_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1900__PHY_GRP0_SLAVE_DELAY_3_WIDTH                11U
#define LPDDR4__PHY_GRP0_SLAVE_DELAY_3__REG DENALI_PHY_1900
#define LPDDR4__PHY_GRP0_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_1900__PHY_GRP0_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_1901_READ_MASK                            0x000007FFU
#define LPDDR4__DENALI_PHY_1901_WRITE_MASK                           0x000007FFU
#define LPDDR4__DENALI_PHY_1901__PHY_GRP1_SLAVE_DELAY_3_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1901__PHY_GRP1_SLAVE_DELAY_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1901__PHY_GRP1_SLAVE_DELAY_3_WIDTH                11U
#define LPDDR4__PHY_GRP1_SLAVE_DELAY_3__REG DENALI_PHY_1901
#define LPDDR4__PHY_GRP1_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_1901__PHY_GRP1_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_1902_READ_MASK                            0x000007FFU
#define LPDDR4__DENALI_PHY_1902_WRITE_MASK                           0x000007FFU
#define LPDDR4__DENALI_PHY_1902__PHY_GRP2_SLAVE_DELAY_3_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1902__PHY_GRP2_SLAVE_DELAY_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1902__PHY_GRP2_SLAVE_DELAY_3_WIDTH                11U
#define LPDDR4__PHY_GRP2_SLAVE_DELAY_3__REG DENALI_PHY_1902
#define LPDDR4__PHY_GRP2_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_1902__PHY_GRP2_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_1903_READ_MASK                            0x000007FFU
#define LPDDR4__DENALI_PHY_1903_WRITE_MASK                           0x000007FFU
#define LPDDR4__DENALI_PHY_1903__PHY_GRP3_SLAVE_DELAY_3_MASK         0x000007FFU
#define LPDDR4__DENALI_PHY_1903__PHY_GRP3_SLAVE_DELAY_3_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1903__PHY_GRP3_SLAVE_DELAY_3_WIDTH                11U
#define LPDDR4__PHY_GRP3_SLAVE_DELAY_3__REG DENALI_PHY_1903
#define LPDDR4__PHY_GRP3_SLAVE_DELAY_3__FLD LPDDR4__DENALI_PHY_1903__PHY_GRP3_SLAVE_DELAY_3

#define LPDDR4__DENALI_PHY_1904_READ_MASK                            0x00000007U
#define LPDDR4__DENALI_PHY_1904_WRITE_MASK                           0x00000007U
#define LPDDR4__DENALI_PHY_1904__PHY_CLK_DC_CAL_CLK_SEL_MASK         0x00000007U
#define LPDDR4__DENALI_PHY_1904__PHY_CLK_DC_CAL_CLK_SEL_SHIFT                 0U
#define LPDDR4__DENALI_PHY_1904__PHY_CLK_DC_CAL_CLK_SEL_WIDTH                 3U
#define LPDDR4__PHY_CLK_DC_CAL_CLK_SEL__REG DENALI_PHY_1904
#define LPDDR4__PHY_CLK_DC_CAL_CLK_SEL__FLD LPDDR4__DENALI_PHY_1904__PHY_CLK_DC_CAL_CLK_SEL

#define LPDDR4__DENALI_PHY_1905_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1905_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1905__PHY_PAD_FDBK_DRIVE_MASK             0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1905__PHY_PAD_FDBK_DRIVE_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1905__PHY_PAD_FDBK_DRIVE_WIDTH                    30U
#define LPDDR4__PHY_PAD_FDBK_DRIVE__REG DENALI_PHY_1905
#define LPDDR4__PHY_PAD_FDBK_DRIVE__FLD LPDDR4__DENALI_PHY_1905__PHY_PAD_FDBK_DRIVE

#define LPDDR4__DENALI_PHY_1906_READ_MASK                            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1906_WRITE_MASK                           0x0003FFFFU
#define LPDDR4__DENALI_PHY_1906__PHY_PAD_FDBK_DRIVE2_MASK            0x0003FFFFU
#define LPDDR4__DENALI_PHY_1906__PHY_PAD_FDBK_DRIVE2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1906__PHY_PAD_FDBK_DRIVE2_WIDTH                   18U
#define LPDDR4__PHY_PAD_FDBK_DRIVE2__REG DENALI_PHY_1906
#define LPDDR4__PHY_PAD_FDBK_DRIVE2__FLD LPDDR4__DENALI_PHY_1906__PHY_PAD_FDBK_DRIVE2

#define LPDDR4__DENALI_PHY_1907_READ_MASK                            0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_1907_WRITE_MASK                           0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_1907__PHY_PAD_DATA_DRIVE_MASK             0x7FFFFFFFU
#define LPDDR4__DENALI_PHY_1907__PHY_PAD_DATA_DRIVE_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1907__PHY_PAD_DATA_DRIVE_WIDTH                    31U
#define LPDDR4__PHY_PAD_DATA_DRIVE__REG DENALI_PHY_1907
#define LPDDR4__PHY_PAD_DATA_DRIVE__FLD LPDDR4__DENALI_PHY_1907__PHY_PAD_DATA_DRIVE

#define LPDDR4__DENALI_PHY_1908_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1908_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1908__PHY_PAD_DQS_DRIVE_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1908__PHY_PAD_DQS_DRIVE_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1908__PHY_PAD_DQS_DRIVE_WIDTH                     32U
#define LPDDR4__PHY_PAD_DQS_DRIVE__REG DENALI_PHY_1908
#define LPDDR4__PHY_PAD_DQS_DRIVE__FLD LPDDR4__DENALI_PHY_1908__PHY_PAD_DQS_DRIVE

#define LPDDR4__DENALI_PHY_1909_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1909_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1909__PHY_PAD_ADDR_DRIVE_MASK             0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1909__PHY_PAD_ADDR_DRIVE_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1909__PHY_PAD_ADDR_DRIVE_WIDTH                    30U
#define LPDDR4__PHY_PAD_ADDR_DRIVE__REG DENALI_PHY_1909
#define LPDDR4__PHY_PAD_ADDR_DRIVE__FLD LPDDR4__DENALI_PHY_1909__PHY_PAD_ADDR_DRIVE

#define LPDDR4__DENALI_PHY_1910_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1910_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1910__PHY_PAD_ADDR_DRIVE2_MASK            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1910__PHY_PAD_ADDR_DRIVE2_SHIFT                    0U
#define LPDDR4__DENALI_PHY_1910__PHY_PAD_ADDR_DRIVE2_WIDTH                   28U
#define LPDDR4__PHY_PAD_ADDR_DRIVE2__REG DENALI_PHY_1910
#define LPDDR4__PHY_PAD_ADDR_DRIVE2__FLD LPDDR4__DENALI_PHY_1910__PHY_PAD_ADDR_DRIVE2

#define LPDDR4__DENALI_PHY_1911_READ_MASK                            0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1911_WRITE_MASK                           0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1911__PHY_PAD_CLK_DRIVE_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PHY_1911__PHY_PAD_CLK_DRIVE_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1911__PHY_PAD_CLK_DRIVE_WIDTH                     32U
#define LPDDR4__PHY_PAD_CLK_DRIVE__REG DENALI_PHY_1911
#define LPDDR4__PHY_PAD_CLK_DRIVE__FLD LPDDR4__DENALI_PHY_1911__PHY_PAD_CLK_DRIVE

#define LPDDR4__DENALI_PHY_1912_READ_MASK                            0x0007FFFFU
#define LPDDR4__DENALI_PHY_1912_WRITE_MASK                           0x0007FFFFU
#define LPDDR4__DENALI_PHY_1912__PHY_PAD_CLK_DRIVE2_MASK             0x0007FFFFU
#define LPDDR4__DENALI_PHY_1912__PHY_PAD_CLK_DRIVE2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1912__PHY_PAD_CLK_DRIVE2_WIDTH                    19U
#define LPDDR4__PHY_PAD_CLK_DRIVE2__REG DENALI_PHY_1912
#define LPDDR4__PHY_PAD_CLK_DRIVE2__FLD LPDDR4__DENALI_PHY_1912__PHY_PAD_CLK_DRIVE2

#define LPDDR4__DENALI_PHY_1913_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1913_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1913__PHY_PAD_ERR_DRIVE_MASK              0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1913__PHY_PAD_ERR_DRIVE_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1913__PHY_PAD_ERR_DRIVE_WIDTH                     30U
#define LPDDR4__PHY_PAD_ERR_DRIVE__REG DENALI_PHY_1913
#define LPDDR4__PHY_PAD_ERR_DRIVE__FLD LPDDR4__DENALI_PHY_1913__PHY_PAD_ERR_DRIVE

#define LPDDR4__DENALI_PHY_1914_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1914_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1914__PHY_PAD_ERR_DRIVE2_MASK             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1914__PHY_PAD_ERR_DRIVE2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1914__PHY_PAD_ERR_DRIVE2_WIDTH                    28U
#define LPDDR4__PHY_PAD_ERR_DRIVE2__REG DENALI_PHY_1914
#define LPDDR4__PHY_PAD_ERR_DRIVE2__FLD LPDDR4__DENALI_PHY_1914__PHY_PAD_ERR_DRIVE2

#define LPDDR4__DENALI_PHY_1915_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1915_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1915__PHY_PAD_CKE_DRIVE_MASK              0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1915__PHY_PAD_CKE_DRIVE_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1915__PHY_PAD_CKE_DRIVE_WIDTH                     30U
#define LPDDR4__PHY_PAD_CKE_DRIVE__REG DENALI_PHY_1915
#define LPDDR4__PHY_PAD_CKE_DRIVE__FLD LPDDR4__DENALI_PHY_1915__PHY_PAD_CKE_DRIVE

#define LPDDR4__DENALI_PHY_1916_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1916_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1916__PHY_PAD_CKE_DRIVE2_MASK             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1916__PHY_PAD_CKE_DRIVE2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1916__PHY_PAD_CKE_DRIVE2_WIDTH                    28U
#define LPDDR4__PHY_PAD_CKE_DRIVE2__REG DENALI_PHY_1916
#define LPDDR4__PHY_PAD_CKE_DRIVE2__FLD LPDDR4__DENALI_PHY_1916__PHY_PAD_CKE_DRIVE2

#define LPDDR4__DENALI_PHY_1917_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1917_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1917__PHY_PAD_RST_DRIVE_MASK              0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1917__PHY_PAD_RST_DRIVE_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1917__PHY_PAD_RST_DRIVE_WIDTH                     30U
#define LPDDR4__PHY_PAD_RST_DRIVE__REG DENALI_PHY_1917
#define LPDDR4__PHY_PAD_RST_DRIVE__FLD LPDDR4__DENALI_PHY_1917__PHY_PAD_RST_DRIVE

#define LPDDR4__DENALI_PHY_1918_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1918_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1918__PHY_PAD_RST_DRIVE2_MASK             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1918__PHY_PAD_RST_DRIVE2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1918__PHY_PAD_RST_DRIVE2_WIDTH                    28U
#define LPDDR4__PHY_PAD_RST_DRIVE2__REG DENALI_PHY_1918
#define LPDDR4__PHY_PAD_RST_DRIVE2__FLD LPDDR4__DENALI_PHY_1918__PHY_PAD_RST_DRIVE2

#define LPDDR4__DENALI_PHY_1919_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1919_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1919__PHY_PAD_CS_DRIVE_MASK               0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1919__PHY_PAD_CS_DRIVE_SHIFT                       0U
#define LPDDR4__DENALI_PHY_1919__PHY_PAD_CS_DRIVE_WIDTH                      30U
#define LPDDR4__PHY_PAD_CS_DRIVE__REG DENALI_PHY_1919
#define LPDDR4__PHY_PAD_CS_DRIVE__FLD LPDDR4__DENALI_PHY_1919__PHY_PAD_CS_DRIVE

#define LPDDR4__DENALI_PHY_1920_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1920_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1920__PHY_PAD_CS_DRIVE2_MASK              0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1920__PHY_PAD_CS_DRIVE2_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1920__PHY_PAD_CS_DRIVE2_WIDTH                     28U
#define LPDDR4__PHY_PAD_CS_DRIVE2__REG DENALI_PHY_1920
#define LPDDR4__PHY_PAD_CS_DRIVE2__FLD LPDDR4__DENALI_PHY_1920__PHY_PAD_CS_DRIVE2

#define LPDDR4__DENALI_PHY_1921_READ_MASK                            0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1921_WRITE_MASK                           0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1921__PHY_PAD_ODT_DRIVE_MASK              0x3FFFFFFFU
#define LPDDR4__DENALI_PHY_1921__PHY_PAD_ODT_DRIVE_SHIFT                      0U
#define LPDDR4__DENALI_PHY_1921__PHY_PAD_ODT_DRIVE_WIDTH                     30U
#define LPDDR4__PHY_PAD_ODT_DRIVE__REG DENALI_PHY_1921
#define LPDDR4__PHY_PAD_ODT_DRIVE__FLD LPDDR4__DENALI_PHY_1921__PHY_PAD_ODT_DRIVE

#define LPDDR4__DENALI_PHY_1922_READ_MASK                            0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1922_WRITE_MASK                           0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1922__PHY_PAD_ODT_DRIVE2_MASK             0x0FFFFFFFU
#define LPDDR4__DENALI_PHY_1922__PHY_PAD_ODT_DRIVE2_SHIFT                     0U
#define LPDDR4__DENALI_PHY_1922__PHY_PAD_ODT_DRIVE2_WIDTH                    28U
#define LPDDR4__PHY_PAD_ODT_DRIVE2__REG DENALI_PHY_1922
#define LPDDR4__PHY_PAD_ODT_DRIVE2__FLD LPDDR4__DENALI_PHY_1922__PHY_PAD_ODT_DRIVE2

#define LPDDR4__DENALI_PHY_1923_READ_MASK                            0x7FFFFF07U
#define LPDDR4__DENALI_PHY_1923_WRITE_MASK                           0x7FFFFF07U
#define LPDDR4__DENALI_PHY_1923__PHY_CAL_CLK_SELECT_0_MASK           0x00000007U
#define LPDDR4__DENALI_PHY_1923__PHY_CAL_CLK_SELECT_0_SHIFT                   0U
#define LPDDR4__DENALI_PHY_1923__PHY_CAL_CLK_SELECT_0_WIDTH                   3U
#define LPDDR4__PHY_CAL_CLK_SELECT_0__REG DENALI_PHY_1923
#define LPDDR4__PHY_CAL_CLK_SELECT_0__FLD LPDDR4__DENALI_PHY_1923__PHY_CAL_CLK_SELECT_0

#define LPDDR4__DENALI_PHY_1923__PHY_CAL_VREF_SWITCH_TIMER_0_MASK    0x00FFFF00U
#define LPDDR4__DENALI_PHY_1923__PHY_CAL_VREF_SWITCH_TIMER_0_SHIFT            8U
#define LPDDR4__DENALI_PHY_1923__PHY_CAL_VREF_SWITCH_TIMER_0_WIDTH           16U
#define LPDDR4__PHY_CAL_VREF_SWITCH_TIMER_0__REG DENALI_PHY_1923
#define LPDDR4__PHY_CAL_VREF_SWITCH_TIMER_0__FLD LPDDR4__DENALI_PHY_1923__PHY_CAL_VREF_SWITCH_TIMER_0

#define LPDDR4__DENALI_PHY_1923__PHY_CAL_SETTLING_PRD_0_MASK         0x7F000000U
#define LPDDR4__DENALI_PHY_1923__PHY_CAL_SETTLING_PRD_0_SHIFT                24U
#define LPDDR4__DENALI_PHY_1923__PHY_CAL_SETTLING_PRD_0_WIDTH                 7U
#define LPDDR4__PHY_CAL_SETTLING_PRD_0__REG DENALI_PHY_1923
#define LPDDR4__PHY_CAL_SETTLING_PRD_0__FLD LPDDR4__DENALI_PHY_1923__PHY_CAL_SETTLING_PRD_0

#endif /* REG_LPDDR4_PHY_CORE_MACROS_H_ */

/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */

