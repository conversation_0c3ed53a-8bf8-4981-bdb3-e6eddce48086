%%{
    let module = system.modules['/drivers/mcan/mcan'];
%%}
/*
 * MCAN
 */
#include <drivers/mcan.h>

/* MCAN Instance Macros */
% for(let i = 0; i < module.$instances.length; i++) {
    % let instance = module.$instances[i];
    % let config = module.getInstanceConfig(instance);
#define `instance.$name.toUpperCase()`_BASE_ADDR (`config.baseAddr`)
#define `instance.$name.toUpperCase()`_INTR (`config.intrNum`U)
% }
#define CONFIG_MCAN_NUM_INSTANCES (`module.$instances.length`U)
