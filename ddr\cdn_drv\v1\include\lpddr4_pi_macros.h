/* parasoft-begin-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" parasoft-begin-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-begin-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */

/**********************************************************************
 * Copyright (C) 2012-2022 Cadence Design Systems, Inc.
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 
 * 1. Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THIS FILE IS AUTOMATICALLY GENERATED, DO NOT EDIT
 *
 **********************************************************************/

#ifndef REG_LPDDR4_PI_MACROS_H_
#define REG_LPDDR4_PI_MACROS_H_

#define LPDDR4__DENALI_PI_0_READ_MASK                                0x00000F01U
#define LPDDR4__DENALI_PI_0_WRITE_MASK                               0x00000F01U
#define LPDDR4__DENALI_PI_0__PI_START_MASK                           0x00000001U
#define LPDDR4__DENALI_PI_0__PI_START_SHIFT                                   0U
#define LPDDR4__DENALI_PI_0__PI_START_WIDTH                                   1U
#define LPDDR4__DENALI_PI_0__PI_START_WOCLR                                   0U
#define LPDDR4__DENALI_PI_0__PI_START_WOSET                                   0U
#define LPDDR4__PI_START__REG DENALI_PI_0
#define LPDDR4__PI_START__FLD LPDDR4__DENALI_PI_0__PI_START

#define LPDDR4__DENALI_PI_0__PI_DRAM_CLASS_MASK                      0x00000F00U
#define LPDDR4__DENALI_PI_0__PI_DRAM_CLASS_SHIFT                              8U
#define LPDDR4__DENALI_PI_0__PI_DRAM_CLASS_WIDTH                              4U
#define LPDDR4__PI_DRAM_CLASS__REG DENALI_PI_0
#define LPDDR4__PI_DRAM_CLASS__FLD LPDDR4__DENALI_PI_0__PI_DRAM_CLASS

#define LPDDR4__DENALI_PI_1_READ_MASK                                0xFFFFFFFFU
#define LPDDR4__DENALI_PI_1_WRITE_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_1__PI_VERSION_0_MASK                       0xFFFFFFFFU
#define LPDDR4__DENALI_PI_1__PI_VERSION_0_SHIFT                               0U
#define LPDDR4__DENALI_PI_1__PI_VERSION_0_WIDTH                              32U
#define LPDDR4__PI_VERSION_0__REG DENALI_PI_1
#define LPDDR4__PI_VERSION_0__FLD LPDDR4__DENALI_PI_1__PI_VERSION_0

#define LPDDR4__DENALI_PI_2_READ_MASK                                0xFFFFFFFFU
#define LPDDR4__DENALI_PI_2_WRITE_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_2__PI_VERSION_1_MASK                       0xFFFFFFFFU
#define LPDDR4__DENALI_PI_2__PI_VERSION_1_SHIFT                               0U
#define LPDDR4__DENALI_PI_2__PI_VERSION_1_WIDTH                              32U
#define LPDDR4__PI_VERSION_1__REG DENALI_PI_2
#define LPDDR4__PI_VERSION_1__FLD LPDDR4__DENALI_PI_2__PI_VERSION_1

#define LPDDR4__DENALI_PI_3_READ_MASK                                0x0101FFFFU
#define LPDDR4__DENALI_PI_3_WRITE_MASK                               0x0101FFFFU
#define LPDDR4__DENALI_PI_3__PI_ID_MASK                              0x0000FFFFU
#define LPDDR4__DENALI_PI_3__PI_ID_SHIFT                                      0U
#define LPDDR4__DENALI_PI_3__PI_ID_WIDTH                                     16U
#define LPDDR4__PI_ID__REG DENALI_PI_3
#define LPDDR4__PI_ID__FLD LPDDR4__DENALI_PI_3__PI_ID

#define LPDDR4__DENALI_PI_3__PI_RELEASE_DFI_MASK                     0x00010000U
#define LPDDR4__DENALI_PI_3__PI_RELEASE_DFI_SHIFT                            16U
#define LPDDR4__DENALI_PI_3__PI_RELEASE_DFI_WIDTH                             1U
#define LPDDR4__DENALI_PI_3__PI_RELEASE_DFI_WOCLR                             0U
#define LPDDR4__DENALI_PI_3__PI_RELEASE_DFI_WOSET                             0U
#define LPDDR4__PI_RELEASE_DFI__REG DENALI_PI_3
#define LPDDR4__PI_RELEASE_DFI__FLD LPDDR4__DENALI_PI_3__PI_RELEASE_DFI

#define LPDDR4__DENALI_PI_3__PI_NORMAL_LVL_SEQ_MASK                  0x01000000U
#define LPDDR4__DENALI_PI_3__PI_NORMAL_LVL_SEQ_SHIFT                         24U
#define LPDDR4__DENALI_PI_3__PI_NORMAL_LVL_SEQ_WIDTH                          1U
#define LPDDR4__DENALI_PI_3__PI_NORMAL_LVL_SEQ_WOCLR                          0U
#define LPDDR4__DENALI_PI_3__PI_NORMAL_LVL_SEQ_WOSET                          0U
#define LPDDR4__PI_NORMAL_LVL_SEQ__REG DENALI_PI_3
#define LPDDR4__PI_NORMAL_LVL_SEQ__FLD LPDDR4__DENALI_PI_3__PI_NORMAL_LVL_SEQ

#define LPDDR4__DENALI_PI_4_READ_MASK                                0xFFFF0301U
#define LPDDR4__DENALI_PI_4_WRITE_MASK                               0xFFFF0301U
#define LPDDR4__DENALI_PI_4__PI_INIT_LVL_EN_MASK                     0x00000001U
#define LPDDR4__DENALI_PI_4__PI_INIT_LVL_EN_SHIFT                             0U
#define LPDDR4__DENALI_PI_4__PI_INIT_LVL_EN_WIDTH                             1U
#define LPDDR4__DENALI_PI_4__PI_INIT_LVL_EN_WOCLR                             0U
#define LPDDR4__DENALI_PI_4__PI_INIT_LVL_EN_WOSET                             0U
#define LPDDR4__PI_INIT_LVL_EN__REG DENALI_PI_4
#define LPDDR4__PI_INIT_LVL_EN__FLD LPDDR4__DENALI_PI_4__PI_INIT_LVL_EN

#define LPDDR4__DENALI_PI_4__PI_NOTCARE_PHYUPD_MASK                  0x00000300U
#define LPDDR4__DENALI_PI_4__PI_NOTCARE_PHYUPD_SHIFT                          8U
#define LPDDR4__DENALI_PI_4__PI_NOTCARE_PHYUPD_WIDTH                          2U
#define LPDDR4__PI_NOTCARE_PHYUPD__REG DENALI_PI_4
#define LPDDR4__PI_NOTCARE_PHYUPD__FLD LPDDR4__DENALI_PI_4__PI_NOTCARE_PHYUPD

#define LPDDR4__DENALI_PI_4__PI_TCMD_GAP_MASK                        0xFFFF0000U
#define LPDDR4__DENALI_PI_4__PI_TCMD_GAP_SHIFT                               16U
#define LPDDR4__DENALI_PI_4__PI_TCMD_GAP_WIDTH                               16U
#define LPDDR4__PI_TCMD_GAP__REG DENALI_PI_4
#define LPDDR4__PI_TCMD_GAP__FLD LPDDR4__DENALI_PI_4__PI_TCMD_GAP

#define LPDDR4__DENALI_PI_5_READ_MASK                                0x030100FFU
#define LPDDR4__DENALI_PI_5_WRITE_MASK                               0x030100FFU
#define LPDDR4__DENALI_PI_5__PI_RESERVED0_MASK                       0x000000FFU
#define LPDDR4__DENALI_PI_5__PI_RESERVED0_SHIFT                               0U
#define LPDDR4__DENALI_PI_5__PI_RESERVED0_WIDTH                               8U
#define LPDDR4__PI_RESERVED0__REG DENALI_PI_5
#define LPDDR4__PI_RESERVED0__FLD LPDDR4__DENALI_PI_5__PI_RESERVED0

#define LPDDR4__DENALI_PI_5__PI_TRAIN_ALL_FREQ_REQ_MASK              0x00000100U
#define LPDDR4__DENALI_PI_5__PI_TRAIN_ALL_FREQ_REQ_SHIFT                      8U
#define LPDDR4__DENALI_PI_5__PI_TRAIN_ALL_FREQ_REQ_WIDTH                      1U
#define LPDDR4__DENALI_PI_5__PI_TRAIN_ALL_FREQ_REQ_WOCLR                      0U
#define LPDDR4__DENALI_PI_5__PI_TRAIN_ALL_FREQ_REQ_WOSET                      0U
#define LPDDR4__PI_TRAIN_ALL_FREQ_REQ__REG DENALI_PI_5
#define LPDDR4__PI_TRAIN_ALL_FREQ_REQ__FLD LPDDR4__DENALI_PI_5__PI_TRAIN_ALL_FREQ_REQ

#define LPDDR4__DENALI_PI_5__PI_DFI_VERSION_MASK                     0x00010000U
#define LPDDR4__DENALI_PI_5__PI_DFI_VERSION_SHIFT                            16U
#define LPDDR4__DENALI_PI_5__PI_DFI_VERSION_WIDTH                             1U
#define LPDDR4__DENALI_PI_5__PI_DFI_VERSION_WOCLR                             0U
#define LPDDR4__DENALI_PI_5__PI_DFI_VERSION_WOSET                             0U
#define LPDDR4__PI_DFI_VERSION__REG DENALI_PI_5
#define LPDDR4__PI_DFI_VERSION__FLD LPDDR4__DENALI_PI_5__PI_DFI_VERSION

#define LPDDR4__DENALI_PI_5__PI_DFI_PHYMSTR_TYPE_MASK                0x03000000U
#define LPDDR4__DENALI_PI_5__PI_DFI_PHYMSTR_TYPE_SHIFT                       24U
#define LPDDR4__DENALI_PI_5__PI_DFI_PHYMSTR_TYPE_WIDTH                        2U
#define LPDDR4__PI_DFI_PHYMSTR_TYPE__REG DENALI_PI_5
#define LPDDR4__PI_DFI_PHYMSTR_TYPE__FLD LPDDR4__DENALI_PI_5__PI_DFI_PHYMSTR_TYPE

#define LPDDR4__DENALI_PI_6_READ_MASK                                0x00000101U
#define LPDDR4__DENALI_PI_6_WRITE_MASK                               0x00000101U
#define LPDDR4__DENALI_PI_6__PI_DFI_PHYMSTR_CS_STATE_R_MASK          0x00000001U
#define LPDDR4__DENALI_PI_6__PI_DFI_PHYMSTR_CS_STATE_R_SHIFT                  0U
#define LPDDR4__DENALI_PI_6__PI_DFI_PHYMSTR_CS_STATE_R_WIDTH                  1U
#define LPDDR4__DENALI_PI_6__PI_DFI_PHYMSTR_CS_STATE_R_WOCLR                  0U
#define LPDDR4__DENALI_PI_6__PI_DFI_PHYMSTR_CS_STATE_R_WOSET                  0U
#define LPDDR4__PI_DFI_PHYMSTR_CS_STATE_R__REG DENALI_PI_6
#define LPDDR4__PI_DFI_PHYMSTR_CS_STATE_R__FLD LPDDR4__DENALI_PI_6__PI_DFI_PHYMSTR_CS_STATE_R

#define LPDDR4__DENALI_PI_6__PI_DFI_PHYMSTR_STATE_SEL_R_MASK         0x00000100U
#define LPDDR4__DENALI_PI_6__PI_DFI_PHYMSTR_STATE_SEL_R_SHIFT                 8U
#define LPDDR4__DENALI_PI_6__PI_DFI_PHYMSTR_STATE_SEL_R_WIDTH                 1U
#define LPDDR4__DENALI_PI_6__PI_DFI_PHYMSTR_STATE_SEL_R_WOCLR                 0U
#define LPDDR4__DENALI_PI_6__PI_DFI_PHYMSTR_STATE_SEL_R_WOSET                 0U
#define LPDDR4__PI_DFI_PHYMSTR_STATE_SEL_R__REG DENALI_PI_6
#define LPDDR4__PI_DFI_PHYMSTR_STATE_SEL_R__FLD LPDDR4__DENALI_PI_6__PI_DFI_PHYMSTR_STATE_SEL_R

#define LPDDR4__DENALI_PI_7_READ_MASK                                0xFFFFFFFFU
#define LPDDR4__DENALI_PI_7_WRITE_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_7__PI_TDFI_PHYMSTR_MAX_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PI_7__PI_TDFI_PHYMSTR_MAX_SHIFT                        0U
#define LPDDR4__DENALI_PI_7__PI_TDFI_PHYMSTR_MAX_WIDTH                       32U
#define LPDDR4__PI_TDFI_PHYMSTR_MAX__REG DENALI_PI_7
#define LPDDR4__PI_TDFI_PHYMSTR_MAX__FLD LPDDR4__DENALI_PI_7__PI_TDFI_PHYMSTR_MAX

#define LPDDR4__DENALI_PI_8_READ_MASK                                0x000FFFFFU
#define LPDDR4__DENALI_PI_8_WRITE_MASK                               0x000FFFFFU
#define LPDDR4__DENALI_PI_8__PI_TDFI_PHYMSTR_RESP_MASK               0x000FFFFFU
#define LPDDR4__DENALI_PI_8__PI_TDFI_PHYMSTR_RESP_SHIFT                       0U
#define LPDDR4__DENALI_PI_8__PI_TDFI_PHYMSTR_RESP_WIDTH                      20U
#define LPDDR4__PI_TDFI_PHYMSTR_RESP__REG DENALI_PI_8
#define LPDDR4__PI_TDFI_PHYMSTR_RESP__FLD LPDDR4__DENALI_PI_8__PI_TDFI_PHYMSTR_RESP

#define LPDDR4__DENALI_PI_9_READ_MASK                                0x000FFFFFU
#define LPDDR4__DENALI_PI_9_WRITE_MASK                               0x000FFFFFU
#define LPDDR4__DENALI_PI_9__PI_TDFI_PHYUPD_RESP_MASK                0x000FFFFFU
#define LPDDR4__DENALI_PI_9__PI_TDFI_PHYUPD_RESP_SHIFT                        0U
#define LPDDR4__DENALI_PI_9__PI_TDFI_PHYUPD_RESP_WIDTH                       20U
#define LPDDR4__PI_TDFI_PHYUPD_RESP__REG DENALI_PI_9
#define LPDDR4__PI_TDFI_PHYUPD_RESP__FLD LPDDR4__DENALI_PI_9__PI_TDFI_PHYUPD_RESP

#define LPDDR4__DENALI_PI_10_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_10_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_10__PI_TDFI_PHYUPD_MAX_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PI_10__PI_TDFI_PHYUPD_MAX_SHIFT                        0U
#define LPDDR4__DENALI_PI_10__PI_TDFI_PHYUPD_MAX_WIDTH                       32U
#define LPDDR4__PI_TDFI_PHYUPD_MAX__REG DENALI_PI_10
#define LPDDR4__PI_TDFI_PHYUPD_MAX__FLD LPDDR4__DENALI_PI_10__PI_TDFI_PHYUPD_MAX

#define LPDDR4__DENALI_PI_11_READ_MASK                               0x0000011FU
#define LPDDR4__DENALI_PI_11_WRITE_MASK                              0x0000011FU
#define LPDDR4__DENALI_PI_11__PI_INIT_WORK_FREQ_MASK                 0x0000001FU
#define LPDDR4__DENALI_PI_11__PI_INIT_WORK_FREQ_SHIFT                         0U
#define LPDDR4__DENALI_PI_11__PI_INIT_WORK_FREQ_WIDTH                         5U
#define LPDDR4__PI_INIT_WORK_FREQ__REG DENALI_PI_11
#define LPDDR4__PI_INIT_WORK_FREQ__FLD LPDDR4__DENALI_PI_11__PI_INIT_WORK_FREQ

#define LPDDR4__DENALI_PI_11__PI_INIT_DFS_CALVL_ONLY_MASK            0x00000100U
#define LPDDR4__DENALI_PI_11__PI_INIT_DFS_CALVL_ONLY_SHIFT                    8U
#define LPDDR4__DENALI_PI_11__PI_INIT_DFS_CALVL_ONLY_WIDTH                    1U
#define LPDDR4__DENALI_PI_11__PI_INIT_DFS_CALVL_ONLY_WOCLR                    0U
#define LPDDR4__DENALI_PI_11__PI_INIT_DFS_CALVL_ONLY_WOSET                    0U
#define LPDDR4__PI_INIT_DFS_CALVL_ONLY__REG DENALI_PI_11
#define LPDDR4__PI_INIT_DFS_CALVL_ONLY__FLD LPDDR4__DENALI_PI_11__PI_INIT_DFS_CALVL_ONLY

#define LPDDR4__DENALI_PI_12_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_12_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_12__PI_FREQ_MAP_MASK                       0xFFFFFFFFU
#define LPDDR4__DENALI_PI_12__PI_FREQ_MAP_SHIFT                               0U
#define LPDDR4__DENALI_PI_12__PI_FREQ_MAP_WIDTH                              32U
#define LPDDR4__PI_FREQ_MAP__REG DENALI_PI_12
#define LPDDR4__PI_FREQ_MAP__FLD LPDDR4__DENALI_PI_12__PI_FREQ_MAP

#define LPDDR4__DENALI_PI_13_READ_MASK                               0x010F0101U
#define LPDDR4__DENALI_PI_13_WRITE_MASK                              0x010F0101U
#define LPDDR4__DENALI_PI_13__PI_SW_RST_N_MASK                       0x00000001U
#define LPDDR4__DENALI_PI_13__PI_SW_RST_N_SHIFT                               0U
#define LPDDR4__DENALI_PI_13__PI_SW_RST_N_WIDTH                               1U
#define LPDDR4__DENALI_PI_13__PI_SW_RST_N_WOCLR                               0U
#define LPDDR4__DENALI_PI_13__PI_SW_RST_N_WOSET                               0U
#define LPDDR4__PI_SW_RST_N__REG DENALI_PI_13
#define LPDDR4__PI_SW_RST_N__FLD LPDDR4__DENALI_PI_13__PI_SW_RST_N

#define LPDDR4__DENALI_PI_13__PI_RESERVED1_MASK                      0x00000100U
#define LPDDR4__DENALI_PI_13__PI_RESERVED1_SHIFT                              8U
#define LPDDR4__DENALI_PI_13__PI_RESERVED1_WIDTH                              1U
#define LPDDR4__DENALI_PI_13__PI_RESERVED1_WOCLR                              0U
#define LPDDR4__DENALI_PI_13__PI_RESERVED1_WOSET                              0U
#define LPDDR4__PI_RESERVED1__REG DENALI_PI_13
#define LPDDR4__PI_RESERVED1__FLD LPDDR4__DENALI_PI_13__PI_RESERVED1

#define LPDDR4__DENALI_PI_13__PI_CS_MAP_MASK                         0x000F0000U
#define LPDDR4__DENALI_PI_13__PI_CS_MAP_SHIFT                                16U
#define LPDDR4__DENALI_PI_13__PI_CS_MAP_WIDTH                                 4U
#define LPDDR4__PI_CS_MAP__REG DENALI_PI_13
#define LPDDR4__PI_CS_MAP__FLD LPDDR4__DENALI_PI_13__PI_CS_MAP

#define LPDDR4__DENALI_PI_13__PI_SWLVL_CS_SEL_MASK                   0x01000000U
#define LPDDR4__DENALI_PI_13__PI_SWLVL_CS_SEL_SHIFT                          24U
#define LPDDR4__DENALI_PI_13__PI_SWLVL_CS_SEL_WIDTH                           1U
#define LPDDR4__DENALI_PI_13__PI_SWLVL_CS_SEL_WOCLR                           0U
#define LPDDR4__DENALI_PI_13__PI_SWLVL_CS_SEL_WOSET                           0U
#define LPDDR4__PI_SWLVL_CS_SEL__REG DENALI_PI_13
#define LPDDR4__PI_SWLVL_CS_SEL__FLD LPDDR4__DENALI_PI_13__PI_SWLVL_CS_SEL

#define LPDDR4__DENALI_PI_14_READ_MASK                               0x0F011F0FU
#define LPDDR4__DENALI_PI_14_WRITE_MASK                              0x0F011F0FU
#define LPDDR4__DENALI_PI_14__PI_CS_MASK_MASK                        0x0000000FU
#define LPDDR4__DENALI_PI_14__PI_CS_MASK_SHIFT                                0U
#define LPDDR4__DENALI_PI_14__PI_CS_MASK_WIDTH                                4U
#define LPDDR4__PI_CS_MASK__REG DENALI_PI_14
#define LPDDR4__PI_CS_MASK__FLD LPDDR4__DENALI_PI_14__PI_CS_MASK

#define LPDDR4__DENALI_PI_14__PI_RANK_NUM_PER_CKE_MASK               0x00001F00U
#define LPDDR4__DENALI_PI_14__PI_RANK_NUM_PER_CKE_SHIFT                       8U
#define LPDDR4__DENALI_PI_14__PI_RANK_NUM_PER_CKE_WIDTH                       5U
#define LPDDR4__PI_RANK_NUM_PER_CKE__REG DENALI_PI_14
#define LPDDR4__PI_RANK_NUM_PER_CKE__FLD LPDDR4__DENALI_PI_14__PI_RANK_NUM_PER_CKE

#define LPDDR4__DENALI_PI_14__PI_SRX_LVL_TARGET_CS_EN_MASK           0x00010000U
#define LPDDR4__DENALI_PI_14__PI_SRX_LVL_TARGET_CS_EN_SHIFT                  16U
#define LPDDR4__DENALI_PI_14__PI_SRX_LVL_TARGET_CS_EN_WIDTH                   1U
#define LPDDR4__DENALI_PI_14__PI_SRX_LVL_TARGET_CS_EN_WOCLR                   0U
#define LPDDR4__DENALI_PI_14__PI_SRX_LVL_TARGET_CS_EN_WOSET                   0U
#define LPDDR4__PI_SRX_LVL_TARGET_CS_EN__REG DENALI_PI_14
#define LPDDR4__PI_SRX_LVL_TARGET_CS_EN__FLD LPDDR4__DENALI_PI_14__PI_SRX_LVL_TARGET_CS_EN

#define LPDDR4__DENALI_PI_14__PI_TMRR_MASK                           0x0F000000U
#define LPDDR4__DENALI_PI_14__PI_TMRR_SHIFT                                  24U
#define LPDDR4__DENALI_PI_14__PI_TMRR_WIDTH                                   4U
#define LPDDR4__PI_TMRR__REG DENALI_PI_14
#define LPDDR4__PI_TMRR__FLD LPDDR4__DENALI_PI_14__PI_TMRR

#define LPDDR4__DENALI_PI_15_READ_MASK                               0x0101070FU
#define LPDDR4__DENALI_PI_15_WRITE_MASK                              0x0101070FU
#define LPDDR4__DENALI_PI_15__PI_TMPRR_MASK                          0x0000000FU
#define LPDDR4__DENALI_PI_15__PI_TMPRR_SHIFT                                  0U
#define LPDDR4__DENALI_PI_15__PI_TMPRR_WIDTH                                  4U
#define LPDDR4__PI_TMPRR__REG DENALI_PI_15
#define LPDDR4__PI_TMPRR__FLD LPDDR4__DENALI_PI_15__PI_TMPRR

#define LPDDR4__DENALI_PI_15__PI_VRCG_EN_MASK                        0x00000700U
#define LPDDR4__DENALI_PI_15__PI_VRCG_EN_SHIFT                                8U
#define LPDDR4__DENALI_PI_15__PI_VRCG_EN_WIDTH                                3U
#define LPDDR4__PI_VRCG_EN__REG DENALI_PI_15
#define LPDDR4__PI_VRCG_EN__FLD LPDDR4__DENALI_PI_15__PI_VRCG_EN

#define LPDDR4__DENALI_PI_15__PI_MCAREF_FORWARD_ONLY_MASK            0x00010000U
#define LPDDR4__DENALI_PI_15__PI_MCAREF_FORWARD_ONLY_SHIFT                   16U
#define LPDDR4__DENALI_PI_15__PI_MCAREF_FORWARD_ONLY_WIDTH                    1U
#define LPDDR4__DENALI_PI_15__PI_MCAREF_FORWARD_ONLY_WOCLR                    0U
#define LPDDR4__DENALI_PI_15__PI_MCAREF_FORWARD_ONLY_WOSET                    0U
#define LPDDR4__PI_MCAREF_FORWARD_ONLY__REG DENALI_PI_15
#define LPDDR4__PI_MCAREF_FORWARD_ONLY__FLD LPDDR4__DENALI_PI_15__PI_MCAREF_FORWARD_ONLY

#define LPDDR4__DENALI_PI_15__PI_RESERVED2_MASK                      0x01000000U
#define LPDDR4__DENALI_PI_15__PI_RESERVED2_SHIFT                             24U
#define LPDDR4__DENALI_PI_15__PI_RESERVED2_WIDTH                              1U
#define LPDDR4__DENALI_PI_15__PI_RESERVED2_WOCLR                              0U
#define LPDDR4__DENALI_PI_15__PI_RESERVED2_WOSET                              0U
#define LPDDR4__PI_RESERVED2__REG DENALI_PI_15
#define LPDDR4__PI_RESERVED2__FLD LPDDR4__DENALI_PI_15__PI_RESERVED2

#define LPDDR4__DENALI_PI_16_READ_MASK                               0x010FFFFFU
#define LPDDR4__DENALI_PI_16_WRITE_MASK                              0x010FFFFFU
#define LPDDR4__DENALI_PI_16__PI_TREF_INTERVAL_MASK                  0x000FFFFFU
#define LPDDR4__DENALI_PI_16__PI_TREF_INTERVAL_SHIFT                          0U
#define LPDDR4__DENALI_PI_16__PI_TREF_INTERVAL_WIDTH                         20U
#define LPDDR4__PI_TREF_INTERVAL__REG DENALI_PI_16
#define LPDDR4__PI_TREF_INTERVAL__FLD LPDDR4__DENALI_PI_16__PI_TREF_INTERVAL

#define LPDDR4__DENALI_PI_16__PI_ON_DFIBUS_MASK                      0x01000000U
#define LPDDR4__DENALI_PI_16__PI_ON_DFIBUS_SHIFT                             24U
#define LPDDR4__DENALI_PI_16__PI_ON_DFIBUS_WIDTH                              1U
#define LPDDR4__DENALI_PI_16__PI_ON_DFIBUS_WOCLR                              0U
#define LPDDR4__DENALI_PI_16__PI_ON_DFIBUS_WOSET                              0U
#define LPDDR4__PI_ON_DFIBUS__REG DENALI_PI_16
#define LPDDR4__PI_ON_DFIBUS__FLD LPDDR4__DENALI_PI_16__PI_ON_DFIBUS

#define LPDDR4__DENALI_PI_17_READ_MASK                               0x01010001U
#define LPDDR4__DENALI_PI_17_WRITE_MASK                              0x01010001U
#define LPDDR4__DENALI_PI_17__PI_DATA_RETENTION_MASK                 0x00000001U
#define LPDDR4__DENALI_PI_17__PI_DATA_RETENTION_SHIFT                         0U
#define LPDDR4__DENALI_PI_17__PI_DATA_RETENTION_WIDTH                         1U
#define LPDDR4__DENALI_PI_17__PI_DATA_RETENTION_WOCLR                         0U
#define LPDDR4__DENALI_PI_17__PI_DATA_RETENTION_WOSET                         0U
#define LPDDR4__PI_DATA_RETENTION__REG DENALI_PI_17
#define LPDDR4__PI_DATA_RETENTION__FLD LPDDR4__DENALI_PI_17__PI_DATA_RETENTION

#define LPDDR4__DENALI_PI_17__PI_SWLVL_LOAD_MASK                     0x00000100U
#define LPDDR4__DENALI_PI_17__PI_SWLVL_LOAD_SHIFT                             8U
#define LPDDR4__DENALI_PI_17__PI_SWLVL_LOAD_WIDTH                             1U
#define LPDDR4__DENALI_PI_17__PI_SWLVL_LOAD_WOCLR                             0U
#define LPDDR4__DENALI_PI_17__PI_SWLVL_LOAD_WOSET                             0U
#define LPDDR4__PI_SWLVL_LOAD__REG DENALI_PI_17
#define LPDDR4__PI_SWLVL_LOAD__FLD LPDDR4__DENALI_PI_17__PI_SWLVL_LOAD

#define LPDDR4__DENALI_PI_17__PI_SWLVL_OP_DONE_MASK                  0x00010000U
#define LPDDR4__DENALI_PI_17__PI_SWLVL_OP_DONE_SHIFT                         16U
#define LPDDR4__DENALI_PI_17__PI_SWLVL_OP_DONE_WIDTH                          1U
#define LPDDR4__DENALI_PI_17__PI_SWLVL_OP_DONE_WOCLR                          0U
#define LPDDR4__DENALI_PI_17__PI_SWLVL_OP_DONE_WOSET                          0U
#define LPDDR4__PI_SWLVL_OP_DONE__REG DENALI_PI_17
#define LPDDR4__PI_SWLVL_OP_DONE__FLD LPDDR4__DENALI_PI_17__PI_SWLVL_OP_DONE

#define LPDDR4__DENALI_PI_17__PI_SW_WRLVL_RESP_0_MASK                0x01000000U
#define LPDDR4__DENALI_PI_17__PI_SW_WRLVL_RESP_0_SHIFT                       24U
#define LPDDR4__DENALI_PI_17__PI_SW_WRLVL_RESP_0_WIDTH                        1U
#define LPDDR4__DENALI_PI_17__PI_SW_WRLVL_RESP_0_WOCLR                        0U
#define LPDDR4__DENALI_PI_17__PI_SW_WRLVL_RESP_0_WOSET                        0U
#define LPDDR4__PI_SW_WRLVL_RESP_0__REG DENALI_PI_17
#define LPDDR4__PI_SW_WRLVL_RESP_0__FLD LPDDR4__DENALI_PI_17__PI_SW_WRLVL_RESP_0

#define LPDDR4__DENALI_PI_18_READ_MASK                               0x03010101U
#define LPDDR4__DENALI_PI_18_WRITE_MASK                              0x03010101U
#define LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_1_MASK                0x00000001U
#define LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_1_SHIFT                        0U
#define LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_1_WIDTH                        1U
#define LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_1_WOCLR                        0U
#define LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_1_WOSET                        0U
#define LPDDR4__PI_SW_WRLVL_RESP_1__REG DENALI_PI_18
#define LPDDR4__PI_SW_WRLVL_RESP_1__FLD LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_1

#define LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_2_MASK                0x00000100U
#define LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_2_SHIFT                        8U
#define LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_2_WIDTH                        1U
#define LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_2_WOCLR                        0U
#define LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_2_WOSET                        0U
#define LPDDR4__PI_SW_WRLVL_RESP_2__REG DENALI_PI_18
#define LPDDR4__PI_SW_WRLVL_RESP_2__FLD LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_2

#define LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_3_MASK                0x00010000U
#define LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_3_SHIFT                       16U
#define LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_3_WIDTH                        1U
#define LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_3_WOCLR                        0U
#define LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_3_WOSET                        0U
#define LPDDR4__PI_SW_WRLVL_RESP_3__REG DENALI_PI_18
#define LPDDR4__PI_SW_WRLVL_RESP_3__FLD LPDDR4__DENALI_PI_18__PI_SW_WRLVL_RESP_3

#define LPDDR4__DENALI_PI_18__PI_SW_RDLVL_RESP_0_MASK                0x03000000U
#define LPDDR4__DENALI_PI_18__PI_SW_RDLVL_RESP_0_SHIFT                       24U
#define LPDDR4__DENALI_PI_18__PI_SW_RDLVL_RESP_0_WIDTH                        2U
#define LPDDR4__PI_SW_RDLVL_RESP_0__REG DENALI_PI_18
#define LPDDR4__PI_SW_RDLVL_RESP_0__FLD LPDDR4__DENALI_PI_18__PI_SW_RDLVL_RESP_0

#define LPDDR4__DENALI_PI_19_READ_MASK                               0x03030303U
#define LPDDR4__DENALI_PI_19_WRITE_MASK                              0x03030303U
#define LPDDR4__DENALI_PI_19__PI_SW_RDLVL_RESP_1_MASK                0x00000003U
#define LPDDR4__DENALI_PI_19__PI_SW_RDLVL_RESP_1_SHIFT                        0U
#define LPDDR4__DENALI_PI_19__PI_SW_RDLVL_RESP_1_WIDTH                        2U
#define LPDDR4__PI_SW_RDLVL_RESP_1__REG DENALI_PI_19
#define LPDDR4__PI_SW_RDLVL_RESP_1__FLD LPDDR4__DENALI_PI_19__PI_SW_RDLVL_RESP_1

#define LPDDR4__DENALI_PI_19__PI_SW_RDLVL_RESP_2_MASK                0x00000300U
#define LPDDR4__DENALI_PI_19__PI_SW_RDLVL_RESP_2_SHIFT                        8U
#define LPDDR4__DENALI_PI_19__PI_SW_RDLVL_RESP_2_WIDTH                        2U
#define LPDDR4__PI_SW_RDLVL_RESP_2__REG DENALI_PI_19
#define LPDDR4__PI_SW_RDLVL_RESP_2__FLD LPDDR4__DENALI_PI_19__PI_SW_RDLVL_RESP_2

#define LPDDR4__DENALI_PI_19__PI_SW_RDLVL_RESP_3_MASK                0x00030000U
#define LPDDR4__DENALI_PI_19__PI_SW_RDLVL_RESP_3_SHIFT                       16U
#define LPDDR4__DENALI_PI_19__PI_SW_RDLVL_RESP_3_WIDTH                        2U
#define LPDDR4__PI_SW_RDLVL_RESP_3__REG DENALI_PI_19
#define LPDDR4__PI_SW_RDLVL_RESP_3__FLD LPDDR4__DENALI_PI_19__PI_SW_RDLVL_RESP_3

#define LPDDR4__DENALI_PI_19__PI_SW_CALVL_RESP_0_MASK                0x03000000U
#define LPDDR4__DENALI_PI_19__PI_SW_CALVL_RESP_0_SHIFT                       24U
#define LPDDR4__DENALI_PI_19__PI_SW_CALVL_RESP_0_WIDTH                        2U
#define LPDDR4__PI_SW_CALVL_RESP_0__REG DENALI_PI_19
#define LPDDR4__PI_SW_CALVL_RESP_0__FLD LPDDR4__DENALI_PI_19__PI_SW_CALVL_RESP_0

#define LPDDR4__DENALI_PI_20_READ_MASK                               0x00000007U
#define LPDDR4__DENALI_PI_20_WRITE_MASK                              0x00000007U
#define LPDDR4__DENALI_PI_20__PI_SW_LEVELING_MODE_MASK               0x00000007U
#define LPDDR4__DENALI_PI_20__PI_SW_LEVELING_MODE_SHIFT                       0U
#define LPDDR4__DENALI_PI_20__PI_SW_LEVELING_MODE_WIDTH                       3U
#define LPDDR4__PI_SW_LEVELING_MODE__REG DENALI_PI_20
#define LPDDR4__PI_SW_LEVELING_MODE__FLD LPDDR4__DENALI_PI_20__PI_SW_LEVELING_MODE

#define LPDDR4__DENALI_PI_20__PI_SWLVL_START_MASK                    0x00000100U
#define LPDDR4__DENALI_PI_20__PI_SWLVL_START_SHIFT                            8U
#define LPDDR4__DENALI_PI_20__PI_SWLVL_START_WIDTH                            1U
#define LPDDR4__DENALI_PI_20__PI_SWLVL_START_WOCLR                            0U
#define LPDDR4__DENALI_PI_20__PI_SWLVL_START_WOSET                            0U
#define LPDDR4__PI_SWLVL_START__REG DENALI_PI_20
#define LPDDR4__PI_SWLVL_START__FLD LPDDR4__DENALI_PI_20__PI_SWLVL_START

#define LPDDR4__DENALI_PI_20__PI_SWLVL_EXIT_MASK                     0x00010000U
#define LPDDR4__DENALI_PI_20__PI_SWLVL_EXIT_SHIFT                            16U
#define LPDDR4__DENALI_PI_20__PI_SWLVL_EXIT_WIDTH                             1U
#define LPDDR4__DENALI_PI_20__PI_SWLVL_EXIT_WOCLR                             0U
#define LPDDR4__DENALI_PI_20__PI_SWLVL_EXIT_WOSET                             0U
#define LPDDR4__PI_SWLVL_EXIT__REG DENALI_PI_20
#define LPDDR4__PI_SWLVL_EXIT__FLD LPDDR4__DENALI_PI_20__PI_SWLVL_EXIT

#define LPDDR4__DENALI_PI_20__PI_SWLVL_WR_SLICE_0_MASK               0x01000000U
#define LPDDR4__DENALI_PI_20__PI_SWLVL_WR_SLICE_0_SHIFT                      24U
#define LPDDR4__DENALI_PI_20__PI_SWLVL_WR_SLICE_0_WIDTH                       1U
#define LPDDR4__DENALI_PI_20__PI_SWLVL_WR_SLICE_0_WOCLR                       0U
#define LPDDR4__DENALI_PI_20__PI_SWLVL_WR_SLICE_0_WOSET                       0U
#define LPDDR4__PI_SWLVL_WR_SLICE_0__REG DENALI_PI_20
#define LPDDR4__PI_SWLVL_WR_SLICE_0__FLD LPDDR4__DENALI_PI_20__PI_SWLVL_WR_SLICE_0

#define LPDDR4__DENALI_PI_21_READ_MASK                               0x00030000U
#define LPDDR4__DENALI_PI_21_WRITE_MASK                              0x00030000U
#define LPDDR4__DENALI_PI_21__PI_SWLVL_RD_SLICE_0_MASK               0x00000001U
#define LPDDR4__DENALI_PI_21__PI_SWLVL_RD_SLICE_0_SHIFT                       0U
#define LPDDR4__DENALI_PI_21__PI_SWLVL_RD_SLICE_0_WIDTH                       1U
#define LPDDR4__DENALI_PI_21__PI_SWLVL_RD_SLICE_0_WOCLR                       0U
#define LPDDR4__DENALI_PI_21__PI_SWLVL_RD_SLICE_0_WOSET                       0U
#define LPDDR4__PI_SWLVL_RD_SLICE_0__REG DENALI_PI_21
#define LPDDR4__PI_SWLVL_RD_SLICE_0__FLD LPDDR4__DENALI_PI_21__PI_SWLVL_RD_SLICE_0

#define LPDDR4__DENALI_PI_21__PI_SWLVL_VREF_UPDATE_SLICE_0_MASK      0x00000100U
#define LPDDR4__DENALI_PI_21__PI_SWLVL_VREF_UPDATE_SLICE_0_SHIFT              8U
#define LPDDR4__DENALI_PI_21__PI_SWLVL_VREF_UPDATE_SLICE_0_WIDTH              1U
#define LPDDR4__DENALI_PI_21__PI_SWLVL_VREF_UPDATE_SLICE_0_WOCLR              0U
#define LPDDR4__DENALI_PI_21__PI_SWLVL_VREF_UPDATE_SLICE_0_WOSET              0U
#define LPDDR4__PI_SWLVL_VREF_UPDATE_SLICE_0__REG DENALI_PI_21
#define LPDDR4__PI_SWLVL_VREF_UPDATE_SLICE_0__FLD LPDDR4__DENALI_PI_21__PI_SWLVL_VREF_UPDATE_SLICE_0

#define LPDDR4__DENALI_PI_21__PI_SW_WDQLVL_RESP_0_MASK               0x00030000U
#define LPDDR4__DENALI_PI_21__PI_SW_WDQLVL_RESP_0_SHIFT                      16U
#define LPDDR4__DENALI_PI_21__PI_SW_WDQLVL_RESP_0_WIDTH                       2U
#define LPDDR4__PI_SW_WDQLVL_RESP_0__REG DENALI_PI_21
#define LPDDR4__PI_SW_WDQLVL_RESP_0__FLD LPDDR4__DENALI_PI_21__PI_SW_WDQLVL_RESP_0

#define LPDDR4__DENALI_PI_21__PI_SWLVL_WR_SLICE_1_MASK               0x01000000U
#define LPDDR4__DENALI_PI_21__PI_SWLVL_WR_SLICE_1_SHIFT                      24U
#define LPDDR4__DENALI_PI_21__PI_SWLVL_WR_SLICE_1_WIDTH                       1U
#define LPDDR4__DENALI_PI_21__PI_SWLVL_WR_SLICE_1_WOCLR                       0U
#define LPDDR4__DENALI_PI_21__PI_SWLVL_WR_SLICE_1_WOSET                       0U
#define LPDDR4__PI_SWLVL_WR_SLICE_1__REG DENALI_PI_21
#define LPDDR4__PI_SWLVL_WR_SLICE_1__FLD LPDDR4__DENALI_PI_21__PI_SWLVL_WR_SLICE_1

#define LPDDR4__DENALI_PI_22_READ_MASK                               0x00030000U
#define LPDDR4__DENALI_PI_22_WRITE_MASK                              0x00030000U
#define LPDDR4__DENALI_PI_22__PI_SWLVL_RD_SLICE_1_MASK               0x00000001U
#define LPDDR4__DENALI_PI_22__PI_SWLVL_RD_SLICE_1_SHIFT                       0U
#define LPDDR4__DENALI_PI_22__PI_SWLVL_RD_SLICE_1_WIDTH                       1U
#define LPDDR4__DENALI_PI_22__PI_SWLVL_RD_SLICE_1_WOCLR                       0U
#define LPDDR4__DENALI_PI_22__PI_SWLVL_RD_SLICE_1_WOSET                       0U
#define LPDDR4__PI_SWLVL_RD_SLICE_1__REG DENALI_PI_22
#define LPDDR4__PI_SWLVL_RD_SLICE_1__FLD LPDDR4__DENALI_PI_22__PI_SWLVL_RD_SLICE_1

#define LPDDR4__DENALI_PI_22__PI_SWLVL_VREF_UPDATE_SLICE_1_MASK      0x00000100U
#define LPDDR4__DENALI_PI_22__PI_SWLVL_VREF_UPDATE_SLICE_1_SHIFT              8U
#define LPDDR4__DENALI_PI_22__PI_SWLVL_VREF_UPDATE_SLICE_1_WIDTH              1U
#define LPDDR4__DENALI_PI_22__PI_SWLVL_VREF_UPDATE_SLICE_1_WOCLR              0U
#define LPDDR4__DENALI_PI_22__PI_SWLVL_VREF_UPDATE_SLICE_1_WOSET              0U
#define LPDDR4__PI_SWLVL_VREF_UPDATE_SLICE_1__REG DENALI_PI_22
#define LPDDR4__PI_SWLVL_VREF_UPDATE_SLICE_1__FLD LPDDR4__DENALI_PI_22__PI_SWLVL_VREF_UPDATE_SLICE_1

#define LPDDR4__DENALI_PI_22__PI_SW_WDQLVL_RESP_1_MASK               0x00030000U
#define LPDDR4__DENALI_PI_22__PI_SW_WDQLVL_RESP_1_SHIFT                      16U
#define LPDDR4__DENALI_PI_22__PI_SW_WDQLVL_RESP_1_WIDTH                       2U
#define LPDDR4__PI_SW_WDQLVL_RESP_1__REG DENALI_PI_22
#define LPDDR4__PI_SW_WDQLVL_RESP_1__FLD LPDDR4__DENALI_PI_22__PI_SW_WDQLVL_RESP_1

#define LPDDR4__DENALI_PI_22__PI_SWLVL_WR_SLICE_2_MASK               0x01000000U
#define LPDDR4__DENALI_PI_22__PI_SWLVL_WR_SLICE_2_SHIFT                      24U
#define LPDDR4__DENALI_PI_22__PI_SWLVL_WR_SLICE_2_WIDTH                       1U
#define LPDDR4__DENALI_PI_22__PI_SWLVL_WR_SLICE_2_WOCLR                       0U
#define LPDDR4__DENALI_PI_22__PI_SWLVL_WR_SLICE_2_WOSET                       0U
#define LPDDR4__PI_SWLVL_WR_SLICE_2__REG DENALI_PI_22
#define LPDDR4__PI_SWLVL_WR_SLICE_2__FLD LPDDR4__DENALI_PI_22__PI_SWLVL_WR_SLICE_2

#define LPDDR4__DENALI_PI_23_READ_MASK                               0x00030000U
#define LPDDR4__DENALI_PI_23_WRITE_MASK                              0x00030000U
#define LPDDR4__DENALI_PI_23__PI_SWLVL_RD_SLICE_2_MASK               0x00000001U
#define LPDDR4__DENALI_PI_23__PI_SWLVL_RD_SLICE_2_SHIFT                       0U
#define LPDDR4__DENALI_PI_23__PI_SWLVL_RD_SLICE_2_WIDTH                       1U
#define LPDDR4__DENALI_PI_23__PI_SWLVL_RD_SLICE_2_WOCLR                       0U
#define LPDDR4__DENALI_PI_23__PI_SWLVL_RD_SLICE_2_WOSET                       0U
#define LPDDR4__PI_SWLVL_RD_SLICE_2__REG DENALI_PI_23
#define LPDDR4__PI_SWLVL_RD_SLICE_2__FLD LPDDR4__DENALI_PI_23__PI_SWLVL_RD_SLICE_2

#define LPDDR4__DENALI_PI_23__PI_SWLVL_VREF_UPDATE_SLICE_2_MASK      0x00000100U
#define LPDDR4__DENALI_PI_23__PI_SWLVL_VREF_UPDATE_SLICE_2_SHIFT              8U
#define LPDDR4__DENALI_PI_23__PI_SWLVL_VREF_UPDATE_SLICE_2_WIDTH              1U
#define LPDDR4__DENALI_PI_23__PI_SWLVL_VREF_UPDATE_SLICE_2_WOCLR              0U
#define LPDDR4__DENALI_PI_23__PI_SWLVL_VREF_UPDATE_SLICE_2_WOSET              0U
#define LPDDR4__PI_SWLVL_VREF_UPDATE_SLICE_2__REG DENALI_PI_23
#define LPDDR4__PI_SWLVL_VREF_UPDATE_SLICE_2__FLD LPDDR4__DENALI_PI_23__PI_SWLVL_VREF_UPDATE_SLICE_2

#define LPDDR4__DENALI_PI_23__PI_SW_WDQLVL_RESP_2_MASK               0x00030000U
#define LPDDR4__DENALI_PI_23__PI_SW_WDQLVL_RESP_2_SHIFT                      16U
#define LPDDR4__DENALI_PI_23__PI_SW_WDQLVL_RESP_2_WIDTH                       2U
#define LPDDR4__PI_SW_WDQLVL_RESP_2__REG DENALI_PI_23
#define LPDDR4__PI_SW_WDQLVL_RESP_2__FLD LPDDR4__DENALI_PI_23__PI_SW_WDQLVL_RESP_2

#define LPDDR4__DENALI_PI_23__PI_SWLVL_WR_SLICE_3_MASK               0x01000000U
#define LPDDR4__DENALI_PI_23__PI_SWLVL_WR_SLICE_3_SHIFT                      24U
#define LPDDR4__DENALI_PI_23__PI_SWLVL_WR_SLICE_3_WIDTH                       1U
#define LPDDR4__DENALI_PI_23__PI_SWLVL_WR_SLICE_3_WOCLR                       0U
#define LPDDR4__DENALI_PI_23__PI_SWLVL_WR_SLICE_3_WOSET                       0U
#define LPDDR4__PI_SWLVL_WR_SLICE_3__REG DENALI_PI_23
#define LPDDR4__PI_SWLVL_WR_SLICE_3__FLD LPDDR4__DENALI_PI_23__PI_SWLVL_WR_SLICE_3

#define LPDDR4__DENALI_PI_24_READ_MASK                               0x00030000U
#define LPDDR4__DENALI_PI_24_WRITE_MASK                              0x00030000U
#define LPDDR4__DENALI_PI_24__PI_SWLVL_RD_SLICE_3_MASK               0x00000001U
#define LPDDR4__DENALI_PI_24__PI_SWLVL_RD_SLICE_3_SHIFT                       0U
#define LPDDR4__DENALI_PI_24__PI_SWLVL_RD_SLICE_3_WIDTH                       1U
#define LPDDR4__DENALI_PI_24__PI_SWLVL_RD_SLICE_3_WOCLR                       0U
#define LPDDR4__DENALI_PI_24__PI_SWLVL_RD_SLICE_3_WOSET                       0U
#define LPDDR4__PI_SWLVL_RD_SLICE_3__REG DENALI_PI_24
#define LPDDR4__PI_SWLVL_RD_SLICE_3__FLD LPDDR4__DENALI_PI_24__PI_SWLVL_RD_SLICE_3

#define LPDDR4__DENALI_PI_24__PI_SWLVL_VREF_UPDATE_SLICE_3_MASK      0x00000100U
#define LPDDR4__DENALI_PI_24__PI_SWLVL_VREF_UPDATE_SLICE_3_SHIFT              8U
#define LPDDR4__DENALI_PI_24__PI_SWLVL_VREF_UPDATE_SLICE_3_WIDTH              1U
#define LPDDR4__DENALI_PI_24__PI_SWLVL_VREF_UPDATE_SLICE_3_WOCLR              0U
#define LPDDR4__DENALI_PI_24__PI_SWLVL_VREF_UPDATE_SLICE_3_WOSET              0U
#define LPDDR4__PI_SWLVL_VREF_UPDATE_SLICE_3__REG DENALI_PI_24
#define LPDDR4__PI_SWLVL_VREF_UPDATE_SLICE_3__FLD LPDDR4__DENALI_PI_24__PI_SWLVL_VREF_UPDATE_SLICE_3

#define LPDDR4__DENALI_PI_24__PI_SW_WDQLVL_RESP_3_MASK               0x00030000U
#define LPDDR4__DENALI_PI_24__PI_SW_WDQLVL_RESP_3_SHIFT                      16U
#define LPDDR4__DENALI_PI_24__PI_SW_WDQLVL_RESP_3_WIDTH                       2U
#define LPDDR4__PI_SW_WDQLVL_RESP_3__REG DENALI_PI_24
#define LPDDR4__PI_SW_WDQLVL_RESP_3__FLD LPDDR4__DENALI_PI_24__PI_SW_WDQLVL_RESP_3

#define LPDDR4__DENALI_PI_24__PI_SWLVL_SM2_START_MASK                0x01000000U
#define LPDDR4__DENALI_PI_24__PI_SWLVL_SM2_START_SHIFT                       24U
#define LPDDR4__DENALI_PI_24__PI_SWLVL_SM2_START_WIDTH                        1U
#define LPDDR4__DENALI_PI_24__PI_SWLVL_SM2_START_WOCLR                        0U
#define LPDDR4__DENALI_PI_24__PI_SWLVL_SM2_START_WOSET                        0U
#define LPDDR4__PI_SWLVL_SM2_START__REG DENALI_PI_24
#define LPDDR4__PI_SWLVL_SM2_START__FLD LPDDR4__DENALI_PI_24__PI_SWLVL_SM2_START

#define LPDDR4__DENALI_PI_25_READ_MASK                               0x01000000U
#define LPDDR4__DENALI_PI_25_WRITE_MASK                              0x01000000U
#define LPDDR4__DENALI_PI_25__PI_SWLVL_SM2_WR_MASK                   0x00000001U
#define LPDDR4__DENALI_PI_25__PI_SWLVL_SM2_WR_SHIFT                           0U
#define LPDDR4__DENALI_PI_25__PI_SWLVL_SM2_WR_WIDTH                           1U
#define LPDDR4__DENALI_PI_25__PI_SWLVL_SM2_WR_WOCLR                           0U
#define LPDDR4__DENALI_PI_25__PI_SWLVL_SM2_WR_WOSET                           0U
#define LPDDR4__PI_SWLVL_SM2_WR__REG DENALI_PI_25
#define LPDDR4__PI_SWLVL_SM2_WR__FLD LPDDR4__DENALI_PI_25__PI_SWLVL_SM2_WR

#define LPDDR4__DENALI_PI_25__PI_SWLVL_SM2_RD_MASK                   0x00000100U
#define LPDDR4__DENALI_PI_25__PI_SWLVL_SM2_RD_SHIFT                           8U
#define LPDDR4__DENALI_PI_25__PI_SWLVL_SM2_RD_WIDTH                           1U
#define LPDDR4__DENALI_PI_25__PI_SWLVL_SM2_RD_WOCLR                           0U
#define LPDDR4__DENALI_PI_25__PI_SWLVL_SM2_RD_WOSET                           0U
#define LPDDR4__PI_SWLVL_SM2_RD__REG DENALI_PI_25
#define LPDDR4__PI_SWLVL_SM2_RD__FLD LPDDR4__DENALI_PI_25__PI_SWLVL_SM2_RD

#define LPDDR4__DENALI_PI_25__PI_SEQUENTIAL_LVL_REQ_MASK             0x00010000U
#define LPDDR4__DENALI_PI_25__PI_SEQUENTIAL_LVL_REQ_SHIFT                    16U
#define LPDDR4__DENALI_PI_25__PI_SEQUENTIAL_LVL_REQ_WIDTH                     1U
#define LPDDR4__DENALI_PI_25__PI_SEQUENTIAL_LVL_REQ_WOCLR                     0U
#define LPDDR4__DENALI_PI_25__PI_SEQUENTIAL_LVL_REQ_WOSET                     0U
#define LPDDR4__PI_SEQUENTIAL_LVL_REQ__REG DENALI_PI_25
#define LPDDR4__PI_SEQUENTIAL_LVL_REQ__FLD LPDDR4__DENALI_PI_25__PI_SEQUENTIAL_LVL_REQ

#define LPDDR4__DENALI_PI_25__PI_DFS_PERIOD_EN_MASK                  0x01000000U
#define LPDDR4__DENALI_PI_25__PI_DFS_PERIOD_EN_SHIFT                         24U
#define LPDDR4__DENALI_PI_25__PI_DFS_PERIOD_EN_WIDTH                          1U
#define LPDDR4__DENALI_PI_25__PI_DFS_PERIOD_EN_WOCLR                          0U
#define LPDDR4__DENALI_PI_25__PI_DFS_PERIOD_EN_WOSET                          0U
#define LPDDR4__PI_DFS_PERIOD_EN__REG DENALI_PI_25
#define LPDDR4__PI_DFS_PERIOD_EN__FLD LPDDR4__DENALI_PI_25__PI_DFS_PERIOD_EN

#define LPDDR4__DENALI_PI_26_READ_MASK                               0x01010101U
#define LPDDR4__DENALI_PI_26_WRITE_MASK                              0x01010101U
#define LPDDR4__DENALI_PI_26__PI_SRE_PERIOD_EN_MASK                  0x00000001U
#define LPDDR4__DENALI_PI_26__PI_SRE_PERIOD_EN_SHIFT                          0U
#define LPDDR4__DENALI_PI_26__PI_SRE_PERIOD_EN_WIDTH                          1U
#define LPDDR4__DENALI_PI_26__PI_SRE_PERIOD_EN_WOCLR                          0U
#define LPDDR4__DENALI_PI_26__PI_SRE_PERIOD_EN_WOSET                          0U
#define LPDDR4__PI_SRE_PERIOD_EN__REG DENALI_PI_26
#define LPDDR4__PI_SRE_PERIOD_EN__FLD LPDDR4__DENALI_PI_26__PI_SRE_PERIOD_EN

#define LPDDR4__DENALI_PI_26__PI_MPD_PERIOD_EN_MASK                  0x00000100U
#define LPDDR4__DENALI_PI_26__PI_MPD_PERIOD_EN_SHIFT                          8U
#define LPDDR4__DENALI_PI_26__PI_MPD_PERIOD_EN_WIDTH                          1U
#define LPDDR4__DENALI_PI_26__PI_MPD_PERIOD_EN_WOCLR                          0U
#define LPDDR4__DENALI_PI_26__PI_MPD_PERIOD_EN_WOSET                          0U
#define LPDDR4__PI_MPD_PERIOD_EN__REG DENALI_PI_26
#define LPDDR4__PI_MPD_PERIOD_EN__FLD LPDDR4__DENALI_PI_26__PI_MPD_PERIOD_EN

#define LPDDR4__DENALI_PI_26__PI_DFI40_POLARITY_MASK                 0x00010000U
#define LPDDR4__DENALI_PI_26__PI_DFI40_POLARITY_SHIFT                        16U
#define LPDDR4__DENALI_PI_26__PI_DFI40_POLARITY_WIDTH                         1U
#define LPDDR4__DENALI_PI_26__PI_DFI40_POLARITY_WOCLR                         0U
#define LPDDR4__DENALI_PI_26__PI_DFI40_POLARITY_WOSET                         0U
#define LPDDR4__PI_DFI40_POLARITY__REG DENALI_PI_26
#define LPDDR4__PI_DFI40_POLARITY__FLD LPDDR4__DENALI_PI_26__PI_DFI40_POLARITY

#define LPDDR4__DENALI_PI_26__PI_16BIT_DRAM_CONNECT_MASK             0x01000000U
#define LPDDR4__DENALI_PI_26__PI_16BIT_DRAM_CONNECT_SHIFT                    24U
#define LPDDR4__DENALI_PI_26__PI_16BIT_DRAM_CONNECT_WIDTH                     1U
#define LPDDR4__DENALI_PI_26__PI_16BIT_DRAM_CONNECT_WOCLR                     0U
#define LPDDR4__DENALI_PI_26__PI_16BIT_DRAM_CONNECT_WOSET                     0U
#define LPDDR4__PI_16BIT_DRAM_CONNECT__REG DENALI_PI_26
#define LPDDR4__PI_16BIT_DRAM_CONNECT__FLD LPDDR4__DENALI_PI_26__PI_16BIT_DRAM_CONNECT

#define LPDDR4__DENALI_PI_27_READ_MASK                               0x3F030F00U
#define LPDDR4__DENALI_PI_27_WRITE_MASK                              0x3F030F00U
#define LPDDR4__DENALI_PI_27__PI_WRLVL_REQ_MASK                      0x00000001U
#define LPDDR4__DENALI_PI_27__PI_WRLVL_REQ_SHIFT                              0U
#define LPDDR4__DENALI_PI_27__PI_WRLVL_REQ_WIDTH                              1U
#define LPDDR4__DENALI_PI_27__PI_WRLVL_REQ_WOCLR                              0U
#define LPDDR4__DENALI_PI_27__PI_WRLVL_REQ_WOSET                              0U
#define LPDDR4__PI_WRLVL_REQ__REG DENALI_PI_27
#define LPDDR4__PI_WRLVL_REQ__FLD LPDDR4__DENALI_PI_27__PI_WRLVL_REQ

#define LPDDR4__DENALI_PI_27__PI_WRLVL_CS_SW_MASK                    0x00000F00U
#define LPDDR4__DENALI_PI_27__PI_WRLVL_CS_SW_SHIFT                            8U
#define LPDDR4__DENALI_PI_27__PI_WRLVL_CS_SW_WIDTH                            4U
#define LPDDR4__PI_WRLVL_CS_SW__REG DENALI_PI_27
#define LPDDR4__PI_WRLVL_CS_SW__FLD LPDDR4__DENALI_PI_27__PI_WRLVL_CS_SW

#define LPDDR4__DENALI_PI_27__PI_WRLVL_CS_MASK                       0x00030000U
#define LPDDR4__DENALI_PI_27__PI_WRLVL_CS_SHIFT                              16U
#define LPDDR4__DENALI_PI_27__PI_WRLVL_CS_WIDTH                               2U
#define LPDDR4__PI_WRLVL_CS__REG DENALI_PI_27
#define LPDDR4__PI_WRLVL_CS__FLD LPDDR4__DENALI_PI_27__PI_WRLVL_CS

#define LPDDR4__DENALI_PI_27__PI_WLDQSEN_MASK                        0x3F000000U
#define LPDDR4__DENALI_PI_27__PI_WLDQSEN_SHIFT                               24U
#define LPDDR4__DENALI_PI_27__PI_WLDQSEN_WIDTH                                6U
#define LPDDR4__PI_WLDQSEN__REG DENALI_PI_27
#define LPDDR4__PI_WLDQSEN__FLD LPDDR4__DENALI_PI_27__PI_WLDQSEN

#define LPDDR4__DENALI_PI_28_READ_MASK                               0x01FFFF3FU
#define LPDDR4__DENALI_PI_28_WRITE_MASK                              0x01FFFF3FU
#define LPDDR4__DENALI_PI_28__PI_WLMRD_MASK                          0x0000003FU
#define LPDDR4__DENALI_PI_28__PI_WLMRD_SHIFT                                  0U
#define LPDDR4__DENALI_PI_28__PI_WLMRD_WIDTH                                  6U
#define LPDDR4__PI_WLMRD__REG DENALI_PI_28
#define LPDDR4__PI_WLMRD__FLD LPDDR4__DENALI_PI_28__PI_WLMRD

#define LPDDR4__DENALI_PI_28__PI_WRLVL_INTERVAL_MASK                 0x00FFFF00U
#define LPDDR4__DENALI_PI_28__PI_WRLVL_INTERVAL_SHIFT                         8U
#define LPDDR4__DENALI_PI_28__PI_WRLVL_INTERVAL_WIDTH                        16U
#define LPDDR4__PI_WRLVL_INTERVAL__REG DENALI_PI_28
#define LPDDR4__PI_WRLVL_INTERVAL__FLD LPDDR4__DENALI_PI_28__PI_WRLVL_INTERVAL

#define LPDDR4__DENALI_PI_28__PI_WRLVL_ON_SREF_EXIT_MASK             0x01000000U
#define LPDDR4__DENALI_PI_28__PI_WRLVL_ON_SREF_EXIT_SHIFT                    24U
#define LPDDR4__DENALI_PI_28__PI_WRLVL_ON_SREF_EXIT_WIDTH                     1U
#define LPDDR4__DENALI_PI_28__PI_WRLVL_ON_SREF_EXIT_WOCLR                     0U
#define LPDDR4__DENALI_PI_28__PI_WRLVL_ON_SREF_EXIT_WOSET                     0U
#define LPDDR4__PI_WRLVL_ON_SREF_EXIT__REG DENALI_PI_28
#define LPDDR4__PI_WRLVL_ON_SREF_EXIT__FLD LPDDR4__DENALI_PI_28__PI_WRLVL_ON_SREF_EXIT

#define LPDDR4__DENALI_PI_29_READ_MASK                               0x0F010F01U
#define LPDDR4__DENALI_PI_29_WRITE_MASK                              0x0F010F01U
#define LPDDR4__DENALI_PI_29__PI_WRLVL_DISABLE_DFS_MASK              0x00000001U
#define LPDDR4__DENALI_PI_29__PI_WRLVL_DISABLE_DFS_SHIFT                      0U
#define LPDDR4__DENALI_PI_29__PI_WRLVL_DISABLE_DFS_WIDTH                      1U
#define LPDDR4__DENALI_PI_29__PI_WRLVL_DISABLE_DFS_WOCLR                      0U
#define LPDDR4__DENALI_PI_29__PI_WRLVL_DISABLE_DFS_WOSET                      0U
#define LPDDR4__PI_WRLVL_DISABLE_DFS__REG DENALI_PI_29
#define LPDDR4__PI_WRLVL_DISABLE_DFS__FLD LPDDR4__DENALI_PI_29__PI_WRLVL_DISABLE_DFS

#define LPDDR4__DENALI_PI_29__PI_WRLVL_RESP_MASK_MASK                0x00000F00U
#define LPDDR4__DENALI_PI_29__PI_WRLVL_RESP_MASK_SHIFT                        8U
#define LPDDR4__DENALI_PI_29__PI_WRLVL_RESP_MASK_WIDTH                        4U
#define LPDDR4__PI_WRLVL_RESP_MASK__REG DENALI_PI_29
#define LPDDR4__PI_WRLVL_RESP_MASK__FLD LPDDR4__DENALI_PI_29__PI_WRLVL_RESP_MASK

#define LPDDR4__DENALI_PI_29__PI_WRLVL_ROTATE_MASK                   0x00010000U
#define LPDDR4__DENALI_PI_29__PI_WRLVL_ROTATE_SHIFT                          16U
#define LPDDR4__DENALI_PI_29__PI_WRLVL_ROTATE_WIDTH                           1U
#define LPDDR4__DENALI_PI_29__PI_WRLVL_ROTATE_WOCLR                           0U
#define LPDDR4__DENALI_PI_29__PI_WRLVL_ROTATE_WOSET                           0U
#define LPDDR4__PI_WRLVL_ROTATE__REG DENALI_PI_29
#define LPDDR4__PI_WRLVL_ROTATE__FLD LPDDR4__DENALI_PI_29__PI_WRLVL_ROTATE

#define LPDDR4__DENALI_PI_29__PI_WRLVL_CS_MAP_MASK                   0x0F000000U
#define LPDDR4__DENALI_PI_29__PI_WRLVL_CS_MAP_SHIFT                          24U
#define LPDDR4__DENALI_PI_29__PI_WRLVL_CS_MAP_WIDTH                           4U
#define LPDDR4__PI_WRLVL_CS_MAP__REG DENALI_PI_29
#define LPDDR4__PI_WRLVL_CS_MAP__FLD LPDDR4__DENALI_PI_29__PI_WRLVL_CS_MAP

#define LPDDR4__DENALI_PI_30_READ_MASK                               0x00FF0101U
#define LPDDR4__DENALI_PI_30_WRITE_MASK                              0x00FF0101U
#define LPDDR4__DENALI_PI_30__PI_WRLVL_ON_MPD_EXIT_MASK              0x00000001U
#define LPDDR4__DENALI_PI_30__PI_WRLVL_ON_MPD_EXIT_SHIFT                      0U
#define LPDDR4__DENALI_PI_30__PI_WRLVL_ON_MPD_EXIT_WIDTH                      1U
#define LPDDR4__DENALI_PI_30__PI_WRLVL_ON_MPD_EXIT_WOCLR                      0U
#define LPDDR4__DENALI_PI_30__PI_WRLVL_ON_MPD_EXIT_WOSET                      0U
#define LPDDR4__PI_WRLVL_ON_MPD_EXIT__REG DENALI_PI_30
#define LPDDR4__PI_WRLVL_ON_MPD_EXIT__FLD LPDDR4__DENALI_PI_30__PI_WRLVL_ON_MPD_EXIT

#define LPDDR4__DENALI_PI_30__PI_WRLVL_ERROR_STATUS_MASK             0x00000100U
#define LPDDR4__DENALI_PI_30__PI_WRLVL_ERROR_STATUS_SHIFT                     8U
#define LPDDR4__DENALI_PI_30__PI_WRLVL_ERROR_STATUS_WIDTH                     1U
#define LPDDR4__DENALI_PI_30__PI_WRLVL_ERROR_STATUS_WOCLR                     0U
#define LPDDR4__DENALI_PI_30__PI_WRLVL_ERROR_STATUS_WOSET                     0U
#define LPDDR4__PI_WRLVL_ERROR_STATUS__REG DENALI_PI_30
#define LPDDR4__PI_WRLVL_ERROR_STATUS__FLD LPDDR4__DENALI_PI_30__PI_WRLVL_ERROR_STATUS

#define LPDDR4__DENALI_PI_30__PI_TDFI_WRLVL_EN_MASK                  0x00FF0000U
#define LPDDR4__DENALI_PI_30__PI_TDFI_WRLVL_EN_SHIFT                         16U
#define LPDDR4__DENALI_PI_30__PI_TDFI_WRLVL_EN_WIDTH                          8U
#define LPDDR4__PI_TDFI_WRLVL_EN__REG DENALI_PI_30
#define LPDDR4__PI_TDFI_WRLVL_EN__FLD LPDDR4__DENALI_PI_30__PI_TDFI_WRLVL_EN

#define LPDDR4__DENALI_PI_31_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_31_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_31__PI_TDFI_WRLVL_RESP_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PI_31__PI_TDFI_WRLVL_RESP_SHIFT                        0U
#define LPDDR4__DENALI_PI_31__PI_TDFI_WRLVL_RESP_WIDTH                       32U
#define LPDDR4__PI_TDFI_WRLVL_RESP__REG DENALI_PI_31
#define LPDDR4__PI_TDFI_WRLVL_RESP__FLD LPDDR4__DENALI_PI_31__PI_TDFI_WRLVL_RESP

#define LPDDR4__DENALI_PI_32_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_32_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_32__PI_TDFI_WRLVL_MAX_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_PI_32__PI_TDFI_WRLVL_MAX_SHIFT                         0U
#define LPDDR4__DENALI_PI_32__PI_TDFI_WRLVL_MAX_WIDTH                        32U
#define LPDDR4__PI_TDFI_WRLVL_MAX__REG DENALI_PI_32
#define LPDDR4__PI_TDFI_WRLVL_MAX__FLD LPDDR4__DENALI_PI_32__PI_TDFI_WRLVL_MAX

#define LPDDR4__DENALI_PI_33_READ_MASK                               0x0F0F0F1FU
#define LPDDR4__DENALI_PI_33_WRITE_MASK                              0x0F0F0F1FU
#define LPDDR4__DENALI_PI_33__PI_WRLVL_STROBE_NUM_MASK               0x0000001FU
#define LPDDR4__DENALI_PI_33__PI_WRLVL_STROBE_NUM_SHIFT                       0U
#define LPDDR4__DENALI_PI_33__PI_WRLVL_STROBE_NUM_WIDTH                       5U
#define LPDDR4__PI_WRLVL_STROBE_NUM__REG DENALI_PI_33
#define LPDDR4__PI_WRLVL_STROBE_NUM__FLD LPDDR4__DENALI_PI_33__PI_WRLVL_STROBE_NUM

#define LPDDR4__DENALI_PI_33__PI_TODTH_WR_MASK                       0x00000F00U
#define LPDDR4__DENALI_PI_33__PI_TODTH_WR_SHIFT                               8U
#define LPDDR4__DENALI_PI_33__PI_TODTH_WR_WIDTH                               4U
#define LPDDR4__PI_TODTH_WR__REG DENALI_PI_33
#define LPDDR4__PI_TODTH_WR__FLD LPDDR4__DENALI_PI_33__PI_TODTH_WR

#define LPDDR4__DENALI_PI_33__PI_TODTH_RD_MASK                       0x000F0000U
#define LPDDR4__DENALI_PI_33__PI_TODTH_RD_SHIFT                              16U
#define LPDDR4__DENALI_PI_33__PI_TODTH_RD_WIDTH                               4U
#define LPDDR4__PI_TODTH_RD__REG DENALI_PI_33
#define LPDDR4__PI_TODTH_RD__FLD LPDDR4__DENALI_PI_33__PI_TODTH_RD

#define LPDDR4__DENALI_PI_33__PI_ODT_VALUE_MASK                      0x0F000000U
#define LPDDR4__DENALI_PI_33__PI_ODT_VALUE_SHIFT                             24U
#define LPDDR4__DENALI_PI_33__PI_ODT_VALUE_WIDTH                              4U
#define LPDDR4__PI_ODT_VALUE__REG DENALI_PI_33
#define LPDDR4__PI_ODT_VALUE__FLD LPDDR4__DENALI_PI_33__PI_ODT_VALUE

#define LPDDR4__DENALI_PI_34_READ_MASK                               0x0000000FU
#define LPDDR4__DENALI_PI_34_WRITE_MASK                              0x0000000FU
#define LPDDR4__DENALI_PI_34__PI_ADDRESS_MIRRORING_MASK              0x0000000FU
#define LPDDR4__DENALI_PI_34__PI_ADDRESS_MIRRORING_SHIFT                      0U
#define LPDDR4__DENALI_PI_34__PI_ADDRESS_MIRRORING_WIDTH                      4U
#define LPDDR4__PI_ADDRESS_MIRRORING__REG DENALI_PI_34
#define LPDDR4__PI_ADDRESS_MIRRORING__FLD LPDDR4__DENALI_PI_34__PI_ADDRESS_MIRRORING

#define LPDDR4__DENALI_PI_35_READ_MASK                               0x03FFFFFFU
#define LPDDR4__DENALI_PI_35_WRITE_MASK                              0x03FFFFFFU
#define LPDDR4__DENALI_PI_35__PI_CA_PARITY_ERROR_INJECT_MASK         0x03FFFFFFU
#define LPDDR4__DENALI_PI_35__PI_CA_PARITY_ERROR_INJECT_SHIFT                 0U
#define LPDDR4__DENALI_PI_35__PI_CA_PARITY_ERROR_INJECT_WIDTH                26U
#define LPDDR4__PI_CA_PARITY_ERROR_INJECT__REG DENALI_PI_35
#define LPDDR4__PI_CA_PARITY_ERROR_INJECT__FLD LPDDR4__DENALI_PI_35__PI_CA_PARITY_ERROR_INJECT

#define LPDDR4__DENALI_PI_36_READ_MASK                               0x00000F07U
#define LPDDR4__DENALI_PI_36_WRITE_MASK                              0x00000F07U
#define LPDDR4__DENALI_PI_36__PI_RESERVED3_MASK                      0x00000007U
#define LPDDR4__DENALI_PI_36__PI_RESERVED3_SHIFT                              0U
#define LPDDR4__DENALI_PI_36__PI_RESERVED3_WIDTH                              3U
#define LPDDR4__PI_RESERVED3__REG DENALI_PI_36
#define LPDDR4__PI_RESERVED3__FLD LPDDR4__DENALI_PI_36__PI_RESERVED3

#define LPDDR4__DENALI_PI_36__PI_RESERVED4_MASK                      0x00000F00U
#define LPDDR4__DENALI_PI_36__PI_RESERVED4_SHIFT                              8U
#define LPDDR4__DENALI_PI_36__PI_RESERVED4_WIDTH                              4U
#define LPDDR4__PI_RESERVED4__REG DENALI_PI_36
#define LPDDR4__PI_RESERVED4__FLD LPDDR4__DENALI_PI_36__PI_RESERVED4

#define LPDDR4__DENALI_PI_36__PI_RDLVL_REQ_MASK                      0x00010000U
#define LPDDR4__DENALI_PI_36__PI_RDLVL_REQ_SHIFT                             16U
#define LPDDR4__DENALI_PI_36__PI_RDLVL_REQ_WIDTH                              1U
#define LPDDR4__DENALI_PI_36__PI_RDLVL_REQ_WOCLR                              0U
#define LPDDR4__DENALI_PI_36__PI_RDLVL_REQ_WOSET                              0U
#define LPDDR4__PI_RDLVL_REQ__REG DENALI_PI_36
#define LPDDR4__PI_RDLVL_REQ__FLD LPDDR4__DENALI_PI_36__PI_RDLVL_REQ

#define LPDDR4__DENALI_PI_36__PI_RDLVL_GATE_REQ_MASK                 0x01000000U
#define LPDDR4__DENALI_PI_36__PI_RDLVL_GATE_REQ_SHIFT                        24U
#define LPDDR4__DENALI_PI_36__PI_RDLVL_GATE_REQ_WIDTH                         1U
#define LPDDR4__DENALI_PI_36__PI_RDLVL_GATE_REQ_WOCLR                         0U
#define LPDDR4__DENALI_PI_36__PI_RDLVL_GATE_REQ_WOSET                         0U
#define LPDDR4__PI_RDLVL_GATE_REQ__REG DENALI_PI_36
#define LPDDR4__PI_RDLVL_GATE_REQ__FLD LPDDR4__DENALI_PI_36__PI_RDLVL_GATE_REQ

#define LPDDR4__DENALI_PI_37_READ_MASK                               0x0000030FU
#define LPDDR4__DENALI_PI_37_WRITE_MASK                              0x0000030FU
#define LPDDR4__DENALI_PI_37__PI_RDLVL_CS_SW_MASK                    0x0000000FU
#define LPDDR4__DENALI_PI_37__PI_RDLVL_CS_SW_SHIFT                            0U
#define LPDDR4__DENALI_PI_37__PI_RDLVL_CS_SW_WIDTH                            4U
#define LPDDR4__PI_RDLVL_CS_SW__REG DENALI_PI_37
#define LPDDR4__PI_RDLVL_CS_SW__FLD LPDDR4__DENALI_PI_37__PI_RDLVL_CS_SW

#define LPDDR4__DENALI_PI_37__PI_RDLVL_CS_MASK                       0x00000300U
#define LPDDR4__DENALI_PI_37__PI_RDLVL_CS_SHIFT                               8U
#define LPDDR4__DENALI_PI_37__PI_RDLVL_CS_WIDTH                               2U
#define LPDDR4__PI_RDLVL_CS__REG DENALI_PI_37
#define LPDDR4__PI_RDLVL_CS__FLD LPDDR4__DENALI_PI_37__PI_RDLVL_CS

#define LPDDR4__DENALI_PI_38_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_38_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_38__PI_RDLVL_PAT_0_MASK                    0xFFFFFFFFU
#define LPDDR4__DENALI_PI_38__PI_RDLVL_PAT_0_SHIFT                            0U
#define LPDDR4__DENALI_PI_38__PI_RDLVL_PAT_0_WIDTH                           32U
#define LPDDR4__PI_RDLVL_PAT_0__REG DENALI_PI_38
#define LPDDR4__PI_RDLVL_PAT_0__FLD LPDDR4__DENALI_PI_38__PI_RDLVL_PAT_0

#define LPDDR4__DENALI_PI_39_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_39_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_39__PI_RDLVL_PAT_1_MASK                    0xFFFFFFFFU
#define LPDDR4__DENALI_PI_39__PI_RDLVL_PAT_1_SHIFT                            0U
#define LPDDR4__DENALI_PI_39__PI_RDLVL_PAT_1_WIDTH                           32U
#define LPDDR4__PI_RDLVL_PAT_1__REG DENALI_PI_39
#define LPDDR4__PI_RDLVL_PAT_1__FLD LPDDR4__DENALI_PI_39__PI_RDLVL_PAT_1

#define LPDDR4__DENALI_PI_40_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_40_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_40__PI_RDLVL_PAT_2_MASK                    0xFFFFFFFFU
#define LPDDR4__DENALI_PI_40__PI_RDLVL_PAT_2_SHIFT                            0U
#define LPDDR4__DENALI_PI_40__PI_RDLVL_PAT_2_WIDTH                           32U
#define LPDDR4__PI_RDLVL_PAT_2__REG DENALI_PI_40
#define LPDDR4__PI_RDLVL_PAT_2__FLD LPDDR4__DENALI_PI_40__PI_RDLVL_PAT_2

#define LPDDR4__DENALI_PI_41_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_41_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_41__PI_RDLVL_PAT_3_MASK                    0xFFFFFFFFU
#define LPDDR4__DENALI_PI_41__PI_RDLVL_PAT_3_SHIFT                            0U
#define LPDDR4__DENALI_PI_41__PI_RDLVL_PAT_3_WIDTH                           32U
#define LPDDR4__PI_RDLVL_PAT_3__REG DENALI_PI_41
#define LPDDR4__PI_RDLVL_PAT_3__FLD LPDDR4__DENALI_PI_41__PI_RDLVL_PAT_3

#define LPDDR4__DENALI_PI_42_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_42_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_42__PI_RDLVL_PAT_4_MASK                    0xFFFFFFFFU
#define LPDDR4__DENALI_PI_42__PI_RDLVL_PAT_4_SHIFT                            0U
#define LPDDR4__DENALI_PI_42__PI_RDLVL_PAT_4_WIDTH                           32U
#define LPDDR4__PI_RDLVL_PAT_4__REG DENALI_PI_42
#define LPDDR4__PI_RDLVL_PAT_4__FLD LPDDR4__DENALI_PI_42__PI_RDLVL_PAT_4

#define LPDDR4__DENALI_PI_43_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_43_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_43__PI_RDLVL_PAT_5_MASK                    0xFFFFFFFFU
#define LPDDR4__DENALI_PI_43__PI_RDLVL_PAT_5_SHIFT                            0U
#define LPDDR4__DENALI_PI_43__PI_RDLVL_PAT_5_WIDTH                           32U
#define LPDDR4__PI_RDLVL_PAT_5__REG DENALI_PI_43
#define LPDDR4__PI_RDLVL_PAT_5__FLD LPDDR4__DENALI_PI_43__PI_RDLVL_PAT_5

#define LPDDR4__DENALI_PI_44_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_44_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_44__PI_RDLVL_PAT_6_MASK                    0xFFFFFFFFU
#define LPDDR4__DENALI_PI_44__PI_RDLVL_PAT_6_SHIFT                            0U
#define LPDDR4__DENALI_PI_44__PI_RDLVL_PAT_6_WIDTH                           32U
#define LPDDR4__PI_RDLVL_PAT_6__REG DENALI_PI_44
#define LPDDR4__PI_RDLVL_PAT_6__FLD LPDDR4__DENALI_PI_44__PI_RDLVL_PAT_6

#define LPDDR4__DENALI_PI_45_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_45_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_45__PI_RDLVL_PAT_7_MASK                    0xFFFFFFFFU
#define LPDDR4__DENALI_PI_45__PI_RDLVL_PAT_7_SHIFT                            0U
#define LPDDR4__DENALI_PI_45__PI_RDLVL_PAT_7_WIDTH                           32U
#define LPDDR4__PI_RDLVL_PAT_7__REG DENALI_PI_45
#define LPDDR4__PI_RDLVL_PAT_7__FLD LPDDR4__DENALI_PI_45__PI_RDLVL_PAT_7

#define LPDDR4__DENALI_PI_46_READ_MASK                               0x0101010FU
#define LPDDR4__DENALI_PI_46_WRITE_MASK                              0x0101010FU
#define LPDDR4__DENALI_PI_46__PI_RDLVL_SEQ_EN_MASK                   0x0000000FU
#define LPDDR4__DENALI_PI_46__PI_RDLVL_SEQ_EN_SHIFT                           0U
#define LPDDR4__DENALI_PI_46__PI_RDLVL_SEQ_EN_WIDTH                           4U
#define LPDDR4__PI_RDLVL_SEQ_EN__REG DENALI_PI_46
#define LPDDR4__PI_RDLVL_SEQ_EN__FLD LPDDR4__DENALI_PI_46__PI_RDLVL_SEQ_EN

#define LPDDR4__DENALI_PI_46__PI_RDLVL_ON_SREF_EXIT_MASK             0x00000100U
#define LPDDR4__DENALI_PI_46__PI_RDLVL_ON_SREF_EXIT_SHIFT                     8U
#define LPDDR4__DENALI_PI_46__PI_RDLVL_ON_SREF_EXIT_WIDTH                     1U
#define LPDDR4__DENALI_PI_46__PI_RDLVL_ON_SREF_EXIT_WOCLR                     0U
#define LPDDR4__DENALI_PI_46__PI_RDLVL_ON_SREF_EXIT_WOSET                     0U
#define LPDDR4__PI_RDLVL_ON_SREF_EXIT__REG DENALI_PI_46
#define LPDDR4__PI_RDLVL_ON_SREF_EXIT__FLD LPDDR4__DENALI_PI_46__PI_RDLVL_ON_SREF_EXIT

#define LPDDR4__DENALI_PI_46__PI_RDLVL_DISABLE_DFS_MASK              0x00010000U
#define LPDDR4__DENALI_PI_46__PI_RDLVL_DISABLE_DFS_SHIFT                     16U
#define LPDDR4__DENALI_PI_46__PI_RDLVL_DISABLE_DFS_WIDTH                      1U
#define LPDDR4__DENALI_PI_46__PI_RDLVL_DISABLE_DFS_WOCLR                      0U
#define LPDDR4__DENALI_PI_46__PI_RDLVL_DISABLE_DFS_WOSET                      0U
#define LPDDR4__PI_RDLVL_DISABLE_DFS__REG DENALI_PI_46
#define LPDDR4__PI_RDLVL_DISABLE_DFS__FLD LPDDR4__DENALI_PI_46__PI_RDLVL_DISABLE_DFS

#define LPDDR4__DENALI_PI_46__PI_RDLVL_GATE_ON_SREF_EXIT_MASK        0x01000000U
#define LPDDR4__DENALI_PI_46__PI_RDLVL_GATE_ON_SREF_EXIT_SHIFT               24U
#define LPDDR4__DENALI_PI_46__PI_RDLVL_GATE_ON_SREF_EXIT_WIDTH                1U
#define LPDDR4__DENALI_PI_46__PI_RDLVL_GATE_ON_SREF_EXIT_WOCLR                0U
#define LPDDR4__DENALI_PI_46__PI_RDLVL_GATE_ON_SREF_EXIT_WOSET                0U
#define LPDDR4__PI_RDLVL_GATE_ON_SREF_EXIT__REG DENALI_PI_46
#define LPDDR4__PI_RDLVL_GATE_ON_SREF_EXIT__FLD LPDDR4__DENALI_PI_46__PI_RDLVL_GATE_ON_SREF_EXIT

#define LPDDR4__DENALI_PI_47_READ_MASK                               0x01010101U
#define LPDDR4__DENALI_PI_47_WRITE_MASK                              0x01010101U
#define LPDDR4__DENALI_PI_47__PI_RDLVL_GATE_DISABLE_DFS_MASK         0x00000001U
#define LPDDR4__DENALI_PI_47__PI_RDLVL_GATE_DISABLE_DFS_SHIFT                 0U
#define LPDDR4__DENALI_PI_47__PI_RDLVL_GATE_DISABLE_DFS_WIDTH                 1U
#define LPDDR4__DENALI_PI_47__PI_RDLVL_GATE_DISABLE_DFS_WOCLR                 0U
#define LPDDR4__DENALI_PI_47__PI_RDLVL_GATE_DISABLE_DFS_WOSET                 0U
#define LPDDR4__PI_RDLVL_GATE_DISABLE_DFS__REG DENALI_PI_47
#define LPDDR4__PI_RDLVL_GATE_DISABLE_DFS__FLD LPDDR4__DENALI_PI_47__PI_RDLVL_GATE_DISABLE_DFS

#define LPDDR4__DENALI_PI_47__PI_RDLVL_ON_MPD_EXIT_MASK              0x00000100U
#define LPDDR4__DENALI_PI_47__PI_RDLVL_ON_MPD_EXIT_SHIFT                      8U
#define LPDDR4__DENALI_PI_47__PI_RDLVL_ON_MPD_EXIT_WIDTH                      1U
#define LPDDR4__DENALI_PI_47__PI_RDLVL_ON_MPD_EXIT_WOCLR                      0U
#define LPDDR4__DENALI_PI_47__PI_RDLVL_ON_MPD_EXIT_WOSET                      0U
#define LPDDR4__PI_RDLVL_ON_MPD_EXIT__REG DENALI_PI_47
#define LPDDR4__PI_RDLVL_ON_MPD_EXIT__FLD LPDDR4__DENALI_PI_47__PI_RDLVL_ON_MPD_EXIT

#define LPDDR4__DENALI_PI_47__PI_RDLVL_GATE_ON_MPD_EXIT_MASK         0x00010000U
#define LPDDR4__DENALI_PI_47__PI_RDLVL_GATE_ON_MPD_EXIT_SHIFT                16U
#define LPDDR4__DENALI_PI_47__PI_RDLVL_GATE_ON_MPD_EXIT_WIDTH                 1U
#define LPDDR4__DENALI_PI_47__PI_RDLVL_GATE_ON_MPD_EXIT_WOCLR                 0U
#define LPDDR4__DENALI_PI_47__PI_RDLVL_GATE_ON_MPD_EXIT_WOSET                 0U
#define LPDDR4__PI_RDLVL_GATE_ON_MPD_EXIT__REG DENALI_PI_47
#define LPDDR4__PI_RDLVL_GATE_ON_MPD_EXIT__FLD LPDDR4__DENALI_PI_47__PI_RDLVL_GATE_ON_MPD_EXIT

#define LPDDR4__DENALI_PI_47__PI_RDLVL_ROTATE_MASK                   0x01000000U
#define LPDDR4__DENALI_PI_47__PI_RDLVL_ROTATE_SHIFT                          24U
#define LPDDR4__DENALI_PI_47__PI_RDLVL_ROTATE_WIDTH                           1U
#define LPDDR4__DENALI_PI_47__PI_RDLVL_ROTATE_WOCLR                           0U
#define LPDDR4__DENALI_PI_47__PI_RDLVL_ROTATE_WOSET                           0U
#define LPDDR4__PI_RDLVL_ROTATE__REG DENALI_PI_47
#define LPDDR4__PI_RDLVL_ROTATE__FLD LPDDR4__DENALI_PI_47__PI_RDLVL_ROTATE

#define LPDDR4__DENALI_PI_48_READ_MASK                               0x000F0F01U
#define LPDDR4__DENALI_PI_48_WRITE_MASK                              0x000F0F01U
#define LPDDR4__DENALI_PI_48__PI_RDLVL_GATE_ROTATE_MASK              0x00000001U
#define LPDDR4__DENALI_PI_48__PI_RDLVL_GATE_ROTATE_SHIFT                      0U
#define LPDDR4__DENALI_PI_48__PI_RDLVL_GATE_ROTATE_WIDTH                      1U
#define LPDDR4__DENALI_PI_48__PI_RDLVL_GATE_ROTATE_WOCLR                      0U
#define LPDDR4__DENALI_PI_48__PI_RDLVL_GATE_ROTATE_WOSET                      0U
#define LPDDR4__PI_RDLVL_GATE_ROTATE__REG DENALI_PI_48
#define LPDDR4__PI_RDLVL_GATE_ROTATE__FLD LPDDR4__DENALI_PI_48__PI_RDLVL_GATE_ROTATE

#define LPDDR4__DENALI_PI_48__PI_RDLVL_CS_MAP_MASK                   0x00000F00U
#define LPDDR4__DENALI_PI_48__PI_RDLVL_CS_MAP_SHIFT                           8U
#define LPDDR4__DENALI_PI_48__PI_RDLVL_CS_MAP_WIDTH                           4U
#define LPDDR4__PI_RDLVL_CS_MAP__REG DENALI_PI_48
#define LPDDR4__PI_RDLVL_CS_MAP__FLD LPDDR4__DENALI_PI_48__PI_RDLVL_CS_MAP

#define LPDDR4__DENALI_PI_48__PI_RDLVL_GATE_CS_MAP_MASK              0x000F0000U
#define LPDDR4__DENALI_PI_48__PI_RDLVL_GATE_CS_MAP_SHIFT                     16U
#define LPDDR4__DENALI_PI_48__PI_RDLVL_GATE_CS_MAP_WIDTH                      4U
#define LPDDR4__PI_RDLVL_GATE_CS_MAP__REG DENALI_PI_48
#define LPDDR4__PI_RDLVL_GATE_CS_MAP__FLD LPDDR4__DENALI_PI_48__PI_RDLVL_GATE_CS_MAP

#define LPDDR4__DENALI_PI_49_READ_MASK                               0x000003FFU
#define LPDDR4__DENALI_PI_49_WRITE_MASK                              0x000003FFU
#define LPDDR4__DENALI_PI_49__PI_TDFI_RDLVL_RR_MASK                  0x000003FFU
#define LPDDR4__DENALI_PI_49__PI_TDFI_RDLVL_RR_SHIFT                          0U
#define LPDDR4__DENALI_PI_49__PI_TDFI_RDLVL_RR_WIDTH                         10U
#define LPDDR4__PI_TDFI_RDLVL_RR__REG DENALI_PI_49
#define LPDDR4__PI_TDFI_RDLVL_RR__FLD LPDDR4__DENALI_PI_49__PI_TDFI_RDLVL_RR

#define LPDDR4__DENALI_PI_50_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_50_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_50__PI_TDFI_RDLVL_RESP_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PI_50__PI_TDFI_RDLVL_RESP_SHIFT                        0U
#define LPDDR4__DENALI_PI_50__PI_TDFI_RDLVL_RESP_WIDTH                       32U
#define LPDDR4__PI_TDFI_RDLVL_RESP__REG DENALI_PI_50
#define LPDDR4__PI_TDFI_RDLVL_RESP__FLD LPDDR4__DENALI_PI_50__PI_TDFI_RDLVL_RESP

#define LPDDR4__DENALI_PI_51_READ_MASK                               0x0000FF0FU
#define LPDDR4__DENALI_PI_51_WRITE_MASK                              0x0000FF0FU
#define LPDDR4__DENALI_PI_51__PI_RDLVL_RESP_MASK_MASK                0x0000000FU
#define LPDDR4__DENALI_PI_51__PI_RDLVL_RESP_MASK_SHIFT                        0U
#define LPDDR4__DENALI_PI_51__PI_RDLVL_RESP_MASK_WIDTH                        4U
#define LPDDR4__PI_RDLVL_RESP_MASK__REG DENALI_PI_51
#define LPDDR4__PI_RDLVL_RESP_MASK__FLD LPDDR4__DENALI_PI_51__PI_RDLVL_RESP_MASK

#define LPDDR4__DENALI_PI_51__PI_TDFI_RDLVL_EN_MASK                  0x0000FF00U
#define LPDDR4__DENALI_PI_51__PI_TDFI_RDLVL_EN_SHIFT                          8U
#define LPDDR4__DENALI_PI_51__PI_TDFI_RDLVL_EN_WIDTH                          8U
#define LPDDR4__PI_TDFI_RDLVL_EN__REG DENALI_PI_51
#define LPDDR4__PI_TDFI_RDLVL_EN__FLD LPDDR4__DENALI_PI_51__PI_TDFI_RDLVL_EN

#define LPDDR4__DENALI_PI_52_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_52_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_52__PI_TDFI_RDLVL_MAX_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_PI_52__PI_TDFI_RDLVL_MAX_SHIFT                         0U
#define LPDDR4__DENALI_PI_52__PI_TDFI_RDLVL_MAX_WIDTH                        32U
#define LPDDR4__PI_TDFI_RDLVL_MAX__REG DENALI_PI_52
#define LPDDR4__PI_TDFI_RDLVL_MAX__FLD LPDDR4__DENALI_PI_52__PI_TDFI_RDLVL_MAX

#define LPDDR4__DENALI_PI_53_READ_MASK                               0x00FFFF01U
#define LPDDR4__DENALI_PI_53_WRITE_MASK                              0x00FFFF01U
#define LPDDR4__DENALI_PI_53__PI_RDLVL_ERROR_STATUS_MASK             0x00000001U
#define LPDDR4__DENALI_PI_53__PI_RDLVL_ERROR_STATUS_SHIFT                     0U
#define LPDDR4__DENALI_PI_53__PI_RDLVL_ERROR_STATUS_WIDTH                     1U
#define LPDDR4__DENALI_PI_53__PI_RDLVL_ERROR_STATUS_WOCLR                     0U
#define LPDDR4__DENALI_PI_53__PI_RDLVL_ERROR_STATUS_WOSET                     0U
#define LPDDR4__PI_RDLVL_ERROR_STATUS__REG DENALI_PI_53
#define LPDDR4__PI_RDLVL_ERROR_STATUS__FLD LPDDR4__DENALI_PI_53__PI_RDLVL_ERROR_STATUS

#define LPDDR4__DENALI_PI_53__PI_RDLVL_INTERVAL_MASK                 0x00FFFF00U
#define LPDDR4__DENALI_PI_53__PI_RDLVL_INTERVAL_SHIFT                         8U
#define LPDDR4__DENALI_PI_53__PI_RDLVL_INTERVAL_WIDTH                        16U
#define LPDDR4__PI_RDLVL_INTERVAL__REG DENALI_PI_53
#define LPDDR4__PI_RDLVL_INTERVAL__FLD LPDDR4__DENALI_PI_53__PI_RDLVL_INTERVAL

#define LPDDR4__DENALI_PI_54_READ_MASK                               0x0F0FFFFFU
#define LPDDR4__DENALI_PI_54_WRITE_MASK                              0x0F0FFFFFU
#define LPDDR4__DENALI_PI_54__PI_RDLVL_GATE_INTERVAL_MASK            0x0000FFFFU
#define LPDDR4__DENALI_PI_54__PI_RDLVL_GATE_INTERVAL_SHIFT                    0U
#define LPDDR4__DENALI_PI_54__PI_RDLVL_GATE_INTERVAL_WIDTH                   16U
#define LPDDR4__PI_RDLVL_GATE_INTERVAL__REG DENALI_PI_54
#define LPDDR4__PI_RDLVL_GATE_INTERVAL__FLD LPDDR4__DENALI_PI_54__PI_RDLVL_GATE_INTERVAL

#define LPDDR4__DENALI_PI_54__PI_RDLVL_PATTERN_START_MASK            0x000F0000U
#define LPDDR4__DENALI_PI_54__PI_RDLVL_PATTERN_START_SHIFT                   16U
#define LPDDR4__DENALI_PI_54__PI_RDLVL_PATTERN_START_WIDTH                    4U
#define LPDDR4__PI_RDLVL_PATTERN_START__REG DENALI_PI_54
#define LPDDR4__PI_RDLVL_PATTERN_START__FLD LPDDR4__DENALI_PI_54__PI_RDLVL_PATTERN_START

#define LPDDR4__DENALI_PI_54__PI_RDLVL_PATTERN_NUM_MASK              0x0F000000U
#define LPDDR4__DENALI_PI_54__PI_RDLVL_PATTERN_NUM_SHIFT                     24U
#define LPDDR4__DENALI_PI_54__PI_RDLVL_PATTERN_NUM_WIDTH                      4U
#define LPDDR4__PI_RDLVL_PATTERN_NUM__REG DENALI_PI_54
#define LPDDR4__PI_RDLVL_PATTERN_NUM__FLD LPDDR4__DENALI_PI_54__PI_RDLVL_PATTERN_NUM

#define LPDDR4__DENALI_PI_55_READ_MASK                               0x01011F1FU
#define LPDDR4__DENALI_PI_55_WRITE_MASK                              0x01011F1FU
#define LPDDR4__DENALI_PI_55__PI_RDLVL_STROBE_NUM_MASK               0x0000001FU
#define LPDDR4__DENALI_PI_55__PI_RDLVL_STROBE_NUM_SHIFT                       0U
#define LPDDR4__DENALI_PI_55__PI_RDLVL_STROBE_NUM_WIDTH                       5U
#define LPDDR4__PI_RDLVL_STROBE_NUM__REG DENALI_PI_55
#define LPDDR4__PI_RDLVL_STROBE_NUM__FLD LPDDR4__DENALI_PI_55__PI_RDLVL_STROBE_NUM

#define LPDDR4__DENALI_PI_55__PI_RDLVL_GATE_STROBE_NUM_MASK          0x00001F00U
#define LPDDR4__DENALI_PI_55__PI_RDLVL_GATE_STROBE_NUM_SHIFT                  8U
#define LPDDR4__DENALI_PI_55__PI_RDLVL_GATE_STROBE_NUM_WIDTH                  5U
#define LPDDR4__PI_RDLVL_GATE_STROBE_NUM__REG DENALI_PI_55
#define LPDDR4__PI_RDLVL_GATE_STROBE_NUM__FLD LPDDR4__DENALI_PI_55__PI_RDLVL_GATE_STROBE_NUM

#define LPDDR4__DENALI_PI_55__PI_RD_PREAMBLE_TRAINING_EN_MASK        0x00010000U
#define LPDDR4__DENALI_PI_55__PI_RD_PREAMBLE_TRAINING_EN_SHIFT               16U
#define LPDDR4__DENALI_PI_55__PI_RD_PREAMBLE_TRAINING_EN_WIDTH                1U
#define LPDDR4__DENALI_PI_55__PI_RD_PREAMBLE_TRAINING_EN_WOCLR                0U
#define LPDDR4__DENALI_PI_55__PI_RD_PREAMBLE_TRAINING_EN_WOSET                0U
#define LPDDR4__PI_RD_PREAMBLE_TRAINING_EN__REG DENALI_PI_55
#define LPDDR4__PI_RD_PREAMBLE_TRAINING_EN__FLD LPDDR4__DENALI_PI_55__PI_RD_PREAMBLE_TRAINING_EN

#define LPDDR4__DENALI_PI_55__PI_REG_DIMM_ENABLE_MASK                0x01000000U
#define LPDDR4__DENALI_PI_55__PI_REG_DIMM_ENABLE_SHIFT                       24U
#define LPDDR4__DENALI_PI_55__PI_REG_DIMM_ENABLE_WIDTH                        1U
#define LPDDR4__DENALI_PI_55__PI_REG_DIMM_ENABLE_WOCLR                        0U
#define LPDDR4__DENALI_PI_55__PI_REG_DIMM_ENABLE_WOSET                        0U
#define LPDDR4__PI_REG_DIMM_ENABLE__REG DENALI_PI_55
#define LPDDR4__PI_REG_DIMM_ENABLE__FLD LPDDR4__DENALI_PI_55__PI_REG_DIMM_ENABLE

#define LPDDR4__DENALI_PI_56_READ_MASK                               0x0F00FFFFU
#define LPDDR4__DENALI_PI_56_WRITE_MASK                              0x0F00FFFFU
#define LPDDR4__DENALI_PI_56__PI_TDFI_RDDATA_EN_MASK                 0x000000FFU
#define LPDDR4__DENALI_PI_56__PI_TDFI_RDDATA_EN_SHIFT                         0U
#define LPDDR4__DENALI_PI_56__PI_TDFI_RDDATA_EN_WIDTH                         8U
#define LPDDR4__PI_TDFI_RDDATA_EN__REG DENALI_PI_56
#define LPDDR4__PI_TDFI_RDDATA_EN__FLD LPDDR4__DENALI_PI_56__PI_TDFI_RDDATA_EN

#define LPDDR4__DENALI_PI_56__PI_TDFI_PHY_WRLAT_MASK                 0x0000FF00U
#define LPDDR4__DENALI_PI_56__PI_TDFI_PHY_WRLAT_SHIFT                         8U
#define LPDDR4__DENALI_PI_56__PI_TDFI_PHY_WRLAT_WIDTH                         8U
#define LPDDR4__PI_TDFI_PHY_WRLAT__REG DENALI_PI_56
#define LPDDR4__PI_TDFI_PHY_WRLAT__FLD LPDDR4__DENALI_PI_56__PI_TDFI_PHY_WRLAT

#define LPDDR4__DENALI_PI_56__PI_CALVL_REQ_MASK                      0x00010000U
#define LPDDR4__DENALI_PI_56__PI_CALVL_REQ_SHIFT                             16U
#define LPDDR4__DENALI_PI_56__PI_CALVL_REQ_WIDTH                              1U
#define LPDDR4__DENALI_PI_56__PI_CALVL_REQ_WOCLR                              0U
#define LPDDR4__DENALI_PI_56__PI_CALVL_REQ_WOSET                              0U
#define LPDDR4__PI_CALVL_REQ__REG DENALI_PI_56
#define LPDDR4__PI_CALVL_REQ__FLD LPDDR4__DENALI_PI_56__PI_CALVL_REQ

#define LPDDR4__DENALI_PI_56__PI_CALVL_CS_SW_MASK                    0x0F000000U
#define LPDDR4__DENALI_PI_56__PI_CALVL_CS_SW_SHIFT                           24U
#define LPDDR4__DENALI_PI_56__PI_CALVL_CS_SW_WIDTH                            4U
#define LPDDR4__PI_CALVL_CS_SW__REG DENALI_PI_56
#define LPDDR4__PI_CALVL_CS_SW__FLD LPDDR4__DENALI_PI_56__PI_CALVL_CS_SW

#define LPDDR4__DENALI_PI_57_READ_MASK                               0x030F0103U
#define LPDDR4__DENALI_PI_57_WRITE_MASK                              0x030F0103U
#define LPDDR4__DENALI_PI_57__PI_CALVL_CS_MASK                       0x00000003U
#define LPDDR4__DENALI_PI_57__PI_CALVL_CS_SHIFT                               0U
#define LPDDR4__DENALI_PI_57__PI_CALVL_CS_WIDTH                               2U
#define LPDDR4__PI_CALVL_CS__REG DENALI_PI_57
#define LPDDR4__PI_CALVL_CS__FLD LPDDR4__DENALI_PI_57__PI_CALVL_CS

#define LPDDR4__DENALI_PI_57__PI_RESERVED5_MASK                      0x00000100U
#define LPDDR4__DENALI_PI_57__PI_RESERVED5_SHIFT                              8U
#define LPDDR4__DENALI_PI_57__PI_RESERVED5_WIDTH                              1U
#define LPDDR4__DENALI_PI_57__PI_RESERVED5_WOCLR                              0U
#define LPDDR4__DENALI_PI_57__PI_RESERVED5_WOSET                              0U
#define LPDDR4__PI_RESERVED5__REG DENALI_PI_57
#define LPDDR4__PI_RESERVED5__FLD LPDDR4__DENALI_PI_57__PI_RESERVED5

#define LPDDR4__DENALI_PI_57__PI_RESERVED6_MASK                      0x000F0000U
#define LPDDR4__DENALI_PI_57__PI_RESERVED6_SHIFT                             16U
#define LPDDR4__DENALI_PI_57__PI_RESERVED6_WIDTH                              4U
#define LPDDR4__PI_RESERVED6__REG DENALI_PI_57
#define LPDDR4__PI_RESERVED6__FLD LPDDR4__DENALI_PI_57__PI_RESERVED6

#define LPDDR4__DENALI_PI_57__PI_CALVL_SEQ_EN_MASK                   0x03000000U
#define LPDDR4__DENALI_PI_57__PI_CALVL_SEQ_EN_SHIFT                          24U
#define LPDDR4__DENALI_PI_57__PI_CALVL_SEQ_EN_WIDTH                           2U
#define LPDDR4__PI_CALVL_SEQ_EN__REG DENALI_PI_57
#define LPDDR4__PI_CALVL_SEQ_EN__FLD LPDDR4__DENALI_PI_57__PI_CALVL_SEQ_EN

#define LPDDR4__DENALI_PI_58_READ_MASK                               0x01010101U
#define LPDDR4__DENALI_PI_58_WRITE_MASK                              0x01010101U
#define LPDDR4__DENALI_PI_58__PI_CALVL_PERIODIC_MASK                 0x00000001U
#define LPDDR4__DENALI_PI_58__PI_CALVL_PERIODIC_SHIFT                         0U
#define LPDDR4__DENALI_PI_58__PI_CALVL_PERIODIC_WIDTH                         1U
#define LPDDR4__DENALI_PI_58__PI_CALVL_PERIODIC_WOCLR                         0U
#define LPDDR4__DENALI_PI_58__PI_CALVL_PERIODIC_WOSET                         0U
#define LPDDR4__PI_CALVL_PERIODIC__REG DENALI_PI_58
#define LPDDR4__PI_CALVL_PERIODIC__FLD LPDDR4__DENALI_PI_58__PI_CALVL_PERIODIC

#define LPDDR4__DENALI_PI_58__PI_CALVL_ON_SREF_EXIT_MASK             0x00000100U
#define LPDDR4__DENALI_PI_58__PI_CALVL_ON_SREF_EXIT_SHIFT                     8U
#define LPDDR4__DENALI_PI_58__PI_CALVL_ON_SREF_EXIT_WIDTH                     1U
#define LPDDR4__DENALI_PI_58__PI_CALVL_ON_SREF_EXIT_WOCLR                     0U
#define LPDDR4__DENALI_PI_58__PI_CALVL_ON_SREF_EXIT_WOSET                     0U
#define LPDDR4__PI_CALVL_ON_SREF_EXIT__REG DENALI_PI_58
#define LPDDR4__PI_CALVL_ON_SREF_EXIT__FLD LPDDR4__DENALI_PI_58__PI_CALVL_ON_SREF_EXIT

#define LPDDR4__DENALI_PI_58__PI_CALVL_DISABLE_DFS_MASK              0x00010000U
#define LPDDR4__DENALI_PI_58__PI_CALVL_DISABLE_DFS_SHIFT                     16U
#define LPDDR4__DENALI_PI_58__PI_CALVL_DISABLE_DFS_WIDTH                      1U
#define LPDDR4__DENALI_PI_58__PI_CALVL_DISABLE_DFS_WOCLR                      0U
#define LPDDR4__DENALI_PI_58__PI_CALVL_DISABLE_DFS_WOSET                      0U
#define LPDDR4__PI_CALVL_DISABLE_DFS__REG DENALI_PI_58
#define LPDDR4__PI_CALVL_DISABLE_DFS__FLD LPDDR4__DENALI_PI_58__PI_CALVL_DISABLE_DFS

#define LPDDR4__DENALI_PI_58__PI_CALVL_ROTATE_MASK                   0x01000000U
#define LPDDR4__DENALI_PI_58__PI_CALVL_ROTATE_SHIFT                          24U
#define LPDDR4__DENALI_PI_58__PI_CALVL_ROTATE_WIDTH                           1U
#define LPDDR4__DENALI_PI_58__PI_CALVL_ROTATE_WOCLR                           0U
#define LPDDR4__DENALI_PI_58__PI_CALVL_ROTATE_WOSET                           0U
#define LPDDR4__PI_CALVL_ROTATE__REG DENALI_PI_58
#define LPDDR4__PI_CALVL_ROTATE__FLD LPDDR4__DENALI_PI_58__PI_CALVL_ROTATE

#define LPDDR4__DENALI_PI_59_READ_MASK                               0x0000FF0FU
#define LPDDR4__DENALI_PI_59_WRITE_MASK                              0x0000FF0FU
#define LPDDR4__DENALI_PI_59__PI_CALVL_CS_MAP_MASK                   0x0000000FU
#define LPDDR4__DENALI_PI_59__PI_CALVL_CS_MAP_SHIFT                           0U
#define LPDDR4__DENALI_PI_59__PI_CALVL_CS_MAP_WIDTH                           4U
#define LPDDR4__PI_CALVL_CS_MAP__REG DENALI_PI_59
#define LPDDR4__PI_CALVL_CS_MAP__FLD LPDDR4__DENALI_PI_59__PI_CALVL_CS_MAP

#define LPDDR4__DENALI_PI_59__PI_TDFI_CALVL_EN_MASK                  0x0000FF00U
#define LPDDR4__DENALI_PI_59__PI_TDFI_CALVL_EN_SHIFT                          8U
#define LPDDR4__DENALI_PI_59__PI_TDFI_CALVL_EN_WIDTH                          8U
#define LPDDR4__PI_TDFI_CALVL_EN__REG DENALI_PI_59
#define LPDDR4__PI_TDFI_CALVL_EN__FLD LPDDR4__DENALI_PI_59__PI_TDFI_CALVL_EN

#define LPDDR4__DENALI_PI_60_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_60_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_60__PI_TDFI_CALVL_RESP_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PI_60__PI_TDFI_CALVL_RESP_SHIFT                        0U
#define LPDDR4__DENALI_PI_60__PI_TDFI_CALVL_RESP_WIDTH                       32U
#define LPDDR4__PI_TDFI_CALVL_RESP__REG DENALI_PI_60
#define LPDDR4__PI_TDFI_CALVL_RESP__FLD LPDDR4__DENALI_PI_60__PI_TDFI_CALVL_RESP

#define LPDDR4__DENALI_PI_61_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_61_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_61__PI_TDFI_CALVL_MAX_MASK                 0xFFFFFFFFU
#define LPDDR4__DENALI_PI_61__PI_TDFI_CALVL_MAX_SHIFT                         0U
#define LPDDR4__DENALI_PI_61__PI_TDFI_CALVL_MAX_WIDTH                        32U
#define LPDDR4__PI_TDFI_CALVL_MAX__REG DENALI_PI_61
#define LPDDR4__PI_TDFI_CALVL_MAX__FLD LPDDR4__DENALI_PI_61__PI_TDFI_CALVL_MAX

#define LPDDR4__DENALI_PI_62_READ_MASK                               0xFFFF0301U
#define LPDDR4__DENALI_PI_62_WRITE_MASK                              0xFFFF0301U
#define LPDDR4__DENALI_PI_62__PI_CALVL_RESP_MASK_MASK                0x00000001U
#define LPDDR4__DENALI_PI_62__PI_CALVL_RESP_MASK_SHIFT                        0U
#define LPDDR4__DENALI_PI_62__PI_CALVL_RESP_MASK_WIDTH                        1U
#define LPDDR4__DENALI_PI_62__PI_CALVL_RESP_MASK_WOCLR                        0U
#define LPDDR4__DENALI_PI_62__PI_CALVL_RESP_MASK_WOSET                        0U
#define LPDDR4__PI_CALVL_RESP_MASK__REG DENALI_PI_62
#define LPDDR4__PI_CALVL_RESP_MASK__FLD LPDDR4__DENALI_PI_62__PI_CALVL_RESP_MASK

#define LPDDR4__DENALI_PI_62__PI_CALVL_ERROR_STATUS_MASK             0x00000300U
#define LPDDR4__DENALI_PI_62__PI_CALVL_ERROR_STATUS_SHIFT                     8U
#define LPDDR4__DENALI_PI_62__PI_CALVL_ERROR_STATUS_WIDTH                     2U
#define LPDDR4__PI_CALVL_ERROR_STATUS__REG DENALI_PI_62
#define LPDDR4__PI_CALVL_ERROR_STATUS__FLD LPDDR4__DENALI_PI_62__PI_CALVL_ERROR_STATUS

#define LPDDR4__DENALI_PI_62__PI_CALVL_INTERVAL_MASK                 0xFFFF0000U
#define LPDDR4__DENALI_PI_62__PI_CALVL_INTERVAL_SHIFT                        16U
#define LPDDR4__DENALI_PI_62__PI_CALVL_INTERVAL_WIDTH                        16U
#define LPDDR4__PI_CALVL_INTERVAL__REG DENALI_PI_62
#define LPDDR4__PI_CALVL_INTERVAL__FLD LPDDR4__DENALI_PI_62__PI_CALVL_INTERVAL

#define LPDDR4__DENALI_PI_63_READ_MASK                               0x1F1F3F1FU
#define LPDDR4__DENALI_PI_63_WRITE_MASK                              0x1F1F3F1FU
#define LPDDR4__DENALI_PI_63__PI_TCACKEL_MASK                        0x0000001FU
#define LPDDR4__DENALI_PI_63__PI_TCACKEL_SHIFT                                0U
#define LPDDR4__DENALI_PI_63__PI_TCACKEL_WIDTH                                5U
#define LPDDR4__PI_TCACKEL__REG DENALI_PI_63
#define LPDDR4__PI_TCACKEL__FLD LPDDR4__DENALI_PI_63__PI_TCACKEL

#define LPDDR4__DENALI_PI_63__PI_TCAMRD_MASK                         0x00003F00U
#define LPDDR4__DENALI_PI_63__PI_TCAMRD_SHIFT                                 8U
#define LPDDR4__DENALI_PI_63__PI_TCAMRD_WIDTH                                 6U
#define LPDDR4__PI_TCAMRD__REG DENALI_PI_63
#define LPDDR4__PI_TCAMRD__FLD LPDDR4__DENALI_PI_63__PI_TCAMRD

#define LPDDR4__DENALI_PI_63__PI_TCACKEH_MASK                        0x001F0000U
#define LPDDR4__DENALI_PI_63__PI_TCACKEH_SHIFT                               16U
#define LPDDR4__DENALI_PI_63__PI_TCACKEH_WIDTH                                5U
#define LPDDR4__PI_TCACKEH__REG DENALI_PI_63
#define LPDDR4__PI_TCACKEH__FLD LPDDR4__DENALI_PI_63__PI_TCACKEH

#define LPDDR4__DENALI_PI_63__PI_TCAEXT_MASK                         0x1F000000U
#define LPDDR4__DENALI_PI_63__PI_TCAEXT_SHIFT                                24U
#define LPDDR4__DENALI_PI_63__PI_TCAEXT_WIDTH                                 5U
#define LPDDR4__PI_TCAEXT__REG DENALI_PI_63
#define LPDDR4__PI_TCAEXT__FLD LPDDR4__DENALI_PI_63__PI_TCAEXT

#define LPDDR4__DENALI_PI_64_READ_MASK                               0xFF0F0F01U
#define LPDDR4__DENALI_PI_64_WRITE_MASK                              0xFF0F0F01U
#define LPDDR4__DENALI_PI_64__PI_CA_TRAIN_VREF_EN_MASK               0x00000001U
#define LPDDR4__DENALI_PI_64__PI_CA_TRAIN_VREF_EN_SHIFT                       0U
#define LPDDR4__DENALI_PI_64__PI_CA_TRAIN_VREF_EN_WIDTH                       1U
#define LPDDR4__DENALI_PI_64__PI_CA_TRAIN_VREF_EN_WOCLR                       0U
#define LPDDR4__DENALI_PI_64__PI_CA_TRAIN_VREF_EN_WOSET                       0U
#define LPDDR4__PI_CA_TRAIN_VREF_EN__REG DENALI_PI_64
#define LPDDR4__PI_CA_TRAIN_VREF_EN__FLD LPDDR4__DENALI_PI_64__PI_CA_TRAIN_VREF_EN

#define LPDDR4__DENALI_PI_64__PI_CALVL_VREF_INITIAL_STEPSIZE_MASK    0x00000F00U
#define LPDDR4__DENALI_PI_64__PI_CALVL_VREF_INITIAL_STEPSIZE_SHIFT            8U
#define LPDDR4__DENALI_PI_64__PI_CALVL_VREF_INITIAL_STEPSIZE_WIDTH            4U
#define LPDDR4__PI_CALVL_VREF_INITIAL_STEPSIZE__REG DENALI_PI_64
#define LPDDR4__PI_CALVL_VREF_INITIAL_STEPSIZE__FLD LPDDR4__DENALI_PI_64__PI_CALVL_VREF_INITIAL_STEPSIZE

#define LPDDR4__DENALI_PI_64__PI_CALVL_VREF_NORMAL_STEPSIZE_MASK     0x000F0000U
#define LPDDR4__DENALI_PI_64__PI_CALVL_VREF_NORMAL_STEPSIZE_SHIFT            16U
#define LPDDR4__DENALI_PI_64__PI_CALVL_VREF_NORMAL_STEPSIZE_WIDTH             4U
#define LPDDR4__PI_CALVL_VREF_NORMAL_STEPSIZE__REG DENALI_PI_64
#define LPDDR4__PI_CALVL_VREF_NORMAL_STEPSIZE__FLD LPDDR4__DENALI_PI_64__PI_CALVL_VREF_NORMAL_STEPSIZE

#define LPDDR4__DENALI_PI_64__PI_TDFI_INIT_START_MIN_MASK            0xFF000000U
#define LPDDR4__DENALI_PI_64__PI_TDFI_INIT_START_MIN_SHIFT                   24U
#define LPDDR4__DENALI_PI_64__PI_TDFI_INIT_START_MIN_WIDTH                    8U
#define LPDDR4__PI_TDFI_INIT_START_MIN__REG DENALI_PI_64
#define LPDDR4__PI_TDFI_INIT_START_MIN__FLD LPDDR4__DENALI_PI_64__PI_TDFI_INIT_START_MIN

#define LPDDR4__DENALI_PI_65_READ_MASK                               0x017F1FFFU
#define LPDDR4__DENALI_PI_65_WRITE_MASK                              0x017F1FFFU
#define LPDDR4__DENALI_PI_65__PI_TCKCKEH_MASK                        0x000000FFU
#define LPDDR4__DENALI_PI_65__PI_TCKCKEH_SHIFT                                0U
#define LPDDR4__DENALI_PI_65__PI_TCKCKEH_WIDTH                                8U
#define LPDDR4__PI_TCKCKEH__REG DENALI_PI_65
#define LPDDR4__PI_TCKCKEH__FLD LPDDR4__DENALI_PI_65__PI_TCKCKEH

#define LPDDR4__DENALI_PI_65__PI_CALVL_STROBE_NUM_MASK               0x00001F00U
#define LPDDR4__DENALI_PI_65__PI_CALVL_STROBE_NUM_SHIFT                       8U
#define LPDDR4__DENALI_PI_65__PI_CALVL_STROBE_NUM_WIDTH                       5U
#define LPDDR4__PI_CALVL_STROBE_NUM__REG DENALI_PI_65
#define LPDDR4__PI_CALVL_STROBE_NUM__FLD LPDDR4__DENALI_PI_65__PI_CALVL_STROBE_NUM

#define LPDDR4__DENALI_PI_65__PI_SW_CA_TRAIN_VREF_MASK               0x007F0000U
#define LPDDR4__DENALI_PI_65__PI_SW_CA_TRAIN_VREF_SHIFT                      16U
#define LPDDR4__DENALI_PI_65__PI_SW_CA_TRAIN_VREF_WIDTH                       7U
#define LPDDR4__PI_SW_CA_TRAIN_VREF__REG DENALI_PI_65
#define LPDDR4__PI_SW_CA_TRAIN_VREF__FLD LPDDR4__DENALI_PI_65__PI_SW_CA_TRAIN_VREF

#define LPDDR4__DENALI_PI_65__PI_REFRESH_BETWEEN_SEGMENT_DISABLE_MASK 0x01000000U
#define LPDDR4__DENALI_PI_65__PI_REFRESH_BETWEEN_SEGMENT_DISABLE_SHIFT       24U
#define LPDDR4__DENALI_PI_65__PI_REFRESH_BETWEEN_SEGMENT_DISABLE_WIDTH        1U
#define LPDDR4__DENALI_PI_65__PI_REFRESH_BETWEEN_SEGMENT_DISABLE_WOCLR        0U
#define LPDDR4__DENALI_PI_65__PI_REFRESH_BETWEEN_SEGMENT_DISABLE_WOSET        0U
#define LPDDR4__PI_REFRESH_BETWEEN_SEGMENT_DISABLE__REG DENALI_PI_65
#define LPDDR4__PI_REFRESH_BETWEEN_SEGMENT_DISABLE__FLD LPDDR4__DENALI_PI_65__PI_REFRESH_BETWEEN_SEGMENT_DISABLE

#define LPDDR4__DENALI_PI_66_READ_MASK                               0xFF01FFFFU
#define LPDDR4__DENALI_PI_66_WRITE_MASK                              0xFF01FFFFU
#define LPDDR4__DENALI_PI_66__PI_CLKDISABLE_2_INIT_START_MASK        0x000000FFU
#define LPDDR4__DENALI_PI_66__PI_CLKDISABLE_2_INIT_START_SHIFT                0U
#define LPDDR4__DENALI_PI_66__PI_CLKDISABLE_2_INIT_START_WIDTH                8U
#define LPDDR4__PI_CLKDISABLE_2_INIT_START__REG DENALI_PI_66
#define LPDDR4__PI_CLKDISABLE_2_INIT_START__FLD LPDDR4__DENALI_PI_66__PI_CLKDISABLE_2_INIT_START

#define LPDDR4__DENALI_PI_66__PI_INIT_STARTORCOMPLETE_2_CLKDISABLE_MASK 0x0000FF00U
#define LPDDR4__DENALI_PI_66__PI_INIT_STARTORCOMPLETE_2_CLKDISABLE_SHIFT      8U
#define LPDDR4__DENALI_PI_66__PI_INIT_STARTORCOMPLETE_2_CLKDISABLE_WIDTH      8U
#define LPDDR4__PI_INIT_STARTORCOMPLETE_2_CLKDISABLE__REG DENALI_PI_66
#define LPDDR4__PI_INIT_STARTORCOMPLETE_2_CLKDISABLE__FLD LPDDR4__DENALI_PI_66__PI_INIT_STARTORCOMPLETE_2_CLKDISABLE

#define LPDDR4__DENALI_PI_66__PI_DRAM_CLK_DISABLE_DEASSERT_SEL_MASK  0x00010000U
#define LPDDR4__DENALI_PI_66__PI_DRAM_CLK_DISABLE_DEASSERT_SEL_SHIFT         16U
#define LPDDR4__DENALI_PI_66__PI_DRAM_CLK_DISABLE_DEASSERT_SEL_WIDTH          1U
#define LPDDR4__DENALI_PI_66__PI_DRAM_CLK_DISABLE_DEASSERT_SEL_WOCLR          0U
#define LPDDR4__DENALI_PI_66__PI_DRAM_CLK_DISABLE_DEASSERT_SEL_WOSET          0U
#define LPDDR4__PI_DRAM_CLK_DISABLE_DEASSERT_SEL__REG DENALI_PI_66
#define LPDDR4__PI_DRAM_CLK_DISABLE_DEASSERT_SEL__FLD LPDDR4__DENALI_PI_66__PI_DRAM_CLK_DISABLE_DEASSERT_SEL

#define LPDDR4__DENALI_PI_66__PI_TDFI_INIT_COMPLETE_MIN_MASK         0xFF000000U
#define LPDDR4__DENALI_PI_66__PI_TDFI_INIT_COMPLETE_MIN_SHIFT                24U
#define LPDDR4__DENALI_PI_66__PI_TDFI_INIT_COMPLETE_MIN_WIDTH                 8U
#define LPDDR4__PI_TDFI_INIT_COMPLETE_MIN__REG DENALI_PI_66
#define LPDDR4__PI_TDFI_INIT_COMPLETE_MIN__FLD LPDDR4__DENALI_PI_66__PI_TDFI_INIT_COMPLETE_MIN

#define LPDDR4__DENALI_PI_67_READ_MASK                               0x01010103U
#define LPDDR4__DENALI_PI_67_WRITE_MASK                              0x01010103U
#define LPDDR4__DENALI_PI_67__PI_VREF_CS_MASK                        0x00000003U
#define LPDDR4__DENALI_PI_67__PI_VREF_CS_SHIFT                                0U
#define LPDDR4__DENALI_PI_67__PI_VREF_CS_WIDTH                                2U
#define LPDDR4__PI_VREF_CS__REG DENALI_PI_67
#define LPDDR4__PI_VREF_CS__FLD LPDDR4__DENALI_PI_67__PI_VREF_CS

#define LPDDR4__DENALI_PI_67__PI_VREF_PDA_EN_MASK                    0x00000100U
#define LPDDR4__DENALI_PI_67__PI_VREF_PDA_EN_SHIFT                            8U
#define LPDDR4__DENALI_PI_67__PI_VREF_PDA_EN_WIDTH                            1U
#define LPDDR4__DENALI_PI_67__PI_VREF_PDA_EN_WOCLR                            0U
#define LPDDR4__DENALI_PI_67__PI_VREF_PDA_EN_WOSET                            0U
#define LPDDR4__PI_VREF_PDA_EN__REG DENALI_PI_67
#define LPDDR4__PI_VREF_PDA_EN__FLD LPDDR4__DENALI_PI_67__PI_VREF_PDA_EN

#define LPDDR4__DENALI_PI_67__PI_VREFLVL_DISABLE_DFS_MASK            0x00010000U
#define LPDDR4__DENALI_PI_67__PI_VREFLVL_DISABLE_DFS_SHIFT                   16U
#define LPDDR4__DENALI_PI_67__PI_VREFLVL_DISABLE_DFS_WIDTH                    1U
#define LPDDR4__DENALI_PI_67__PI_VREFLVL_DISABLE_DFS_WOCLR                    0U
#define LPDDR4__DENALI_PI_67__PI_VREFLVL_DISABLE_DFS_WOSET                    0U
#define LPDDR4__PI_VREFLVL_DISABLE_DFS__REG DENALI_PI_67
#define LPDDR4__PI_VREFLVL_DISABLE_DFS__FLD LPDDR4__DENALI_PI_67__PI_VREFLVL_DISABLE_DFS

#define LPDDR4__DENALI_PI_67__PI_MC_DFS_PI_SET_VREF_ENABLE_MASK      0x01000000U
#define LPDDR4__DENALI_PI_67__PI_MC_DFS_PI_SET_VREF_ENABLE_SHIFT             24U
#define LPDDR4__DENALI_PI_67__PI_MC_DFS_PI_SET_VREF_ENABLE_WIDTH              1U
#define LPDDR4__DENALI_PI_67__PI_MC_DFS_PI_SET_VREF_ENABLE_WOCLR              0U
#define LPDDR4__DENALI_PI_67__PI_MC_DFS_PI_SET_VREF_ENABLE_WOSET              0U
#define LPDDR4__PI_MC_DFS_PI_SET_VREF_ENABLE__REG DENALI_PI_67
#define LPDDR4__PI_MC_DFS_PI_SET_VREF_ENABLE__FLD LPDDR4__DENALI_PI_67__PI_MC_DFS_PI_SET_VREF_ENABLE

#define LPDDR4__DENALI_PI_68_READ_MASK                               0x0F0701FFU
#define LPDDR4__DENALI_PI_68_WRITE_MASK                              0x0F0701FFU
#define LPDDR4__DENALI_PI_68__PI_INIT_COMPLETE_TO_MC_DELAY_COUNT_MASK 0x000000FFU
#define LPDDR4__DENALI_PI_68__PI_INIT_COMPLETE_TO_MC_DELAY_COUNT_SHIFT        0U
#define LPDDR4__DENALI_PI_68__PI_INIT_COMPLETE_TO_MC_DELAY_COUNT_WIDTH        8U
#define LPDDR4__PI_INIT_COMPLETE_TO_MC_DELAY_COUNT__REG DENALI_PI_68
#define LPDDR4__PI_INIT_COMPLETE_TO_MC_DELAY_COUNT__FLD LPDDR4__DENALI_PI_68__PI_INIT_COMPLETE_TO_MC_DELAY_COUNT

#define LPDDR4__DENALI_PI_68__PI_WDQLVL_VREF_EN_MASK                 0x00000100U
#define LPDDR4__DENALI_PI_68__PI_WDQLVL_VREF_EN_SHIFT                         8U
#define LPDDR4__DENALI_PI_68__PI_WDQLVL_VREF_EN_WIDTH                         1U
#define LPDDR4__DENALI_PI_68__PI_WDQLVL_VREF_EN_WOCLR                         0U
#define LPDDR4__DENALI_PI_68__PI_WDQLVL_VREF_EN_WOSET                         0U
#define LPDDR4__PI_WDQLVL_VREF_EN__REG DENALI_PI_68
#define LPDDR4__PI_WDQLVL_VREF_EN__FLD LPDDR4__DENALI_PI_68__PI_WDQLVL_VREF_EN

#define LPDDR4__DENALI_PI_68__PI_WDQLVL_BST_NUM_MASK                 0x00070000U
#define LPDDR4__DENALI_PI_68__PI_WDQLVL_BST_NUM_SHIFT                        16U
#define LPDDR4__DENALI_PI_68__PI_WDQLVL_BST_NUM_WIDTH                         3U
#define LPDDR4__PI_WDQLVL_BST_NUM__REG DENALI_PI_68
#define LPDDR4__PI_WDQLVL_BST_NUM__FLD LPDDR4__DENALI_PI_68__PI_WDQLVL_BST_NUM

#define LPDDR4__DENALI_PI_68__PI_WDQLVL_RESP_MASK_MASK               0x0F000000U
#define LPDDR4__DENALI_PI_68__PI_WDQLVL_RESP_MASK_SHIFT                      24U
#define LPDDR4__DENALI_PI_68__PI_WDQLVL_RESP_MASK_WIDTH                       4U
#define LPDDR4__PI_WDQLVL_RESP_MASK__REG DENALI_PI_68
#define LPDDR4__PI_WDQLVL_RESP_MASK__FLD LPDDR4__DENALI_PI_68__PI_WDQLVL_RESP_MASK

#define LPDDR4__DENALI_PI_69_READ_MASK                               0x1F1F0F01U
#define LPDDR4__DENALI_PI_69_WRITE_MASK                              0x1F1F0F01U
#define LPDDR4__DENALI_PI_69__PI_WDQLVL_ROTATE_MASK                  0x00000001U
#define LPDDR4__DENALI_PI_69__PI_WDQLVL_ROTATE_SHIFT                          0U
#define LPDDR4__DENALI_PI_69__PI_WDQLVL_ROTATE_WIDTH                          1U
#define LPDDR4__DENALI_PI_69__PI_WDQLVL_ROTATE_WOCLR                          0U
#define LPDDR4__DENALI_PI_69__PI_WDQLVL_ROTATE_WOSET                          0U
#define LPDDR4__PI_WDQLVL_ROTATE__REG DENALI_PI_69
#define LPDDR4__PI_WDQLVL_ROTATE__FLD LPDDR4__DENALI_PI_69__PI_WDQLVL_ROTATE

#define LPDDR4__DENALI_PI_69__PI_WDQLVL_CS_MAP_MASK                  0x00000F00U
#define LPDDR4__DENALI_PI_69__PI_WDQLVL_CS_MAP_SHIFT                          8U
#define LPDDR4__DENALI_PI_69__PI_WDQLVL_CS_MAP_WIDTH                          4U
#define LPDDR4__PI_WDQLVL_CS_MAP__REG DENALI_PI_69
#define LPDDR4__PI_WDQLVL_CS_MAP__FLD LPDDR4__DENALI_PI_69__PI_WDQLVL_CS_MAP

#define LPDDR4__DENALI_PI_69__PI_WDQLVL_VREF_INITIAL_STEPSIZE_MASK   0x001F0000U
#define LPDDR4__DENALI_PI_69__PI_WDQLVL_VREF_INITIAL_STEPSIZE_SHIFT          16U
#define LPDDR4__DENALI_PI_69__PI_WDQLVL_VREF_INITIAL_STEPSIZE_WIDTH           5U
#define LPDDR4__PI_WDQLVL_VREF_INITIAL_STEPSIZE__REG DENALI_PI_69
#define LPDDR4__PI_WDQLVL_VREF_INITIAL_STEPSIZE__FLD LPDDR4__DENALI_PI_69__PI_WDQLVL_VREF_INITIAL_STEPSIZE

#define LPDDR4__DENALI_PI_69__PI_WDQLVL_VREF_NORMAL_STEPSIZE_MASK    0x1F000000U
#define LPDDR4__DENALI_PI_69__PI_WDQLVL_VREF_NORMAL_STEPSIZE_SHIFT           24U
#define LPDDR4__DENALI_PI_69__PI_WDQLVL_VREF_NORMAL_STEPSIZE_WIDTH            5U
#define LPDDR4__PI_WDQLVL_VREF_NORMAL_STEPSIZE__REG DENALI_PI_69
#define LPDDR4__PI_WDQLVL_VREF_NORMAL_STEPSIZE__FLD LPDDR4__DENALI_PI_69__PI_WDQLVL_VREF_NORMAL_STEPSIZE

#define LPDDR4__DENALI_PI_70_READ_MASK                               0x030F0001U
#define LPDDR4__DENALI_PI_70_WRITE_MASK                              0x030F0001U
#define LPDDR4__DENALI_PI_70__PI_WDQLVL_PERIODIC_MASK                0x00000001U
#define LPDDR4__DENALI_PI_70__PI_WDQLVL_PERIODIC_SHIFT                        0U
#define LPDDR4__DENALI_PI_70__PI_WDQLVL_PERIODIC_WIDTH                        1U
#define LPDDR4__DENALI_PI_70__PI_WDQLVL_PERIODIC_WOCLR                        0U
#define LPDDR4__DENALI_PI_70__PI_WDQLVL_PERIODIC_WOSET                        0U
#define LPDDR4__PI_WDQLVL_PERIODIC__REG DENALI_PI_70
#define LPDDR4__PI_WDQLVL_PERIODIC__FLD LPDDR4__DENALI_PI_70__PI_WDQLVL_PERIODIC

#define LPDDR4__DENALI_PI_70__PI_WDQLVL_REQ_MASK                     0x00000100U
#define LPDDR4__DENALI_PI_70__PI_WDQLVL_REQ_SHIFT                             8U
#define LPDDR4__DENALI_PI_70__PI_WDQLVL_REQ_WIDTH                             1U
#define LPDDR4__DENALI_PI_70__PI_WDQLVL_REQ_WOCLR                             0U
#define LPDDR4__DENALI_PI_70__PI_WDQLVL_REQ_WOSET                             0U
#define LPDDR4__PI_WDQLVL_REQ__REG DENALI_PI_70
#define LPDDR4__PI_WDQLVL_REQ__FLD LPDDR4__DENALI_PI_70__PI_WDQLVL_REQ

#define LPDDR4__DENALI_PI_70__PI_WDQLVL_CS_SW_MASK                   0x000F0000U
#define LPDDR4__DENALI_PI_70__PI_WDQLVL_CS_SW_SHIFT                          16U
#define LPDDR4__DENALI_PI_70__PI_WDQLVL_CS_SW_WIDTH                           4U
#define LPDDR4__PI_WDQLVL_CS_SW__REG DENALI_PI_70
#define LPDDR4__PI_WDQLVL_CS_SW__FLD LPDDR4__DENALI_PI_70__PI_WDQLVL_CS_SW

#define LPDDR4__DENALI_PI_70__PI_WDQLVL_CS_MASK                      0x03000000U
#define LPDDR4__DENALI_PI_70__PI_WDQLVL_CS_SHIFT                             24U
#define LPDDR4__DENALI_PI_70__PI_WDQLVL_CS_WIDTH                              2U
#define LPDDR4__PI_WDQLVL_CS__REG DENALI_PI_70
#define LPDDR4__PI_WDQLVL_CS__FLD LPDDR4__DENALI_PI_70__PI_WDQLVL_CS

#define LPDDR4__DENALI_PI_71_READ_MASK                               0x000000FFU
#define LPDDR4__DENALI_PI_71_WRITE_MASK                              0x000000FFU
#define LPDDR4__DENALI_PI_71__PI_TDFI_WDQLVL_EN_MASK                 0x000000FFU
#define LPDDR4__DENALI_PI_71__PI_TDFI_WDQLVL_EN_SHIFT                         0U
#define LPDDR4__DENALI_PI_71__PI_TDFI_WDQLVL_EN_WIDTH                         8U
#define LPDDR4__PI_TDFI_WDQLVL_EN__REG DENALI_PI_71
#define LPDDR4__PI_TDFI_WDQLVL_EN__FLD LPDDR4__DENALI_PI_71__PI_TDFI_WDQLVL_EN

#define LPDDR4__DENALI_PI_72_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_72_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_72__PI_TDFI_WDQLVL_RESP_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_72__PI_TDFI_WDQLVL_RESP_SHIFT                       0U
#define LPDDR4__DENALI_PI_72__PI_TDFI_WDQLVL_RESP_WIDTH                      32U
#define LPDDR4__PI_TDFI_WDQLVL_RESP__REG DENALI_PI_72
#define LPDDR4__PI_TDFI_WDQLVL_RESP__FLD LPDDR4__DENALI_PI_72__PI_TDFI_WDQLVL_RESP

#define LPDDR4__DENALI_PI_73_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_73_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_73__PI_TDFI_WDQLVL_MAX_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PI_73__PI_TDFI_WDQLVL_MAX_SHIFT                        0U
#define LPDDR4__DENALI_PI_73__PI_TDFI_WDQLVL_MAX_WIDTH                       32U
#define LPDDR4__PI_TDFI_WDQLVL_MAX__REG DENALI_PI_73
#define LPDDR4__PI_TDFI_WDQLVL_MAX__FLD LPDDR4__DENALI_PI_73__PI_TDFI_WDQLVL_MAX

#define LPDDR4__DENALI_PI_74_READ_MASK                               0x0101FFFFU
#define LPDDR4__DENALI_PI_74_WRITE_MASK                              0x0101FFFFU
#define LPDDR4__DENALI_PI_74__PI_WDQLVL_INTERVAL_MASK                0x0000FFFFU
#define LPDDR4__DENALI_PI_74__PI_WDQLVL_INTERVAL_SHIFT                        0U
#define LPDDR4__DENALI_PI_74__PI_WDQLVL_INTERVAL_WIDTH                       16U
#define LPDDR4__PI_WDQLVL_INTERVAL__REG DENALI_PI_74
#define LPDDR4__PI_WDQLVL_INTERVAL__FLD LPDDR4__DENALI_PI_74__PI_WDQLVL_INTERVAL

#define LPDDR4__DENALI_PI_74__PI_WDQLVL_ON_SREF_EXIT_MASK            0x00010000U
#define LPDDR4__DENALI_PI_74__PI_WDQLVL_ON_SREF_EXIT_SHIFT                   16U
#define LPDDR4__DENALI_PI_74__PI_WDQLVL_ON_SREF_EXIT_WIDTH                    1U
#define LPDDR4__DENALI_PI_74__PI_WDQLVL_ON_SREF_EXIT_WOCLR                    0U
#define LPDDR4__DENALI_PI_74__PI_WDQLVL_ON_SREF_EXIT_WOSET                    0U
#define LPDDR4__PI_WDQLVL_ON_SREF_EXIT__REG DENALI_PI_74
#define LPDDR4__PI_WDQLVL_ON_SREF_EXIT__FLD LPDDR4__DENALI_PI_74__PI_WDQLVL_ON_SREF_EXIT

#define LPDDR4__DENALI_PI_74__PI_WDQLVL_ON_MPD_EXIT_MASK             0x01000000U
#define LPDDR4__DENALI_PI_74__PI_WDQLVL_ON_MPD_EXIT_SHIFT                    24U
#define LPDDR4__DENALI_PI_74__PI_WDQLVL_ON_MPD_EXIT_WIDTH                     1U
#define LPDDR4__DENALI_PI_74__PI_WDQLVL_ON_MPD_EXIT_WOCLR                     0U
#define LPDDR4__DENALI_PI_74__PI_WDQLVL_ON_MPD_EXIT_WOSET                     0U
#define LPDDR4__PI_WDQLVL_ON_MPD_EXIT__REG DENALI_PI_74
#define LPDDR4__PI_WDQLVL_ON_MPD_EXIT__FLD LPDDR4__DENALI_PI_74__PI_WDQLVL_ON_MPD_EXIT

#define LPDDR4__DENALI_PI_75_READ_MASK                               0x00030301U
#define LPDDR4__DENALI_PI_75_WRITE_MASK                              0x00030301U
#define LPDDR4__DENALI_PI_75__PI_WDQLVL_DISABLE_DFS_MASK             0x00000001U
#define LPDDR4__DENALI_PI_75__PI_WDQLVL_DISABLE_DFS_SHIFT                     0U
#define LPDDR4__DENALI_PI_75__PI_WDQLVL_DISABLE_DFS_WIDTH                     1U
#define LPDDR4__DENALI_PI_75__PI_WDQLVL_DISABLE_DFS_WOCLR                     0U
#define LPDDR4__DENALI_PI_75__PI_WDQLVL_DISABLE_DFS_WOSET                     0U
#define LPDDR4__PI_WDQLVL_DISABLE_DFS__REG DENALI_PI_75
#define LPDDR4__PI_WDQLVL_DISABLE_DFS__FLD LPDDR4__DENALI_PI_75__PI_WDQLVL_DISABLE_DFS

#define LPDDR4__DENALI_PI_75__PI_WDQLVL_ERROR_STATUS_MASK            0x00000300U
#define LPDDR4__DENALI_PI_75__PI_WDQLVL_ERROR_STATUS_SHIFT                    8U
#define LPDDR4__DENALI_PI_75__PI_WDQLVL_ERROR_STATUS_WIDTH                    2U
#define LPDDR4__PI_WDQLVL_ERROR_STATUS__REG DENALI_PI_75
#define LPDDR4__PI_WDQLVL_ERROR_STATUS__FLD LPDDR4__DENALI_PI_75__PI_WDQLVL_ERROR_STATUS

#define LPDDR4__DENALI_PI_75__PI_WDQLVL_NEED_SAVE_RESTORE_MASK       0x00030000U
#define LPDDR4__DENALI_PI_75__PI_WDQLVL_NEED_SAVE_RESTORE_SHIFT              16U
#define LPDDR4__DENALI_PI_75__PI_WDQLVL_NEED_SAVE_RESTORE_WIDTH               2U
#define LPDDR4__PI_WDQLVL_NEED_SAVE_RESTORE__REG DENALI_PI_75
#define LPDDR4__PI_WDQLVL_NEED_SAVE_RESTORE__FLD LPDDR4__DENALI_PI_75__PI_WDQLVL_NEED_SAVE_RESTORE

#define LPDDR4__DENALI_PI_76_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_76_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_76__PI_WDQLVL_DRAM_LVL_START_ADDR_0_MASK   0xFFFFFFFFU
#define LPDDR4__DENALI_PI_76__PI_WDQLVL_DRAM_LVL_START_ADDR_0_SHIFT           0U
#define LPDDR4__DENALI_PI_76__PI_WDQLVL_DRAM_LVL_START_ADDR_0_WIDTH          32U
#define LPDDR4__PI_WDQLVL_DRAM_LVL_START_ADDR_0__REG DENALI_PI_76
#define LPDDR4__PI_WDQLVL_DRAM_LVL_START_ADDR_0__FLD LPDDR4__DENALI_PI_76__PI_WDQLVL_DRAM_LVL_START_ADDR_0

#define LPDDR4__DENALI_PI_77_READ_MASK                               0x00010107U
#define LPDDR4__DENALI_PI_77_WRITE_MASK                              0x00010107U
#define LPDDR4__DENALI_PI_77__PI_WDQLVL_DRAM_LVL_START_ADDR_1_MASK   0x00000007U
#define LPDDR4__DENALI_PI_77__PI_WDQLVL_DRAM_LVL_START_ADDR_1_SHIFT           0U
#define LPDDR4__DENALI_PI_77__PI_WDQLVL_DRAM_LVL_START_ADDR_1_WIDTH           3U
#define LPDDR4__PI_WDQLVL_DRAM_LVL_START_ADDR_1__REG DENALI_PI_77
#define LPDDR4__PI_WDQLVL_DRAM_LVL_START_ADDR_1__FLD LPDDR4__DENALI_PI_77__PI_WDQLVL_DRAM_LVL_START_ADDR_1

#define LPDDR4__DENALI_PI_77__PI_WDQLVL_DM_LEVEL_EN_MASK             0x00000100U
#define LPDDR4__DENALI_PI_77__PI_WDQLVL_DM_LEVEL_EN_SHIFT                     8U
#define LPDDR4__DENALI_PI_77__PI_WDQLVL_DM_LEVEL_EN_WIDTH                     1U
#define LPDDR4__DENALI_PI_77__PI_WDQLVL_DM_LEVEL_EN_WOCLR                     0U
#define LPDDR4__DENALI_PI_77__PI_WDQLVL_DM_LEVEL_EN_WOSET                     0U
#define LPDDR4__PI_WDQLVL_DM_LEVEL_EN__REG DENALI_PI_77
#define LPDDR4__PI_WDQLVL_DM_LEVEL_EN__FLD LPDDR4__DENALI_PI_77__PI_WDQLVL_DM_LEVEL_EN

#define LPDDR4__DENALI_PI_77__PI_NO_MEMORY_DM_MASK                   0x00010000U
#define LPDDR4__DENALI_PI_77__PI_NO_MEMORY_DM_SHIFT                          16U
#define LPDDR4__DENALI_PI_77__PI_NO_MEMORY_DM_WIDTH                           1U
#define LPDDR4__DENALI_PI_77__PI_NO_MEMORY_DM_WOCLR                           0U
#define LPDDR4__DENALI_PI_77__PI_NO_MEMORY_DM_WOSET                           0U
#define LPDDR4__PI_NO_MEMORY_DM__REG DENALI_PI_77
#define LPDDR4__PI_NO_MEMORY_DM__FLD LPDDR4__DENALI_PI_77__PI_NO_MEMORY_DM

#define LPDDR4__DENALI_PI_78_READ_MASK                               0x010003FFU
#define LPDDR4__DENALI_PI_78_WRITE_MASK                              0x010003FFU
#define LPDDR4__DENALI_PI_78__PI_TDFI_WDQLVL_WW_MASK                 0x000003FFU
#define LPDDR4__DENALI_PI_78__PI_TDFI_WDQLVL_WW_SHIFT                         0U
#define LPDDR4__DENALI_PI_78__PI_TDFI_WDQLVL_WW_WIDTH                        10U
#define LPDDR4__PI_TDFI_WDQLVL_WW__REG DENALI_PI_78
#define LPDDR4__PI_TDFI_WDQLVL_WW__FLD LPDDR4__DENALI_PI_78__PI_TDFI_WDQLVL_WW

#define LPDDR4__DENALI_PI_78__PI_SWLVL_SM2_DM_NIBBLE_START_MASK      0x00010000U
#define LPDDR4__DENALI_PI_78__PI_SWLVL_SM2_DM_NIBBLE_START_SHIFT             16U
#define LPDDR4__DENALI_PI_78__PI_SWLVL_SM2_DM_NIBBLE_START_WIDTH              1U
#define LPDDR4__DENALI_PI_78__PI_SWLVL_SM2_DM_NIBBLE_START_WOCLR              0U
#define LPDDR4__DENALI_PI_78__PI_SWLVL_SM2_DM_NIBBLE_START_WOSET              0U
#define LPDDR4__PI_SWLVL_SM2_DM_NIBBLE_START__REG DENALI_PI_78
#define LPDDR4__PI_SWLVL_SM2_DM_NIBBLE_START__FLD LPDDR4__DENALI_PI_78__PI_SWLVL_SM2_DM_NIBBLE_START

#define LPDDR4__DENALI_PI_78__PI_WDQLVL_NIBBLE_MODE_MASK             0x01000000U
#define LPDDR4__DENALI_PI_78__PI_WDQLVL_NIBBLE_MODE_SHIFT                    24U
#define LPDDR4__DENALI_PI_78__PI_WDQLVL_NIBBLE_MODE_WIDTH                     1U
#define LPDDR4__DENALI_PI_78__PI_WDQLVL_NIBBLE_MODE_WOCLR                     0U
#define LPDDR4__DENALI_PI_78__PI_WDQLVL_NIBBLE_MODE_WOSET                     0U
#define LPDDR4__PI_WDQLVL_NIBBLE_MODE__REG DENALI_PI_78
#define LPDDR4__PI_WDQLVL_NIBBLE_MODE__FLD LPDDR4__DENALI_PI_78__PI_WDQLVL_NIBBLE_MODE

#define LPDDR4__DENALI_PI_79_READ_MASK                               0x01010101U
#define LPDDR4__DENALI_PI_79_WRITE_MASK                              0x01010101U
#define LPDDR4__DENALI_PI_79__PI_WDQLVL_OSC_EN_MASK                  0x00000001U
#define LPDDR4__DENALI_PI_79__PI_WDQLVL_OSC_EN_SHIFT                          0U
#define LPDDR4__DENALI_PI_79__PI_WDQLVL_OSC_EN_WIDTH                          1U
#define LPDDR4__DENALI_PI_79__PI_WDQLVL_OSC_EN_WOCLR                          0U
#define LPDDR4__DENALI_PI_79__PI_WDQLVL_OSC_EN_WOSET                          0U
#define LPDDR4__PI_WDQLVL_OSC_EN__REG DENALI_PI_79
#define LPDDR4__PI_WDQLVL_OSC_EN__FLD LPDDR4__DENALI_PI_79__PI_WDQLVL_OSC_EN

#define LPDDR4__DENALI_PI_79__PI_DQS_OSC_PERIOD_EN_MASK              0x00000100U
#define LPDDR4__DENALI_PI_79__PI_DQS_OSC_PERIOD_EN_SHIFT                      8U
#define LPDDR4__DENALI_PI_79__PI_DQS_OSC_PERIOD_EN_WIDTH                      1U
#define LPDDR4__DENALI_PI_79__PI_DQS_OSC_PERIOD_EN_WOCLR                      0U
#define LPDDR4__DENALI_PI_79__PI_DQS_OSC_PERIOD_EN_WOSET                      0U
#define LPDDR4__PI_DQS_OSC_PERIOD_EN__REG DENALI_PI_79
#define LPDDR4__PI_DQS_OSC_PERIOD_EN__FLD LPDDR4__DENALI_PI_79__PI_DQS_OSC_PERIOD_EN

#define LPDDR4__DENALI_PI_79__PI_WDQLVL_PDA_EN_MASK                  0x00010000U
#define LPDDR4__DENALI_PI_79__PI_WDQLVL_PDA_EN_SHIFT                         16U
#define LPDDR4__DENALI_PI_79__PI_WDQLVL_PDA_EN_WIDTH                          1U
#define LPDDR4__DENALI_PI_79__PI_WDQLVL_PDA_EN_WOCLR                          0U
#define LPDDR4__DENALI_PI_79__PI_WDQLVL_PDA_EN_WOSET                          0U
#define LPDDR4__PI_WDQLVL_PDA_EN__REG DENALI_PI_79
#define LPDDR4__PI_WDQLVL_PDA_EN__FLD LPDDR4__DENALI_PI_79__PI_WDQLVL_PDA_EN

#define LPDDR4__DENALI_PI_79__PI_WDQLVL_PDA_VREF_TRAIN_MASK          0x01000000U
#define LPDDR4__DENALI_PI_79__PI_WDQLVL_PDA_VREF_TRAIN_SHIFT                 24U
#define LPDDR4__DENALI_PI_79__PI_WDQLVL_PDA_VREF_TRAIN_WIDTH                  1U
#define LPDDR4__DENALI_PI_79__PI_WDQLVL_PDA_VREF_TRAIN_WOCLR                  0U
#define LPDDR4__DENALI_PI_79__PI_WDQLVL_PDA_VREF_TRAIN_WOSET                  0U
#define LPDDR4__PI_WDQLVL_PDA_VREF_TRAIN__REG DENALI_PI_79
#define LPDDR4__PI_WDQLVL_PDA_VREF_TRAIN__FLD LPDDR4__DENALI_PI_79__PI_WDQLVL_PDA_VREF_TRAIN

#define LPDDR4__DENALI_PI_80_READ_MASK                               0x07030F01U
#define LPDDR4__DENALI_PI_80_WRITE_MASK                              0x07030F01U
#define LPDDR4__DENALI_PI_80__PI_PARALLEL_WDQLVL_EN_MASK             0x00000001U
#define LPDDR4__DENALI_PI_80__PI_PARALLEL_WDQLVL_EN_SHIFT                     0U
#define LPDDR4__DENALI_PI_80__PI_PARALLEL_WDQLVL_EN_WIDTH                     1U
#define LPDDR4__DENALI_PI_80__PI_PARALLEL_WDQLVL_EN_WOCLR                     0U
#define LPDDR4__DENALI_PI_80__PI_PARALLEL_WDQLVL_EN_WOSET                     0U
#define LPDDR4__PI_PARALLEL_WDQLVL_EN__REG DENALI_PI_80
#define LPDDR4__PI_PARALLEL_WDQLVL_EN__FLD LPDDR4__DENALI_PI_80__PI_PARALLEL_WDQLVL_EN

#define LPDDR4__DENALI_PI_80__PI_DBILVL_RESP_MASK_MASK               0x00000F00U
#define LPDDR4__DENALI_PI_80__PI_DBILVL_RESP_MASK_SHIFT                       8U
#define LPDDR4__DENALI_PI_80__PI_DBILVL_RESP_MASK_WIDTH                       4U
#define LPDDR4__PI_DBILVL_RESP_MASK__REG DENALI_PI_80
#define LPDDR4__PI_DBILVL_RESP_MASK__FLD LPDDR4__DENALI_PI_80__PI_DBILVL_RESP_MASK

#define LPDDR4__DENALI_PI_80__PI_BANK_DIFF_MASK                      0x00030000U
#define LPDDR4__DENALI_PI_80__PI_BANK_DIFF_SHIFT                             16U
#define LPDDR4__DENALI_PI_80__PI_BANK_DIFF_WIDTH                              2U
#define LPDDR4__PI_BANK_DIFF__REG DENALI_PI_80
#define LPDDR4__PI_BANK_DIFF__FLD LPDDR4__DENALI_PI_80__PI_BANK_DIFF

#define LPDDR4__DENALI_PI_80__PI_ROW_DIFF_MASK                       0x07000000U
#define LPDDR4__DENALI_PI_80__PI_ROW_DIFF_SHIFT                              24U
#define LPDDR4__DENALI_PI_80__PI_ROW_DIFF_WIDTH                               3U
#define LPDDR4__PI_ROW_DIFF__REG DENALI_PI_80
#define LPDDR4__PI_ROW_DIFF__FLD LPDDR4__DENALI_PI_80__PI_ROW_DIFF

#define LPDDR4__DENALI_PI_81_READ_MASK                               0x0F0F0F1FU
#define LPDDR4__DENALI_PI_81_WRITE_MASK                              0x0F0F0F1FU
#define LPDDR4__DENALI_PI_81__PI_TCCD_MASK                           0x0000001FU
#define LPDDR4__DENALI_PI_81__PI_TCCD_SHIFT                                   0U
#define LPDDR4__DENALI_PI_81__PI_TCCD_WIDTH                                   5U
#define LPDDR4__PI_TCCD__REG DENALI_PI_81
#define LPDDR4__PI_TCCD__FLD LPDDR4__DENALI_PI_81__PI_TCCD

#define LPDDR4__DENALI_PI_81__PI_RESERVED7_MASK                      0x00000F00U
#define LPDDR4__DENALI_PI_81__PI_RESERVED7_SHIFT                              8U
#define LPDDR4__DENALI_PI_81__PI_RESERVED7_WIDTH                              4U
#define LPDDR4__PI_RESERVED7__REG DENALI_PI_81
#define LPDDR4__PI_RESERVED7__FLD LPDDR4__DENALI_PI_81__PI_RESERVED7

#define LPDDR4__DENALI_PI_81__PI_RESERVED8_MASK                      0x000F0000U
#define LPDDR4__DENALI_PI_81__PI_RESERVED8_SHIFT                             16U
#define LPDDR4__DENALI_PI_81__PI_RESERVED8_WIDTH                              4U
#define LPDDR4__PI_RESERVED8__REG DENALI_PI_81
#define LPDDR4__PI_RESERVED8__FLD LPDDR4__DENALI_PI_81__PI_RESERVED8

#define LPDDR4__DENALI_PI_81__PI_RESERVED9_MASK                      0x0F000000U
#define LPDDR4__DENALI_PI_81__PI_RESERVED9_SHIFT                             24U
#define LPDDR4__DENALI_PI_81__PI_RESERVED9_WIDTH                              4U
#define LPDDR4__PI_RESERVED9__REG DENALI_PI_81
#define LPDDR4__PI_RESERVED9__FLD LPDDR4__DENALI_PI_81__PI_RESERVED9

#define LPDDR4__DENALI_PI_82_READ_MASK                               0x0F0F0F0FU
#define LPDDR4__DENALI_PI_82_WRITE_MASK                              0x0F0F0F0FU
#define LPDDR4__DENALI_PI_82__PI_RESERVED10_MASK                     0x0000000FU
#define LPDDR4__DENALI_PI_82__PI_RESERVED10_SHIFT                             0U
#define LPDDR4__DENALI_PI_82__PI_RESERVED10_WIDTH                             4U
#define LPDDR4__PI_RESERVED10__REG DENALI_PI_82
#define LPDDR4__PI_RESERVED10__FLD LPDDR4__DENALI_PI_82__PI_RESERVED10

#define LPDDR4__DENALI_PI_82__PI_RESERVED11_MASK                     0x00000F00U
#define LPDDR4__DENALI_PI_82__PI_RESERVED11_SHIFT                             8U
#define LPDDR4__DENALI_PI_82__PI_RESERVED11_WIDTH                             4U
#define LPDDR4__PI_RESERVED11__REG DENALI_PI_82
#define LPDDR4__PI_RESERVED11__FLD LPDDR4__DENALI_PI_82__PI_RESERVED11

#define LPDDR4__DENALI_PI_82__PI_RESERVED12_MASK                     0x000F0000U
#define LPDDR4__DENALI_PI_82__PI_RESERVED12_SHIFT                            16U
#define LPDDR4__DENALI_PI_82__PI_RESERVED12_WIDTH                             4U
#define LPDDR4__PI_RESERVED12__REG DENALI_PI_82
#define LPDDR4__PI_RESERVED12__FLD LPDDR4__DENALI_PI_82__PI_RESERVED12

#define LPDDR4__DENALI_PI_82__PI_RESERVED13_MASK                     0x0F000000U
#define LPDDR4__DENALI_PI_82__PI_RESERVED13_SHIFT                            24U
#define LPDDR4__DENALI_PI_82__PI_RESERVED13_WIDTH                             4U
#define LPDDR4__PI_RESERVED13__REG DENALI_PI_82
#define LPDDR4__PI_RESERVED13__FLD LPDDR4__DENALI_PI_82__PI_RESERVED13

#define LPDDR4__DENALI_PI_83_READ_MASK                               0x0F0F0F0FU
#define LPDDR4__DENALI_PI_83_WRITE_MASK                              0x0F0F0F0FU
#define LPDDR4__DENALI_PI_83__PI_RESERVED14_MASK                     0x0000000FU
#define LPDDR4__DENALI_PI_83__PI_RESERVED14_SHIFT                             0U
#define LPDDR4__DENALI_PI_83__PI_RESERVED14_WIDTH                             4U
#define LPDDR4__PI_RESERVED14__REG DENALI_PI_83
#define LPDDR4__PI_RESERVED14__FLD LPDDR4__DENALI_PI_83__PI_RESERVED14

#define LPDDR4__DENALI_PI_83__PI_RESERVED15_MASK                     0x00000F00U
#define LPDDR4__DENALI_PI_83__PI_RESERVED15_SHIFT                             8U
#define LPDDR4__DENALI_PI_83__PI_RESERVED15_WIDTH                             4U
#define LPDDR4__PI_RESERVED15__REG DENALI_PI_83
#define LPDDR4__PI_RESERVED15__FLD LPDDR4__DENALI_PI_83__PI_RESERVED15

#define LPDDR4__DENALI_PI_83__PI_RESERVED16_MASK                     0x000F0000U
#define LPDDR4__DENALI_PI_83__PI_RESERVED16_SHIFT                            16U
#define LPDDR4__DENALI_PI_83__PI_RESERVED16_WIDTH                             4U
#define LPDDR4__PI_RESERVED16__REG DENALI_PI_83
#define LPDDR4__PI_RESERVED16__FLD LPDDR4__DENALI_PI_83__PI_RESERVED16

#define LPDDR4__DENALI_PI_83__PI_RESERVED17_MASK                     0x0F000000U
#define LPDDR4__DENALI_PI_83__PI_RESERVED17_SHIFT                            24U
#define LPDDR4__DENALI_PI_83__PI_RESERVED17_WIDTH                             4U
#define LPDDR4__PI_RESERVED17__REG DENALI_PI_83
#define LPDDR4__PI_RESERVED17__FLD LPDDR4__DENALI_PI_83__PI_RESERVED17

#define LPDDR4__DENALI_PI_84_READ_MASK                               0x0F0F0F0FU
#define LPDDR4__DENALI_PI_84_WRITE_MASK                              0x0F0F0F0FU
#define LPDDR4__DENALI_PI_84__PI_RESERVED18_MASK                     0x0000000FU
#define LPDDR4__DENALI_PI_84__PI_RESERVED18_SHIFT                             0U
#define LPDDR4__DENALI_PI_84__PI_RESERVED18_WIDTH                             4U
#define LPDDR4__PI_RESERVED18__REG DENALI_PI_84
#define LPDDR4__PI_RESERVED18__FLD LPDDR4__DENALI_PI_84__PI_RESERVED18

#define LPDDR4__DENALI_PI_84__PI_RESERVED19_MASK                     0x00000F00U
#define LPDDR4__DENALI_PI_84__PI_RESERVED19_SHIFT                             8U
#define LPDDR4__DENALI_PI_84__PI_RESERVED19_WIDTH                             4U
#define LPDDR4__PI_RESERVED19__REG DENALI_PI_84
#define LPDDR4__PI_RESERVED19__FLD LPDDR4__DENALI_PI_84__PI_RESERVED19

#define LPDDR4__DENALI_PI_84__PI_RESERVED20_MASK                     0x000F0000U
#define LPDDR4__DENALI_PI_84__PI_RESERVED20_SHIFT                            16U
#define LPDDR4__DENALI_PI_84__PI_RESERVED20_WIDTH                             4U
#define LPDDR4__PI_RESERVED20__REG DENALI_PI_84
#define LPDDR4__PI_RESERVED20__FLD LPDDR4__DENALI_PI_84__PI_RESERVED20

#define LPDDR4__DENALI_PI_84__PI_RESERVED21_MASK                     0x0F000000U
#define LPDDR4__DENALI_PI_84__PI_RESERVED21_SHIFT                            24U
#define LPDDR4__DENALI_PI_84__PI_RESERVED21_WIDTH                             4U
#define LPDDR4__PI_RESERVED21__REG DENALI_PI_84
#define LPDDR4__PI_RESERVED21__FLD LPDDR4__DENALI_PI_84__PI_RESERVED21

#define LPDDR4__DENALI_PI_85_READ_MASK                               0x0F0F0F0FU
#define LPDDR4__DENALI_PI_85_WRITE_MASK                              0x0F0F0F0FU
#define LPDDR4__DENALI_PI_85__PI_RESERVED22_MASK                     0x0000000FU
#define LPDDR4__DENALI_PI_85__PI_RESERVED22_SHIFT                             0U
#define LPDDR4__DENALI_PI_85__PI_RESERVED22_WIDTH                             4U
#define LPDDR4__PI_RESERVED22__REG DENALI_PI_85
#define LPDDR4__PI_RESERVED22__FLD LPDDR4__DENALI_PI_85__PI_RESERVED22

#define LPDDR4__DENALI_PI_85__PI_RESERVED23_MASK                     0x00000F00U
#define LPDDR4__DENALI_PI_85__PI_RESERVED23_SHIFT                             8U
#define LPDDR4__DENALI_PI_85__PI_RESERVED23_WIDTH                             4U
#define LPDDR4__PI_RESERVED23__REG DENALI_PI_85
#define LPDDR4__PI_RESERVED23__FLD LPDDR4__DENALI_PI_85__PI_RESERVED23

#define LPDDR4__DENALI_PI_85__PI_RESERVED24_MASK                     0x000F0000U
#define LPDDR4__DENALI_PI_85__PI_RESERVED24_SHIFT                            16U
#define LPDDR4__DENALI_PI_85__PI_RESERVED24_WIDTH                             4U
#define LPDDR4__PI_RESERVED24__REG DENALI_PI_85
#define LPDDR4__PI_RESERVED24__FLD LPDDR4__DENALI_PI_85__PI_RESERVED24

#define LPDDR4__DENALI_PI_85__PI_RESERVED25_MASK                     0x0F000000U
#define LPDDR4__DENALI_PI_85__PI_RESERVED25_SHIFT                            24U
#define LPDDR4__DENALI_PI_85__PI_RESERVED25_WIDTH                             4U
#define LPDDR4__PI_RESERVED25__REG DENALI_PI_85
#define LPDDR4__PI_RESERVED25__FLD LPDDR4__DENALI_PI_85__PI_RESERVED25

#define LPDDR4__DENALI_PI_86_READ_MASK                               0x0000000FU
#define LPDDR4__DENALI_PI_86_WRITE_MASK                              0x0000000FU
#define LPDDR4__DENALI_PI_86__PI_RESERVED26_MASK                     0x0000000FU
#define LPDDR4__DENALI_PI_86__PI_RESERVED26_SHIFT                             0U
#define LPDDR4__DENALI_PI_86__PI_RESERVED26_WIDTH                             4U
#define LPDDR4__PI_RESERVED26__REG DENALI_PI_86
#define LPDDR4__PI_RESERVED26__FLD LPDDR4__DENALI_PI_86__PI_RESERVED26

#define LPDDR4__DENALI_PI_87_READ_MASK                               0x3FFFFFFFU
#define LPDDR4__DENALI_PI_87_WRITE_MASK                              0x3FFFFFFFU
#define LPDDR4__DENALI_PI_87__PI_INT_STATUS_MASK                     0x3FFFFFFFU
#define LPDDR4__DENALI_PI_87__PI_INT_STATUS_SHIFT                             0U
#define LPDDR4__DENALI_PI_87__PI_INT_STATUS_WIDTH                            30U
#define LPDDR4__PI_INT_STATUS__REG DENALI_PI_87
#define LPDDR4__PI_INT_STATUS__FLD LPDDR4__DENALI_PI_87__PI_INT_STATUS

#define LPDDR4__DENALI_PI_88__PI_INT_ACK_MASK                        0x1FFFFFFFU
#define LPDDR4__DENALI_PI_88__PI_INT_ACK_SHIFT                                0U
#define LPDDR4__DENALI_PI_88__PI_INT_ACK_WIDTH                               29U
#define LPDDR4__PI_INT_ACK__REG DENALI_PI_88
#define LPDDR4__PI_INT_ACK__FLD LPDDR4__DENALI_PI_88__PI_INT_ACK

#define LPDDR4__DENALI_PI_89_READ_MASK                               0x3FFFFFFFU
#define LPDDR4__DENALI_PI_89_WRITE_MASK                              0x3FFFFFFFU
#define LPDDR4__DENALI_PI_89__PI_INT_MASK_MASK                       0x3FFFFFFFU
#define LPDDR4__DENALI_PI_89__PI_INT_MASK_SHIFT                               0U
#define LPDDR4__DENALI_PI_89__PI_INT_MASK_WIDTH                              30U
#define LPDDR4__PI_INT_MASK__REG DENALI_PI_89
#define LPDDR4__PI_INT_MASK__FLD LPDDR4__DENALI_PI_89__PI_INT_MASK

#define LPDDR4__DENALI_PI_90_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_90_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_90__PI_BIST_EXP_DATA_0_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PI_90__PI_BIST_EXP_DATA_0_SHIFT                        0U
#define LPDDR4__DENALI_PI_90__PI_BIST_EXP_DATA_0_WIDTH                       32U
#define LPDDR4__PI_BIST_EXP_DATA_0__REG DENALI_PI_90
#define LPDDR4__PI_BIST_EXP_DATA_0__FLD LPDDR4__DENALI_PI_90__PI_BIST_EXP_DATA_0

#define LPDDR4__DENALI_PI_91_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_91_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_91__PI_BIST_EXP_DATA_1_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PI_91__PI_BIST_EXP_DATA_1_SHIFT                        0U
#define LPDDR4__DENALI_PI_91__PI_BIST_EXP_DATA_1_WIDTH                       32U
#define LPDDR4__PI_BIST_EXP_DATA_1__REG DENALI_PI_91
#define LPDDR4__PI_BIST_EXP_DATA_1__FLD LPDDR4__DENALI_PI_91__PI_BIST_EXP_DATA_1

#define LPDDR4__DENALI_PI_92_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_92_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_92__PI_BIST_EXP_DATA_2_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PI_92__PI_BIST_EXP_DATA_2_SHIFT                        0U
#define LPDDR4__DENALI_PI_92__PI_BIST_EXP_DATA_2_WIDTH                       32U
#define LPDDR4__PI_BIST_EXP_DATA_2__REG DENALI_PI_92
#define LPDDR4__PI_BIST_EXP_DATA_2__FLD LPDDR4__DENALI_PI_92__PI_BIST_EXP_DATA_2

#define LPDDR4__DENALI_PI_93_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_93_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_93__PI_BIST_EXP_DATA_3_MASK                0xFFFFFFFFU
#define LPDDR4__DENALI_PI_93__PI_BIST_EXP_DATA_3_SHIFT                        0U
#define LPDDR4__DENALI_PI_93__PI_BIST_EXP_DATA_3_WIDTH                       32U
#define LPDDR4__PI_BIST_EXP_DATA_3__REG DENALI_PI_93
#define LPDDR4__PI_BIST_EXP_DATA_3__FLD LPDDR4__DENALI_PI_93__PI_BIST_EXP_DATA_3

#define LPDDR4__DENALI_PI_94_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_94_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_94__PI_BIST_FAIL_DATA_0_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_94__PI_BIST_FAIL_DATA_0_SHIFT                       0U
#define LPDDR4__DENALI_PI_94__PI_BIST_FAIL_DATA_0_WIDTH                      32U
#define LPDDR4__PI_BIST_FAIL_DATA_0__REG DENALI_PI_94
#define LPDDR4__PI_BIST_FAIL_DATA_0__FLD LPDDR4__DENALI_PI_94__PI_BIST_FAIL_DATA_0

#define LPDDR4__DENALI_PI_95_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_95_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_95__PI_BIST_FAIL_DATA_1_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_95__PI_BIST_FAIL_DATA_1_SHIFT                       0U
#define LPDDR4__DENALI_PI_95__PI_BIST_FAIL_DATA_1_WIDTH                      32U
#define LPDDR4__PI_BIST_FAIL_DATA_1__REG DENALI_PI_95
#define LPDDR4__PI_BIST_FAIL_DATA_1__FLD LPDDR4__DENALI_PI_95__PI_BIST_FAIL_DATA_1

#define LPDDR4__DENALI_PI_96_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_96_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_96__PI_BIST_FAIL_DATA_2_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_96__PI_BIST_FAIL_DATA_2_SHIFT                       0U
#define LPDDR4__DENALI_PI_96__PI_BIST_FAIL_DATA_2_WIDTH                      32U
#define LPDDR4__PI_BIST_FAIL_DATA_2__REG DENALI_PI_96
#define LPDDR4__PI_BIST_FAIL_DATA_2__FLD LPDDR4__DENALI_PI_96__PI_BIST_FAIL_DATA_2

#define LPDDR4__DENALI_PI_97_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_97_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_97__PI_BIST_FAIL_DATA_3_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_97__PI_BIST_FAIL_DATA_3_SHIFT                       0U
#define LPDDR4__DENALI_PI_97__PI_BIST_FAIL_DATA_3_WIDTH                      32U
#define LPDDR4__PI_BIST_FAIL_DATA_3__REG DENALI_PI_97
#define LPDDR4__PI_BIST_FAIL_DATA_3__FLD LPDDR4__DENALI_PI_97__PI_BIST_FAIL_DATA_3

#define LPDDR4__DENALI_PI_98_READ_MASK                               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_98_WRITE_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_98__PI_BIST_FAIL_ADDR_0_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_98__PI_BIST_FAIL_ADDR_0_SHIFT                       0U
#define LPDDR4__DENALI_PI_98__PI_BIST_FAIL_ADDR_0_WIDTH                      32U
#define LPDDR4__PI_BIST_FAIL_ADDR_0__REG DENALI_PI_98
#define LPDDR4__PI_BIST_FAIL_ADDR_0__FLD LPDDR4__DENALI_PI_98__PI_BIST_FAIL_ADDR_0

#define LPDDR4__DENALI_PI_99_READ_MASK                               0x011F3F07U
#define LPDDR4__DENALI_PI_99_WRITE_MASK                              0x011F3F07U
#define LPDDR4__DENALI_PI_99__PI_BIST_FAIL_ADDR_1_MASK               0x00000007U
#define LPDDR4__DENALI_PI_99__PI_BIST_FAIL_ADDR_1_SHIFT                       0U
#define LPDDR4__DENALI_PI_99__PI_BIST_FAIL_ADDR_1_WIDTH                       3U
#define LPDDR4__PI_BIST_FAIL_ADDR_1__REG DENALI_PI_99
#define LPDDR4__PI_BIST_FAIL_ADDR_1__FLD LPDDR4__DENALI_PI_99__PI_BIST_FAIL_ADDR_1

#define LPDDR4__DENALI_PI_99__PI_BSTLEN_MASK                         0x00003F00U
#define LPDDR4__DENALI_PI_99__PI_BSTLEN_SHIFT                                 8U
#define LPDDR4__DENALI_PI_99__PI_BSTLEN_WIDTH                                 6U
#define LPDDR4__PI_BSTLEN__REG DENALI_PI_99
#define LPDDR4__PI_BSTLEN__FLD LPDDR4__DENALI_PI_99__PI_BSTLEN

#define LPDDR4__DENALI_PI_99__PI_LONG_COUNT_MASK_MASK                0x001F0000U
#define LPDDR4__DENALI_PI_99__PI_LONG_COUNT_MASK_SHIFT                       16U
#define LPDDR4__DENALI_PI_99__PI_LONG_COUNT_MASK_WIDTH                        5U
#define LPDDR4__PI_LONG_COUNT_MASK__REG DENALI_PI_99
#define LPDDR4__PI_LONG_COUNT_MASK__FLD LPDDR4__DENALI_PI_99__PI_LONG_COUNT_MASK

#define LPDDR4__DENALI_PI_99__PI_CMD_SWAP_EN_MASK                    0x01000000U
#define LPDDR4__DENALI_PI_99__PI_CMD_SWAP_EN_SHIFT                           24U
#define LPDDR4__DENALI_PI_99__PI_CMD_SWAP_EN_WIDTH                            1U
#define LPDDR4__DENALI_PI_99__PI_CMD_SWAP_EN_WOCLR                            0U
#define LPDDR4__DENALI_PI_99__PI_CMD_SWAP_EN_WOSET                            0U
#define LPDDR4__PI_CMD_SWAP_EN__REG DENALI_PI_99
#define LPDDR4__PI_CMD_SWAP_EN__FLD LPDDR4__DENALI_PI_99__PI_CMD_SWAP_EN

#define LPDDR4__DENALI_PI_100_READ_MASK                              0x1F1F1F1FU
#define LPDDR4__DENALI_PI_100_WRITE_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_PI_100__PI_PARITY_IN_MUX_MASK                 0x0000001FU
#define LPDDR4__DENALI_PI_100__PI_PARITY_IN_MUX_SHIFT                         0U
#define LPDDR4__DENALI_PI_100__PI_PARITY_IN_MUX_WIDTH                         5U
#define LPDDR4__PI_PARITY_IN_MUX__REG DENALI_PI_100
#define LPDDR4__PI_PARITY_IN_MUX__FLD LPDDR4__DENALI_PI_100__PI_PARITY_IN_MUX

#define LPDDR4__DENALI_PI_100__PI_ACT_N_MUX_MASK                     0x00001F00U
#define LPDDR4__DENALI_PI_100__PI_ACT_N_MUX_SHIFT                             8U
#define LPDDR4__DENALI_PI_100__PI_ACT_N_MUX_WIDTH                             5U
#define LPDDR4__PI_ACT_N_MUX__REG DENALI_PI_100
#define LPDDR4__PI_ACT_N_MUX__FLD LPDDR4__DENALI_PI_100__PI_ACT_N_MUX

#define LPDDR4__DENALI_PI_100__PI_BG_MUX_0_MASK                      0x001F0000U
#define LPDDR4__DENALI_PI_100__PI_BG_MUX_0_SHIFT                             16U
#define LPDDR4__DENALI_PI_100__PI_BG_MUX_0_WIDTH                              5U
#define LPDDR4__PI_BG_MUX_0__REG DENALI_PI_100
#define LPDDR4__PI_BG_MUX_0__FLD LPDDR4__DENALI_PI_100__PI_BG_MUX_0

#define LPDDR4__DENALI_PI_100__PI_BG_MUX_1_MASK                      0x1F000000U
#define LPDDR4__DENALI_PI_100__PI_BG_MUX_1_SHIFT                             24U
#define LPDDR4__DENALI_PI_100__PI_BG_MUX_1_WIDTH                              5U
#define LPDDR4__PI_BG_MUX_1__REG DENALI_PI_100
#define LPDDR4__PI_BG_MUX_1__FLD LPDDR4__DENALI_PI_100__PI_BG_MUX_1

#define LPDDR4__DENALI_PI_101_READ_MASK                              0x1F1F1F1FU
#define LPDDR4__DENALI_PI_101_WRITE_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_PI_101__PI_RAS_N_MUX_MASK                     0x0000001FU
#define LPDDR4__DENALI_PI_101__PI_RAS_N_MUX_SHIFT                             0U
#define LPDDR4__DENALI_PI_101__PI_RAS_N_MUX_WIDTH                             5U
#define LPDDR4__PI_RAS_N_MUX__REG DENALI_PI_101
#define LPDDR4__PI_RAS_N_MUX__FLD LPDDR4__DENALI_PI_101__PI_RAS_N_MUX

#define LPDDR4__DENALI_PI_101__PI_CAS_N_MUX_MASK                     0x00001F00U
#define LPDDR4__DENALI_PI_101__PI_CAS_N_MUX_SHIFT                             8U
#define LPDDR4__DENALI_PI_101__PI_CAS_N_MUX_WIDTH                             5U
#define LPDDR4__PI_CAS_N_MUX__REG DENALI_PI_101
#define LPDDR4__PI_CAS_N_MUX__FLD LPDDR4__DENALI_PI_101__PI_CAS_N_MUX

#define LPDDR4__DENALI_PI_101__PI_WE_N_MUX_MASK                      0x001F0000U
#define LPDDR4__DENALI_PI_101__PI_WE_N_MUX_SHIFT                             16U
#define LPDDR4__DENALI_PI_101__PI_WE_N_MUX_WIDTH                              5U
#define LPDDR4__PI_WE_N_MUX__REG DENALI_PI_101
#define LPDDR4__PI_WE_N_MUX__FLD LPDDR4__DENALI_PI_101__PI_WE_N_MUX

#define LPDDR4__DENALI_PI_101__PI_BANK_MUX_0_MASK                    0x1F000000U
#define LPDDR4__DENALI_PI_101__PI_BANK_MUX_0_SHIFT                           24U
#define LPDDR4__DENALI_PI_101__PI_BANK_MUX_0_WIDTH                            5U
#define LPDDR4__PI_BANK_MUX_0__REG DENALI_PI_101
#define LPDDR4__PI_BANK_MUX_0__FLD LPDDR4__DENALI_PI_101__PI_BANK_MUX_0

#define LPDDR4__DENALI_PI_102_READ_MASK                              0x0303011FU
#define LPDDR4__DENALI_PI_102_WRITE_MASK                             0x0303011FU
#define LPDDR4__DENALI_PI_102__PI_BANK_MUX_1_MASK                    0x0000001FU
#define LPDDR4__DENALI_PI_102__PI_BANK_MUX_1_SHIFT                            0U
#define LPDDR4__DENALI_PI_102__PI_BANK_MUX_1_WIDTH                            5U
#define LPDDR4__PI_BANK_MUX_1__REG DENALI_PI_102
#define LPDDR4__PI_BANK_MUX_1__FLD LPDDR4__DENALI_PI_102__PI_BANK_MUX_1

#define LPDDR4__DENALI_PI_102__PI_DATA_BYTE_SWAP_EN_MASK             0x00000100U
#define LPDDR4__DENALI_PI_102__PI_DATA_BYTE_SWAP_EN_SHIFT                     8U
#define LPDDR4__DENALI_PI_102__PI_DATA_BYTE_SWAP_EN_WIDTH                     1U
#define LPDDR4__DENALI_PI_102__PI_DATA_BYTE_SWAP_EN_WOCLR                     0U
#define LPDDR4__DENALI_PI_102__PI_DATA_BYTE_SWAP_EN_WOSET                     0U
#define LPDDR4__PI_DATA_BYTE_SWAP_EN__REG DENALI_PI_102
#define LPDDR4__PI_DATA_BYTE_SWAP_EN__FLD LPDDR4__DENALI_PI_102__PI_DATA_BYTE_SWAP_EN

#define LPDDR4__DENALI_PI_102__PI_DATA_BYTE_SWAP_SLICE0_MASK         0x00030000U
#define LPDDR4__DENALI_PI_102__PI_DATA_BYTE_SWAP_SLICE0_SHIFT                16U
#define LPDDR4__DENALI_PI_102__PI_DATA_BYTE_SWAP_SLICE0_WIDTH                 2U
#define LPDDR4__PI_DATA_BYTE_SWAP_SLICE0__REG DENALI_PI_102
#define LPDDR4__PI_DATA_BYTE_SWAP_SLICE0__FLD LPDDR4__DENALI_PI_102__PI_DATA_BYTE_SWAP_SLICE0

#define LPDDR4__DENALI_PI_102__PI_DATA_BYTE_SWAP_SLICE1_MASK         0x03000000U
#define LPDDR4__DENALI_PI_102__PI_DATA_BYTE_SWAP_SLICE1_SHIFT                24U
#define LPDDR4__DENALI_PI_102__PI_DATA_BYTE_SWAP_SLICE1_WIDTH                 2U
#define LPDDR4__PI_DATA_BYTE_SWAP_SLICE1__REG DENALI_PI_102
#define LPDDR4__PI_DATA_BYTE_SWAP_SLICE1__FLD LPDDR4__DENALI_PI_102__PI_DATA_BYTE_SWAP_SLICE1

#define LPDDR4__DENALI_PI_103_READ_MASK                              0x00010303U
#define LPDDR4__DENALI_PI_103_WRITE_MASK                             0x00010303U
#define LPDDR4__DENALI_PI_103__PI_DATA_BYTE_SWAP_SLICE2_MASK         0x00000003U
#define LPDDR4__DENALI_PI_103__PI_DATA_BYTE_SWAP_SLICE2_SHIFT                 0U
#define LPDDR4__DENALI_PI_103__PI_DATA_BYTE_SWAP_SLICE2_WIDTH                 2U
#define LPDDR4__PI_DATA_BYTE_SWAP_SLICE2__REG DENALI_PI_103
#define LPDDR4__PI_DATA_BYTE_SWAP_SLICE2__FLD LPDDR4__DENALI_PI_103__PI_DATA_BYTE_SWAP_SLICE2

#define LPDDR4__DENALI_PI_103__PI_DATA_BYTE_SWAP_SLICE3_MASK         0x00000300U
#define LPDDR4__DENALI_PI_103__PI_DATA_BYTE_SWAP_SLICE3_SHIFT                 8U
#define LPDDR4__DENALI_PI_103__PI_DATA_BYTE_SWAP_SLICE3_WIDTH                 2U
#define LPDDR4__PI_DATA_BYTE_SWAP_SLICE3__REG DENALI_PI_103
#define LPDDR4__PI_DATA_BYTE_SWAP_SLICE3__FLD LPDDR4__DENALI_PI_103__PI_DATA_BYTE_SWAP_SLICE3

#define LPDDR4__DENALI_PI_103__PI_CTRLUPD_REQ_PER_AREF_EN_MASK       0x00010000U
#define LPDDR4__DENALI_PI_103__PI_CTRLUPD_REQ_PER_AREF_EN_SHIFT              16U
#define LPDDR4__DENALI_PI_103__PI_CTRLUPD_REQ_PER_AREF_EN_WIDTH               1U
#define LPDDR4__DENALI_PI_103__PI_CTRLUPD_REQ_PER_AREF_EN_WOCLR               0U
#define LPDDR4__DENALI_PI_103__PI_CTRLUPD_REQ_PER_AREF_EN_WOSET               0U
#define LPDDR4__PI_CTRLUPD_REQ_PER_AREF_EN__REG DENALI_PI_103
#define LPDDR4__PI_CTRLUPD_REQ_PER_AREF_EN__FLD LPDDR4__DENALI_PI_103__PI_CTRLUPD_REQ_PER_AREF_EN

#define LPDDR4__DENALI_PI_104_READ_MASK                              0x0703FFFFU
#define LPDDR4__DENALI_PI_104_WRITE_MASK                             0x0703FFFFU
#define LPDDR4__DENALI_PI_104__PI_TDFI_CTRLUPD_MIN_MASK              0x0000FFFFU
#define LPDDR4__DENALI_PI_104__PI_TDFI_CTRLUPD_MIN_SHIFT                      0U
#define LPDDR4__DENALI_PI_104__PI_TDFI_CTRLUPD_MIN_WIDTH                     16U
#define LPDDR4__PI_TDFI_CTRLUPD_MIN__REG DENALI_PI_104
#define LPDDR4__PI_TDFI_CTRLUPD_MIN__FLD LPDDR4__DENALI_PI_104__PI_TDFI_CTRLUPD_MIN

#define LPDDR4__DENALI_PI_104__PI_UPDATE_ERROR_STATUS_MASK           0x00030000U
#define LPDDR4__DENALI_PI_104__PI_UPDATE_ERROR_STATUS_SHIFT                  16U
#define LPDDR4__DENALI_PI_104__PI_UPDATE_ERROR_STATUS_WIDTH                   2U
#define LPDDR4__PI_UPDATE_ERROR_STATUS__REG DENALI_PI_104
#define LPDDR4__PI_UPDATE_ERROR_STATUS__FLD LPDDR4__DENALI_PI_104__PI_UPDATE_ERROR_STATUS

#define LPDDR4__DENALI_PI_104__PI_TDFI_PARIN_LAT_MASK                0x07000000U
#define LPDDR4__DENALI_PI_104__PI_TDFI_PARIN_LAT_SHIFT                       24U
#define LPDDR4__DENALI_PI_104__PI_TDFI_PARIN_LAT_WIDTH                        3U
#define LPDDR4__PI_TDFI_PARIN_LAT__REG DENALI_PI_104
#define LPDDR4__PI_TDFI_PARIN_LAT__FLD LPDDR4__DENALI_PI_104__PI_TDFI_PARIN_LAT

#define LPDDR4__DENALI_PI_105_READ_MASK                              0xFF010301U
#define LPDDR4__DENALI_PI_105_WRITE_MASK                             0xFF010301U
#define LPDDR4__DENALI_PI_105__PI_BIST_GO_MASK                       0x00000001U
#define LPDDR4__DENALI_PI_105__PI_BIST_GO_SHIFT                               0U
#define LPDDR4__DENALI_PI_105__PI_BIST_GO_WIDTH                               1U
#define LPDDR4__DENALI_PI_105__PI_BIST_GO_WOCLR                               0U
#define LPDDR4__DENALI_PI_105__PI_BIST_GO_WOSET                               0U
#define LPDDR4__PI_BIST_GO__REG DENALI_PI_105
#define LPDDR4__PI_BIST_GO__FLD LPDDR4__DENALI_PI_105__PI_BIST_GO

#define LPDDR4__DENALI_PI_105__PI_BIST_RESULT_MASK                   0x00000300U
#define LPDDR4__DENALI_PI_105__PI_BIST_RESULT_SHIFT                           8U
#define LPDDR4__DENALI_PI_105__PI_BIST_RESULT_WIDTH                           2U
#define LPDDR4__PI_BIST_RESULT__REG DENALI_PI_105
#define LPDDR4__PI_BIST_RESULT__FLD LPDDR4__DENALI_PI_105__PI_BIST_RESULT

#define LPDDR4__DENALI_PI_105__PI_BIST_LFSR_PATTERN_DONE_MASK        0x00010000U
#define LPDDR4__DENALI_PI_105__PI_BIST_LFSR_PATTERN_DONE_SHIFT               16U
#define LPDDR4__DENALI_PI_105__PI_BIST_LFSR_PATTERN_DONE_WIDTH                1U
#define LPDDR4__DENALI_PI_105__PI_BIST_LFSR_PATTERN_DONE_WOCLR                0U
#define LPDDR4__DENALI_PI_105__PI_BIST_LFSR_PATTERN_DONE_WOSET                0U
#define LPDDR4__PI_BIST_LFSR_PATTERN_DONE__REG DENALI_PI_105
#define LPDDR4__PI_BIST_LFSR_PATTERN_DONE__FLD LPDDR4__DENALI_PI_105__PI_BIST_LFSR_PATTERN_DONE

#define LPDDR4__DENALI_PI_105__PI_ADDR_SPACE_MASK                    0xFF000000U
#define LPDDR4__DENALI_PI_105__PI_ADDR_SPACE_SHIFT                           24U
#define LPDDR4__DENALI_PI_105__PI_ADDR_SPACE_WIDTH                            8U
#define LPDDR4__PI_ADDR_SPACE__REG DENALI_PI_105
#define LPDDR4__PI_ADDR_SPACE__FLD LPDDR4__DENALI_PI_105__PI_ADDR_SPACE

#define LPDDR4__DENALI_PI_106_READ_MASK                              0x00000101U
#define LPDDR4__DENALI_PI_106_WRITE_MASK                             0x00000101U
#define LPDDR4__DENALI_PI_106__PI_BIST_DATA_CHECK_MASK               0x00000001U
#define LPDDR4__DENALI_PI_106__PI_BIST_DATA_CHECK_SHIFT                       0U
#define LPDDR4__DENALI_PI_106__PI_BIST_DATA_CHECK_WIDTH                       1U
#define LPDDR4__DENALI_PI_106__PI_BIST_DATA_CHECK_WOCLR                       0U
#define LPDDR4__DENALI_PI_106__PI_BIST_DATA_CHECK_WOSET                       0U
#define LPDDR4__PI_BIST_DATA_CHECK__REG DENALI_PI_106
#define LPDDR4__PI_BIST_DATA_CHECK__FLD LPDDR4__DENALI_PI_106__PI_BIST_DATA_CHECK

#define LPDDR4__DENALI_PI_106__PI_BIST_ADDR_CHECK_MASK               0x00000100U
#define LPDDR4__DENALI_PI_106__PI_BIST_ADDR_CHECK_SHIFT                       8U
#define LPDDR4__DENALI_PI_106__PI_BIST_ADDR_CHECK_WIDTH                       1U
#define LPDDR4__DENALI_PI_106__PI_BIST_ADDR_CHECK_WOCLR                       0U
#define LPDDR4__DENALI_PI_106__PI_BIST_ADDR_CHECK_WOSET                       0U
#define LPDDR4__PI_BIST_ADDR_CHECK__REG DENALI_PI_106
#define LPDDR4__PI_BIST_ADDR_CHECK__FLD LPDDR4__DENALI_PI_106__PI_BIST_ADDR_CHECK

#define LPDDR4__DENALI_PI_107_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_107_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_107__PI_BIST_START_ADDRESS_0_MASK          0xFFFFFFFFU
#define LPDDR4__DENALI_PI_107__PI_BIST_START_ADDRESS_0_SHIFT                  0U
#define LPDDR4__DENALI_PI_107__PI_BIST_START_ADDRESS_0_WIDTH                 32U
#define LPDDR4__PI_BIST_START_ADDRESS_0__REG DENALI_PI_107
#define LPDDR4__PI_BIST_START_ADDRESS_0__FLD LPDDR4__DENALI_PI_107__PI_BIST_START_ADDRESS_0

#define LPDDR4__DENALI_PI_108_READ_MASK                              0x0000FF07U
#define LPDDR4__DENALI_PI_108_WRITE_MASK                             0x0000FF07U
#define LPDDR4__DENALI_PI_108__PI_BIST_START_ADDRESS_1_MASK          0x00000007U
#define LPDDR4__DENALI_PI_108__PI_BIST_START_ADDRESS_1_SHIFT                  0U
#define LPDDR4__DENALI_PI_108__PI_BIST_START_ADDRESS_1_WIDTH                  3U
#define LPDDR4__PI_BIST_START_ADDRESS_1__REG DENALI_PI_108
#define LPDDR4__PI_BIST_START_ADDRESS_1__FLD LPDDR4__DENALI_PI_108__PI_BIST_START_ADDRESS_1

#define LPDDR4__DENALI_PI_108__PI_MBIST_INIT_PATTERN_MASK            0x0000FF00U
#define LPDDR4__DENALI_PI_108__PI_MBIST_INIT_PATTERN_SHIFT                    8U
#define LPDDR4__DENALI_PI_108__PI_MBIST_INIT_PATTERN_WIDTH                    8U
#define LPDDR4__PI_MBIST_INIT_PATTERN__REG DENALI_PI_108
#define LPDDR4__PI_MBIST_INIT_PATTERN__FLD LPDDR4__DENALI_PI_108__PI_MBIST_INIT_PATTERN

#define LPDDR4__DENALI_PI_109_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_109_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_109__PI_BIST_DATA_MASK_0_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_109__PI_BIST_DATA_MASK_0_SHIFT                      0U
#define LPDDR4__DENALI_PI_109__PI_BIST_DATA_MASK_0_WIDTH                     32U
#define LPDDR4__PI_BIST_DATA_MASK_0__REG DENALI_PI_109
#define LPDDR4__PI_BIST_DATA_MASK_0__FLD LPDDR4__DENALI_PI_109__PI_BIST_DATA_MASK_0

#define LPDDR4__DENALI_PI_110_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_110_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_110__PI_BIST_DATA_MASK_1_MASK              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_110__PI_BIST_DATA_MASK_1_SHIFT                      0U
#define LPDDR4__DENALI_PI_110__PI_BIST_DATA_MASK_1_WIDTH                     32U
#define LPDDR4__PI_BIST_DATA_MASK_1__REG DENALI_PI_110
#define LPDDR4__PI_BIST_DATA_MASK_1__FLD LPDDR4__DENALI_PI_110__PI_BIST_DATA_MASK_1

#define LPDDR4__DENALI_PI_111_READ_MASK                              0x0FFF0FFFU
#define LPDDR4__DENALI_PI_111_WRITE_MASK                             0x0FFF0FFFU
#define LPDDR4__DENALI_PI_111__PI_BIST_ERR_COUNT_MASK                0x00000FFFU
#define LPDDR4__DENALI_PI_111__PI_BIST_ERR_COUNT_SHIFT                        0U
#define LPDDR4__DENALI_PI_111__PI_BIST_ERR_COUNT_WIDTH                       12U
#define LPDDR4__PI_BIST_ERR_COUNT__REG DENALI_PI_111
#define LPDDR4__PI_BIST_ERR_COUNT__FLD LPDDR4__DENALI_PI_111__PI_BIST_ERR_COUNT

#define LPDDR4__DENALI_PI_111__PI_BIST_ERR_STOP_MASK                 0x0FFF0000U
#define LPDDR4__DENALI_PI_111__PI_BIST_ERR_STOP_SHIFT                        16U
#define LPDDR4__DENALI_PI_111__PI_BIST_ERR_STOP_WIDTH                        12U
#define LPDDR4__PI_BIST_ERR_STOP__REG DENALI_PI_111
#define LPDDR4__PI_BIST_ERR_STOP__FLD LPDDR4__DENALI_PI_111__PI_BIST_ERR_STOP

#define LPDDR4__DENALI_PI_112_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_112_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_112__PI_BIST_ADDR_MASK_0_0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PI_112__PI_BIST_ADDR_MASK_0_0_SHIFT                    0U
#define LPDDR4__DENALI_PI_112__PI_BIST_ADDR_MASK_0_0_WIDTH                   32U
#define LPDDR4__PI_BIST_ADDR_MASK_0_0__REG DENALI_PI_112
#define LPDDR4__PI_BIST_ADDR_MASK_0_0__FLD LPDDR4__DENALI_PI_112__PI_BIST_ADDR_MASK_0_0

#define LPDDR4__DENALI_PI_113_READ_MASK                              0x0000000FU
#define LPDDR4__DENALI_PI_113_WRITE_MASK                             0x0000000FU
#define LPDDR4__DENALI_PI_113__PI_BIST_ADDR_MASK_0_1_MASK            0x0000000FU
#define LPDDR4__DENALI_PI_113__PI_BIST_ADDR_MASK_0_1_SHIFT                    0U
#define LPDDR4__DENALI_PI_113__PI_BIST_ADDR_MASK_0_1_WIDTH                    4U
#define LPDDR4__PI_BIST_ADDR_MASK_0_1__REG DENALI_PI_113
#define LPDDR4__PI_BIST_ADDR_MASK_0_1__FLD LPDDR4__DENALI_PI_113__PI_BIST_ADDR_MASK_0_1

#define LPDDR4__DENALI_PI_114_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_114_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_114__PI_BIST_ADDR_MASK_1_0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PI_114__PI_BIST_ADDR_MASK_1_0_SHIFT                    0U
#define LPDDR4__DENALI_PI_114__PI_BIST_ADDR_MASK_1_0_WIDTH                   32U
#define LPDDR4__PI_BIST_ADDR_MASK_1_0__REG DENALI_PI_114
#define LPDDR4__PI_BIST_ADDR_MASK_1_0__FLD LPDDR4__DENALI_PI_114__PI_BIST_ADDR_MASK_1_0

#define LPDDR4__DENALI_PI_115_READ_MASK                              0x0000000FU
#define LPDDR4__DENALI_PI_115_WRITE_MASK                             0x0000000FU
#define LPDDR4__DENALI_PI_115__PI_BIST_ADDR_MASK_1_1_MASK            0x0000000FU
#define LPDDR4__DENALI_PI_115__PI_BIST_ADDR_MASK_1_1_SHIFT                    0U
#define LPDDR4__DENALI_PI_115__PI_BIST_ADDR_MASK_1_1_WIDTH                    4U
#define LPDDR4__PI_BIST_ADDR_MASK_1_1__REG DENALI_PI_115
#define LPDDR4__PI_BIST_ADDR_MASK_1_1__FLD LPDDR4__DENALI_PI_115__PI_BIST_ADDR_MASK_1_1

#define LPDDR4__DENALI_PI_116_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_116_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_116__PI_BIST_ADDR_MASK_2_0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PI_116__PI_BIST_ADDR_MASK_2_0_SHIFT                    0U
#define LPDDR4__DENALI_PI_116__PI_BIST_ADDR_MASK_2_0_WIDTH                   32U
#define LPDDR4__PI_BIST_ADDR_MASK_2_0__REG DENALI_PI_116
#define LPDDR4__PI_BIST_ADDR_MASK_2_0__FLD LPDDR4__DENALI_PI_116__PI_BIST_ADDR_MASK_2_0

#define LPDDR4__DENALI_PI_117_READ_MASK                              0x0000000FU
#define LPDDR4__DENALI_PI_117_WRITE_MASK                             0x0000000FU
#define LPDDR4__DENALI_PI_117__PI_BIST_ADDR_MASK_2_1_MASK            0x0000000FU
#define LPDDR4__DENALI_PI_117__PI_BIST_ADDR_MASK_2_1_SHIFT                    0U
#define LPDDR4__DENALI_PI_117__PI_BIST_ADDR_MASK_2_1_WIDTH                    4U
#define LPDDR4__PI_BIST_ADDR_MASK_2_1__REG DENALI_PI_117
#define LPDDR4__PI_BIST_ADDR_MASK_2_1__FLD LPDDR4__DENALI_PI_117__PI_BIST_ADDR_MASK_2_1

#define LPDDR4__DENALI_PI_118_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_118_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_118__PI_BIST_ADDR_MASK_3_0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PI_118__PI_BIST_ADDR_MASK_3_0_SHIFT                    0U
#define LPDDR4__DENALI_PI_118__PI_BIST_ADDR_MASK_3_0_WIDTH                   32U
#define LPDDR4__PI_BIST_ADDR_MASK_3_0__REG DENALI_PI_118
#define LPDDR4__PI_BIST_ADDR_MASK_3_0__FLD LPDDR4__DENALI_PI_118__PI_BIST_ADDR_MASK_3_0

#define LPDDR4__DENALI_PI_119_READ_MASK                              0x0000000FU
#define LPDDR4__DENALI_PI_119_WRITE_MASK                             0x0000000FU
#define LPDDR4__DENALI_PI_119__PI_BIST_ADDR_MASK_3_1_MASK            0x0000000FU
#define LPDDR4__DENALI_PI_119__PI_BIST_ADDR_MASK_3_1_SHIFT                    0U
#define LPDDR4__DENALI_PI_119__PI_BIST_ADDR_MASK_3_1_WIDTH                    4U
#define LPDDR4__PI_BIST_ADDR_MASK_3_1__REG DENALI_PI_119
#define LPDDR4__PI_BIST_ADDR_MASK_3_1__FLD LPDDR4__DENALI_PI_119__PI_BIST_ADDR_MASK_3_1

#define LPDDR4__DENALI_PI_120_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_120_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_120__PI_BIST_ADDR_MASK_4_0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PI_120__PI_BIST_ADDR_MASK_4_0_SHIFT                    0U
#define LPDDR4__DENALI_PI_120__PI_BIST_ADDR_MASK_4_0_WIDTH                   32U
#define LPDDR4__PI_BIST_ADDR_MASK_4_0__REG DENALI_PI_120
#define LPDDR4__PI_BIST_ADDR_MASK_4_0__FLD LPDDR4__DENALI_PI_120__PI_BIST_ADDR_MASK_4_0

#define LPDDR4__DENALI_PI_121_READ_MASK                              0x0000000FU
#define LPDDR4__DENALI_PI_121_WRITE_MASK                             0x0000000FU
#define LPDDR4__DENALI_PI_121__PI_BIST_ADDR_MASK_4_1_MASK            0x0000000FU
#define LPDDR4__DENALI_PI_121__PI_BIST_ADDR_MASK_4_1_SHIFT                    0U
#define LPDDR4__DENALI_PI_121__PI_BIST_ADDR_MASK_4_1_WIDTH                    4U
#define LPDDR4__PI_BIST_ADDR_MASK_4_1__REG DENALI_PI_121
#define LPDDR4__PI_BIST_ADDR_MASK_4_1__FLD LPDDR4__DENALI_PI_121__PI_BIST_ADDR_MASK_4_1

#define LPDDR4__DENALI_PI_122_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_122_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_122__PI_BIST_ADDR_MASK_5_0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PI_122__PI_BIST_ADDR_MASK_5_0_SHIFT                    0U
#define LPDDR4__DENALI_PI_122__PI_BIST_ADDR_MASK_5_0_WIDTH                   32U
#define LPDDR4__PI_BIST_ADDR_MASK_5_0__REG DENALI_PI_122
#define LPDDR4__PI_BIST_ADDR_MASK_5_0__FLD LPDDR4__DENALI_PI_122__PI_BIST_ADDR_MASK_5_0

#define LPDDR4__DENALI_PI_123_READ_MASK                              0x0000000FU
#define LPDDR4__DENALI_PI_123_WRITE_MASK                             0x0000000FU
#define LPDDR4__DENALI_PI_123__PI_BIST_ADDR_MASK_5_1_MASK            0x0000000FU
#define LPDDR4__DENALI_PI_123__PI_BIST_ADDR_MASK_5_1_SHIFT                    0U
#define LPDDR4__DENALI_PI_123__PI_BIST_ADDR_MASK_5_1_WIDTH                    4U
#define LPDDR4__PI_BIST_ADDR_MASK_5_1__REG DENALI_PI_123
#define LPDDR4__PI_BIST_ADDR_MASK_5_1__FLD LPDDR4__DENALI_PI_123__PI_BIST_ADDR_MASK_5_1

#define LPDDR4__DENALI_PI_124_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_124_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_124__PI_BIST_ADDR_MASK_6_0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PI_124__PI_BIST_ADDR_MASK_6_0_SHIFT                    0U
#define LPDDR4__DENALI_PI_124__PI_BIST_ADDR_MASK_6_0_WIDTH                   32U
#define LPDDR4__PI_BIST_ADDR_MASK_6_0__REG DENALI_PI_124
#define LPDDR4__PI_BIST_ADDR_MASK_6_0__FLD LPDDR4__DENALI_PI_124__PI_BIST_ADDR_MASK_6_0

#define LPDDR4__DENALI_PI_125_READ_MASK                              0x0000000FU
#define LPDDR4__DENALI_PI_125_WRITE_MASK                             0x0000000FU
#define LPDDR4__DENALI_PI_125__PI_BIST_ADDR_MASK_6_1_MASK            0x0000000FU
#define LPDDR4__DENALI_PI_125__PI_BIST_ADDR_MASK_6_1_SHIFT                    0U
#define LPDDR4__DENALI_PI_125__PI_BIST_ADDR_MASK_6_1_WIDTH                    4U
#define LPDDR4__PI_BIST_ADDR_MASK_6_1__REG DENALI_PI_125
#define LPDDR4__PI_BIST_ADDR_MASK_6_1__FLD LPDDR4__DENALI_PI_125__PI_BIST_ADDR_MASK_6_1

#define LPDDR4__DENALI_PI_126_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_126_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_126__PI_BIST_ADDR_MASK_7_0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PI_126__PI_BIST_ADDR_MASK_7_0_SHIFT                    0U
#define LPDDR4__DENALI_PI_126__PI_BIST_ADDR_MASK_7_0_WIDTH                   32U
#define LPDDR4__PI_BIST_ADDR_MASK_7_0__REG DENALI_PI_126
#define LPDDR4__PI_BIST_ADDR_MASK_7_0__FLD LPDDR4__DENALI_PI_126__PI_BIST_ADDR_MASK_7_0

#define LPDDR4__DENALI_PI_127_READ_MASK                              0x0000000FU
#define LPDDR4__DENALI_PI_127_WRITE_MASK                             0x0000000FU
#define LPDDR4__DENALI_PI_127__PI_BIST_ADDR_MASK_7_1_MASK            0x0000000FU
#define LPDDR4__DENALI_PI_127__PI_BIST_ADDR_MASK_7_1_SHIFT                    0U
#define LPDDR4__DENALI_PI_127__PI_BIST_ADDR_MASK_7_1_WIDTH                    4U
#define LPDDR4__PI_BIST_ADDR_MASK_7_1__REG DENALI_PI_127
#define LPDDR4__PI_BIST_ADDR_MASK_7_1__FLD LPDDR4__DENALI_PI_127__PI_BIST_ADDR_MASK_7_1

#define LPDDR4__DENALI_PI_128_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_128_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_128__PI_BIST_ADDR_MASK_8_0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PI_128__PI_BIST_ADDR_MASK_8_0_SHIFT                    0U
#define LPDDR4__DENALI_PI_128__PI_BIST_ADDR_MASK_8_0_WIDTH                   32U
#define LPDDR4__PI_BIST_ADDR_MASK_8_0__REG DENALI_PI_128
#define LPDDR4__PI_BIST_ADDR_MASK_8_0__FLD LPDDR4__DENALI_PI_128__PI_BIST_ADDR_MASK_8_0

#define LPDDR4__DENALI_PI_129_READ_MASK                              0x0000000FU
#define LPDDR4__DENALI_PI_129_WRITE_MASK                             0x0000000FU
#define LPDDR4__DENALI_PI_129__PI_BIST_ADDR_MASK_8_1_MASK            0x0000000FU
#define LPDDR4__DENALI_PI_129__PI_BIST_ADDR_MASK_8_1_SHIFT                    0U
#define LPDDR4__DENALI_PI_129__PI_BIST_ADDR_MASK_8_1_WIDTH                    4U
#define LPDDR4__PI_BIST_ADDR_MASK_8_1__REG DENALI_PI_129
#define LPDDR4__PI_BIST_ADDR_MASK_8_1__FLD LPDDR4__DENALI_PI_129__PI_BIST_ADDR_MASK_8_1

#define LPDDR4__DENALI_PI_130_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_130_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_130__PI_BIST_ADDR_MASK_9_0_MASK            0xFFFFFFFFU
#define LPDDR4__DENALI_PI_130__PI_BIST_ADDR_MASK_9_0_SHIFT                    0U
#define LPDDR4__DENALI_PI_130__PI_BIST_ADDR_MASK_9_0_WIDTH                   32U
#define LPDDR4__PI_BIST_ADDR_MASK_9_0__REG DENALI_PI_130
#define LPDDR4__PI_BIST_ADDR_MASK_9_0__FLD LPDDR4__DENALI_PI_130__PI_BIST_ADDR_MASK_9_0

#define LPDDR4__DENALI_PI_131_READ_MASK                              0x0303070FU
#define LPDDR4__DENALI_PI_131_WRITE_MASK                             0x0303070FU
#define LPDDR4__DENALI_PI_131__PI_BIST_ADDR_MASK_9_1_MASK            0x0000000FU
#define LPDDR4__DENALI_PI_131__PI_BIST_ADDR_MASK_9_1_SHIFT                    0U
#define LPDDR4__DENALI_PI_131__PI_BIST_ADDR_MASK_9_1_WIDTH                    4U
#define LPDDR4__PI_BIST_ADDR_MASK_9_1__REG DENALI_PI_131
#define LPDDR4__PI_BIST_ADDR_MASK_9_1__FLD LPDDR4__DENALI_PI_131__PI_BIST_ADDR_MASK_9_1

#define LPDDR4__DENALI_PI_131__PI_BIST_MODE_MASK                     0x00000700U
#define LPDDR4__DENALI_PI_131__PI_BIST_MODE_SHIFT                             8U
#define LPDDR4__DENALI_PI_131__PI_BIST_MODE_WIDTH                             3U
#define LPDDR4__PI_BIST_MODE__REG DENALI_PI_131
#define LPDDR4__PI_BIST_MODE__FLD LPDDR4__DENALI_PI_131__PI_BIST_MODE

#define LPDDR4__DENALI_PI_131__PI_BIST_ADDR_MODE_MASK                0x00030000U
#define LPDDR4__DENALI_PI_131__PI_BIST_ADDR_MODE_SHIFT                       16U
#define LPDDR4__DENALI_PI_131__PI_BIST_ADDR_MODE_WIDTH                        2U
#define LPDDR4__PI_BIST_ADDR_MODE__REG DENALI_PI_131
#define LPDDR4__PI_BIST_ADDR_MODE__FLD LPDDR4__DENALI_PI_131__PI_BIST_ADDR_MODE

#define LPDDR4__DENALI_PI_131__PI_BIST_PAT_MODE_MASK                 0x03000000U
#define LPDDR4__DENALI_PI_131__PI_BIST_PAT_MODE_SHIFT                        24U
#define LPDDR4__DENALI_PI_131__PI_BIST_PAT_MODE_WIDTH                         2U
#define LPDDR4__PI_BIST_PAT_MODE__REG DENALI_PI_131
#define LPDDR4__PI_BIST_PAT_MODE__FLD LPDDR4__DENALI_PI_131__PI_BIST_PAT_MODE

#define LPDDR4__DENALI_PI_132_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_132_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_132__PI_BIST_USER_PAT_0_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_132__PI_BIST_USER_PAT_0_SHIFT                       0U
#define LPDDR4__DENALI_PI_132__PI_BIST_USER_PAT_0_WIDTH                      32U
#define LPDDR4__PI_BIST_USER_PAT_0__REG DENALI_PI_132
#define LPDDR4__PI_BIST_USER_PAT_0__FLD LPDDR4__DENALI_PI_132__PI_BIST_USER_PAT_0

#define LPDDR4__DENALI_PI_133_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_133_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_133__PI_BIST_USER_PAT_1_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_133__PI_BIST_USER_PAT_1_SHIFT                       0U
#define LPDDR4__DENALI_PI_133__PI_BIST_USER_PAT_1_WIDTH                      32U
#define LPDDR4__PI_BIST_USER_PAT_1__REG DENALI_PI_133
#define LPDDR4__PI_BIST_USER_PAT_1__FLD LPDDR4__DENALI_PI_133__PI_BIST_USER_PAT_1

#define LPDDR4__DENALI_PI_134_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_134_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_134__PI_BIST_USER_PAT_2_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_134__PI_BIST_USER_PAT_2_SHIFT                       0U
#define LPDDR4__DENALI_PI_134__PI_BIST_USER_PAT_2_WIDTH                      32U
#define LPDDR4__PI_BIST_USER_PAT_2__REG DENALI_PI_134
#define LPDDR4__PI_BIST_USER_PAT_2__FLD LPDDR4__DENALI_PI_134__PI_BIST_USER_PAT_2

#define LPDDR4__DENALI_PI_135_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_135_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_135__PI_BIST_USER_PAT_3_MASK               0xFFFFFFFFU
#define LPDDR4__DENALI_PI_135__PI_BIST_USER_PAT_3_SHIFT                       0U
#define LPDDR4__DENALI_PI_135__PI_BIST_USER_PAT_3_WIDTH                      32U
#define LPDDR4__PI_BIST_USER_PAT_3__REG DENALI_PI_135
#define LPDDR4__PI_BIST_USER_PAT_3__FLD LPDDR4__DENALI_PI_135__PI_BIST_USER_PAT_3

#define LPDDR4__DENALI_PI_136_READ_MASK                              0x0000007FU
#define LPDDR4__DENALI_PI_136_WRITE_MASK                             0x0000007FU
#define LPDDR4__DENALI_PI_136__PI_BIST_PAT_NUM_MASK                  0x0000007FU
#define LPDDR4__DENALI_PI_136__PI_BIST_PAT_NUM_SHIFT                          0U
#define LPDDR4__DENALI_PI_136__PI_BIST_PAT_NUM_WIDTH                          7U
#define LPDDR4__PI_BIST_PAT_NUM__REG DENALI_PI_136
#define LPDDR4__PI_BIST_PAT_NUM__FLD LPDDR4__DENALI_PI_136__PI_BIST_PAT_NUM

#define LPDDR4__DENALI_PI_137_READ_MASK                              0x3FFFFFFFU
#define LPDDR4__DENALI_PI_137_WRITE_MASK                             0x3FFFFFFFU
#define LPDDR4__DENALI_PI_137__PI_BIST_STAGE_0_MASK                  0x3FFFFFFFU
#define LPDDR4__DENALI_PI_137__PI_BIST_STAGE_0_SHIFT                          0U
#define LPDDR4__DENALI_PI_137__PI_BIST_STAGE_0_WIDTH                         30U
#define LPDDR4__PI_BIST_STAGE_0__REG DENALI_PI_137
#define LPDDR4__PI_BIST_STAGE_0__FLD LPDDR4__DENALI_PI_137__PI_BIST_STAGE_0

#define LPDDR4__DENALI_PI_138_READ_MASK                              0x3FFFFFFFU
#define LPDDR4__DENALI_PI_138_WRITE_MASK                             0x3FFFFFFFU
#define LPDDR4__DENALI_PI_138__PI_BIST_STAGE_1_MASK                  0x3FFFFFFFU
#define LPDDR4__DENALI_PI_138__PI_BIST_STAGE_1_SHIFT                          0U
#define LPDDR4__DENALI_PI_138__PI_BIST_STAGE_1_WIDTH                         30U
#define LPDDR4__PI_BIST_STAGE_1__REG DENALI_PI_138
#define LPDDR4__PI_BIST_STAGE_1__FLD LPDDR4__DENALI_PI_138__PI_BIST_STAGE_1

#define LPDDR4__DENALI_PI_139_READ_MASK                              0x3FFFFFFFU
#define LPDDR4__DENALI_PI_139_WRITE_MASK                             0x3FFFFFFFU
#define LPDDR4__DENALI_PI_139__PI_BIST_STAGE_2_MASK                  0x3FFFFFFFU
#define LPDDR4__DENALI_PI_139__PI_BIST_STAGE_2_SHIFT                          0U
#define LPDDR4__DENALI_PI_139__PI_BIST_STAGE_2_WIDTH                         30U
#define LPDDR4__PI_BIST_STAGE_2__REG DENALI_PI_139
#define LPDDR4__PI_BIST_STAGE_2__FLD LPDDR4__DENALI_PI_139__PI_BIST_STAGE_2

#define LPDDR4__DENALI_PI_140_READ_MASK                              0x3FFFFFFFU
#define LPDDR4__DENALI_PI_140_WRITE_MASK                             0x3FFFFFFFU
#define LPDDR4__DENALI_PI_140__PI_BIST_STAGE_3_MASK                  0x3FFFFFFFU
#define LPDDR4__DENALI_PI_140__PI_BIST_STAGE_3_SHIFT                          0U
#define LPDDR4__DENALI_PI_140__PI_BIST_STAGE_3_WIDTH                         30U
#define LPDDR4__PI_BIST_STAGE_3__REG DENALI_PI_140
#define LPDDR4__PI_BIST_STAGE_3__FLD LPDDR4__DENALI_PI_140__PI_BIST_STAGE_3

#define LPDDR4__DENALI_PI_141_READ_MASK                              0x3FFFFFFFU
#define LPDDR4__DENALI_PI_141_WRITE_MASK                             0x3FFFFFFFU
#define LPDDR4__DENALI_PI_141__PI_BIST_STAGE_4_MASK                  0x3FFFFFFFU
#define LPDDR4__DENALI_PI_141__PI_BIST_STAGE_4_SHIFT                          0U
#define LPDDR4__DENALI_PI_141__PI_BIST_STAGE_4_WIDTH                         30U
#define LPDDR4__PI_BIST_STAGE_4__REG DENALI_PI_141
#define LPDDR4__PI_BIST_STAGE_4__FLD LPDDR4__DENALI_PI_141__PI_BIST_STAGE_4

#define LPDDR4__DENALI_PI_142_READ_MASK                              0x3FFFFFFFU
#define LPDDR4__DENALI_PI_142_WRITE_MASK                             0x3FFFFFFFU
#define LPDDR4__DENALI_PI_142__PI_BIST_STAGE_5_MASK                  0x3FFFFFFFU
#define LPDDR4__DENALI_PI_142__PI_BIST_STAGE_5_SHIFT                          0U
#define LPDDR4__DENALI_PI_142__PI_BIST_STAGE_5_WIDTH                         30U
#define LPDDR4__PI_BIST_STAGE_5__REG DENALI_PI_142
#define LPDDR4__PI_BIST_STAGE_5__FLD LPDDR4__DENALI_PI_142__PI_BIST_STAGE_5

#define LPDDR4__DENALI_PI_143_READ_MASK                              0x3FFFFFFFU
#define LPDDR4__DENALI_PI_143_WRITE_MASK                             0x3FFFFFFFU
#define LPDDR4__DENALI_PI_143__PI_BIST_STAGE_6_MASK                  0x3FFFFFFFU
#define LPDDR4__DENALI_PI_143__PI_BIST_STAGE_6_SHIFT                          0U
#define LPDDR4__DENALI_PI_143__PI_BIST_STAGE_6_WIDTH                         30U
#define LPDDR4__PI_BIST_STAGE_6__REG DENALI_PI_143
#define LPDDR4__PI_BIST_STAGE_6__FLD LPDDR4__DENALI_PI_143__PI_BIST_STAGE_6

#define LPDDR4__DENALI_PI_144_READ_MASK                              0x3FFFFFFFU
#define LPDDR4__DENALI_PI_144_WRITE_MASK                             0x3FFFFFFFU
#define LPDDR4__DENALI_PI_144__PI_BIST_STAGE_7_MASK                  0x3FFFFFFFU
#define LPDDR4__DENALI_PI_144__PI_BIST_STAGE_7_SHIFT                          0U
#define LPDDR4__DENALI_PI_144__PI_BIST_STAGE_7_WIDTH                         30U
#define LPDDR4__PI_BIST_STAGE_7__REG DENALI_PI_144
#define LPDDR4__PI_BIST_STAGE_7__FLD LPDDR4__DENALI_PI_144__PI_BIST_STAGE_7

#define LPDDR4__DENALI_PI_145_READ_MASK                              0x0101010FU
#define LPDDR4__DENALI_PI_145_WRITE_MASK                             0x0101010FU
#define LPDDR4__DENALI_PI_145__PI_COL_DIFF_MASK                      0x0000000FU
#define LPDDR4__DENALI_PI_145__PI_COL_DIFF_SHIFT                              0U
#define LPDDR4__DENALI_PI_145__PI_COL_DIFF_WIDTH                              4U
#define LPDDR4__PI_COL_DIFF__REG DENALI_PI_145
#define LPDDR4__PI_COL_DIFF__FLD LPDDR4__DENALI_PI_145__PI_COL_DIFF

#define LPDDR4__DENALI_PI_145__PI_BG_ROTATE_EN_MASK                  0x00000100U
#define LPDDR4__DENALI_PI_145__PI_BG_ROTATE_EN_SHIFT                          8U
#define LPDDR4__DENALI_PI_145__PI_BG_ROTATE_EN_WIDTH                          1U
#define LPDDR4__DENALI_PI_145__PI_BG_ROTATE_EN_WOCLR                          0U
#define LPDDR4__DENALI_PI_145__PI_BG_ROTATE_EN_WOSET                          0U
#define LPDDR4__PI_BG_ROTATE_EN__REG DENALI_PI_145
#define LPDDR4__PI_BG_ROTATE_EN__FLD LPDDR4__DENALI_PI_145__PI_BG_ROTATE_EN

#define LPDDR4__DENALI_PI_145__PI_CRC_CALC_MASK                      0x00010000U
#define LPDDR4__DENALI_PI_145__PI_CRC_CALC_SHIFT                             16U
#define LPDDR4__DENALI_PI_145__PI_CRC_CALC_WIDTH                              1U
#define LPDDR4__DENALI_PI_145__PI_CRC_CALC_WOCLR                              0U
#define LPDDR4__DENALI_PI_145__PI_CRC_CALC_WOSET                              0U
#define LPDDR4__PI_CRC_CALC__REG DENALI_PI_145
#define LPDDR4__PI_CRC_CALC__FLD LPDDR4__DENALI_PI_145__PI_CRC_CALC

#define LPDDR4__DENALI_PI_145__PI_SELF_REFRESH_EN_MASK               0x01000000U
#define LPDDR4__DENALI_PI_145__PI_SELF_REFRESH_EN_SHIFT                      24U
#define LPDDR4__DENALI_PI_145__PI_SELF_REFRESH_EN_WIDTH                       1U
#define LPDDR4__DENALI_PI_145__PI_SELF_REFRESH_EN_WOCLR                       0U
#define LPDDR4__DENALI_PI_145__PI_SELF_REFRESH_EN_WOSET                       0U
#define LPDDR4__PI_SELF_REFRESH_EN__REG DENALI_PI_145
#define LPDDR4__PI_SELF_REFRESH_EN__FLD LPDDR4__DENALI_PI_145__PI_SELF_REFRESH_EN

#define LPDDR4__DENALI_PI_146_READ_MASK                              0x00010101U
#define LPDDR4__DENALI_PI_146_WRITE_MASK                             0x00010101U
#define LPDDR4__DENALI_PI_146__PI_MC_PWRUP_SREFRESH_EXIT_MASK        0x00000001U
#define LPDDR4__DENALI_PI_146__PI_MC_PWRUP_SREFRESH_EXIT_SHIFT                0U
#define LPDDR4__DENALI_PI_146__PI_MC_PWRUP_SREFRESH_EXIT_WIDTH                1U
#define LPDDR4__DENALI_PI_146__PI_MC_PWRUP_SREFRESH_EXIT_WOCLR                0U
#define LPDDR4__DENALI_PI_146__PI_MC_PWRUP_SREFRESH_EXIT_WOSET                0U
#define LPDDR4__PI_MC_PWRUP_SREFRESH_EXIT__REG DENALI_PI_146
#define LPDDR4__PI_MC_PWRUP_SREFRESH_EXIT__FLD LPDDR4__DENALI_PI_146__PI_MC_PWRUP_SREFRESH_EXIT

#define LPDDR4__DENALI_PI_146__PI_PWRUP_SREFRESH_EXIT_MASK           0x00000100U
#define LPDDR4__DENALI_PI_146__PI_PWRUP_SREFRESH_EXIT_SHIFT                   8U
#define LPDDR4__DENALI_PI_146__PI_PWRUP_SREFRESH_EXIT_WIDTH                   1U
#define LPDDR4__DENALI_PI_146__PI_PWRUP_SREFRESH_EXIT_WOCLR                   0U
#define LPDDR4__DENALI_PI_146__PI_PWRUP_SREFRESH_EXIT_WOSET                   0U
#define LPDDR4__PI_PWRUP_SREFRESH_EXIT__REG DENALI_PI_146
#define LPDDR4__PI_PWRUP_SREFRESH_EXIT__FLD LPDDR4__DENALI_PI_146__PI_PWRUP_SREFRESH_EXIT

#define LPDDR4__DENALI_PI_146__PI_SREFRESH_EXIT_NO_REFRESH_MASK      0x00010000U
#define LPDDR4__DENALI_PI_146__PI_SREFRESH_EXIT_NO_REFRESH_SHIFT             16U
#define LPDDR4__DENALI_PI_146__PI_SREFRESH_EXIT_NO_REFRESH_WIDTH              1U
#define LPDDR4__DENALI_PI_146__PI_SREFRESH_EXIT_NO_REFRESH_WOCLR              0U
#define LPDDR4__DENALI_PI_146__PI_SREFRESH_EXIT_NO_REFRESH_WOSET              0U
#define LPDDR4__PI_SREFRESH_EXIT_NO_REFRESH__REG DENALI_PI_146
#define LPDDR4__PI_SREFRESH_EXIT_NO_REFRESH__FLD LPDDR4__DENALI_PI_146__PI_SREFRESH_EXIT_NO_REFRESH

#define LPDDR4__DENALI_PI_146__PI_SREF_ENTRY_REQ_MASK                0x01000000U
#define LPDDR4__DENALI_PI_146__PI_SREF_ENTRY_REQ_SHIFT                       24U
#define LPDDR4__DENALI_PI_146__PI_SREF_ENTRY_REQ_WIDTH                        1U
#define LPDDR4__DENALI_PI_146__PI_SREF_ENTRY_REQ_WOCLR                        0U
#define LPDDR4__DENALI_PI_146__PI_SREF_ENTRY_REQ_WOSET                        0U
#define LPDDR4__PI_SREF_ENTRY_REQ__REG DENALI_PI_146
#define LPDDR4__PI_SREF_ENTRY_REQ__FLD LPDDR4__DENALI_PI_146__PI_SREF_ENTRY_REQ

#define LPDDR4__DENALI_PI_147_READ_MASK                              0x01010101U
#define LPDDR4__DENALI_PI_147_WRITE_MASK                             0x01010101U
#define LPDDR4__DENALI_PI_147__PI_NO_MRW_BT_INIT_MASK                0x00000001U
#define LPDDR4__DENALI_PI_147__PI_NO_MRW_BT_INIT_SHIFT                        0U
#define LPDDR4__DENALI_PI_147__PI_NO_MRW_BT_INIT_WIDTH                        1U
#define LPDDR4__DENALI_PI_147__PI_NO_MRW_BT_INIT_WOCLR                        0U
#define LPDDR4__DENALI_PI_147__PI_NO_MRW_BT_INIT_WOSET                        0U
#define LPDDR4__PI_NO_MRW_BT_INIT__REG DENALI_PI_147
#define LPDDR4__PI_NO_MRW_BT_INIT__FLD LPDDR4__DENALI_PI_147__PI_NO_MRW_BT_INIT

#define LPDDR4__DENALI_PI_147__PI_NO_MRW_INIT_MASK                   0x00000100U
#define LPDDR4__DENALI_PI_147__PI_NO_MRW_INIT_SHIFT                           8U
#define LPDDR4__DENALI_PI_147__PI_NO_MRW_INIT_WIDTH                           1U
#define LPDDR4__DENALI_PI_147__PI_NO_MRW_INIT_WOCLR                           0U
#define LPDDR4__DENALI_PI_147__PI_NO_MRW_INIT_WOSET                           0U
#define LPDDR4__PI_NO_MRW_INIT__REG DENALI_PI_147
#define LPDDR4__PI_NO_MRW_INIT__FLD LPDDR4__DENALI_PI_147__PI_NO_MRW_INIT

#define LPDDR4__DENALI_PI_147__PI_NO_PHY_IND_TRAIN_INIT_MASK         0x00010000U
#define LPDDR4__DENALI_PI_147__PI_NO_PHY_IND_TRAIN_INIT_SHIFT                16U
#define LPDDR4__DENALI_PI_147__PI_NO_PHY_IND_TRAIN_INIT_WIDTH                 1U
#define LPDDR4__DENALI_PI_147__PI_NO_PHY_IND_TRAIN_INIT_WOCLR                 0U
#define LPDDR4__DENALI_PI_147__PI_NO_PHY_IND_TRAIN_INIT_WOSET                 0U
#define LPDDR4__PI_NO_PHY_IND_TRAIN_INIT__REG DENALI_PI_147
#define LPDDR4__PI_NO_PHY_IND_TRAIN_INIT__FLD LPDDR4__DENALI_PI_147__PI_NO_PHY_IND_TRAIN_INIT

#define LPDDR4__DENALI_PI_147__PI_NO_AUTO_MRR_INIT_MASK              0x01000000U
#define LPDDR4__DENALI_PI_147__PI_NO_AUTO_MRR_INIT_SHIFT                     24U
#define LPDDR4__DENALI_PI_147__PI_NO_AUTO_MRR_INIT_WIDTH                      1U
#define LPDDR4__DENALI_PI_147__PI_NO_AUTO_MRR_INIT_WOCLR                      0U
#define LPDDR4__DENALI_PI_147__PI_NO_AUTO_MRR_INIT_WOSET                      0U
#define LPDDR4__PI_NO_AUTO_MRR_INIT__REG DENALI_PI_147
#define LPDDR4__PI_NO_AUTO_MRR_INIT__FLD LPDDR4__DENALI_PI_147__PI_NO_AUTO_MRR_INIT

#define LPDDR4__DENALI_PI_148_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_148_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_148__PI_TRST_PWRON_MASK                    0xFFFFFFFFU
#define LPDDR4__DENALI_PI_148__PI_TRST_PWRON_SHIFT                            0U
#define LPDDR4__DENALI_PI_148__PI_TRST_PWRON_WIDTH                           32U
#define LPDDR4__PI_TRST_PWRON__REG DENALI_PI_148
#define LPDDR4__PI_TRST_PWRON__FLD LPDDR4__DENALI_PI_148__PI_TRST_PWRON

#define LPDDR4__DENALI_PI_149_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_149_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_149__PI_CKE_INACTIVE_MASK                  0xFFFFFFFFU
#define LPDDR4__DENALI_PI_149__PI_CKE_INACTIVE_SHIFT                          0U
#define LPDDR4__DENALI_PI_149__PI_CKE_INACTIVE_WIDTH                         32U
#define LPDDR4__PI_CKE_INACTIVE__REG DENALI_PI_149
#define LPDDR4__PI_CKE_INACTIVE__FLD LPDDR4__DENALI_PI_149__PI_CKE_INACTIVE

#define LPDDR4__DENALI_PI_150_READ_MASK                              0xFFFF0101U
#define LPDDR4__DENALI_PI_150_WRITE_MASK                             0xFFFF0101U
#define LPDDR4__DENALI_PI_150__PI_DLL_RST_MASK                       0x00000001U
#define LPDDR4__DENALI_PI_150__PI_DLL_RST_SHIFT                               0U
#define LPDDR4__DENALI_PI_150__PI_DLL_RST_WIDTH                               1U
#define LPDDR4__DENALI_PI_150__PI_DLL_RST_WOCLR                               0U
#define LPDDR4__DENALI_PI_150__PI_DLL_RST_WOSET                               0U
#define LPDDR4__PI_DLL_RST__REG DENALI_PI_150
#define LPDDR4__PI_DLL_RST__FLD LPDDR4__DENALI_PI_150__PI_DLL_RST

#define LPDDR4__DENALI_PI_150__PI_DRAM_INIT_EN_MASK                  0x00000100U
#define LPDDR4__DENALI_PI_150__PI_DRAM_INIT_EN_SHIFT                          8U
#define LPDDR4__DENALI_PI_150__PI_DRAM_INIT_EN_WIDTH                          1U
#define LPDDR4__DENALI_PI_150__PI_DRAM_INIT_EN_WOCLR                          0U
#define LPDDR4__DENALI_PI_150__PI_DRAM_INIT_EN_WOSET                          0U
#define LPDDR4__PI_DRAM_INIT_EN__REG DENALI_PI_150
#define LPDDR4__PI_DRAM_INIT_EN__FLD LPDDR4__DENALI_PI_150__PI_DRAM_INIT_EN

#define LPDDR4__DENALI_PI_150__PI_DLL_RST_DELAY_MASK                 0xFFFF0000U
#define LPDDR4__DENALI_PI_150__PI_DLL_RST_DELAY_SHIFT                        16U
#define LPDDR4__DENALI_PI_150__PI_DLL_RST_DELAY_WIDTH                        16U
#define LPDDR4__PI_DLL_RST_DELAY__REG DENALI_PI_150
#define LPDDR4__PI_DLL_RST_DELAY__FLD LPDDR4__DENALI_PI_150__PI_DLL_RST_DELAY

#define LPDDR4__DENALI_PI_151_READ_MASK                              0x000000FFU
#define LPDDR4__DENALI_PI_151_WRITE_MASK                             0x000000FFU
#define LPDDR4__DENALI_PI_151__PI_DLL_RST_ADJ_DLY_MASK               0x000000FFU
#define LPDDR4__DENALI_PI_151__PI_DLL_RST_ADJ_DLY_SHIFT                       0U
#define LPDDR4__DENALI_PI_151__PI_DLL_RST_ADJ_DLY_WIDTH                       8U
#define LPDDR4__PI_DLL_RST_ADJ_DLY__REG DENALI_PI_151
#define LPDDR4__PI_DLL_RST_ADJ_DLY__FLD LPDDR4__DENALI_PI_151__PI_DLL_RST_ADJ_DLY

#define LPDDR4__DENALI_PI_152_READ_MASK                              0x03FFFFFFU
#define LPDDR4__DENALI_PI_152_WRITE_MASK                             0x03FFFFFFU
#define LPDDR4__DENALI_PI_152__PI_WRITE_MODEREG_MASK                 0x03FFFFFFU
#define LPDDR4__DENALI_PI_152__PI_WRITE_MODEREG_SHIFT                         0U
#define LPDDR4__DENALI_PI_152__PI_WRITE_MODEREG_WIDTH                        26U
#define LPDDR4__PI_WRITE_MODEREG__REG DENALI_PI_152
#define LPDDR4__PI_WRITE_MODEREG__FLD LPDDR4__DENALI_PI_152__PI_WRITE_MODEREG

#define LPDDR4__DENALI_PI_153_READ_MASK                              0x000001FFU
#define LPDDR4__DENALI_PI_153_WRITE_MASK                             0x000001FFU
#define LPDDR4__DENALI_PI_153__PI_MRW_STATUS_MASK                    0x000000FFU
#define LPDDR4__DENALI_PI_153__PI_MRW_STATUS_SHIFT                            0U
#define LPDDR4__DENALI_PI_153__PI_MRW_STATUS_WIDTH                            8U
#define LPDDR4__PI_MRW_STATUS__REG DENALI_PI_153
#define LPDDR4__PI_MRW_STATUS__FLD LPDDR4__DENALI_PI_153__PI_MRW_STATUS

#define LPDDR4__DENALI_PI_153__PI_RESERVED27_MASK                    0x00000100U
#define LPDDR4__DENALI_PI_153__PI_RESERVED27_SHIFT                            8U
#define LPDDR4__DENALI_PI_153__PI_RESERVED27_WIDTH                            1U
#define LPDDR4__DENALI_PI_153__PI_RESERVED27_WOCLR                            0U
#define LPDDR4__DENALI_PI_153__PI_RESERVED27_WOSET                            0U
#define LPDDR4__PI_RESERVED27__REG DENALI_PI_153
#define LPDDR4__PI_RESERVED27__FLD LPDDR4__DENALI_PI_153__PI_RESERVED27

#define LPDDR4__DENALI_PI_154_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_154_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_154__PI_READ_MODEREG_MASK                  0x0001FFFFU
#define LPDDR4__DENALI_PI_154__PI_READ_MODEREG_SHIFT                          0U
#define LPDDR4__DENALI_PI_154__PI_READ_MODEREG_WIDTH                         17U
#define LPDDR4__PI_READ_MODEREG__REG DENALI_PI_154
#define LPDDR4__PI_READ_MODEREG__FLD LPDDR4__DENALI_PI_154__PI_READ_MODEREG

#define LPDDR4__DENALI_PI_155_READ_MASK                              0x01FFFFFFU
#define LPDDR4__DENALI_PI_155_WRITE_MASK                             0x01FFFFFFU
#define LPDDR4__DENALI_PI_155__PI_PERIPHERAL_MRR_DATA_0_MASK         0x00FFFFFFU
#define LPDDR4__DENALI_PI_155__PI_PERIPHERAL_MRR_DATA_0_SHIFT                 0U
#define LPDDR4__DENALI_PI_155__PI_PERIPHERAL_MRR_DATA_0_WIDTH                24U
#define LPDDR4__PI_PERIPHERAL_MRR_DATA_0__REG DENALI_PI_155
#define LPDDR4__PI_PERIPHERAL_MRR_DATA_0__FLD LPDDR4__DENALI_PI_155__PI_PERIPHERAL_MRR_DATA_0

#define LPDDR4__DENALI_PI_155__PI_NO_ZQ_INIT_MASK                    0x01000000U
#define LPDDR4__DENALI_PI_155__PI_NO_ZQ_INIT_SHIFT                           24U
#define LPDDR4__DENALI_PI_155__PI_NO_ZQ_INIT_WIDTH                            1U
#define LPDDR4__DENALI_PI_155__PI_NO_ZQ_INIT_WOCLR                            0U
#define LPDDR4__DENALI_PI_155__PI_NO_ZQ_INIT_WOSET                            0U
#define LPDDR4__PI_NO_ZQ_INIT__REG DENALI_PI_155
#define LPDDR4__PI_NO_ZQ_INIT__FLD LPDDR4__DENALI_PI_155__PI_NO_ZQ_INIT

#define LPDDR4__DENALI_PI_156_READ_MASK                              0x0101000FU
#define LPDDR4__DENALI_PI_156_WRITE_MASK                             0x0101000FU
#define LPDDR4__DENALI_PI_156__PI_RESERVED28_MASK                    0x0000000FU
#define LPDDR4__DENALI_PI_156__PI_RESERVED28_SHIFT                            0U
#define LPDDR4__DENALI_PI_156__PI_RESERVED28_WIDTH                            4U
#define LPDDR4__PI_RESERVED28__REG DENALI_PI_156
#define LPDDR4__PI_RESERVED28__FLD LPDDR4__DENALI_PI_156__PI_RESERVED28

#define LPDDR4__DENALI_PI_156__PI_RESERVED29_MASK                    0x00000F00U
#define LPDDR4__DENALI_PI_156__PI_RESERVED29_SHIFT                            8U
#define LPDDR4__DENALI_PI_156__PI_RESERVED29_WIDTH                            4U
#define LPDDR4__PI_RESERVED29__REG DENALI_PI_156
#define LPDDR4__PI_RESERVED29__FLD LPDDR4__DENALI_PI_156__PI_RESERVED29

#define LPDDR4__DENALI_PI_156__PI_ZQ_REQ_PENDING_MASK                0x00010000U
#define LPDDR4__DENALI_PI_156__PI_ZQ_REQ_PENDING_SHIFT                       16U
#define LPDDR4__DENALI_PI_156__PI_ZQ_REQ_PENDING_WIDTH                        1U
#define LPDDR4__DENALI_PI_156__PI_ZQ_REQ_PENDING_WOCLR                        0U
#define LPDDR4__DENALI_PI_156__PI_ZQ_REQ_PENDING_WOSET                        0U
#define LPDDR4__PI_ZQ_REQ_PENDING__REG DENALI_PI_156
#define LPDDR4__PI_ZQ_REQ_PENDING__FLD LPDDR4__DENALI_PI_156__PI_ZQ_REQ_PENDING

#define LPDDR4__DENALI_PI_156__PI_RESERVED30_MASK                    0x01000000U
#define LPDDR4__DENALI_PI_156__PI_RESERVED30_SHIFT                           24U
#define LPDDR4__DENALI_PI_156__PI_RESERVED30_WIDTH                            1U
#define LPDDR4__DENALI_PI_156__PI_RESERVED30_WOCLR                            0U
#define LPDDR4__DENALI_PI_156__PI_RESERVED30_WOSET                            0U
#define LPDDR4__PI_RESERVED30__REG DENALI_PI_156
#define LPDDR4__PI_RESERVED30__FLD LPDDR4__DENALI_PI_156__PI_RESERVED30

#define LPDDR4__DENALI_PI_157_READ_MASK                              0xFF010F07U
#define LPDDR4__DENALI_PI_157_WRITE_MASK                             0xFF010F07U
#define LPDDR4__DENALI_PI_157__PI_RESERVED31_MASK                    0x00000007U
#define LPDDR4__DENALI_PI_157__PI_RESERVED31_SHIFT                            0U
#define LPDDR4__DENALI_PI_157__PI_RESERVED31_WIDTH                            3U
#define LPDDR4__PI_RESERVED31__REG DENALI_PI_157
#define LPDDR4__PI_RESERVED31__FLD LPDDR4__DENALI_PI_157__PI_RESERVED31

#define LPDDR4__DENALI_PI_157__PI_MONITOR_SRC_SEL_0_MASK             0x00000F00U
#define LPDDR4__DENALI_PI_157__PI_MONITOR_SRC_SEL_0_SHIFT                     8U
#define LPDDR4__DENALI_PI_157__PI_MONITOR_SRC_SEL_0_WIDTH                     4U
#define LPDDR4__PI_MONITOR_SRC_SEL_0__REG DENALI_PI_157
#define LPDDR4__PI_MONITOR_SRC_SEL_0__FLD LPDDR4__DENALI_PI_157__PI_MONITOR_SRC_SEL_0

#define LPDDR4__DENALI_PI_157__PI_MONITOR_CAP_SEL_0_MASK             0x00010000U
#define LPDDR4__DENALI_PI_157__PI_MONITOR_CAP_SEL_0_SHIFT                    16U
#define LPDDR4__DENALI_PI_157__PI_MONITOR_CAP_SEL_0_WIDTH                     1U
#define LPDDR4__DENALI_PI_157__PI_MONITOR_CAP_SEL_0_WOCLR                     0U
#define LPDDR4__DENALI_PI_157__PI_MONITOR_CAP_SEL_0_WOSET                     0U
#define LPDDR4__PI_MONITOR_CAP_SEL_0__REG DENALI_PI_157
#define LPDDR4__PI_MONITOR_CAP_SEL_0__FLD LPDDR4__DENALI_PI_157__PI_MONITOR_CAP_SEL_0

#define LPDDR4__DENALI_PI_157__PI_MONITOR_0_MASK                     0xFF000000U
#define LPDDR4__DENALI_PI_157__PI_MONITOR_0_SHIFT                            24U
#define LPDDR4__DENALI_PI_157__PI_MONITOR_0_WIDTH                             8U
#define LPDDR4__PI_MONITOR_0__REG DENALI_PI_157
#define LPDDR4__PI_MONITOR_0__FLD LPDDR4__DENALI_PI_157__PI_MONITOR_0

#define LPDDR4__DENALI_PI_158_READ_MASK                              0x0FFF010FU
#define LPDDR4__DENALI_PI_158_WRITE_MASK                             0x0FFF010FU
#define LPDDR4__DENALI_PI_158__PI_MONITOR_SRC_SEL_1_MASK             0x0000000FU
#define LPDDR4__DENALI_PI_158__PI_MONITOR_SRC_SEL_1_SHIFT                     0U
#define LPDDR4__DENALI_PI_158__PI_MONITOR_SRC_SEL_1_WIDTH                     4U
#define LPDDR4__PI_MONITOR_SRC_SEL_1__REG DENALI_PI_158
#define LPDDR4__PI_MONITOR_SRC_SEL_1__FLD LPDDR4__DENALI_PI_158__PI_MONITOR_SRC_SEL_1

#define LPDDR4__DENALI_PI_158__PI_MONITOR_CAP_SEL_1_MASK             0x00000100U
#define LPDDR4__DENALI_PI_158__PI_MONITOR_CAP_SEL_1_SHIFT                     8U
#define LPDDR4__DENALI_PI_158__PI_MONITOR_CAP_SEL_1_WIDTH                     1U
#define LPDDR4__DENALI_PI_158__PI_MONITOR_CAP_SEL_1_WOCLR                     0U
#define LPDDR4__DENALI_PI_158__PI_MONITOR_CAP_SEL_1_WOSET                     0U
#define LPDDR4__PI_MONITOR_CAP_SEL_1__REG DENALI_PI_158
#define LPDDR4__PI_MONITOR_CAP_SEL_1__FLD LPDDR4__DENALI_PI_158__PI_MONITOR_CAP_SEL_1

#define LPDDR4__DENALI_PI_158__PI_MONITOR_1_MASK                     0x00FF0000U
#define LPDDR4__DENALI_PI_158__PI_MONITOR_1_SHIFT                            16U
#define LPDDR4__DENALI_PI_158__PI_MONITOR_1_WIDTH                             8U
#define LPDDR4__PI_MONITOR_1__REG DENALI_PI_158
#define LPDDR4__PI_MONITOR_1__FLD LPDDR4__DENALI_PI_158__PI_MONITOR_1

#define LPDDR4__DENALI_PI_158__PI_MONITOR_SRC_SEL_2_MASK             0x0F000000U
#define LPDDR4__DENALI_PI_158__PI_MONITOR_SRC_SEL_2_SHIFT                    24U
#define LPDDR4__DENALI_PI_158__PI_MONITOR_SRC_SEL_2_WIDTH                     4U
#define LPDDR4__PI_MONITOR_SRC_SEL_2__REG DENALI_PI_158
#define LPDDR4__PI_MONITOR_SRC_SEL_2__FLD LPDDR4__DENALI_PI_158__PI_MONITOR_SRC_SEL_2

#define LPDDR4__DENALI_PI_159_READ_MASK                              0x010FFF01U
#define LPDDR4__DENALI_PI_159_WRITE_MASK                             0x010FFF01U
#define LPDDR4__DENALI_PI_159__PI_MONITOR_CAP_SEL_2_MASK             0x00000001U
#define LPDDR4__DENALI_PI_159__PI_MONITOR_CAP_SEL_2_SHIFT                     0U
#define LPDDR4__DENALI_PI_159__PI_MONITOR_CAP_SEL_2_WIDTH                     1U
#define LPDDR4__DENALI_PI_159__PI_MONITOR_CAP_SEL_2_WOCLR                     0U
#define LPDDR4__DENALI_PI_159__PI_MONITOR_CAP_SEL_2_WOSET                     0U
#define LPDDR4__PI_MONITOR_CAP_SEL_2__REG DENALI_PI_159
#define LPDDR4__PI_MONITOR_CAP_SEL_2__FLD LPDDR4__DENALI_PI_159__PI_MONITOR_CAP_SEL_2

#define LPDDR4__DENALI_PI_159__PI_MONITOR_2_MASK                     0x0000FF00U
#define LPDDR4__DENALI_PI_159__PI_MONITOR_2_SHIFT                             8U
#define LPDDR4__DENALI_PI_159__PI_MONITOR_2_WIDTH                             8U
#define LPDDR4__PI_MONITOR_2__REG DENALI_PI_159
#define LPDDR4__PI_MONITOR_2__FLD LPDDR4__DENALI_PI_159__PI_MONITOR_2

#define LPDDR4__DENALI_PI_159__PI_MONITOR_SRC_SEL_3_MASK             0x000F0000U
#define LPDDR4__DENALI_PI_159__PI_MONITOR_SRC_SEL_3_SHIFT                    16U
#define LPDDR4__DENALI_PI_159__PI_MONITOR_SRC_SEL_3_WIDTH                     4U
#define LPDDR4__PI_MONITOR_SRC_SEL_3__REG DENALI_PI_159
#define LPDDR4__PI_MONITOR_SRC_SEL_3__FLD LPDDR4__DENALI_PI_159__PI_MONITOR_SRC_SEL_3

#define LPDDR4__DENALI_PI_159__PI_MONITOR_CAP_SEL_3_MASK             0x01000000U
#define LPDDR4__DENALI_PI_159__PI_MONITOR_CAP_SEL_3_SHIFT                    24U
#define LPDDR4__DENALI_PI_159__PI_MONITOR_CAP_SEL_3_WIDTH                     1U
#define LPDDR4__DENALI_PI_159__PI_MONITOR_CAP_SEL_3_WOCLR                     0U
#define LPDDR4__DENALI_PI_159__PI_MONITOR_CAP_SEL_3_WOSET                     0U
#define LPDDR4__PI_MONITOR_CAP_SEL_3__REG DENALI_PI_159
#define LPDDR4__PI_MONITOR_CAP_SEL_3__FLD LPDDR4__DENALI_PI_159__PI_MONITOR_CAP_SEL_3

#define LPDDR4__DENALI_PI_160_READ_MASK                              0xFF010FFFU
#define LPDDR4__DENALI_PI_160_WRITE_MASK                             0xFF010FFFU
#define LPDDR4__DENALI_PI_160__PI_MONITOR_3_MASK                     0x000000FFU
#define LPDDR4__DENALI_PI_160__PI_MONITOR_3_SHIFT                             0U
#define LPDDR4__DENALI_PI_160__PI_MONITOR_3_WIDTH                             8U
#define LPDDR4__PI_MONITOR_3__REG DENALI_PI_160
#define LPDDR4__PI_MONITOR_3__FLD LPDDR4__DENALI_PI_160__PI_MONITOR_3

#define LPDDR4__DENALI_PI_160__PI_MONITOR_SRC_SEL_4_MASK             0x00000F00U
#define LPDDR4__DENALI_PI_160__PI_MONITOR_SRC_SEL_4_SHIFT                     8U
#define LPDDR4__DENALI_PI_160__PI_MONITOR_SRC_SEL_4_WIDTH                     4U
#define LPDDR4__PI_MONITOR_SRC_SEL_4__REG DENALI_PI_160
#define LPDDR4__PI_MONITOR_SRC_SEL_4__FLD LPDDR4__DENALI_PI_160__PI_MONITOR_SRC_SEL_4

#define LPDDR4__DENALI_PI_160__PI_MONITOR_CAP_SEL_4_MASK             0x00010000U
#define LPDDR4__DENALI_PI_160__PI_MONITOR_CAP_SEL_4_SHIFT                    16U
#define LPDDR4__DENALI_PI_160__PI_MONITOR_CAP_SEL_4_WIDTH                     1U
#define LPDDR4__DENALI_PI_160__PI_MONITOR_CAP_SEL_4_WOCLR                     0U
#define LPDDR4__DENALI_PI_160__PI_MONITOR_CAP_SEL_4_WOSET                     0U
#define LPDDR4__PI_MONITOR_CAP_SEL_4__REG DENALI_PI_160
#define LPDDR4__PI_MONITOR_CAP_SEL_4__FLD LPDDR4__DENALI_PI_160__PI_MONITOR_CAP_SEL_4

#define LPDDR4__DENALI_PI_160__PI_MONITOR_4_MASK                     0xFF000000U
#define LPDDR4__DENALI_PI_160__PI_MONITOR_4_SHIFT                            24U
#define LPDDR4__DENALI_PI_160__PI_MONITOR_4_WIDTH                             8U
#define LPDDR4__PI_MONITOR_4__REG DENALI_PI_160
#define LPDDR4__PI_MONITOR_4__FLD LPDDR4__DENALI_PI_160__PI_MONITOR_4

#define LPDDR4__DENALI_PI_161_READ_MASK                              0x0FFF010FU
#define LPDDR4__DENALI_PI_161_WRITE_MASK                             0x0FFF010FU
#define LPDDR4__DENALI_PI_161__PI_MONITOR_SRC_SEL_5_MASK             0x0000000FU
#define LPDDR4__DENALI_PI_161__PI_MONITOR_SRC_SEL_5_SHIFT                     0U
#define LPDDR4__DENALI_PI_161__PI_MONITOR_SRC_SEL_5_WIDTH                     4U
#define LPDDR4__PI_MONITOR_SRC_SEL_5__REG DENALI_PI_161
#define LPDDR4__PI_MONITOR_SRC_SEL_5__FLD LPDDR4__DENALI_PI_161__PI_MONITOR_SRC_SEL_5

#define LPDDR4__DENALI_PI_161__PI_MONITOR_CAP_SEL_5_MASK             0x00000100U
#define LPDDR4__DENALI_PI_161__PI_MONITOR_CAP_SEL_5_SHIFT                     8U
#define LPDDR4__DENALI_PI_161__PI_MONITOR_CAP_SEL_5_WIDTH                     1U
#define LPDDR4__DENALI_PI_161__PI_MONITOR_CAP_SEL_5_WOCLR                     0U
#define LPDDR4__DENALI_PI_161__PI_MONITOR_CAP_SEL_5_WOSET                     0U
#define LPDDR4__PI_MONITOR_CAP_SEL_5__REG DENALI_PI_161
#define LPDDR4__PI_MONITOR_CAP_SEL_5__FLD LPDDR4__DENALI_PI_161__PI_MONITOR_CAP_SEL_5

#define LPDDR4__DENALI_PI_161__PI_MONITOR_5_MASK                     0x00FF0000U
#define LPDDR4__DENALI_PI_161__PI_MONITOR_5_SHIFT                            16U
#define LPDDR4__DENALI_PI_161__PI_MONITOR_5_WIDTH                             8U
#define LPDDR4__PI_MONITOR_5__REG DENALI_PI_161
#define LPDDR4__PI_MONITOR_5__FLD LPDDR4__DENALI_PI_161__PI_MONITOR_5

#define LPDDR4__DENALI_PI_161__PI_MONITOR_SRC_SEL_6_MASK             0x0F000000U
#define LPDDR4__DENALI_PI_161__PI_MONITOR_SRC_SEL_6_SHIFT                    24U
#define LPDDR4__DENALI_PI_161__PI_MONITOR_SRC_SEL_6_WIDTH                     4U
#define LPDDR4__PI_MONITOR_SRC_SEL_6__REG DENALI_PI_161
#define LPDDR4__PI_MONITOR_SRC_SEL_6__FLD LPDDR4__DENALI_PI_161__PI_MONITOR_SRC_SEL_6

#define LPDDR4__DENALI_PI_162_READ_MASK                              0x010FFF01U
#define LPDDR4__DENALI_PI_162_WRITE_MASK                             0x010FFF01U
#define LPDDR4__DENALI_PI_162__PI_MONITOR_CAP_SEL_6_MASK             0x00000001U
#define LPDDR4__DENALI_PI_162__PI_MONITOR_CAP_SEL_6_SHIFT                     0U
#define LPDDR4__DENALI_PI_162__PI_MONITOR_CAP_SEL_6_WIDTH                     1U
#define LPDDR4__DENALI_PI_162__PI_MONITOR_CAP_SEL_6_WOCLR                     0U
#define LPDDR4__DENALI_PI_162__PI_MONITOR_CAP_SEL_6_WOSET                     0U
#define LPDDR4__PI_MONITOR_CAP_SEL_6__REG DENALI_PI_162
#define LPDDR4__PI_MONITOR_CAP_SEL_6__FLD LPDDR4__DENALI_PI_162__PI_MONITOR_CAP_SEL_6

#define LPDDR4__DENALI_PI_162__PI_MONITOR_6_MASK                     0x0000FF00U
#define LPDDR4__DENALI_PI_162__PI_MONITOR_6_SHIFT                             8U
#define LPDDR4__DENALI_PI_162__PI_MONITOR_6_WIDTH                             8U
#define LPDDR4__PI_MONITOR_6__REG DENALI_PI_162
#define LPDDR4__PI_MONITOR_6__FLD LPDDR4__DENALI_PI_162__PI_MONITOR_6

#define LPDDR4__DENALI_PI_162__PI_MONITOR_SRC_SEL_7_MASK             0x000F0000U
#define LPDDR4__DENALI_PI_162__PI_MONITOR_SRC_SEL_7_SHIFT                    16U
#define LPDDR4__DENALI_PI_162__PI_MONITOR_SRC_SEL_7_WIDTH                     4U
#define LPDDR4__PI_MONITOR_SRC_SEL_7__REG DENALI_PI_162
#define LPDDR4__PI_MONITOR_SRC_SEL_7__FLD LPDDR4__DENALI_PI_162__PI_MONITOR_SRC_SEL_7

#define LPDDR4__DENALI_PI_162__PI_MONITOR_CAP_SEL_7_MASK             0x01000000U
#define LPDDR4__DENALI_PI_162__PI_MONITOR_CAP_SEL_7_SHIFT                    24U
#define LPDDR4__DENALI_PI_162__PI_MONITOR_CAP_SEL_7_WIDTH                     1U
#define LPDDR4__DENALI_PI_162__PI_MONITOR_CAP_SEL_7_WOCLR                     0U
#define LPDDR4__DENALI_PI_162__PI_MONITOR_CAP_SEL_7_WOSET                     0U
#define LPDDR4__PI_MONITOR_CAP_SEL_7__REG DENALI_PI_162
#define LPDDR4__PI_MONITOR_CAP_SEL_7__FLD LPDDR4__DENALI_PI_162__PI_MONITOR_CAP_SEL_7

#define LPDDR4__DENALI_PI_163_READ_MASK                              0x000000FFU
#define LPDDR4__DENALI_PI_163_WRITE_MASK                             0x000000FFU
#define LPDDR4__DENALI_PI_163__PI_MONITOR_7_MASK                     0x000000FFU
#define LPDDR4__DENALI_PI_163__PI_MONITOR_7_SHIFT                             0U
#define LPDDR4__DENALI_PI_163__PI_MONITOR_7_WIDTH                             8U
#define LPDDR4__PI_MONITOR_7__REG DENALI_PI_163
#define LPDDR4__PI_MONITOR_7__FLD LPDDR4__DENALI_PI_163__PI_MONITOR_7

#define LPDDR4__DENALI_PI_164__PI_MONITOR_STROBE_MASK                0x000000FFU
#define LPDDR4__DENALI_PI_164__PI_MONITOR_STROBE_SHIFT                        0U
#define LPDDR4__DENALI_PI_164__PI_MONITOR_STROBE_WIDTH                        8U
#define LPDDR4__PI_MONITOR_STROBE__REG DENALI_PI_164
#define LPDDR4__PI_MONITOR_STROBE__FLD LPDDR4__DENALI_PI_164__PI_MONITOR_STROBE

#define LPDDR4__DENALI_PI_165_READ_MASK                              0x011F1F01U
#define LPDDR4__DENALI_PI_165_WRITE_MASK                             0x011F1F01U
#define LPDDR4__DENALI_PI_165__PI_DLL_LOCK_MASK                      0x00000001U
#define LPDDR4__DENALI_PI_165__PI_DLL_LOCK_SHIFT                              0U
#define LPDDR4__DENALI_PI_165__PI_DLL_LOCK_WIDTH                              1U
#define LPDDR4__DENALI_PI_165__PI_DLL_LOCK_WOCLR                              0U
#define LPDDR4__DENALI_PI_165__PI_DLL_LOCK_WOSET                              0U
#define LPDDR4__PI_DLL_LOCK__REG DENALI_PI_165
#define LPDDR4__PI_DLL_LOCK__FLD LPDDR4__DENALI_PI_165__PI_DLL_LOCK

#define LPDDR4__DENALI_PI_165__PI_FREQ_NUMBER_STATUS_MASK            0x00001F00U
#define LPDDR4__DENALI_PI_165__PI_FREQ_NUMBER_STATUS_SHIFT                    8U
#define LPDDR4__DENALI_PI_165__PI_FREQ_NUMBER_STATUS_WIDTH                    5U
#define LPDDR4__PI_FREQ_NUMBER_STATUS__REG DENALI_PI_165
#define LPDDR4__PI_FREQ_NUMBER_STATUS__FLD LPDDR4__DENALI_PI_165__PI_FREQ_NUMBER_STATUS

#define LPDDR4__DENALI_PI_165__PI_FREQ_RETENTION_NUM_MASK            0x001F0000U
#define LPDDR4__DENALI_PI_165__PI_FREQ_RETENTION_NUM_SHIFT                   16U
#define LPDDR4__DENALI_PI_165__PI_FREQ_RETENTION_NUM_WIDTH                    5U
#define LPDDR4__PI_FREQ_RETENTION_NUM__REG DENALI_PI_165
#define LPDDR4__PI_FREQ_RETENTION_NUM__FLD LPDDR4__DENALI_PI_165__PI_FREQ_RETENTION_NUM

#define LPDDR4__DENALI_PI_165__PI_RESERVED32_MASK                    0x01000000U
#define LPDDR4__DENALI_PI_165__PI_RESERVED32_SHIFT                           24U
#define LPDDR4__DENALI_PI_165__PI_RESERVED32_WIDTH                            1U
#define LPDDR4__DENALI_PI_165__PI_RESERVED32_WOCLR                            0U
#define LPDDR4__DENALI_PI_165__PI_RESERVED32_WOSET                            0U
#define LPDDR4__PI_RESERVED32__REG DENALI_PI_165
#define LPDDR4__PI_RESERVED32__FLD LPDDR4__DENALI_PI_165__PI_RESERVED32

#define LPDDR4__DENALI_PI_166_READ_MASK                              0x01010103U
#define LPDDR4__DENALI_PI_166_WRITE_MASK                             0x01010103U
#define LPDDR4__DENALI_PI_166__PI_PHYMSTR_TYPE_MASK                  0x00000003U
#define LPDDR4__DENALI_PI_166__PI_PHYMSTR_TYPE_SHIFT                          0U
#define LPDDR4__DENALI_PI_166__PI_PHYMSTR_TYPE_WIDTH                          2U
#define LPDDR4__PI_PHYMSTR_TYPE__REG DENALI_PI_166
#define LPDDR4__PI_PHYMSTR_TYPE__FLD LPDDR4__DENALI_PI_166__PI_PHYMSTR_TYPE

#define LPDDR4__DENALI_PI_166__PI_RESERVED33_MASK                    0x00000100U
#define LPDDR4__DENALI_PI_166__PI_RESERVED33_SHIFT                            8U
#define LPDDR4__DENALI_PI_166__PI_RESERVED33_WIDTH                            1U
#define LPDDR4__DENALI_PI_166__PI_RESERVED33_WOCLR                            0U
#define LPDDR4__DENALI_PI_166__PI_RESERVED33_WOSET                            0U
#define LPDDR4__PI_RESERVED33__REG DENALI_PI_166
#define LPDDR4__PI_RESERVED33__FLD LPDDR4__DENALI_PI_166__PI_RESERVED33

#define LPDDR4__DENALI_PI_166__PI_POWER_REDUC_EN_MASK                0x00010000U
#define LPDDR4__DENALI_PI_166__PI_POWER_REDUC_EN_SHIFT                       16U
#define LPDDR4__DENALI_PI_166__PI_POWER_REDUC_EN_WIDTH                        1U
#define LPDDR4__DENALI_PI_166__PI_POWER_REDUC_EN_WOCLR                        0U
#define LPDDR4__DENALI_PI_166__PI_POWER_REDUC_EN_WOSET                        0U
#define LPDDR4__PI_POWER_REDUC_EN__REG DENALI_PI_166
#define LPDDR4__PI_POWER_REDUC_EN__FLD LPDDR4__DENALI_PI_166__PI_POWER_REDUC_EN

#define LPDDR4__DENALI_PI_166__PI_RESERVED34_MASK                    0x01000000U
#define LPDDR4__DENALI_PI_166__PI_RESERVED34_SHIFT                           24U
#define LPDDR4__DENALI_PI_166__PI_RESERVED34_WIDTH                            1U
#define LPDDR4__DENALI_PI_166__PI_RESERVED34_WOCLR                            0U
#define LPDDR4__DENALI_PI_166__PI_RESERVED34_WOSET                            0U
#define LPDDR4__PI_RESERVED34__REG DENALI_PI_166
#define LPDDR4__PI_RESERVED34__FLD LPDDR4__DENALI_PI_166__PI_RESERVED34

#define LPDDR4__DENALI_PI_167_READ_MASK                              0x01010101U
#define LPDDR4__DENALI_PI_167_WRITE_MASK                             0x01010101U
#define LPDDR4__DENALI_PI_167__PI_RESERVED35_MASK                    0x00000001U
#define LPDDR4__DENALI_PI_167__PI_RESERVED35_SHIFT                            0U
#define LPDDR4__DENALI_PI_167__PI_RESERVED35_WIDTH                            1U
#define LPDDR4__DENALI_PI_167__PI_RESERVED35_WOCLR                            0U
#define LPDDR4__DENALI_PI_167__PI_RESERVED35_WOSET                            0U
#define LPDDR4__PI_RESERVED35__REG DENALI_PI_167
#define LPDDR4__PI_RESERVED35__FLD LPDDR4__DENALI_PI_167__PI_RESERVED35

#define LPDDR4__DENALI_PI_167__PI_RESERVED36_MASK                    0x00000100U
#define LPDDR4__DENALI_PI_167__PI_RESERVED36_SHIFT                            8U
#define LPDDR4__DENALI_PI_167__PI_RESERVED36_WIDTH                            1U
#define LPDDR4__DENALI_PI_167__PI_RESERVED36_WOCLR                            0U
#define LPDDR4__DENALI_PI_167__PI_RESERVED36_WOSET                            0U
#define LPDDR4__PI_RESERVED36__REG DENALI_PI_167
#define LPDDR4__PI_RESERVED36__FLD LPDDR4__DENALI_PI_167__PI_RESERVED36

#define LPDDR4__DENALI_PI_167__PI_RESERVED37_MASK                    0x00010000U
#define LPDDR4__DENALI_PI_167__PI_RESERVED37_SHIFT                           16U
#define LPDDR4__DENALI_PI_167__PI_RESERVED37_WIDTH                            1U
#define LPDDR4__DENALI_PI_167__PI_RESERVED37_WOCLR                            0U
#define LPDDR4__DENALI_PI_167__PI_RESERVED37_WOSET                            0U
#define LPDDR4__PI_RESERVED37__REG DENALI_PI_167
#define LPDDR4__PI_RESERVED37__FLD LPDDR4__DENALI_PI_167__PI_RESERVED37

#define LPDDR4__DENALI_PI_167__PI_RESERVED38_MASK                    0x01000000U
#define LPDDR4__DENALI_PI_167__PI_RESERVED38_SHIFT                           24U
#define LPDDR4__DENALI_PI_167__PI_RESERVED38_WIDTH                            1U
#define LPDDR4__DENALI_PI_167__PI_RESERVED38_WOCLR                            0U
#define LPDDR4__DENALI_PI_167__PI_RESERVED38_WOSET                            0U
#define LPDDR4__PI_RESERVED38__REG DENALI_PI_167
#define LPDDR4__PI_RESERVED38__FLD LPDDR4__DENALI_PI_167__PI_RESERVED38

#define LPDDR4__DENALI_PI_168_READ_MASK                              0x01010101U
#define LPDDR4__DENALI_PI_168_WRITE_MASK                             0x01010101U
#define LPDDR4__DENALI_PI_168__PI_RESERVED39_MASK                    0x00000001U
#define LPDDR4__DENALI_PI_168__PI_RESERVED39_SHIFT                            0U
#define LPDDR4__DENALI_PI_168__PI_RESERVED39_WIDTH                            1U
#define LPDDR4__DENALI_PI_168__PI_RESERVED39_WOCLR                            0U
#define LPDDR4__DENALI_PI_168__PI_RESERVED39_WOSET                            0U
#define LPDDR4__PI_RESERVED39__REG DENALI_PI_168
#define LPDDR4__PI_RESERVED39__FLD LPDDR4__DENALI_PI_168__PI_RESERVED39

#define LPDDR4__DENALI_PI_168__PI_RESERVED40_MASK                    0x00000100U
#define LPDDR4__DENALI_PI_168__PI_RESERVED40_SHIFT                            8U
#define LPDDR4__DENALI_PI_168__PI_RESERVED40_WIDTH                            1U
#define LPDDR4__DENALI_PI_168__PI_RESERVED40_WOCLR                            0U
#define LPDDR4__DENALI_PI_168__PI_RESERVED40_WOSET                            0U
#define LPDDR4__PI_RESERVED40__REG DENALI_PI_168
#define LPDDR4__PI_RESERVED40__FLD LPDDR4__DENALI_PI_168__PI_RESERVED40

#define LPDDR4__DENALI_PI_168__PI_RESERVED41_MASK                    0x00010000U
#define LPDDR4__DENALI_PI_168__PI_RESERVED41_SHIFT                           16U
#define LPDDR4__DENALI_PI_168__PI_RESERVED41_WIDTH                            1U
#define LPDDR4__DENALI_PI_168__PI_RESERVED41_WOCLR                            0U
#define LPDDR4__DENALI_PI_168__PI_RESERVED41_WOSET                            0U
#define LPDDR4__PI_RESERVED41__REG DENALI_PI_168
#define LPDDR4__PI_RESERVED41__FLD LPDDR4__DENALI_PI_168__PI_RESERVED41

#define LPDDR4__DENALI_PI_168__PI_RESERVED42_MASK                    0x01000000U
#define LPDDR4__DENALI_PI_168__PI_RESERVED42_SHIFT                           24U
#define LPDDR4__DENALI_PI_168__PI_RESERVED42_WIDTH                            1U
#define LPDDR4__DENALI_PI_168__PI_RESERVED42_WOCLR                            0U
#define LPDDR4__DENALI_PI_168__PI_RESERVED42_WOSET                            0U
#define LPDDR4__PI_RESERVED42__REG DENALI_PI_168
#define LPDDR4__PI_RESERVED42__FLD LPDDR4__DENALI_PI_168__PI_RESERVED42

#define LPDDR4__DENALI_PI_169_READ_MASK                              0x01010101U
#define LPDDR4__DENALI_PI_169_WRITE_MASK                             0x01010101U
#define LPDDR4__DENALI_PI_169__PI_RESERVED43_MASK                    0x00000001U
#define LPDDR4__DENALI_PI_169__PI_RESERVED43_SHIFT                            0U
#define LPDDR4__DENALI_PI_169__PI_RESERVED43_WIDTH                            1U
#define LPDDR4__DENALI_PI_169__PI_RESERVED43_WOCLR                            0U
#define LPDDR4__DENALI_PI_169__PI_RESERVED43_WOSET                            0U
#define LPDDR4__PI_RESERVED43__REG DENALI_PI_169
#define LPDDR4__PI_RESERVED43__FLD LPDDR4__DENALI_PI_169__PI_RESERVED43

#define LPDDR4__DENALI_PI_169__PI_RESERVED44_MASK                    0x00000100U
#define LPDDR4__DENALI_PI_169__PI_RESERVED44_SHIFT                            8U
#define LPDDR4__DENALI_PI_169__PI_RESERVED44_WIDTH                            1U
#define LPDDR4__DENALI_PI_169__PI_RESERVED44_WOCLR                            0U
#define LPDDR4__DENALI_PI_169__PI_RESERVED44_WOSET                            0U
#define LPDDR4__PI_RESERVED44__REG DENALI_PI_169
#define LPDDR4__PI_RESERVED44__FLD LPDDR4__DENALI_PI_169__PI_RESERVED44

#define LPDDR4__DENALI_PI_169__PI_RESERVED45_MASK                    0x00010000U
#define LPDDR4__DENALI_PI_169__PI_RESERVED45_SHIFT                           16U
#define LPDDR4__DENALI_PI_169__PI_RESERVED45_WIDTH                            1U
#define LPDDR4__DENALI_PI_169__PI_RESERVED45_WOCLR                            0U
#define LPDDR4__DENALI_PI_169__PI_RESERVED45_WOSET                            0U
#define LPDDR4__PI_RESERVED45__REG DENALI_PI_169
#define LPDDR4__PI_RESERVED45__FLD LPDDR4__DENALI_PI_169__PI_RESERVED45

#define LPDDR4__DENALI_PI_169__PI_RESERVED46_MASK                    0x01000000U
#define LPDDR4__DENALI_PI_169__PI_RESERVED46_SHIFT                           24U
#define LPDDR4__DENALI_PI_169__PI_RESERVED46_WIDTH                            1U
#define LPDDR4__DENALI_PI_169__PI_RESERVED46_WOCLR                            0U
#define LPDDR4__DENALI_PI_169__PI_RESERVED46_WOSET                            0U
#define LPDDR4__PI_RESERVED46__REG DENALI_PI_169
#define LPDDR4__PI_RESERVED46__FLD LPDDR4__DENALI_PI_169__PI_RESERVED46

#define LPDDR4__DENALI_PI_170_READ_MASK                              0x01010101U
#define LPDDR4__DENALI_PI_170_WRITE_MASK                             0x01010101U
#define LPDDR4__DENALI_PI_170__PI_RESERVED47_MASK                    0x00000001U
#define LPDDR4__DENALI_PI_170__PI_RESERVED47_SHIFT                            0U
#define LPDDR4__DENALI_PI_170__PI_RESERVED47_WIDTH                            1U
#define LPDDR4__DENALI_PI_170__PI_RESERVED47_WOCLR                            0U
#define LPDDR4__DENALI_PI_170__PI_RESERVED47_WOSET                            0U
#define LPDDR4__PI_RESERVED47__REG DENALI_PI_170
#define LPDDR4__PI_RESERVED47__FLD LPDDR4__DENALI_PI_170__PI_RESERVED47

#define LPDDR4__DENALI_PI_170__PI_RESERVED48_MASK                    0x00000100U
#define LPDDR4__DENALI_PI_170__PI_RESERVED48_SHIFT                            8U
#define LPDDR4__DENALI_PI_170__PI_RESERVED48_WIDTH                            1U
#define LPDDR4__DENALI_PI_170__PI_RESERVED48_WOCLR                            0U
#define LPDDR4__DENALI_PI_170__PI_RESERVED48_WOSET                            0U
#define LPDDR4__PI_RESERVED48__REG DENALI_PI_170
#define LPDDR4__PI_RESERVED48__FLD LPDDR4__DENALI_PI_170__PI_RESERVED48

#define LPDDR4__DENALI_PI_170__PI_RESERVED49_MASK                    0x00010000U
#define LPDDR4__DENALI_PI_170__PI_RESERVED49_SHIFT                           16U
#define LPDDR4__DENALI_PI_170__PI_RESERVED49_WIDTH                            1U
#define LPDDR4__DENALI_PI_170__PI_RESERVED49_WOCLR                            0U
#define LPDDR4__DENALI_PI_170__PI_RESERVED49_WOSET                            0U
#define LPDDR4__PI_RESERVED49__REG DENALI_PI_170
#define LPDDR4__PI_RESERVED49__FLD LPDDR4__DENALI_PI_170__PI_RESERVED49

#define LPDDR4__DENALI_PI_170__PI_RESERVED50_MASK                    0x01000000U
#define LPDDR4__DENALI_PI_170__PI_RESERVED50_SHIFT                           24U
#define LPDDR4__DENALI_PI_170__PI_RESERVED50_WIDTH                            1U
#define LPDDR4__DENALI_PI_170__PI_RESERVED50_WOCLR                            0U
#define LPDDR4__DENALI_PI_170__PI_RESERVED50_WOSET                            0U
#define LPDDR4__PI_RESERVED50__REG DENALI_PI_170
#define LPDDR4__PI_RESERVED50__FLD LPDDR4__DENALI_PI_170__PI_RESERVED50

#define LPDDR4__DENALI_PI_171_READ_MASK                              0x00FF0101U
#define LPDDR4__DENALI_PI_171_WRITE_MASK                             0x00FF0101U
#define LPDDR4__DENALI_PI_171__PI_RESERVED51_MASK                    0x00000001U
#define LPDDR4__DENALI_PI_171__PI_RESERVED51_SHIFT                            0U
#define LPDDR4__DENALI_PI_171__PI_RESERVED51_WIDTH                            1U
#define LPDDR4__DENALI_PI_171__PI_RESERVED51_WOCLR                            0U
#define LPDDR4__DENALI_PI_171__PI_RESERVED51_WOSET                            0U
#define LPDDR4__PI_RESERVED51__REG DENALI_PI_171
#define LPDDR4__PI_RESERVED51__FLD LPDDR4__DENALI_PI_171__PI_RESERVED51

#define LPDDR4__DENALI_PI_171__PI_RESERVED52_MASK                    0x00000100U
#define LPDDR4__DENALI_PI_171__PI_RESERVED52_SHIFT                            8U
#define LPDDR4__DENALI_PI_171__PI_RESERVED52_WIDTH                            1U
#define LPDDR4__DENALI_PI_171__PI_RESERVED52_WOCLR                            0U
#define LPDDR4__DENALI_PI_171__PI_RESERVED52_WOSET                            0U
#define LPDDR4__PI_RESERVED52__REG DENALI_PI_171
#define LPDDR4__PI_RESERVED52__FLD LPDDR4__DENALI_PI_171__PI_RESERVED52

#define LPDDR4__DENALI_PI_171__PI_WRLVL_MAX_STROBE_PEND_MASK         0x00FF0000U
#define LPDDR4__DENALI_PI_171__PI_WRLVL_MAX_STROBE_PEND_SHIFT                16U
#define LPDDR4__DENALI_PI_171__PI_WRLVL_MAX_STROBE_PEND_WIDTH                 8U
#define LPDDR4__PI_WRLVL_MAX_STROBE_PEND__REG DENALI_PI_171
#define LPDDR4__PI_WRLVL_MAX_STROBE_PEND__FLD LPDDR4__DENALI_PI_171__PI_WRLVL_MAX_STROBE_PEND

#define LPDDR4__DENALI_PI_172_READ_MASK                              0x000001FFU
#define LPDDR4__DENALI_PI_172_WRITE_MASK                             0x000001FFU
#define LPDDR4__DENALI_PI_172__PI_TREFBW_THR_MASK                    0x000001FFU
#define LPDDR4__DENALI_PI_172__PI_TREFBW_THR_SHIFT                            0U
#define LPDDR4__DENALI_PI_172__PI_TREFBW_THR_WIDTH                            9U
#define LPDDR4__PI_TREFBW_THR__REG DENALI_PI_172
#define LPDDR4__PI_TREFBW_THR__FLD LPDDR4__DENALI_PI_172__PI_TREFBW_THR

#define LPDDR4__DENALI_PI_173_READ_MASK                              0x0000001FU
#define LPDDR4__DENALI_PI_173_WRITE_MASK                             0x0000001FU
#define LPDDR4__DENALI_PI_173__PI_FREQ_CHANGE_REG_COPY_MASK          0x0000001FU
#define LPDDR4__DENALI_PI_173__PI_FREQ_CHANGE_REG_COPY_SHIFT                  0U
#define LPDDR4__DENALI_PI_173__PI_FREQ_CHANGE_REG_COPY_WIDTH                  5U
#define LPDDR4__PI_FREQ_CHANGE_REG_COPY__REG DENALI_PI_173
#define LPDDR4__PI_FREQ_CHANGE_REG_COPY__FLD LPDDR4__DENALI_PI_173__PI_FREQ_CHANGE_REG_COPY

#define LPDDR4__DENALI_PI_174_READ_MASK                              0x0F011F01U
#define LPDDR4__DENALI_PI_174_WRITE_MASK                             0x0F011F01U
#define LPDDR4__DENALI_PI_174__PI_FREQ_SEL_FROM_REGIF_MASK           0x00000001U
#define LPDDR4__DENALI_PI_174__PI_FREQ_SEL_FROM_REGIF_SHIFT                   0U
#define LPDDR4__DENALI_PI_174__PI_FREQ_SEL_FROM_REGIF_WIDTH                   1U
#define LPDDR4__DENALI_PI_174__PI_FREQ_SEL_FROM_REGIF_WOCLR                   0U
#define LPDDR4__DENALI_PI_174__PI_FREQ_SEL_FROM_REGIF_WOSET                   0U
#define LPDDR4__PI_FREQ_SEL_FROM_REGIF__REG DENALI_PI_174
#define LPDDR4__PI_FREQ_SEL_FROM_REGIF__FLD LPDDR4__DENALI_PI_174__PI_FREQ_SEL_FROM_REGIF

#define LPDDR4__DENALI_PI_174__PI_RESERVED53_MASK                    0x00001F00U
#define LPDDR4__DENALI_PI_174__PI_RESERVED53_SHIFT                            8U
#define LPDDR4__DENALI_PI_174__PI_RESERVED53_WIDTH                            5U
#define LPDDR4__PI_RESERVED53__REG DENALI_PI_174
#define LPDDR4__PI_RESERVED53__FLD LPDDR4__DENALI_PI_174__PI_RESERVED53

#define LPDDR4__DENALI_PI_174__PI_PARALLEL_CALVL_EN_MASK             0x00010000U
#define LPDDR4__DENALI_PI_174__PI_PARALLEL_CALVL_EN_SHIFT                    16U
#define LPDDR4__DENALI_PI_174__PI_PARALLEL_CALVL_EN_WIDTH                     1U
#define LPDDR4__DENALI_PI_174__PI_PARALLEL_CALVL_EN_WOCLR                     0U
#define LPDDR4__DENALI_PI_174__PI_PARALLEL_CALVL_EN_WOSET                     0U
#define LPDDR4__PI_PARALLEL_CALVL_EN__REG DENALI_PI_174
#define LPDDR4__PI_PARALLEL_CALVL_EN__FLD LPDDR4__DENALI_PI_174__PI_PARALLEL_CALVL_EN

#define LPDDR4__DENALI_PI_174__PI_CATR_MASK                          0x0F000000U
#define LPDDR4__DENALI_PI_174__PI_CATR_SHIFT                                 24U
#define LPDDR4__DENALI_PI_174__PI_CATR_WIDTH                                  4U
#define LPDDR4__PI_CATR__REG DENALI_PI_174
#define LPDDR4__PI_CATR__FLD LPDDR4__DENALI_PI_174__PI_CATR

#define LPDDR4__DENALI_PI_175_READ_MASK                              0x01010101U
#define LPDDR4__DENALI_PI_175_WRITE_MASK                             0x01010101U
#define LPDDR4__DENALI_PI_175__PI_NO_CATR_READ_MASK                  0x00000001U
#define LPDDR4__DENALI_PI_175__PI_NO_CATR_READ_SHIFT                          0U
#define LPDDR4__DENALI_PI_175__PI_NO_CATR_READ_WIDTH                          1U
#define LPDDR4__DENALI_PI_175__PI_NO_CATR_READ_WOCLR                          0U
#define LPDDR4__DENALI_PI_175__PI_NO_CATR_READ_WOSET                          0U
#define LPDDR4__PI_NO_CATR_READ__REG DENALI_PI_175
#define LPDDR4__PI_NO_CATR_READ__FLD LPDDR4__DENALI_PI_175__PI_NO_CATR_READ

#define LPDDR4__DENALI_PI_175__PI_MASK_INIT_COMPLETE_MASK            0x00000100U
#define LPDDR4__DENALI_PI_175__PI_MASK_INIT_COMPLETE_SHIFT                    8U
#define LPDDR4__DENALI_PI_175__PI_MASK_INIT_COMPLETE_WIDTH                    1U
#define LPDDR4__DENALI_PI_175__PI_MASK_INIT_COMPLETE_WOCLR                    0U
#define LPDDR4__DENALI_PI_175__PI_MASK_INIT_COMPLETE_WOSET                    0U
#define LPDDR4__PI_MASK_INIT_COMPLETE__REG DENALI_PI_175
#define LPDDR4__PI_MASK_INIT_COMPLETE__FLD LPDDR4__DENALI_PI_175__PI_MASK_INIT_COMPLETE

#define LPDDR4__DENALI_PI_175__PI_DISCONNECT_MC_MASK                 0x00010000U
#define LPDDR4__DENALI_PI_175__PI_DISCONNECT_MC_SHIFT                        16U
#define LPDDR4__DENALI_PI_175__PI_DISCONNECT_MC_WIDTH                         1U
#define LPDDR4__DENALI_PI_175__PI_DISCONNECT_MC_WOCLR                         0U
#define LPDDR4__DENALI_PI_175__PI_DISCONNECT_MC_WOSET                         0U
#define LPDDR4__PI_DISCONNECT_MC__REG DENALI_PI_175
#define LPDDR4__PI_DISCONNECT_MC__FLD LPDDR4__DENALI_PI_175__PI_DISCONNECT_MC

#define LPDDR4__DENALI_PI_175__PI_DISABLE_PHYMSTR_REQ_MASK           0x01000000U
#define LPDDR4__DENALI_PI_175__PI_DISABLE_PHYMSTR_REQ_SHIFT                  24U
#define LPDDR4__DENALI_PI_175__PI_DISABLE_PHYMSTR_REQ_WIDTH                   1U
#define LPDDR4__DENALI_PI_175__PI_DISABLE_PHYMSTR_REQ_WOCLR                   0U
#define LPDDR4__DENALI_PI_175__PI_DISABLE_PHYMSTR_REQ_WOSET                   0U
#define LPDDR4__PI_DISABLE_PHYMSTR_REQ__REG DENALI_PI_175
#define LPDDR4__PI_DISABLE_PHYMSTR_REQ__FLD LPDDR4__DENALI_PI_175__PI_DISABLE_PHYMSTR_REQ

#define LPDDR4__DENALI_PI_176_READ_MASK                              0xFFFF0701U
#define LPDDR4__DENALI_PI_176_WRITE_MASK                             0xFFFF0701U
#define LPDDR4__DENALI_PI_176__PI_NOTCARE_MC_INIT_START_MASK         0x00000001U
#define LPDDR4__DENALI_PI_176__PI_NOTCARE_MC_INIT_START_SHIFT                 0U
#define LPDDR4__DENALI_PI_176__PI_NOTCARE_MC_INIT_START_WIDTH                 1U
#define LPDDR4__DENALI_PI_176__PI_NOTCARE_MC_INIT_START_WOCLR                 0U
#define LPDDR4__DENALI_PI_176__PI_NOTCARE_MC_INIT_START_WOSET                 0U
#define LPDDR4__PI_NOTCARE_MC_INIT_START__REG DENALI_PI_176
#define LPDDR4__PI_NOTCARE_MC_INIT_START__FLD LPDDR4__DENALI_PI_176__PI_NOTCARE_MC_INIT_START

#define LPDDR4__DENALI_PI_176__PI_PHYMSTR_REQ_ACK_LOOP_DELAY_MASK    0x00000700U
#define LPDDR4__DENALI_PI_176__PI_PHYMSTR_REQ_ACK_LOOP_DELAY_SHIFT            8U
#define LPDDR4__DENALI_PI_176__PI_PHYMSTR_REQ_ACK_LOOP_DELAY_WIDTH            3U
#define LPDDR4__PI_PHYMSTR_REQ_ACK_LOOP_DELAY__REG DENALI_PI_176
#define LPDDR4__PI_PHYMSTR_REQ_ACK_LOOP_DELAY__FLD LPDDR4__DENALI_PI_176__PI_PHYMSTR_REQ_ACK_LOOP_DELAY

#define LPDDR4__DENALI_PI_176__PI_TVREF_F0_MASK                      0xFFFF0000U
#define LPDDR4__DENALI_PI_176__PI_TVREF_F0_SHIFT                             16U
#define LPDDR4__DENALI_PI_176__PI_TVREF_F0_WIDTH                             16U
#define LPDDR4__PI_TVREF_F0__REG DENALI_PI_176
#define LPDDR4__PI_TVREF_F0__FLD LPDDR4__DENALI_PI_176__PI_TVREF_F0

#define LPDDR4__DENALI_PI_177_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_177_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_177__PI_TVREF_F1_MASK                      0x0000FFFFU
#define LPDDR4__DENALI_PI_177__PI_TVREF_F1_SHIFT                              0U
#define LPDDR4__DENALI_PI_177__PI_TVREF_F1_WIDTH                             16U
#define LPDDR4__PI_TVREF_F1__REG DENALI_PI_177
#define LPDDR4__PI_TVREF_F1__FLD LPDDR4__DENALI_PI_177__PI_TVREF_F1

#define LPDDR4__DENALI_PI_177__PI_TVREF_F2_MASK                      0xFFFF0000U
#define LPDDR4__DENALI_PI_177__PI_TVREF_F2_SHIFT                             16U
#define LPDDR4__DENALI_PI_177__PI_TVREF_F2_WIDTH                             16U
#define LPDDR4__PI_TVREF_F2__REG DENALI_PI_177
#define LPDDR4__PI_TVREF_F2__FLD LPDDR4__DENALI_PI_177__PI_TVREF_F2

#define LPDDR4__DENALI_PI_178_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_178_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_178__PI_TSDO_F0_MASK                       0x000000FFU
#define LPDDR4__DENALI_PI_178__PI_TSDO_F0_SHIFT                               0U
#define LPDDR4__DENALI_PI_178__PI_TSDO_F0_WIDTH                               8U
#define LPDDR4__PI_TSDO_F0__REG DENALI_PI_178
#define LPDDR4__PI_TSDO_F0__FLD LPDDR4__DENALI_PI_178__PI_TSDO_F0

#define LPDDR4__DENALI_PI_178__PI_TSDO_F1_MASK                       0x0000FF00U
#define LPDDR4__DENALI_PI_178__PI_TSDO_F1_SHIFT                               8U
#define LPDDR4__DENALI_PI_178__PI_TSDO_F1_WIDTH                               8U
#define LPDDR4__PI_TSDO_F1__REG DENALI_PI_178
#define LPDDR4__PI_TSDO_F1__FLD LPDDR4__DENALI_PI_178__PI_TSDO_F1

#define LPDDR4__DENALI_PI_178__PI_TSDO_F2_MASK                       0x00FF0000U
#define LPDDR4__DENALI_PI_178__PI_TSDO_F2_SHIFT                              16U
#define LPDDR4__DENALI_PI_178__PI_TSDO_F2_WIDTH                               8U
#define LPDDR4__PI_TSDO_F2__REG DENALI_PI_178
#define LPDDR4__PI_TSDO_F2__FLD LPDDR4__DENALI_PI_178__PI_TSDO_F2

#define LPDDR4__DENALI_PI_179_READ_MASK                              0x000000FFU
#define LPDDR4__DENALI_PI_179_WRITE_MASK                             0x000000FFU
#define LPDDR4__DENALI_PI_179__PI_TDELAY_RDWR_2_BUS_IDLE_F0_MASK     0x000000FFU
#define LPDDR4__DENALI_PI_179__PI_TDELAY_RDWR_2_BUS_IDLE_F0_SHIFT             0U
#define LPDDR4__DENALI_PI_179__PI_TDELAY_RDWR_2_BUS_IDLE_F0_WIDTH             8U
#define LPDDR4__PI_TDELAY_RDWR_2_BUS_IDLE_F0__REG DENALI_PI_179
#define LPDDR4__PI_TDELAY_RDWR_2_BUS_IDLE_F0__FLD LPDDR4__DENALI_PI_179__PI_TDELAY_RDWR_2_BUS_IDLE_F0

#define LPDDR4__DENALI_PI_180_READ_MASK                              0x000000FFU
#define LPDDR4__DENALI_PI_180_WRITE_MASK                             0x000000FFU
#define LPDDR4__DENALI_PI_180__PI_TDELAY_RDWR_2_BUS_IDLE_F1_MASK     0x000000FFU
#define LPDDR4__DENALI_PI_180__PI_TDELAY_RDWR_2_BUS_IDLE_F1_SHIFT             0U
#define LPDDR4__DENALI_PI_180__PI_TDELAY_RDWR_2_BUS_IDLE_F1_WIDTH             8U
#define LPDDR4__PI_TDELAY_RDWR_2_BUS_IDLE_F1__REG DENALI_PI_180
#define LPDDR4__PI_TDELAY_RDWR_2_BUS_IDLE_F1__FLD LPDDR4__DENALI_PI_180__PI_TDELAY_RDWR_2_BUS_IDLE_F1

#define LPDDR4__DENALI_PI_181_READ_MASK                              0x000FFFFFU
#define LPDDR4__DENALI_PI_181_WRITE_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PI_181__PI_TDELAY_RDWR_2_BUS_IDLE_F2_MASK     0x000000FFU
#define LPDDR4__DENALI_PI_181__PI_TDELAY_RDWR_2_BUS_IDLE_F2_SHIFT             0U
#define LPDDR4__DENALI_PI_181__PI_TDELAY_RDWR_2_BUS_IDLE_F2_WIDTH             8U
#define LPDDR4__PI_TDELAY_RDWR_2_BUS_IDLE_F2__REG DENALI_PI_181
#define LPDDR4__PI_TDELAY_RDWR_2_BUS_IDLE_F2__FLD LPDDR4__DENALI_PI_181__PI_TDELAY_RDWR_2_BUS_IDLE_F2

#define LPDDR4__DENALI_PI_181__PI_ZQINIT_F0_MASK                     0x000FFF00U
#define LPDDR4__DENALI_PI_181__PI_ZQINIT_F0_SHIFT                             8U
#define LPDDR4__DENALI_PI_181__PI_ZQINIT_F0_WIDTH                            12U
#define LPDDR4__PI_ZQINIT_F0__REG DENALI_PI_181
#define LPDDR4__PI_ZQINIT_F0__FLD LPDDR4__DENALI_PI_181__PI_ZQINIT_F0

#define LPDDR4__DENALI_PI_182_READ_MASK                              0x0FFF0FFFU
#define LPDDR4__DENALI_PI_182_WRITE_MASK                             0x0FFF0FFFU
#define LPDDR4__DENALI_PI_182__PI_ZQINIT_F1_MASK                     0x00000FFFU
#define LPDDR4__DENALI_PI_182__PI_ZQINIT_F1_SHIFT                             0U
#define LPDDR4__DENALI_PI_182__PI_ZQINIT_F1_WIDTH                            12U
#define LPDDR4__PI_ZQINIT_F1__REG DENALI_PI_182
#define LPDDR4__PI_ZQINIT_F1__FLD LPDDR4__DENALI_PI_182__PI_ZQINIT_F1

#define LPDDR4__DENALI_PI_182__PI_ZQINIT_F2_MASK                     0x0FFF0000U
#define LPDDR4__DENALI_PI_182__PI_ZQINIT_F2_SHIFT                            16U
#define LPDDR4__DENALI_PI_182__PI_ZQINIT_F2_WIDTH                            12U
#define LPDDR4__PI_ZQINIT_F2__REG DENALI_PI_182
#define LPDDR4__PI_ZQINIT_F2__FLD LPDDR4__DENALI_PI_182__PI_ZQINIT_F2

#define LPDDR4__DENALI_PI_183_READ_MASK                              0xFF0F3F7FU
#define LPDDR4__DENALI_PI_183_WRITE_MASK                             0xFF0F3F7FU
#define LPDDR4__DENALI_PI_183__PI_WRLAT_F0_MASK                      0x0000007FU
#define LPDDR4__DENALI_PI_183__PI_WRLAT_F0_SHIFT                              0U
#define LPDDR4__DENALI_PI_183__PI_WRLAT_F0_WIDTH                              7U
#define LPDDR4__PI_WRLAT_F0__REG DENALI_PI_183
#define LPDDR4__PI_WRLAT_F0__FLD LPDDR4__DENALI_PI_183__PI_WRLAT_F0

#define LPDDR4__DENALI_PI_183__PI_ADDITIVE_LAT_F0_MASK               0x00003F00U
#define LPDDR4__DENALI_PI_183__PI_ADDITIVE_LAT_F0_SHIFT                       8U
#define LPDDR4__DENALI_PI_183__PI_ADDITIVE_LAT_F0_WIDTH                       6U
#define LPDDR4__PI_ADDITIVE_LAT_F0__REG DENALI_PI_183
#define LPDDR4__PI_ADDITIVE_LAT_F0__FLD LPDDR4__DENALI_PI_183__PI_ADDITIVE_LAT_F0

#define LPDDR4__DENALI_PI_183__PI_CA_PARITY_LAT_F0_MASK              0x000F0000U
#define LPDDR4__DENALI_PI_183__PI_CA_PARITY_LAT_F0_SHIFT                     16U
#define LPDDR4__DENALI_PI_183__PI_CA_PARITY_LAT_F0_WIDTH                      4U
#define LPDDR4__PI_CA_PARITY_LAT_F0__REG DENALI_PI_183
#define LPDDR4__PI_CA_PARITY_LAT_F0__FLD LPDDR4__DENALI_PI_183__PI_CA_PARITY_LAT_F0

#define LPDDR4__DENALI_PI_183__PI_TPARITY_ERROR_CMD_INHIBIT_F0_MASK  0xFF000000U
#define LPDDR4__DENALI_PI_183__PI_TPARITY_ERROR_CMD_INHIBIT_F0_SHIFT         24U
#define LPDDR4__DENALI_PI_183__PI_TPARITY_ERROR_CMD_INHIBIT_F0_WIDTH          8U
#define LPDDR4__PI_TPARITY_ERROR_CMD_INHIBIT_F0__REG DENALI_PI_183
#define LPDDR4__PI_TPARITY_ERROR_CMD_INHIBIT_F0__FLD LPDDR4__DENALI_PI_183__PI_TPARITY_ERROR_CMD_INHIBIT_F0

#define LPDDR4__DENALI_PI_184_READ_MASK                              0x0F3F7F7FU
#define LPDDR4__DENALI_PI_184_WRITE_MASK                             0x0F3F7F7FU
#define LPDDR4__DENALI_PI_184__PI_CASLAT_LIN_F0_MASK                 0x0000007FU
#define LPDDR4__DENALI_PI_184__PI_CASLAT_LIN_F0_SHIFT                         0U
#define LPDDR4__DENALI_PI_184__PI_CASLAT_LIN_F0_WIDTH                         7U
#define LPDDR4__PI_CASLAT_LIN_F0__REG DENALI_PI_184
#define LPDDR4__PI_CASLAT_LIN_F0__FLD LPDDR4__DENALI_PI_184__PI_CASLAT_LIN_F0

#define LPDDR4__DENALI_PI_184__PI_WRLAT_F1_MASK                      0x00007F00U
#define LPDDR4__DENALI_PI_184__PI_WRLAT_F1_SHIFT                              8U
#define LPDDR4__DENALI_PI_184__PI_WRLAT_F1_WIDTH                              7U
#define LPDDR4__PI_WRLAT_F1__REG DENALI_PI_184
#define LPDDR4__PI_WRLAT_F1__FLD LPDDR4__DENALI_PI_184__PI_WRLAT_F1

#define LPDDR4__DENALI_PI_184__PI_ADDITIVE_LAT_F1_MASK               0x003F0000U
#define LPDDR4__DENALI_PI_184__PI_ADDITIVE_LAT_F1_SHIFT                      16U
#define LPDDR4__DENALI_PI_184__PI_ADDITIVE_LAT_F1_WIDTH                       6U
#define LPDDR4__PI_ADDITIVE_LAT_F1__REG DENALI_PI_184
#define LPDDR4__PI_ADDITIVE_LAT_F1__FLD LPDDR4__DENALI_PI_184__PI_ADDITIVE_LAT_F1

#define LPDDR4__DENALI_PI_184__PI_CA_PARITY_LAT_F1_MASK              0x0F000000U
#define LPDDR4__DENALI_PI_184__PI_CA_PARITY_LAT_F1_SHIFT                     24U
#define LPDDR4__DENALI_PI_184__PI_CA_PARITY_LAT_F1_WIDTH                      4U
#define LPDDR4__PI_CA_PARITY_LAT_F1__REG DENALI_PI_184
#define LPDDR4__PI_CA_PARITY_LAT_F1__FLD LPDDR4__DENALI_PI_184__PI_CA_PARITY_LAT_F1

#define LPDDR4__DENALI_PI_185_READ_MASK                              0x3F7F7FFFU
#define LPDDR4__DENALI_PI_185_WRITE_MASK                             0x3F7F7FFFU
#define LPDDR4__DENALI_PI_185__PI_TPARITY_ERROR_CMD_INHIBIT_F1_MASK  0x000000FFU
#define LPDDR4__DENALI_PI_185__PI_TPARITY_ERROR_CMD_INHIBIT_F1_SHIFT          0U
#define LPDDR4__DENALI_PI_185__PI_TPARITY_ERROR_CMD_INHIBIT_F1_WIDTH          8U
#define LPDDR4__PI_TPARITY_ERROR_CMD_INHIBIT_F1__REG DENALI_PI_185
#define LPDDR4__PI_TPARITY_ERROR_CMD_INHIBIT_F1__FLD LPDDR4__DENALI_PI_185__PI_TPARITY_ERROR_CMD_INHIBIT_F1

#define LPDDR4__DENALI_PI_185__PI_CASLAT_LIN_F1_MASK                 0x00007F00U
#define LPDDR4__DENALI_PI_185__PI_CASLAT_LIN_F1_SHIFT                         8U
#define LPDDR4__DENALI_PI_185__PI_CASLAT_LIN_F1_WIDTH                         7U
#define LPDDR4__PI_CASLAT_LIN_F1__REG DENALI_PI_185
#define LPDDR4__PI_CASLAT_LIN_F1__FLD LPDDR4__DENALI_PI_185__PI_CASLAT_LIN_F1

#define LPDDR4__DENALI_PI_185__PI_WRLAT_F2_MASK                      0x007F0000U
#define LPDDR4__DENALI_PI_185__PI_WRLAT_F2_SHIFT                             16U
#define LPDDR4__DENALI_PI_185__PI_WRLAT_F2_WIDTH                              7U
#define LPDDR4__PI_WRLAT_F2__REG DENALI_PI_185
#define LPDDR4__PI_WRLAT_F2__FLD LPDDR4__DENALI_PI_185__PI_WRLAT_F2

#define LPDDR4__DENALI_PI_185__PI_ADDITIVE_LAT_F2_MASK               0x3F000000U
#define LPDDR4__DENALI_PI_185__PI_ADDITIVE_LAT_F2_SHIFT                      24U
#define LPDDR4__DENALI_PI_185__PI_ADDITIVE_LAT_F2_WIDTH                       6U
#define LPDDR4__PI_ADDITIVE_LAT_F2__REG DENALI_PI_185
#define LPDDR4__PI_ADDITIVE_LAT_F2__FLD LPDDR4__DENALI_PI_185__PI_ADDITIVE_LAT_F2

#define LPDDR4__DENALI_PI_186_READ_MASK                              0x007FFF0FU
#define LPDDR4__DENALI_PI_186_WRITE_MASK                             0x007FFF0FU
#define LPDDR4__DENALI_PI_186__PI_CA_PARITY_LAT_F2_MASK              0x0000000FU
#define LPDDR4__DENALI_PI_186__PI_CA_PARITY_LAT_F2_SHIFT                      0U
#define LPDDR4__DENALI_PI_186__PI_CA_PARITY_LAT_F2_WIDTH                      4U
#define LPDDR4__PI_CA_PARITY_LAT_F2__REG DENALI_PI_186
#define LPDDR4__PI_CA_PARITY_LAT_F2__FLD LPDDR4__DENALI_PI_186__PI_CA_PARITY_LAT_F2

#define LPDDR4__DENALI_PI_186__PI_TPARITY_ERROR_CMD_INHIBIT_F2_MASK  0x0000FF00U
#define LPDDR4__DENALI_PI_186__PI_TPARITY_ERROR_CMD_INHIBIT_F2_SHIFT          8U
#define LPDDR4__DENALI_PI_186__PI_TPARITY_ERROR_CMD_INHIBIT_F2_WIDTH          8U
#define LPDDR4__PI_TPARITY_ERROR_CMD_INHIBIT_F2__REG DENALI_PI_186
#define LPDDR4__PI_TPARITY_ERROR_CMD_INHIBIT_F2__FLD LPDDR4__DENALI_PI_186__PI_TPARITY_ERROR_CMD_INHIBIT_F2

#define LPDDR4__DENALI_PI_186__PI_CASLAT_LIN_F2_MASK                 0x007F0000U
#define LPDDR4__DENALI_PI_186__PI_CASLAT_LIN_F2_SHIFT                        16U
#define LPDDR4__DENALI_PI_186__PI_CASLAT_LIN_F2_WIDTH                         7U
#define LPDDR4__PI_CASLAT_LIN_F2__REG DENALI_PI_186
#define LPDDR4__PI_CASLAT_LIN_F2__FLD LPDDR4__DENALI_PI_186__PI_CASLAT_LIN_F2

#define LPDDR4__DENALI_PI_187_READ_MASK                              0x000003FFU
#define LPDDR4__DENALI_PI_187_WRITE_MASK                             0x000003FFU
#define LPDDR4__DENALI_PI_187__PI_TRFC_F0_MASK                       0x000003FFU
#define LPDDR4__DENALI_PI_187__PI_TRFC_F0_SHIFT                               0U
#define LPDDR4__DENALI_PI_187__PI_TRFC_F0_WIDTH                              10U
#define LPDDR4__PI_TRFC_F0__REG DENALI_PI_187
#define LPDDR4__PI_TRFC_F0__FLD LPDDR4__DENALI_PI_187__PI_TRFC_F0

#define LPDDR4__DENALI_PI_188_READ_MASK                              0x000FFFFFU
#define LPDDR4__DENALI_PI_188_WRITE_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PI_188__PI_TREF_F0_MASK                       0x000FFFFFU
#define LPDDR4__DENALI_PI_188__PI_TREF_F0_SHIFT                               0U
#define LPDDR4__DENALI_PI_188__PI_TREF_F0_WIDTH                              20U
#define LPDDR4__PI_TREF_F0__REG DENALI_PI_188
#define LPDDR4__PI_TREF_F0__FLD LPDDR4__DENALI_PI_188__PI_TREF_F0

#define LPDDR4__DENALI_PI_189_READ_MASK                              0x000003FFU
#define LPDDR4__DENALI_PI_189_WRITE_MASK                             0x000003FFU
#define LPDDR4__DENALI_PI_189__PI_TRFC_F1_MASK                       0x000003FFU
#define LPDDR4__DENALI_PI_189__PI_TRFC_F1_SHIFT                               0U
#define LPDDR4__DENALI_PI_189__PI_TRFC_F1_WIDTH                              10U
#define LPDDR4__PI_TRFC_F1__REG DENALI_PI_189
#define LPDDR4__PI_TRFC_F1__FLD LPDDR4__DENALI_PI_189__PI_TRFC_F1

#define LPDDR4__DENALI_PI_190_READ_MASK                              0x000FFFFFU
#define LPDDR4__DENALI_PI_190_WRITE_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PI_190__PI_TREF_F1_MASK                       0x000FFFFFU
#define LPDDR4__DENALI_PI_190__PI_TREF_F1_SHIFT                               0U
#define LPDDR4__DENALI_PI_190__PI_TREF_F1_WIDTH                              20U
#define LPDDR4__PI_TREF_F1__REG DENALI_PI_190
#define LPDDR4__PI_TREF_F1__FLD LPDDR4__DENALI_PI_190__PI_TREF_F1

#define LPDDR4__DENALI_PI_191_READ_MASK                              0x000003FFU
#define LPDDR4__DENALI_PI_191_WRITE_MASK                             0x000003FFU
#define LPDDR4__DENALI_PI_191__PI_TRFC_F2_MASK                       0x000003FFU
#define LPDDR4__DENALI_PI_191__PI_TRFC_F2_SHIFT                               0U
#define LPDDR4__DENALI_PI_191__PI_TRFC_F2_WIDTH                              10U
#define LPDDR4__PI_TRFC_F2__REG DENALI_PI_191
#define LPDDR4__PI_TRFC_F2__FLD LPDDR4__DENALI_PI_191__PI_TRFC_F2

#define LPDDR4__DENALI_PI_192_READ_MASK                              0x0F0FFFFFU
#define LPDDR4__DENALI_PI_192_WRITE_MASK                             0x0F0FFFFFU
#define LPDDR4__DENALI_PI_192__PI_TREF_F2_MASK                       0x000FFFFFU
#define LPDDR4__DENALI_PI_192__PI_TREF_F2_SHIFT                               0U
#define LPDDR4__DENALI_PI_192__PI_TREF_F2_WIDTH                              20U
#define LPDDR4__PI_TREF_F2__REG DENALI_PI_192
#define LPDDR4__PI_TREF_F2__FLD LPDDR4__DENALI_PI_192__PI_TREF_F2

#define LPDDR4__DENALI_PI_192__PI_TDFI_CTRL_DELAY_F0_MASK            0x0F000000U
#define LPDDR4__DENALI_PI_192__PI_TDFI_CTRL_DELAY_F0_SHIFT                   24U
#define LPDDR4__DENALI_PI_192__PI_TDFI_CTRL_DELAY_F0_WIDTH                    4U
#define LPDDR4__PI_TDFI_CTRL_DELAY_F0__REG DENALI_PI_192
#define LPDDR4__PI_TDFI_CTRL_DELAY_F0__FLD LPDDR4__DENALI_PI_192__PI_TDFI_CTRL_DELAY_F0

#define LPDDR4__DENALI_PI_193_READ_MASK                              0x03030F0FU
#define LPDDR4__DENALI_PI_193_WRITE_MASK                             0x03030F0FU
#define LPDDR4__DENALI_PI_193__PI_TDFI_CTRL_DELAY_F1_MASK            0x0000000FU
#define LPDDR4__DENALI_PI_193__PI_TDFI_CTRL_DELAY_F1_SHIFT                    0U
#define LPDDR4__DENALI_PI_193__PI_TDFI_CTRL_DELAY_F1_WIDTH                    4U
#define LPDDR4__PI_TDFI_CTRL_DELAY_F1__REG DENALI_PI_193
#define LPDDR4__PI_TDFI_CTRL_DELAY_F1__FLD LPDDR4__DENALI_PI_193__PI_TDFI_CTRL_DELAY_F1

#define LPDDR4__DENALI_PI_193__PI_TDFI_CTRL_DELAY_F2_MASK            0x00000F00U
#define LPDDR4__DENALI_PI_193__PI_TDFI_CTRL_DELAY_F2_SHIFT                    8U
#define LPDDR4__DENALI_PI_193__PI_TDFI_CTRL_DELAY_F2_WIDTH                    4U
#define LPDDR4__PI_TDFI_CTRL_DELAY_F2__REG DENALI_PI_193
#define LPDDR4__PI_TDFI_CTRL_DELAY_F2__FLD LPDDR4__DENALI_PI_193__PI_TDFI_CTRL_DELAY_F2

#define LPDDR4__DENALI_PI_193__PI_WRLVL_EN_F0_MASK                   0x00030000U
#define LPDDR4__DENALI_PI_193__PI_WRLVL_EN_F0_SHIFT                          16U
#define LPDDR4__DENALI_PI_193__PI_WRLVL_EN_F0_WIDTH                           2U
#define LPDDR4__PI_WRLVL_EN_F0__REG DENALI_PI_193
#define LPDDR4__PI_WRLVL_EN_F0__FLD LPDDR4__DENALI_PI_193__PI_WRLVL_EN_F0

#define LPDDR4__DENALI_PI_193__PI_WRLVL_EN_F1_MASK                   0x03000000U
#define LPDDR4__DENALI_PI_193__PI_WRLVL_EN_F1_SHIFT                          24U
#define LPDDR4__DENALI_PI_193__PI_WRLVL_EN_F1_WIDTH                           2U
#define LPDDR4__PI_WRLVL_EN_F1__REG DENALI_PI_193
#define LPDDR4__PI_WRLVL_EN_F1__FLD LPDDR4__DENALI_PI_193__PI_WRLVL_EN_F1

#define LPDDR4__DENALI_PI_194_READ_MASK                              0x0003FF03U
#define LPDDR4__DENALI_PI_194_WRITE_MASK                             0x0003FF03U
#define LPDDR4__DENALI_PI_194__PI_WRLVL_EN_F2_MASK                   0x00000003U
#define LPDDR4__DENALI_PI_194__PI_WRLVL_EN_F2_SHIFT                           0U
#define LPDDR4__DENALI_PI_194__PI_WRLVL_EN_F2_WIDTH                           2U
#define LPDDR4__PI_WRLVL_EN_F2__REG DENALI_PI_194
#define LPDDR4__PI_WRLVL_EN_F2__FLD LPDDR4__DENALI_PI_194__PI_WRLVL_EN_F2

#define LPDDR4__DENALI_PI_194__PI_TDFI_WRLVL_WW_F0_MASK              0x0003FF00U
#define LPDDR4__DENALI_PI_194__PI_TDFI_WRLVL_WW_F0_SHIFT                      8U
#define LPDDR4__DENALI_PI_194__PI_TDFI_WRLVL_WW_F0_WIDTH                     10U
#define LPDDR4__PI_TDFI_WRLVL_WW_F0__REG DENALI_PI_194
#define LPDDR4__PI_TDFI_WRLVL_WW_F0__FLD LPDDR4__DENALI_PI_194__PI_TDFI_WRLVL_WW_F0

#define LPDDR4__DENALI_PI_195_READ_MASK                              0x03FF03FFU
#define LPDDR4__DENALI_PI_195_WRITE_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PI_195__PI_TDFI_WRLVL_WW_F1_MASK              0x000003FFU
#define LPDDR4__DENALI_PI_195__PI_TDFI_WRLVL_WW_F1_SHIFT                      0U
#define LPDDR4__DENALI_PI_195__PI_TDFI_WRLVL_WW_F1_WIDTH                     10U
#define LPDDR4__PI_TDFI_WRLVL_WW_F1__REG DENALI_PI_195
#define LPDDR4__PI_TDFI_WRLVL_WW_F1__FLD LPDDR4__DENALI_PI_195__PI_TDFI_WRLVL_WW_F1

#define LPDDR4__DENALI_PI_195__PI_TDFI_WRLVL_WW_F2_MASK              0x03FF0000U
#define LPDDR4__DENALI_PI_195__PI_TDFI_WRLVL_WW_F2_SHIFT                     16U
#define LPDDR4__DENALI_PI_195__PI_TDFI_WRLVL_WW_F2_WIDTH                     10U
#define LPDDR4__PI_TDFI_WRLVL_WW_F2__REG DENALI_PI_195
#define LPDDR4__PI_TDFI_WRLVL_WW_F2__FLD LPDDR4__DENALI_PI_195__PI_TDFI_WRLVL_WW_F2

#define LPDDR4__DENALI_PI_196_READ_MASK                              0x01FF01FFU
#define LPDDR4__DENALI_PI_196_WRITE_MASK                             0x01FF01FFU
#define LPDDR4__DENALI_PI_196__PI_TODTL_2CMD_F0_MASK                 0x000000FFU
#define LPDDR4__DENALI_PI_196__PI_TODTL_2CMD_F0_SHIFT                         0U
#define LPDDR4__DENALI_PI_196__PI_TODTL_2CMD_F0_WIDTH                         8U
#define LPDDR4__PI_TODTL_2CMD_F0__REG DENALI_PI_196
#define LPDDR4__PI_TODTL_2CMD_F0__FLD LPDDR4__DENALI_PI_196__PI_TODTL_2CMD_F0

#define LPDDR4__DENALI_PI_196__PI_ODT_EN_F0_MASK                     0x00000100U
#define LPDDR4__DENALI_PI_196__PI_ODT_EN_F0_SHIFT                             8U
#define LPDDR4__DENALI_PI_196__PI_ODT_EN_F0_WIDTH                             1U
#define LPDDR4__DENALI_PI_196__PI_ODT_EN_F0_WOCLR                             0U
#define LPDDR4__DENALI_PI_196__PI_ODT_EN_F0_WOSET                             0U
#define LPDDR4__PI_ODT_EN_F0__REG DENALI_PI_196
#define LPDDR4__PI_ODT_EN_F0__FLD LPDDR4__DENALI_PI_196__PI_ODT_EN_F0

#define LPDDR4__DENALI_PI_196__PI_TODTL_2CMD_F1_MASK                 0x00FF0000U
#define LPDDR4__DENALI_PI_196__PI_TODTL_2CMD_F1_SHIFT                        16U
#define LPDDR4__DENALI_PI_196__PI_TODTL_2CMD_F1_WIDTH                         8U
#define LPDDR4__PI_TODTL_2CMD_F1__REG DENALI_PI_196
#define LPDDR4__PI_TODTL_2CMD_F1__FLD LPDDR4__DENALI_PI_196__PI_TODTL_2CMD_F1

#define LPDDR4__DENALI_PI_196__PI_ODT_EN_F1_MASK                     0x01000000U
#define LPDDR4__DENALI_PI_196__PI_ODT_EN_F1_SHIFT                            24U
#define LPDDR4__DENALI_PI_196__PI_ODT_EN_F1_WIDTH                             1U
#define LPDDR4__DENALI_PI_196__PI_ODT_EN_F1_WOCLR                             0U
#define LPDDR4__DENALI_PI_196__PI_ODT_EN_F1_WOSET                             0U
#define LPDDR4__PI_ODT_EN_F1__REG DENALI_PI_196
#define LPDDR4__PI_ODT_EN_F1__FLD LPDDR4__DENALI_PI_196__PI_ODT_EN_F1

#define LPDDR4__DENALI_PI_197_READ_MASK                              0x0F0F01FFU
#define LPDDR4__DENALI_PI_197_WRITE_MASK                             0x0F0F01FFU
#define LPDDR4__DENALI_PI_197__PI_TODTL_2CMD_F2_MASK                 0x000000FFU
#define LPDDR4__DENALI_PI_197__PI_TODTL_2CMD_F2_SHIFT                         0U
#define LPDDR4__DENALI_PI_197__PI_TODTL_2CMD_F2_WIDTH                         8U
#define LPDDR4__PI_TODTL_2CMD_F2__REG DENALI_PI_197
#define LPDDR4__PI_TODTL_2CMD_F2__FLD LPDDR4__DENALI_PI_197__PI_TODTL_2CMD_F2

#define LPDDR4__DENALI_PI_197__PI_ODT_EN_F2_MASK                     0x00000100U
#define LPDDR4__DENALI_PI_197__PI_ODT_EN_F2_SHIFT                             8U
#define LPDDR4__DENALI_PI_197__PI_ODT_EN_F2_WIDTH                             1U
#define LPDDR4__DENALI_PI_197__PI_ODT_EN_F2_WOCLR                             0U
#define LPDDR4__DENALI_PI_197__PI_ODT_EN_F2_WOSET                             0U
#define LPDDR4__PI_ODT_EN_F2__REG DENALI_PI_197
#define LPDDR4__PI_ODT_EN_F2__FLD LPDDR4__DENALI_PI_197__PI_ODT_EN_F2

#define LPDDR4__DENALI_PI_197__PI_ODTLON_F0_MASK                     0x000F0000U
#define LPDDR4__DENALI_PI_197__PI_ODTLON_F0_SHIFT                            16U
#define LPDDR4__DENALI_PI_197__PI_ODTLON_F0_WIDTH                             4U
#define LPDDR4__PI_ODTLON_F0__REG DENALI_PI_197
#define LPDDR4__PI_ODTLON_F0__FLD LPDDR4__DENALI_PI_197__PI_ODTLON_F0

#define LPDDR4__DENALI_PI_197__PI_TODTON_MIN_F0_MASK                 0x0F000000U
#define LPDDR4__DENALI_PI_197__PI_TODTON_MIN_F0_SHIFT                        24U
#define LPDDR4__DENALI_PI_197__PI_TODTON_MIN_F0_WIDTH                         4U
#define LPDDR4__PI_TODTON_MIN_F0__REG DENALI_PI_197
#define LPDDR4__PI_TODTON_MIN_F0__FLD LPDDR4__DENALI_PI_197__PI_TODTON_MIN_F0

#define LPDDR4__DENALI_PI_198_READ_MASK                              0x0F0F0F0FU
#define LPDDR4__DENALI_PI_198_WRITE_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_PI_198__PI_ODTLON_F1_MASK                     0x0000000FU
#define LPDDR4__DENALI_PI_198__PI_ODTLON_F1_SHIFT                             0U
#define LPDDR4__DENALI_PI_198__PI_ODTLON_F1_WIDTH                             4U
#define LPDDR4__PI_ODTLON_F1__REG DENALI_PI_198
#define LPDDR4__PI_ODTLON_F1__FLD LPDDR4__DENALI_PI_198__PI_ODTLON_F1

#define LPDDR4__DENALI_PI_198__PI_TODTON_MIN_F1_MASK                 0x00000F00U
#define LPDDR4__DENALI_PI_198__PI_TODTON_MIN_F1_SHIFT                         8U
#define LPDDR4__DENALI_PI_198__PI_TODTON_MIN_F1_WIDTH                         4U
#define LPDDR4__PI_TODTON_MIN_F1__REG DENALI_PI_198
#define LPDDR4__PI_TODTON_MIN_F1__FLD LPDDR4__DENALI_PI_198__PI_TODTON_MIN_F1

#define LPDDR4__DENALI_PI_198__PI_ODTLON_F2_MASK                     0x000F0000U
#define LPDDR4__DENALI_PI_198__PI_ODTLON_F2_SHIFT                            16U
#define LPDDR4__DENALI_PI_198__PI_ODTLON_F2_WIDTH                             4U
#define LPDDR4__PI_ODTLON_F2__REG DENALI_PI_198
#define LPDDR4__PI_ODTLON_F2__FLD LPDDR4__DENALI_PI_198__PI_ODTLON_F2

#define LPDDR4__DENALI_PI_198__PI_TODTON_MIN_F2_MASK                 0x0F000000U
#define LPDDR4__DENALI_PI_198__PI_TODTON_MIN_F2_SHIFT                        24U
#define LPDDR4__DENALI_PI_198__PI_TODTON_MIN_F2_WIDTH                         4U
#define LPDDR4__PI_TODTON_MIN_F2__REG DENALI_PI_198
#define LPDDR4__PI_TODTON_MIN_F2__FLD LPDDR4__DENALI_PI_198__PI_TODTON_MIN_F2

#define LPDDR4__DENALI_PI_199_READ_MASK                              0x3F3F3F3FU
#define LPDDR4__DENALI_PI_199_WRITE_MASK                             0x3F3F3F3FU
#define LPDDR4__DENALI_PI_199__PI_WR_TO_ODTH_F0_MASK                 0x0000003FU
#define LPDDR4__DENALI_PI_199__PI_WR_TO_ODTH_F0_SHIFT                         0U
#define LPDDR4__DENALI_PI_199__PI_WR_TO_ODTH_F0_WIDTH                         6U
#define LPDDR4__PI_WR_TO_ODTH_F0__REG DENALI_PI_199
#define LPDDR4__PI_WR_TO_ODTH_F0__FLD LPDDR4__DENALI_PI_199__PI_WR_TO_ODTH_F0

#define LPDDR4__DENALI_PI_199__PI_WR_TO_ODTH_F1_MASK                 0x00003F00U
#define LPDDR4__DENALI_PI_199__PI_WR_TO_ODTH_F1_SHIFT                         8U
#define LPDDR4__DENALI_PI_199__PI_WR_TO_ODTH_F1_WIDTH                         6U
#define LPDDR4__PI_WR_TO_ODTH_F1__REG DENALI_PI_199
#define LPDDR4__PI_WR_TO_ODTH_F1__FLD LPDDR4__DENALI_PI_199__PI_WR_TO_ODTH_F1

#define LPDDR4__DENALI_PI_199__PI_WR_TO_ODTH_F2_MASK                 0x003F0000U
#define LPDDR4__DENALI_PI_199__PI_WR_TO_ODTH_F2_SHIFT                        16U
#define LPDDR4__DENALI_PI_199__PI_WR_TO_ODTH_F2_WIDTH                         6U
#define LPDDR4__PI_WR_TO_ODTH_F2__REG DENALI_PI_199
#define LPDDR4__PI_WR_TO_ODTH_F2__FLD LPDDR4__DENALI_PI_199__PI_WR_TO_ODTH_F2

#define LPDDR4__DENALI_PI_199__PI_RD_TO_ODTH_F0_MASK                 0x3F000000U
#define LPDDR4__DENALI_PI_199__PI_RD_TO_ODTH_F0_SHIFT                        24U
#define LPDDR4__DENALI_PI_199__PI_RD_TO_ODTH_F0_WIDTH                         6U
#define LPDDR4__PI_RD_TO_ODTH_F0__REG DENALI_PI_199
#define LPDDR4__PI_RD_TO_ODTH_F0__FLD LPDDR4__DENALI_PI_199__PI_RD_TO_ODTH_F0

#define LPDDR4__DENALI_PI_200_READ_MASK                              0x03033F3FU
#define LPDDR4__DENALI_PI_200_WRITE_MASK                             0x03033F3FU
#define LPDDR4__DENALI_PI_200__PI_RD_TO_ODTH_F1_MASK                 0x0000003FU
#define LPDDR4__DENALI_PI_200__PI_RD_TO_ODTH_F1_SHIFT                         0U
#define LPDDR4__DENALI_PI_200__PI_RD_TO_ODTH_F1_WIDTH                         6U
#define LPDDR4__PI_RD_TO_ODTH_F1__REG DENALI_PI_200
#define LPDDR4__PI_RD_TO_ODTH_F1__FLD LPDDR4__DENALI_PI_200__PI_RD_TO_ODTH_F1

#define LPDDR4__DENALI_PI_200__PI_RD_TO_ODTH_F2_MASK                 0x00003F00U
#define LPDDR4__DENALI_PI_200__PI_RD_TO_ODTH_F2_SHIFT                         8U
#define LPDDR4__DENALI_PI_200__PI_RD_TO_ODTH_F2_WIDTH                         6U
#define LPDDR4__PI_RD_TO_ODTH_F2__REG DENALI_PI_200
#define LPDDR4__PI_RD_TO_ODTH_F2__FLD LPDDR4__DENALI_PI_200__PI_RD_TO_ODTH_F2

#define LPDDR4__DENALI_PI_200__PI_RDLVL_EN_F0_MASK                   0x00030000U
#define LPDDR4__DENALI_PI_200__PI_RDLVL_EN_F0_SHIFT                          16U
#define LPDDR4__DENALI_PI_200__PI_RDLVL_EN_F0_WIDTH                           2U
#define LPDDR4__PI_RDLVL_EN_F0__REG DENALI_PI_200
#define LPDDR4__PI_RDLVL_EN_F0__FLD LPDDR4__DENALI_PI_200__PI_RDLVL_EN_F0

#define LPDDR4__DENALI_PI_200__PI_RDLVL_GATE_EN_F0_MASK              0x03000000U
#define LPDDR4__DENALI_PI_200__PI_RDLVL_GATE_EN_F0_SHIFT                     24U
#define LPDDR4__DENALI_PI_200__PI_RDLVL_GATE_EN_F0_WIDTH                      2U
#define LPDDR4__PI_RDLVL_GATE_EN_F0__REG DENALI_PI_200
#define LPDDR4__PI_RDLVL_GATE_EN_F0__FLD LPDDR4__DENALI_PI_200__PI_RDLVL_GATE_EN_F0

#define LPDDR4__DENALI_PI_201_READ_MASK                              0x03030303U
#define LPDDR4__DENALI_PI_201_WRITE_MASK                             0x03030303U
#define LPDDR4__DENALI_PI_201__PI_RDLVL_EN_F1_MASK                   0x00000003U
#define LPDDR4__DENALI_PI_201__PI_RDLVL_EN_F1_SHIFT                           0U
#define LPDDR4__DENALI_PI_201__PI_RDLVL_EN_F1_WIDTH                           2U
#define LPDDR4__PI_RDLVL_EN_F1__REG DENALI_PI_201
#define LPDDR4__PI_RDLVL_EN_F1__FLD LPDDR4__DENALI_PI_201__PI_RDLVL_EN_F1

#define LPDDR4__DENALI_PI_201__PI_RDLVL_GATE_EN_F1_MASK              0x00000300U
#define LPDDR4__DENALI_PI_201__PI_RDLVL_GATE_EN_F1_SHIFT                      8U
#define LPDDR4__DENALI_PI_201__PI_RDLVL_GATE_EN_F1_WIDTH                      2U
#define LPDDR4__PI_RDLVL_GATE_EN_F1__REG DENALI_PI_201
#define LPDDR4__PI_RDLVL_GATE_EN_F1__FLD LPDDR4__DENALI_PI_201__PI_RDLVL_GATE_EN_F1

#define LPDDR4__DENALI_PI_201__PI_RDLVL_EN_F2_MASK                   0x00030000U
#define LPDDR4__DENALI_PI_201__PI_RDLVL_EN_F2_SHIFT                          16U
#define LPDDR4__DENALI_PI_201__PI_RDLVL_EN_F2_WIDTH                           2U
#define LPDDR4__PI_RDLVL_EN_F2__REG DENALI_PI_201
#define LPDDR4__PI_RDLVL_EN_F2__FLD LPDDR4__DENALI_PI_201__PI_RDLVL_EN_F2

#define LPDDR4__DENALI_PI_201__PI_RDLVL_GATE_EN_F2_MASK              0x03000000U
#define LPDDR4__DENALI_PI_201__PI_RDLVL_GATE_EN_F2_SHIFT                     24U
#define LPDDR4__DENALI_PI_201__PI_RDLVL_GATE_EN_F2_WIDTH                      2U
#define LPDDR4__PI_RDLVL_GATE_EN_F2__REG DENALI_PI_201
#define LPDDR4__PI_RDLVL_GATE_EN_F2__FLD LPDDR4__DENALI_PI_201__PI_RDLVL_GATE_EN_F2

#define LPDDR4__DENALI_PI_202_READ_MASK                              0x03FFFFFFU
#define LPDDR4__DENALI_PI_202_WRITE_MASK                             0x03FFFFFFU
#define LPDDR4__DENALI_PI_202__PI_TWR_MPR_F0_MASK                    0x000000FFU
#define LPDDR4__DENALI_PI_202__PI_TWR_MPR_F0_SHIFT                            0U
#define LPDDR4__DENALI_PI_202__PI_TWR_MPR_F0_WIDTH                            8U
#define LPDDR4__PI_TWR_MPR_F0__REG DENALI_PI_202
#define LPDDR4__PI_TWR_MPR_F0__FLD LPDDR4__DENALI_PI_202__PI_TWR_MPR_F0

#define LPDDR4__DENALI_PI_202__PI_TWR_MPR_F1_MASK                    0x0000FF00U
#define LPDDR4__DENALI_PI_202__PI_TWR_MPR_F1_SHIFT                            8U
#define LPDDR4__DENALI_PI_202__PI_TWR_MPR_F1_WIDTH                            8U
#define LPDDR4__PI_TWR_MPR_F1__REG DENALI_PI_202
#define LPDDR4__PI_TWR_MPR_F1__FLD LPDDR4__DENALI_PI_202__PI_TWR_MPR_F1

#define LPDDR4__DENALI_PI_202__PI_TWR_MPR_F2_MASK                    0x00FF0000U
#define LPDDR4__DENALI_PI_202__PI_TWR_MPR_F2_SHIFT                           16U
#define LPDDR4__DENALI_PI_202__PI_TWR_MPR_F2_WIDTH                            8U
#define LPDDR4__PI_TWR_MPR_F2__REG DENALI_PI_202
#define LPDDR4__PI_TWR_MPR_F2__FLD LPDDR4__DENALI_PI_202__PI_TWR_MPR_F2

#define LPDDR4__DENALI_PI_202__PI_RDLVL_PAT0_EN_F0_MASK              0x03000000U
#define LPDDR4__DENALI_PI_202__PI_RDLVL_PAT0_EN_F0_SHIFT                     24U
#define LPDDR4__DENALI_PI_202__PI_RDLVL_PAT0_EN_F0_WIDTH                      2U
#define LPDDR4__PI_RDLVL_PAT0_EN_F0__REG DENALI_PI_202
#define LPDDR4__PI_RDLVL_PAT0_EN_F0__FLD LPDDR4__DENALI_PI_202__PI_RDLVL_PAT0_EN_F0

#define LPDDR4__DENALI_PI_203_READ_MASK                              0x03030303U
#define LPDDR4__DENALI_PI_203_WRITE_MASK                             0x03030303U
#define LPDDR4__DENALI_PI_203__PI_RDLVL_RXCAL_EN_F0_MASK             0x00000003U
#define LPDDR4__DENALI_PI_203__PI_RDLVL_RXCAL_EN_F0_SHIFT                     0U
#define LPDDR4__DENALI_PI_203__PI_RDLVL_RXCAL_EN_F0_WIDTH                     2U
#define LPDDR4__PI_RDLVL_RXCAL_EN_F0__REG DENALI_PI_203
#define LPDDR4__PI_RDLVL_RXCAL_EN_F0__FLD LPDDR4__DENALI_PI_203__PI_RDLVL_RXCAL_EN_F0

#define LPDDR4__DENALI_PI_203__PI_RDLVL_DFE_EN_F0_MASK               0x00000300U
#define LPDDR4__DENALI_PI_203__PI_RDLVL_DFE_EN_F0_SHIFT                       8U
#define LPDDR4__DENALI_PI_203__PI_RDLVL_DFE_EN_F0_WIDTH                       2U
#define LPDDR4__PI_RDLVL_DFE_EN_F0__REG DENALI_PI_203
#define LPDDR4__PI_RDLVL_DFE_EN_F0__FLD LPDDR4__DENALI_PI_203__PI_RDLVL_DFE_EN_F0

#define LPDDR4__DENALI_PI_203__PI_RDLVL_MULTI_EN_F0_MASK             0x00030000U
#define LPDDR4__DENALI_PI_203__PI_RDLVL_MULTI_EN_F0_SHIFT                    16U
#define LPDDR4__DENALI_PI_203__PI_RDLVL_MULTI_EN_F0_WIDTH                     2U
#define LPDDR4__PI_RDLVL_MULTI_EN_F0__REG DENALI_PI_203
#define LPDDR4__PI_RDLVL_MULTI_EN_F0__FLD LPDDR4__DENALI_PI_203__PI_RDLVL_MULTI_EN_F0

#define LPDDR4__DENALI_PI_203__PI_RDLVL_PAT0_EN_F1_MASK              0x03000000U
#define LPDDR4__DENALI_PI_203__PI_RDLVL_PAT0_EN_F1_SHIFT                     24U
#define LPDDR4__DENALI_PI_203__PI_RDLVL_PAT0_EN_F1_WIDTH                      2U
#define LPDDR4__PI_RDLVL_PAT0_EN_F1__REG DENALI_PI_203
#define LPDDR4__PI_RDLVL_PAT0_EN_F1__FLD LPDDR4__DENALI_PI_203__PI_RDLVL_PAT0_EN_F1

#define LPDDR4__DENALI_PI_204_READ_MASK                              0x03030303U
#define LPDDR4__DENALI_PI_204_WRITE_MASK                             0x03030303U
#define LPDDR4__DENALI_PI_204__PI_RDLVL_RXCAL_EN_F1_MASK             0x00000003U
#define LPDDR4__DENALI_PI_204__PI_RDLVL_RXCAL_EN_F1_SHIFT                     0U
#define LPDDR4__DENALI_PI_204__PI_RDLVL_RXCAL_EN_F1_WIDTH                     2U
#define LPDDR4__PI_RDLVL_RXCAL_EN_F1__REG DENALI_PI_204
#define LPDDR4__PI_RDLVL_RXCAL_EN_F1__FLD LPDDR4__DENALI_PI_204__PI_RDLVL_RXCAL_EN_F1

#define LPDDR4__DENALI_PI_204__PI_RDLVL_DFE_EN_F1_MASK               0x00000300U
#define LPDDR4__DENALI_PI_204__PI_RDLVL_DFE_EN_F1_SHIFT                       8U
#define LPDDR4__DENALI_PI_204__PI_RDLVL_DFE_EN_F1_WIDTH                       2U
#define LPDDR4__PI_RDLVL_DFE_EN_F1__REG DENALI_PI_204
#define LPDDR4__PI_RDLVL_DFE_EN_F1__FLD LPDDR4__DENALI_PI_204__PI_RDLVL_DFE_EN_F1

#define LPDDR4__DENALI_PI_204__PI_RDLVL_MULTI_EN_F1_MASK             0x00030000U
#define LPDDR4__DENALI_PI_204__PI_RDLVL_MULTI_EN_F1_SHIFT                    16U
#define LPDDR4__DENALI_PI_204__PI_RDLVL_MULTI_EN_F1_WIDTH                     2U
#define LPDDR4__PI_RDLVL_MULTI_EN_F1__REG DENALI_PI_204
#define LPDDR4__PI_RDLVL_MULTI_EN_F1__FLD LPDDR4__DENALI_PI_204__PI_RDLVL_MULTI_EN_F1

#define LPDDR4__DENALI_PI_204__PI_RDLVL_PAT0_EN_F2_MASK              0x03000000U
#define LPDDR4__DENALI_PI_204__PI_RDLVL_PAT0_EN_F2_SHIFT                     24U
#define LPDDR4__DENALI_PI_204__PI_RDLVL_PAT0_EN_F2_WIDTH                      2U
#define LPDDR4__PI_RDLVL_PAT0_EN_F2__REG DENALI_PI_204
#define LPDDR4__PI_RDLVL_PAT0_EN_F2__FLD LPDDR4__DENALI_PI_204__PI_RDLVL_PAT0_EN_F2

#define LPDDR4__DENALI_PI_205_READ_MASK                              0xFF030303U
#define LPDDR4__DENALI_PI_205_WRITE_MASK                             0xFF030303U
#define LPDDR4__DENALI_PI_205__PI_RDLVL_RXCAL_EN_F2_MASK             0x00000003U
#define LPDDR4__DENALI_PI_205__PI_RDLVL_RXCAL_EN_F2_SHIFT                     0U
#define LPDDR4__DENALI_PI_205__PI_RDLVL_RXCAL_EN_F2_WIDTH                     2U
#define LPDDR4__PI_RDLVL_RXCAL_EN_F2__REG DENALI_PI_205
#define LPDDR4__PI_RDLVL_RXCAL_EN_F2__FLD LPDDR4__DENALI_PI_205__PI_RDLVL_RXCAL_EN_F2

#define LPDDR4__DENALI_PI_205__PI_RDLVL_DFE_EN_F2_MASK               0x00000300U
#define LPDDR4__DENALI_PI_205__PI_RDLVL_DFE_EN_F2_SHIFT                       8U
#define LPDDR4__DENALI_PI_205__PI_RDLVL_DFE_EN_F2_WIDTH                       2U
#define LPDDR4__PI_RDLVL_DFE_EN_F2__REG DENALI_PI_205
#define LPDDR4__PI_RDLVL_DFE_EN_F2__FLD LPDDR4__DENALI_PI_205__PI_RDLVL_DFE_EN_F2

#define LPDDR4__DENALI_PI_205__PI_RDLVL_MULTI_EN_F2_MASK             0x00030000U
#define LPDDR4__DENALI_PI_205__PI_RDLVL_MULTI_EN_F2_SHIFT                    16U
#define LPDDR4__DENALI_PI_205__PI_RDLVL_MULTI_EN_F2_WIDTH                     2U
#define LPDDR4__PI_RDLVL_MULTI_EN_F2__REG DENALI_PI_205
#define LPDDR4__PI_RDLVL_MULTI_EN_F2__FLD LPDDR4__DENALI_PI_205__PI_RDLVL_MULTI_EN_F2

#define LPDDR4__DENALI_PI_205__PI_RDLAT_ADJ_F0_MASK                  0xFF000000U
#define LPDDR4__DENALI_PI_205__PI_RDLAT_ADJ_F0_SHIFT                         24U
#define LPDDR4__DENALI_PI_205__PI_RDLAT_ADJ_F0_WIDTH                          8U
#define LPDDR4__PI_RDLAT_ADJ_F0__REG DENALI_PI_205
#define LPDDR4__PI_RDLAT_ADJ_F0__FLD LPDDR4__DENALI_PI_205__PI_RDLAT_ADJ_F0

#define LPDDR4__DENALI_PI_206_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_206_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_206__PI_RDLAT_ADJ_F1_MASK                  0x000000FFU
#define LPDDR4__DENALI_PI_206__PI_RDLAT_ADJ_F1_SHIFT                          0U
#define LPDDR4__DENALI_PI_206__PI_RDLAT_ADJ_F1_WIDTH                          8U
#define LPDDR4__PI_RDLAT_ADJ_F1__REG DENALI_PI_206
#define LPDDR4__PI_RDLAT_ADJ_F1__FLD LPDDR4__DENALI_PI_206__PI_RDLAT_ADJ_F1

#define LPDDR4__DENALI_PI_206__PI_RDLAT_ADJ_F2_MASK                  0x0000FF00U
#define LPDDR4__DENALI_PI_206__PI_RDLAT_ADJ_F2_SHIFT                          8U
#define LPDDR4__DENALI_PI_206__PI_RDLAT_ADJ_F2_WIDTH                          8U
#define LPDDR4__PI_RDLAT_ADJ_F2__REG DENALI_PI_206
#define LPDDR4__PI_RDLAT_ADJ_F2__FLD LPDDR4__DENALI_PI_206__PI_RDLAT_ADJ_F2

#define LPDDR4__DENALI_PI_206__PI_WRLAT_ADJ_F0_MASK                  0x00FF0000U
#define LPDDR4__DENALI_PI_206__PI_WRLAT_ADJ_F0_SHIFT                         16U
#define LPDDR4__DENALI_PI_206__PI_WRLAT_ADJ_F0_WIDTH                          8U
#define LPDDR4__PI_WRLAT_ADJ_F0__REG DENALI_PI_206
#define LPDDR4__PI_WRLAT_ADJ_F0__FLD LPDDR4__DENALI_PI_206__PI_WRLAT_ADJ_F0

#define LPDDR4__DENALI_PI_206__PI_WRLAT_ADJ_F1_MASK                  0xFF000000U
#define LPDDR4__DENALI_PI_206__PI_WRLAT_ADJ_F1_SHIFT                         24U
#define LPDDR4__DENALI_PI_206__PI_WRLAT_ADJ_F1_WIDTH                          8U
#define LPDDR4__PI_WRLAT_ADJ_F1__REG DENALI_PI_206
#define LPDDR4__PI_WRLAT_ADJ_F1__FLD LPDDR4__DENALI_PI_206__PI_WRLAT_ADJ_F1

#define LPDDR4__DENALI_PI_207_READ_MASK                              0x070707FFU
#define LPDDR4__DENALI_PI_207_WRITE_MASK                             0x070707FFU
#define LPDDR4__DENALI_PI_207__PI_WRLAT_ADJ_F2_MASK                  0x000000FFU
#define LPDDR4__DENALI_PI_207__PI_WRLAT_ADJ_F2_SHIFT                          0U
#define LPDDR4__DENALI_PI_207__PI_WRLAT_ADJ_F2_WIDTH                          8U
#define LPDDR4__PI_WRLAT_ADJ_F2__REG DENALI_PI_207
#define LPDDR4__PI_WRLAT_ADJ_F2__FLD LPDDR4__DENALI_PI_207__PI_WRLAT_ADJ_F2

#define LPDDR4__DENALI_PI_207__PI_TDFI_PHY_WRDATA_F0_MASK            0x00000700U
#define LPDDR4__DENALI_PI_207__PI_TDFI_PHY_WRDATA_F0_SHIFT                    8U
#define LPDDR4__DENALI_PI_207__PI_TDFI_PHY_WRDATA_F0_WIDTH                    3U
#define LPDDR4__PI_TDFI_PHY_WRDATA_F0__REG DENALI_PI_207
#define LPDDR4__PI_TDFI_PHY_WRDATA_F0__FLD LPDDR4__DENALI_PI_207__PI_TDFI_PHY_WRDATA_F0

#define LPDDR4__DENALI_PI_207__PI_TDFI_PHY_WRDATA_F1_MASK            0x00070000U
#define LPDDR4__DENALI_PI_207__PI_TDFI_PHY_WRDATA_F1_SHIFT                   16U
#define LPDDR4__DENALI_PI_207__PI_TDFI_PHY_WRDATA_F1_WIDTH                    3U
#define LPDDR4__PI_TDFI_PHY_WRDATA_F1__REG DENALI_PI_207
#define LPDDR4__PI_TDFI_PHY_WRDATA_F1__FLD LPDDR4__DENALI_PI_207__PI_TDFI_PHY_WRDATA_F1

#define LPDDR4__DENALI_PI_207__PI_TDFI_PHY_WRDATA_F2_MASK            0x07000000U
#define LPDDR4__DENALI_PI_207__PI_TDFI_PHY_WRDATA_F2_SHIFT                   24U
#define LPDDR4__DENALI_PI_207__PI_TDFI_PHY_WRDATA_F2_WIDTH                    3U
#define LPDDR4__PI_TDFI_PHY_WRDATA_F2__REG DENALI_PI_207
#define LPDDR4__PI_TDFI_PHY_WRDATA_F2__FLD LPDDR4__DENALI_PI_207__PI_TDFI_PHY_WRDATA_F2

#define LPDDR4__DENALI_PI_208_READ_MASK                              0x03FF03FFU
#define LPDDR4__DENALI_PI_208_WRITE_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PI_208__PI_TDFI_CALVL_CC_F0_MASK              0x000003FFU
#define LPDDR4__DENALI_PI_208__PI_TDFI_CALVL_CC_F0_SHIFT                      0U
#define LPDDR4__DENALI_PI_208__PI_TDFI_CALVL_CC_F0_WIDTH                     10U
#define LPDDR4__PI_TDFI_CALVL_CC_F0__REG DENALI_PI_208
#define LPDDR4__PI_TDFI_CALVL_CC_F0__FLD LPDDR4__DENALI_PI_208__PI_TDFI_CALVL_CC_F0

#define LPDDR4__DENALI_PI_208__PI_TDFI_CALVL_CAPTURE_F0_MASK         0x03FF0000U
#define LPDDR4__DENALI_PI_208__PI_TDFI_CALVL_CAPTURE_F0_SHIFT                16U
#define LPDDR4__DENALI_PI_208__PI_TDFI_CALVL_CAPTURE_F0_WIDTH                10U
#define LPDDR4__PI_TDFI_CALVL_CAPTURE_F0__REG DENALI_PI_208
#define LPDDR4__PI_TDFI_CALVL_CAPTURE_F0__FLD LPDDR4__DENALI_PI_208__PI_TDFI_CALVL_CAPTURE_F0

#define LPDDR4__DENALI_PI_209_READ_MASK                              0x03FF03FFU
#define LPDDR4__DENALI_PI_209_WRITE_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PI_209__PI_TDFI_CALVL_CC_F1_MASK              0x000003FFU
#define LPDDR4__DENALI_PI_209__PI_TDFI_CALVL_CC_F1_SHIFT                      0U
#define LPDDR4__DENALI_PI_209__PI_TDFI_CALVL_CC_F1_WIDTH                     10U
#define LPDDR4__PI_TDFI_CALVL_CC_F1__REG DENALI_PI_209
#define LPDDR4__PI_TDFI_CALVL_CC_F1__FLD LPDDR4__DENALI_PI_209__PI_TDFI_CALVL_CC_F1

#define LPDDR4__DENALI_PI_209__PI_TDFI_CALVL_CAPTURE_F1_MASK         0x03FF0000U
#define LPDDR4__DENALI_PI_209__PI_TDFI_CALVL_CAPTURE_F1_SHIFT                16U
#define LPDDR4__DENALI_PI_209__PI_TDFI_CALVL_CAPTURE_F1_WIDTH                10U
#define LPDDR4__PI_TDFI_CALVL_CAPTURE_F1__REG DENALI_PI_209
#define LPDDR4__PI_TDFI_CALVL_CAPTURE_F1__FLD LPDDR4__DENALI_PI_209__PI_TDFI_CALVL_CAPTURE_F1

#define LPDDR4__DENALI_PI_210_READ_MASK                              0x03FF03FFU
#define LPDDR4__DENALI_PI_210_WRITE_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PI_210__PI_TDFI_CALVL_CC_F2_MASK              0x000003FFU
#define LPDDR4__DENALI_PI_210__PI_TDFI_CALVL_CC_F2_SHIFT                      0U
#define LPDDR4__DENALI_PI_210__PI_TDFI_CALVL_CC_F2_WIDTH                     10U
#define LPDDR4__PI_TDFI_CALVL_CC_F2__REG DENALI_PI_210
#define LPDDR4__PI_TDFI_CALVL_CC_F2__FLD LPDDR4__DENALI_PI_210__PI_TDFI_CALVL_CC_F2

#define LPDDR4__DENALI_PI_210__PI_TDFI_CALVL_CAPTURE_F2_MASK         0x03FF0000U
#define LPDDR4__DENALI_PI_210__PI_TDFI_CALVL_CAPTURE_F2_SHIFT                16U
#define LPDDR4__DENALI_PI_210__PI_TDFI_CALVL_CAPTURE_F2_WIDTH                10U
#define LPDDR4__PI_TDFI_CALVL_CAPTURE_F2__REG DENALI_PI_210
#define LPDDR4__PI_TDFI_CALVL_CAPTURE_F2__FLD LPDDR4__DENALI_PI_210__PI_TDFI_CALVL_CAPTURE_F2

#define LPDDR4__DENALI_PI_211_READ_MASK                              0x1F030303U
#define LPDDR4__DENALI_PI_211_WRITE_MASK                             0x1F030303U
#define LPDDR4__DENALI_PI_211__PI_CALVL_EN_F0_MASK                   0x00000003U
#define LPDDR4__DENALI_PI_211__PI_CALVL_EN_F0_SHIFT                           0U
#define LPDDR4__DENALI_PI_211__PI_CALVL_EN_F0_WIDTH                           2U
#define LPDDR4__PI_CALVL_EN_F0__REG DENALI_PI_211
#define LPDDR4__PI_CALVL_EN_F0__FLD LPDDR4__DENALI_PI_211__PI_CALVL_EN_F0

#define LPDDR4__DENALI_PI_211__PI_CALVL_EN_F1_MASK                   0x00000300U
#define LPDDR4__DENALI_PI_211__PI_CALVL_EN_F1_SHIFT                           8U
#define LPDDR4__DENALI_PI_211__PI_CALVL_EN_F1_WIDTH                           2U
#define LPDDR4__PI_CALVL_EN_F1__REG DENALI_PI_211
#define LPDDR4__PI_CALVL_EN_F1__FLD LPDDR4__DENALI_PI_211__PI_CALVL_EN_F1

#define LPDDR4__DENALI_PI_211__PI_CALVL_EN_F2_MASK                   0x00030000U
#define LPDDR4__DENALI_PI_211__PI_CALVL_EN_F2_SHIFT                          16U
#define LPDDR4__DENALI_PI_211__PI_CALVL_EN_F2_WIDTH                           2U
#define LPDDR4__PI_CALVL_EN_F2__REG DENALI_PI_211
#define LPDDR4__PI_CALVL_EN_F2__FLD LPDDR4__DENALI_PI_211__PI_CALVL_EN_F2

#define LPDDR4__DENALI_PI_211__PI_TMRZ_F0_MASK                       0x1F000000U
#define LPDDR4__DENALI_PI_211__PI_TMRZ_F0_SHIFT                              24U
#define LPDDR4__DENALI_PI_211__PI_TMRZ_F0_WIDTH                               5U
#define LPDDR4__PI_TMRZ_F0__REG DENALI_PI_211
#define LPDDR4__PI_TMRZ_F0__FLD LPDDR4__DENALI_PI_211__PI_TMRZ_F0

#define LPDDR4__DENALI_PI_212_READ_MASK                              0x001F3FFFU
#define LPDDR4__DENALI_PI_212_WRITE_MASK                             0x001F3FFFU
#define LPDDR4__DENALI_PI_212__PI_TCAENT_F0_MASK                     0x00003FFFU
#define LPDDR4__DENALI_PI_212__PI_TCAENT_F0_SHIFT                             0U
#define LPDDR4__DENALI_PI_212__PI_TCAENT_F0_WIDTH                            14U
#define LPDDR4__PI_TCAENT_F0__REG DENALI_PI_212
#define LPDDR4__PI_TCAENT_F0__FLD LPDDR4__DENALI_PI_212__PI_TCAENT_F0

#define LPDDR4__DENALI_PI_212__PI_TMRZ_F1_MASK                       0x001F0000U
#define LPDDR4__DENALI_PI_212__PI_TMRZ_F1_SHIFT                              16U
#define LPDDR4__DENALI_PI_212__PI_TMRZ_F1_WIDTH                               5U
#define LPDDR4__PI_TMRZ_F1__REG DENALI_PI_212
#define LPDDR4__PI_TMRZ_F1__FLD LPDDR4__DENALI_PI_212__PI_TMRZ_F1

#define LPDDR4__DENALI_PI_213_READ_MASK                              0x001F3FFFU
#define LPDDR4__DENALI_PI_213_WRITE_MASK                             0x001F3FFFU
#define LPDDR4__DENALI_PI_213__PI_TCAENT_F1_MASK                     0x00003FFFU
#define LPDDR4__DENALI_PI_213__PI_TCAENT_F1_SHIFT                             0U
#define LPDDR4__DENALI_PI_213__PI_TCAENT_F1_WIDTH                            14U
#define LPDDR4__PI_TCAENT_F1__REG DENALI_PI_213
#define LPDDR4__PI_TCAENT_F1__FLD LPDDR4__DENALI_PI_213__PI_TCAENT_F1

#define LPDDR4__DENALI_PI_213__PI_TMRZ_F2_MASK                       0x001F0000U
#define LPDDR4__DENALI_PI_213__PI_TMRZ_F2_SHIFT                              16U
#define LPDDR4__DENALI_PI_213__PI_TMRZ_F2_WIDTH                               5U
#define LPDDR4__PI_TMRZ_F2__REG DENALI_PI_213
#define LPDDR4__PI_TMRZ_F2__FLD LPDDR4__DENALI_PI_213__PI_TMRZ_F2

#define LPDDR4__DENALI_PI_214_READ_MASK                              0x1F1F3FFFU
#define LPDDR4__DENALI_PI_214_WRITE_MASK                             0x1F1F3FFFU
#define LPDDR4__DENALI_PI_214__PI_TCAENT_F2_MASK                     0x00003FFFU
#define LPDDR4__DENALI_PI_214__PI_TCAENT_F2_SHIFT                             0U
#define LPDDR4__DENALI_PI_214__PI_TCAENT_F2_WIDTH                            14U
#define LPDDR4__PI_TCAENT_F2__REG DENALI_PI_214
#define LPDDR4__PI_TCAENT_F2__FLD LPDDR4__DENALI_PI_214__PI_TCAENT_F2

#define LPDDR4__DENALI_PI_214__PI_TDFI_CACSCA_F0_MASK                0x001F0000U
#define LPDDR4__DENALI_PI_214__PI_TDFI_CACSCA_F0_SHIFT                       16U
#define LPDDR4__DENALI_PI_214__PI_TDFI_CACSCA_F0_WIDTH                        5U
#define LPDDR4__PI_TDFI_CACSCA_F0__REG DENALI_PI_214
#define LPDDR4__PI_TDFI_CACSCA_F0__FLD LPDDR4__DENALI_PI_214__PI_TDFI_CACSCA_F0

#define LPDDR4__DENALI_PI_214__PI_TDFI_CASEL_F0_MASK                 0x1F000000U
#define LPDDR4__DENALI_PI_214__PI_TDFI_CASEL_F0_SHIFT                        24U
#define LPDDR4__DENALI_PI_214__PI_TDFI_CASEL_F0_WIDTH                         5U
#define LPDDR4__PI_TDFI_CASEL_F0__REG DENALI_PI_214
#define LPDDR4__PI_TDFI_CASEL_F0__FLD LPDDR4__DENALI_PI_214__PI_TDFI_CASEL_F0

#define LPDDR4__DENALI_PI_215_READ_MASK                              0x03FF03FFU
#define LPDDR4__DENALI_PI_215_WRITE_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PI_215__PI_TVREF_SHORT_F0_MASK                0x000003FFU
#define LPDDR4__DENALI_PI_215__PI_TVREF_SHORT_F0_SHIFT                        0U
#define LPDDR4__DENALI_PI_215__PI_TVREF_SHORT_F0_WIDTH                       10U
#define LPDDR4__PI_TVREF_SHORT_F0__REG DENALI_PI_215
#define LPDDR4__PI_TVREF_SHORT_F0__FLD LPDDR4__DENALI_PI_215__PI_TVREF_SHORT_F0

#define LPDDR4__DENALI_PI_215__PI_TVREF_LONG_F0_MASK                 0x03FF0000U
#define LPDDR4__DENALI_PI_215__PI_TVREF_LONG_F0_SHIFT                        16U
#define LPDDR4__DENALI_PI_215__PI_TVREF_LONG_F0_WIDTH                        10U
#define LPDDR4__PI_TVREF_LONG_F0__REG DENALI_PI_215
#define LPDDR4__PI_TVREF_LONG_F0__FLD LPDDR4__DENALI_PI_215__PI_TVREF_LONG_F0

#define LPDDR4__DENALI_PI_216_READ_MASK                              0x03FF1F1FU
#define LPDDR4__DENALI_PI_216_WRITE_MASK                             0x03FF1F1FU
#define LPDDR4__DENALI_PI_216__PI_TDFI_CACSCA_F1_MASK                0x0000001FU
#define LPDDR4__DENALI_PI_216__PI_TDFI_CACSCA_F1_SHIFT                        0U
#define LPDDR4__DENALI_PI_216__PI_TDFI_CACSCA_F1_WIDTH                        5U
#define LPDDR4__PI_TDFI_CACSCA_F1__REG DENALI_PI_216
#define LPDDR4__PI_TDFI_CACSCA_F1__FLD LPDDR4__DENALI_PI_216__PI_TDFI_CACSCA_F1

#define LPDDR4__DENALI_PI_216__PI_TDFI_CASEL_F1_MASK                 0x00001F00U
#define LPDDR4__DENALI_PI_216__PI_TDFI_CASEL_F1_SHIFT                         8U
#define LPDDR4__DENALI_PI_216__PI_TDFI_CASEL_F1_WIDTH                         5U
#define LPDDR4__PI_TDFI_CASEL_F1__REG DENALI_PI_216
#define LPDDR4__PI_TDFI_CASEL_F1__FLD LPDDR4__DENALI_PI_216__PI_TDFI_CASEL_F1

#define LPDDR4__DENALI_PI_216__PI_TVREF_SHORT_F1_MASK                0x03FF0000U
#define LPDDR4__DENALI_PI_216__PI_TVREF_SHORT_F1_SHIFT                       16U
#define LPDDR4__DENALI_PI_216__PI_TVREF_SHORT_F1_WIDTH                       10U
#define LPDDR4__PI_TVREF_SHORT_F1__REG DENALI_PI_216
#define LPDDR4__PI_TVREF_SHORT_F1__FLD LPDDR4__DENALI_PI_216__PI_TVREF_SHORT_F1

#define LPDDR4__DENALI_PI_217_READ_MASK                              0x1F1F03FFU
#define LPDDR4__DENALI_PI_217_WRITE_MASK                             0x1F1F03FFU
#define LPDDR4__DENALI_PI_217__PI_TVREF_LONG_F1_MASK                 0x000003FFU
#define LPDDR4__DENALI_PI_217__PI_TVREF_LONG_F1_SHIFT                         0U
#define LPDDR4__DENALI_PI_217__PI_TVREF_LONG_F1_WIDTH                        10U
#define LPDDR4__PI_TVREF_LONG_F1__REG DENALI_PI_217
#define LPDDR4__PI_TVREF_LONG_F1__FLD LPDDR4__DENALI_PI_217__PI_TVREF_LONG_F1

#define LPDDR4__DENALI_PI_217__PI_TDFI_CACSCA_F2_MASK                0x001F0000U
#define LPDDR4__DENALI_PI_217__PI_TDFI_CACSCA_F2_SHIFT                       16U
#define LPDDR4__DENALI_PI_217__PI_TDFI_CACSCA_F2_WIDTH                        5U
#define LPDDR4__PI_TDFI_CACSCA_F2__REG DENALI_PI_217
#define LPDDR4__PI_TDFI_CACSCA_F2__FLD LPDDR4__DENALI_PI_217__PI_TDFI_CACSCA_F2

#define LPDDR4__DENALI_PI_217__PI_TDFI_CASEL_F2_MASK                 0x1F000000U
#define LPDDR4__DENALI_PI_217__PI_TDFI_CASEL_F2_SHIFT                        24U
#define LPDDR4__DENALI_PI_217__PI_TDFI_CASEL_F2_WIDTH                         5U
#define LPDDR4__PI_TDFI_CASEL_F2__REG DENALI_PI_217
#define LPDDR4__PI_TDFI_CASEL_F2__FLD LPDDR4__DENALI_PI_217__PI_TDFI_CASEL_F2

#define LPDDR4__DENALI_PI_218_READ_MASK                              0x03FF03FFU
#define LPDDR4__DENALI_PI_218_WRITE_MASK                             0x03FF03FFU
#define LPDDR4__DENALI_PI_218__PI_TVREF_SHORT_F2_MASK                0x000003FFU
#define LPDDR4__DENALI_PI_218__PI_TVREF_SHORT_F2_SHIFT                        0U
#define LPDDR4__DENALI_PI_218__PI_TVREF_SHORT_F2_WIDTH                       10U
#define LPDDR4__PI_TVREF_SHORT_F2__REG DENALI_PI_218
#define LPDDR4__PI_TVREF_SHORT_F2__FLD LPDDR4__DENALI_PI_218__PI_TVREF_SHORT_F2

#define LPDDR4__DENALI_PI_218__PI_TVREF_LONG_F2_MASK                 0x03FF0000U
#define LPDDR4__DENALI_PI_218__PI_TVREF_LONG_F2_SHIFT                        16U
#define LPDDR4__DENALI_PI_218__PI_TVREF_LONG_F2_WIDTH                        10U
#define LPDDR4__PI_TVREF_LONG_F2__REG DENALI_PI_218
#define LPDDR4__PI_TVREF_LONG_F2__FLD LPDDR4__DENALI_PI_218__PI_TVREF_LONG_F2

#define LPDDR4__DENALI_PI_219_READ_MASK                              0x7F7F7F7FU
#define LPDDR4__DENALI_PI_219_WRITE_MASK                             0x7F7F7F7FU
#define LPDDR4__DENALI_PI_219__PI_CALVL_VREF_INITIAL_START_POINT_F0_MASK 0x0000007FU
#define LPDDR4__DENALI_PI_219__PI_CALVL_VREF_INITIAL_START_POINT_F0_SHIFT     0U
#define LPDDR4__DENALI_PI_219__PI_CALVL_VREF_INITIAL_START_POINT_F0_WIDTH     7U
#define LPDDR4__PI_CALVL_VREF_INITIAL_START_POINT_F0__REG DENALI_PI_219
#define LPDDR4__PI_CALVL_VREF_INITIAL_START_POINT_F0__FLD LPDDR4__DENALI_PI_219__PI_CALVL_VREF_INITIAL_START_POINT_F0

#define LPDDR4__DENALI_PI_219__PI_CALVL_VREF_INITIAL_STOP_POINT_F0_MASK 0x00007F00U
#define LPDDR4__DENALI_PI_219__PI_CALVL_VREF_INITIAL_STOP_POINT_F0_SHIFT      8U
#define LPDDR4__DENALI_PI_219__PI_CALVL_VREF_INITIAL_STOP_POINT_F0_WIDTH      7U
#define LPDDR4__PI_CALVL_VREF_INITIAL_STOP_POINT_F0__REG DENALI_PI_219
#define LPDDR4__PI_CALVL_VREF_INITIAL_STOP_POINT_F0__FLD LPDDR4__DENALI_PI_219__PI_CALVL_VREF_INITIAL_STOP_POINT_F0

#define LPDDR4__DENALI_PI_219__PI_CALVL_VREF_INITIAL_START_POINT_F1_MASK 0x007F0000U
#define LPDDR4__DENALI_PI_219__PI_CALVL_VREF_INITIAL_START_POINT_F1_SHIFT    16U
#define LPDDR4__DENALI_PI_219__PI_CALVL_VREF_INITIAL_START_POINT_F1_WIDTH     7U
#define LPDDR4__PI_CALVL_VREF_INITIAL_START_POINT_F1__REG DENALI_PI_219
#define LPDDR4__PI_CALVL_VREF_INITIAL_START_POINT_F1__FLD LPDDR4__DENALI_PI_219__PI_CALVL_VREF_INITIAL_START_POINT_F1

#define LPDDR4__DENALI_PI_219__PI_CALVL_VREF_INITIAL_STOP_POINT_F1_MASK 0x7F000000U
#define LPDDR4__DENALI_PI_219__PI_CALVL_VREF_INITIAL_STOP_POINT_F1_SHIFT     24U
#define LPDDR4__DENALI_PI_219__PI_CALVL_VREF_INITIAL_STOP_POINT_F1_WIDTH      7U
#define LPDDR4__PI_CALVL_VREF_INITIAL_STOP_POINT_F1__REG DENALI_PI_219
#define LPDDR4__PI_CALVL_VREF_INITIAL_STOP_POINT_F1__FLD LPDDR4__DENALI_PI_219__PI_CALVL_VREF_INITIAL_STOP_POINT_F1

#define LPDDR4__DENALI_PI_220_READ_MASK                              0x0F0F7F7FU
#define LPDDR4__DENALI_PI_220_WRITE_MASK                             0x0F0F7F7FU
#define LPDDR4__DENALI_PI_220__PI_CALVL_VREF_INITIAL_START_POINT_F2_MASK 0x0000007FU
#define LPDDR4__DENALI_PI_220__PI_CALVL_VREF_INITIAL_START_POINT_F2_SHIFT     0U
#define LPDDR4__DENALI_PI_220__PI_CALVL_VREF_INITIAL_START_POINT_F2_WIDTH     7U
#define LPDDR4__PI_CALVL_VREF_INITIAL_START_POINT_F2__REG DENALI_PI_220
#define LPDDR4__PI_CALVL_VREF_INITIAL_START_POINT_F2__FLD LPDDR4__DENALI_PI_220__PI_CALVL_VREF_INITIAL_START_POINT_F2

#define LPDDR4__DENALI_PI_220__PI_CALVL_VREF_INITIAL_STOP_POINT_F2_MASK 0x00007F00U
#define LPDDR4__DENALI_PI_220__PI_CALVL_VREF_INITIAL_STOP_POINT_F2_SHIFT      8U
#define LPDDR4__DENALI_PI_220__PI_CALVL_VREF_INITIAL_STOP_POINT_F2_WIDTH      7U
#define LPDDR4__PI_CALVL_VREF_INITIAL_STOP_POINT_F2__REG DENALI_PI_220
#define LPDDR4__PI_CALVL_VREF_INITIAL_STOP_POINT_F2__FLD LPDDR4__DENALI_PI_220__PI_CALVL_VREF_INITIAL_STOP_POINT_F2

#define LPDDR4__DENALI_PI_220__PI_CALVL_VREF_DELTA_F0_MASK           0x000F0000U
#define LPDDR4__DENALI_PI_220__PI_CALVL_VREF_DELTA_F0_SHIFT                  16U
#define LPDDR4__DENALI_PI_220__PI_CALVL_VREF_DELTA_F0_WIDTH                   4U
#define LPDDR4__PI_CALVL_VREF_DELTA_F0__REG DENALI_PI_220
#define LPDDR4__PI_CALVL_VREF_DELTA_F0__FLD LPDDR4__DENALI_PI_220__PI_CALVL_VREF_DELTA_F0

#define LPDDR4__DENALI_PI_220__PI_CALVL_VREF_DELTA_F1_MASK           0x0F000000U
#define LPDDR4__DENALI_PI_220__PI_CALVL_VREF_DELTA_F1_SHIFT                  24U
#define LPDDR4__DENALI_PI_220__PI_CALVL_VREF_DELTA_F1_WIDTH                   4U
#define LPDDR4__PI_CALVL_VREF_DELTA_F1__REG DENALI_PI_220
#define LPDDR4__PI_CALVL_VREF_DELTA_F1__FLD LPDDR4__DENALI_PI_220__PI_CALVL_VREF_DELTA_F1

#define LPDDR4__DENALI_PI_221_READ_MASK                              0xFF1F0F0FU
#define LPDDR4__DENALI_PI_221_WRITE_MASK                             0xFF1F0F0FU
#define LPDDR4__DENALI_PI_221__PI_CALVL_VREF_DELTA_F2_MASK           0x0000000FU
#define LPDDR4__DENALI_PI_221__PI_CALVL_VREF_DELTA_F2_SHIFT                   0U
#define LPDDR4__DENALI_PI_221__PI_CALVL_VREF_DELTA_F2_WIDTH                   4U
#define LPDDR4__PI_CALVL_VREF_DELTA_F2__REG DENALI_PI_221
#define LPDDR4__PI_CALVL_VREF_DELTA_F2__FLD LPDDR4__DENALI_PI_221__PI_CALVL_VREF_DELTA_F2

#define LPDDR4__DENALI_PI_221__PI_TDFI_CALVL_STROBE_F0_MASK          0x00000F00U
#define LPDDR4__DENALI_PI_221__PI_TDFI_CALVL_STROBE_F0_SHIFT                  8U
#define LPDDR4__DENALI_PI_221__PI_TDFI_CALVL_STROBE_F0_WIDTH                  4U
#define LPDDR4__PI_TDFI_CALVL_STROBE_F0__REG DENALI_PI_221
#define LPDDR4__PI_TDFI_CALVL_STROBE_F0__FLD LPDDR4__DENALI_PI_221__PI_TDFI_CALVL_STROBE_F0

#define LPDDR4__DENALI_PI_221__PI_TXP_F0_MASK                        0x001F0000U
#define LPDDR4__DENALI_PI_221__PI_TXP_F0_SHIFT                               16U
#define LPDDR4__DENALI_PI_221__PI_TXP_F0_WIDTH                                5U
#define LPDDR4__PI_TXP_F0__REG DENALI_PI_221
#define LPDDR4__PI_TXP_F0__FLD LPDDR4__DENALI_PI_221__PI_TXP_F0

#define LPDDR4__DENALI_PI_221__PI_TMRWCKEL_F0_MASK                   0xFF000000U
#define LPDDR4__DENALI_PI_221__PI_TMRWCKEL_F0_SHIFT                          24U
#define LPDDR4__DENALI_PI_221__PI_TMRWCKEL_F0_WIDTH                           8U
#define LPDDR4__PI_TMRWCKEL_F0__REG DENALI_PI_221
#define LPDDR4__PI_TMRWCKEL_F0__FLD LPDDR4__DENALI_PI_221__PI_TMRWCKEL_F0

#define LPDDR4__DENALI_PI_222_READ_MASK                              0xFF1F0F1FU
#define LPDDR4__DENALI_PI_222_WRITE_MASK                             0xFF1F0F1FU
#define LPDDR4__DENALI_PI_222__PI_TCKELCK_F0_MASK                    0x0000001FU
#define LPDDR4__DENALI_PI_222__PI_TCKELCK_F0_SHIFT                            0U
#define LPDDR4__DENALI_PI_222__PI_TCKELCK_F0_WIDTH                            5U
#define LPDDR4__PI_TCKELCK_F0__REG DENALI_PI_222
#define LPDDR4__PI_TCKELCK_F0__FLD LPDDR4__DENALI_PI_222__PI_TCKELCK_F0

#define LPDDR4__DENALI_PI_222__PI_TDFI_CALVL_STROBE_F1_MASK          0x00000F00U
#define LPDDR4__DENALI_PI_222__PI_TDFI_CALVL_STROBE_F1_SHIFT                  8U
#define LPDDR4__DENALI_PI_222__PI_TDFI_CALVL_STROBE_F1_WIDTH                  4U
#define LPDDR4__PI_TDFI_CALVL_STROBE_F1__REG DENALI_PI_222
#define LPDDR4__PI_TDFI_CALVL_STROBE_F1__FLD LPDDR4__DENALI_PI_222__PI_TDFI_CALVL_STROBE_F1

#define LPDDR4__DENALI_PI_222__PI_TXP_F1_MASK                        0x001F0000U
#define LPDDR4__DENALI_PI_222__PI_TXP_F1_SHIFT                               16U
#define LPDDR4__DENALI_PI_222__PI_TXP_F1_WIDTH                                5U
#define LPDDR4__PI_TXP_F1__REG DENALI_PI_222
#define LPDDR4__PI_TXP_F1__FLD LPDDR4__DENALI_PI_222__PI_TXP_F1

#define LPDDR4__DENALI_PI_222__PI_TMRWCKEL_F1_MASK                   0xFF000000U
#define LPDDR4__DENALI_PI_222__PI_TMRWCKEL_F1_SHIFT                          24U
#define LPDDR4__DENALI_PI_222__PI_TMRWCKEL_F1_WIDTH                           8U
#define LPDDR4__PI_TMRWCKEL_F1__REG DENALI_PI_222
#define LPDDR4__PI_TMRWCKEL_F1__FLD LPDDR4__DENALI_PI_222__PI_TMRWCKEL_F1

#define LPDDR4__DENALI_PI_223_READ_MASK                              0xFF1F0F1FU
#define LPDDR4__DENALI_PI_223_WRITE_MASK                             0xFF1F0F1FU
#define LPDDR4__DENALI_PI_223__PI_TCKELCK_F1_MASK                    0x0000001FU
#define LPDDR4__DENALI_PI_223__PI_TCKELCK_F1_SHIFT                            0U
#define LPDDR4__DENALI_PI_223__PI_TCKELCK_F1_WIDTH                            5U
#define LPDDR4__PI_TCKELCK_F1__REG DENALI_PI_223
#define LPDDR4__PI_TCKELCK_F1__FLD LPDDR4__DENALI_PI_223__PI_TCKELCK_F1

#define LPDDR4__DENALI_PI_223__PI_TDFI_CALVL_STROBE_F2_MASK          0x00000F00U
#define LPDDR4__DENALI_PI_223__PI_TDFI_CALVL_STROBE_F2_SHIFT                  8U
#define LPDDR4__DENALI_PI_223__PI_TDFI_CALVL_STROBE_F2_WIDTH                  4U
#define LPDDR4__PI_TDFI_CALVL_STROBE_F2__REG DENALI_PI_223
#define LPDDR4__PI_TDFI_CALVL_STROBE_F2__FLD LPDDR4__DENALI_PI_223__PI_TDFI_CALVL_STROBE_F2

#define LPDDR4__DENALI_PI_223__PI_TXP_F2_MASK                        0x001F0000U
#define LPDDR4__DENALI_PI_223__PI_TXP_F2_SHIFT                               16U
#define LPDDR4__DENALI_PI_223__PI_TXP_F2_WIDTH                                5U
#define LPDDR4__PI_TXP_F2__REG DENALI_PI_223
#define LPDDR4__PI_TXP_F2__FLD LPDDR4__DENALI_PI_223__PI_TXP_F2

#define LPDDR4__DENALI_PI_223__PI_TMRWCKEL_F2_MASK                   0xFF000000U
#define LPDDR4__DENALI_PI_223__PI_TMRWCKEL_F2_SHIFT                          24U
#define LPDDR4__DENALI_PI_223__PI_TMRWCKEL_F2_WIDTH                           8U
#define LPDDR4__PI_TMRWCKEL_F2__REG DENALI_PI_223
#define LPDDR4__PI_TMRWCKEL_F2__FLD LPDDR4__DENALI_PI_223__PI_TMRWCKEL_F2

#define LPDDR4__DENALI_PI_224_READ_MASK                              0xFFFFFF1FU
#define LPDDR4__DENALI_PI_224_WRITE_MASK                             0xFFFFFF1FU
#define LPDDR4__DENALI_PI_224__PI_TCKELCK_F2_MASK                    0x0000001FU
#define LPDDR4__DENALI_PI_224__PI_TCKELCK_F2_SHIFT                            0U
#define LPDDR4__DENALI_PI_224__PI_TCKELCK_F2_WIDTH                            5U
#define LPDDR4__PI_TCKELCK_F2__REG DENALI_PI_224
#define LPDDR4__PI_TCKELCK_F2__FLD LPDDR4__DENALI_PI_224__PI_TCKELCK_F2

#define LPDDR4__DENALI_PI_224__PI_TDFI_INIT_START_F0_MASK            0xFFFFFF00U
#define LPDDR4__DENALI_PI_224__PI_TDFI_INIT_START_F0_SHIFT                    8U
#define LPDDR4__DENALI_PI_224__PI_TDFI_INIT_START_F0_WIDTH                   24U
#define LPDDR4__PI_TDFI_INIT_START_F0__REG DENALI_PI_224
#define LPDDR4__PI_TDFI_INIT_START_F0__FLD LPDDR4__DENALI_PI_224__PI_TDFI_INIT_START_F0

#define LPDDR4__DENALI_PI_225_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_225_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_225__PI_TDFI_INIT_COMPLETE_F0_MASK         0x00FFFFFFU
#define LPDDR4__DENALI_PI_225__PI_TDFI_INIT_COMPLETE_F0_SHIFT                 0U
#define LPDDR4__DENALI_PI_225__PI_TDFI_INIT_COMPLETE_F0_WIDTH                24U
#define LPDDR4__PI_TDFI_INIT_COMPLETE_F0__REG DENALI_PI_225
#define LPDDR4__PI_TDFI_INIT_COMPLETE_F0__FLD LPDDR4__DENALI_PI_225__PI_TDFI_INIT_COMPLETE_F0

#define LPDDR4__DENALI_PI_226_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_226_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_226__PI_TDFI_INIT_START_F1_MASK            0x00FFFFFFU
#define LPDDR4__DENALI_PI_226__PI_TDFI_INIT_START_F1_SHIFT                    0U
#define LPDDR4__DENALI_PI_226__PI_TDFI_INIT_START_F1_WIDTH                   24U
#define LPDDR4__PI_TDFI_INIT_START_F1__REG DENALI_PI_226
#define LPDDR4__PI_TDFI_INIT_START_F1__FLD LPDDR4__DENALI_PI_226__PI_TDFI_INIT_START_F1

#define LPDDR4__DENALI_PI_227_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_227_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_227__PI_TDFI_INIT_COMPLETE_F1_MASK         0x00FFFFFFU
#define LPDDR4__DENALI_PI_227__PI_TDFI_INIT_COMPLETE_F1_SHIFT                 0U
#define LPDDR4__DENALI_PI_227__PI_TDFI_INIT_COMPLETE_F1_WIDTH                24U
#define LPDDR4__PI_TDFI_INIT_COMPLETE_F1__REG DENALI_PI_227
#define LPDDR4__PI_TDFI_INIT_COMPLETE_F1__FLD LPDDR4__DENALI_PI_227__PI_TDFI_INIT_COMPLETE_F1

#define LPDDR4__DENALI_PI_228_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_228_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_228__PI_TDFI_INIT_START_F2_MASK            0x00FFFFFFU
#define LPDDR4__DENALI_PI_228__PI_TDFI_INIT_START_F2_SHIFT                    0U
#define LPDDR4__DENALI_PI_228__PI_TDFI_INIT_START_F2_WIDTH                   24U
#define LPDDR4__PI_TDFI_INIT_START_F2__REG DENALI_PI_228
#define LPDDR4__PI_TDFI_INIT_START_F2__FLD LPDDR4__DENALI_PI_228__PI_TDFI_INIT_START_F2

#define LPDDR4__DENALI_PI_229_READ_MASK                              0x3FFFFFFFU
#define LPDDR4__DENALI_PI_229_WRITE_MASK                             0x3FFFFFFFU
#define LPDDR4__DENALI_PI_229__PI_TDFI_INIT_COMPLETE_F2_MASK         0x00FFFFFFU
#define LPDDR4__DENALI_PI_229__PI_TDFI_INIT_COMPLETE_F2_SHIFT                 0U
#define LPDDR4__DENALI_PI_229__PI_TDFI_INIT_COMPLETE_F2_WIDTH                24U
#define LPDDR4__PI_TDFI_INIT_COMPLETE_F2__REG DENALI_PI_229
#define LPDDR4__PI_TDFI_INIT_COMPLETE_F2__FLD LPDDR4__DENALI_PI_229__PI_TDFI_INIT_COMPLETE_F2

#define LPDDR4__DENALI_PI_229__PI_TCKEHDQS_F0_MASK                   0x3F000000U
#define LPDDR4__DENALI_PI_229__PI_TCKEHDQS_F0_SHIFT                          24U
#define LPDDR4__DENALI_PI_229__PI_TCKEHDQS_F0_WIDTH                           6U
#define LPDDR4__PI_TCKEHDQS_F0__REG DENALI_PI_229
#define LPDDR4__PI_TCKEHDQS_F0__FLD LPDDR4__DENALI_PI_229__PI_TCKEHDQS_F0

#define LPDDR4__DENALI_PI_230_READ_MASK                              0x003F03FFU
#define LPDDR4__DENALI_PI_230_WRITE_MASK                             0x003F03FFU
#define LPDDR4__DENALI_PI_230__PI_TFC_F0_MASK                        0x000003FFU
#define LPDDR4__DENALI_PI_230__PI_TFC_F0_SHIFT                                0U
#define LPDDR4__DENALI_PI_230__PI_TFC_F0_WIDTH                               10U
#define LPDDR4__PI_TFC_F0__REG DENALI_PI_230
#define LPDDR4__PI_TFC_F0__FLD LPDDR4__DENALI_PI_230__PI_TFC_F0

#define LPDDR4__DENALI_PI_230__PI_TCKEHDQS_F1_MASK                   0x003F0000U
#define LPDDR4__DENALI_PI_230__PI_TCKEHDQS_F1_SHIFT                          16U
#define LPDDR4__DENALI_PI_230__PI_TCKEHDQS_F1_WIDTH                           6U
#define LPDDR4__PI_TCKEHDQS_F1__REG DENALI_PI_230
#define LPDDR4__PI_TCKEHDQS_F1__FLD LPDDR4__DENALI_PI_230__PI_TCKEHDQS_F1

#define LPDDR4__DENALI_PI_231_READ_MASK                              0x003F03FFU
#define LPDDR4__DENALI_PI_231_WRITE_MASK                             0x003F03FFU
#define LPDDR4__DENALI_PI_231__PI_TFC_F1_MASK                        0x000003FFU
#define LPDDR4__DENALI_PI_231__PI_TFC_F1_SHIFT                                0U
#define LPDDR4__DENALI_PI_231__PI_TFC_F1_WIDTH                               10U
#define LPDDR4__PI_TFC_F1__REG DENALI_PI_231
#define LPDDR4__PI_TFC_F1__FLD LPDDR4__DENALI_PI_231__PI_TFC_F1

#define LPDDR4__DENALI_PI_231__PI_TCKEHDQS_F2_MASK                   0x003F0000U
#define LPDDR4__DENALI_PI_231__PI_TCKEHDQS_F2_SHIFT                          16U
#define LPDDR4__DENALI_PI_231__PI_TCKEHDQS_F2_WIDTH                           6U
#define LPDDR4__PI_TCKEHDQS_F2__REG DENALI_PI_231
#define LPDDR4__PI_TCKEHDQS_F2__FLD LPDDR4__DENALI_PI_231__PI_TCKEHDQS_F2

#define LPDDR4__DENALI_PI_232_READ_MASK                              0x030303FFU
#define LPDDR4__DENALI_PI_232_WRITE_MASK                             0x030303FFU
#define LPDDR4__DENALI_PI_232__PI_TFC_F2_MASK                        0x000003FFU
#define LPDDR4__DENALI_PI_232__PI_TFC_F2_SHIFT                                0U
#define LPDDR4__DENALI_PI_232__PI_TFC_F2_WIDTH                               10U
#define LPDDR4__PI_TFC_F2__REG DENALI_PI_232
#define LPDDR4__PI_TFC_F2__FLD LPDDR4__DENALI_PI_232__PI_TFC_F2

#define LPDDR4__DENALI_PI_232__PI_VREF_EN_F0_MASK                    0x00030000U
#define LPDDR4__DENALI_PI_232__PI_VREF_EN_F0_SHIFT                           16U
#define LPDDR4__DENALI_PI_232__PI_VREF_EN_F0_WIDTH                            2U
#define LPDDR4__PI_VREF_EN_F0__REG DENALI_PI_232
#define LPDDR4__PI_VREF_EN_F0__FLD LPDDR4__DENALI_PI_232__PI_VREF_EN_F0

#define LPDDR4__DENALI_PI_232__PI_VREF_EN_F1_MASK                    0x03000000U
#define LPDDR4__DENALI_PI_232__PI_VREF_EN_F1_SHIFT                           24U
#define LPDDR4__DENALI_PI_232__PI_VREF_EN_F1_WIDTH                            2U
#define LPDDR4__PI_VREF_EN_F1__REG DENALI_PI_232
#define LPDDR4__PI_VREF_EN_F1__FLD LPDDR4__DENALI_PI_232__PI_VREF_EN_F1

#define LPDDR4__DENALI_PI_233_READ_MASK                              0x0003FF03U
#define LPDDR4__DENALI_PI_233_WRITE_MASK                             0x0003FF03U
#define LPDDR4__DENALI_PI_233__PI_VREF_EN_F2_MASK                    0x00000003U
#define LPDDR4__DENALI_PI_233__PI_VREF_EN_F2_SHIFT                            0U
#define LPDDR4__DENALI_PI_233__PI_VREF_EN_F2_WIDTH                            2U
#define LPDDR4__PI_VREF_EN_F2__REG DENALI_PI_233
#define LPDDR4__PI_VREF_EN_F2__FLD LPDDR4__DENALI_PI_233__PI_VREF_EN_F2

#define LPDDR4__DENALI_PI_233__PI_TDFI_WDQLVL_WR_F0_MASK             0x0003FF00U
#define LPDDR4__DENALI_PI_233__PI_TDFI_WDQLVL_WR_F0_SHIFT                     8U
#define LPDDR4__DENALI_PI_233__PI_TDFI_WDQLVL_WR_F0_WIDTH                    10U
#define LPDDR4__PI_TDFI_WDQLVL_WR_F0__REG DENALI_PI_233
#define LPDDR4__PI_TDFI_WDQLVL_WR_F0__FLD LPDDR4__DENALI_PI_233__PI_TDFI_WDQLVL_WR_F0

#define LPDDR4__DENALI_PI_234_READ_MASK                              0x7F7F03FFU
#define LPDDR4__DENALI_PI_234_WRITE_MASK                             0x7F7F03FFU
#define LPDDR4__DENALI_PI_234__PI_TDFI_WDQLVL_RW_F0_MASK             0x000003FFU
#define LPDDR4__DENALI_PI_234__PI_TDFI_WDQLVL_RW_F0_SHIFT                     0U
#define LPDDR4__DENALI_PI_234__PI_TDFI_WDQLVL_RW_F0_WIDTH                    10U
#define LPDDR4__PI_TDFI_WDQLVL_RW_F0__REG DENALI_PI_234
#define LPDDR4__PI_TDFI_WDQLVL_RW_F0__FLD LPDDR4__DENALI_PI_234__PI_TDFI_WDQLVL_RW_F0

#define LPDDR4__DENALI_PI_234__PI_WDQLVL_VREF_INITIAL_START_POINT_F0_MASK 0x007F0000U
#define LPDDR4__DENALI_PI_234__PI_WDQLVL_VREF_INITIAL_START_POINT_F0_SHIFT   16U
#define LPDDR4__DENALI_PI_234__PI_WDQLVL_VREF_INITIAL_START_POINT_F0_WIDTH    7U
#define LPDDR4__PI_WDQLVL_VREF_INITIAL_START_POINT_F0__REG DENALI_PI_234
#define LPDDR4__PI_WDQLVL_VREF_INITIAL_START_POINT_F0__FLD LPDDR4__DENALI_PI_234__PI_WDQLVL_VREF_INITIAL_START_POINT_F0

#define LPDDR4__DENALI_PI_234__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F0_MASK 0x7F000000U
#define LPDDR4__DENALI_PI_234__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F0_SHIFT    24U
#define LPDDR4__DENALI_PI_234__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F0_WIDTH     7U
#define LPDDR4__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F0__REG DENALI_PI_234
#define LPDDR4__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F0__FLD LPDDR4__DENALI_PI_234__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F0

#define LPDDR4__DENALI_PI_235_READ_MASK                              0x1F03030FU
#define LPDDR4__DENALI_PI_235_WRITE_MASK                             0x1F03030FU
#define LPDDR4__DENALI_PI_235__PI_WDQLVL_VREF_DELTA_F0_MASK          0x0000000FU
#define LPDDR4__DENALI_PI_235__PI_WDQLVL_VREF_DELTA_F0_SHIFT                  0U
#define LPDDR4__DENALI_PI_235__PI_WDQLVL_VREF_DELTA_F0_WIDTH                  4U
#define LPDDR4__PI_WDQLVL_VREF_DELTA_F0__REG DENALI_PI_235
#define LPDDR4__PI_WDQLVL_VREF_DELTA_F0__FLD LPDDR4__DENALI_PI_235__PI_WDQLVL_VREF_DELTA_F0

#define LPDDR4__DENALI_PI_235__PI_WDQLVL_EN_F0_MASK                  0x00000300U
#define LPDDR4__DENALI_PI_235__PI_WDQLVL_EN_F0_SHIFT                          8U
#define LPDDR4__DENALI_PI_235__PI_WDQLVL_EN_F0_WIDTH                          2U
#define LPDDR4__PI_WDQLVL_EN_F0__REG DENALI_PI_235
#define LPDDR4__PI_WDQLVL_EN_F0__FLD LPDDR4__DENALI_PI_235__PI_WDQLVL_EN_F0

#define LPDDR4__DENALI_PI_235__PI_NTP_TRAIN_EN_F0_MASK               0x00030000U
#define LPDDR4__DENALI_PI_235__PI_NTP_TRAIN_EN_F0_SHIFT                      16U
#define LPDDR4__DENALI_PI_235__PI_NTP_TRAIN_EN_F0_WIDTH                       2U
#define LPDDR4__PI_NTP_TRAIN_EN_F0__REG DENALI_PI_235
#define LPDDR4__PI_NTP_TRAIN_EN_F0__FLD LPDDR4__DENALI_PI_235__PI_NTP_TRAIN_EN_F0

#define LPDDR4__DENALI_PI_235__PI_WDQLVL_CL_F0_MASK                  0x1F000000U
#define LPDDR4__DENALI_PI_235__PI_WDQLVL_CL_F0_SHIFT                         24U
#define LPDDR4__DENALI_PI_235__PI_WDQLVL_CL_F0_WIDTH                          5U
#define LPDDR4__PI_WDQLVL_CL_F0__REG DENALI_PI_235
#define LPDDR4__PI_WDQLVL_CL_F0__FLD LPDDR4__DENALI_PI_235__PI_WDQLVL_CL_F0

#define LPDDR4__DENALI_PI_236_READ_MASK                              0x03FFFFFFU
#define LPDDR4__DENALI_PI_236_WRITE_MASK                             0x03FFFFFFU
#define LPDDR4__DENALI_PI_236__PI_WDQLVL_RDLAT_ADJ_F0_MASK           0x000000FFU
#define LPDDR4__DENALI_PI_236__PI_WDQLVL_RDLAT_ADJ_F0_SHIFT                   0U
#define LPDDR4__DENALI_PI_236__PI_WDQLVL_RDLAT_ADJ_F0_WIDTH                   8U
#define LPDDR4__PI_WDQLVL_RDLAT_ADJ_F0__REG DENALI_PI_236
#define LPDDR4__PI_WDQLVL_RDLAT_ADJ_F0__FLD LPDDR4__DENALI_PI_236__PI_WDQLVL_RDLAT_ADJ_F0

#define LPDDR4__DENALI_PI_236__PI_WDQLVL_WRLAT_ADJ_F0_MASK           0x0000FF00U
#define LPDDR4__DENALI_PI_236__PI_WDQLVL_WRLAT_ADJ_F0_SHIFT                   8U
#define LPDDR4__DENALI_PI_236__PI_WDQLVL_WRLAT_ADJ_F0_WIDTH                   8U
#define LPDDR4__PI_WDQLVL_WRLAT_ADJ_F0__REG DENALI_PI_236
#define LPDDR4__PI_WDQLVL_WRLAT_ADJ_F0__FLD LPDDR4__DENALI_PI_236__PI_WDQLVL_WRLAT_ADJ_F0

#define LPDDR4__DENALI_PI_236__PI_TDFI_WDQLVL_WR_F1_MASK             0x03FF0000U
#define LPDDR4__DENALI_PI_236__PI_TDFI_WDQLVL_WR_F1_SHIFT                    16U
#define LPDDR4__DENALI_PI_236__PI_TDFI_WDQLVL_WR_F1_WIDTH                    10U
#define LPDDR4__PI_TDFI_WDQLVL_WR_F1__REG DENALI_PI_236
#define LPDDR4__PI_TDFI_WDQLVL_WR_F1__FLD LPDDR4__DENALI_PI_236__PI_TDFI_WDQLVL_WR_F1

#define LPDDR4__DENALI_PI_237_READ_MASK                              0x7F7F03FFU
#define LPDDR4__DENALI_PI_237_WRITE_MASK                             0x7F7F03FFU
#define LPDDR4__DENALI_PI_237__PI_TDFI_WDQLVL_RW_F1_MASK             0x000003FFU
#define LPDDR4__DENALI_PI_237__PI_TDFI_WDQLVL_RW_F1_SHIFT                     0U
#define LPDDR4__DENALI_PI_237__PI_TDFI_WDQLVL_RW_F1_WIDTH                    10U
#define LPDDR4__PI_TDFI_WDQLVL_RW_F1__REG DENALI_PI_237
#define LPDDR4__PI_TDFI_WDQLVL_RW_F1__FLD LPDDR4__DENALI_PI_237__PI_TDFI_WDQLVL_RW_F1

#define LPDDR4__DENALI_PI_237__PI_WDQLVL_VREF_INITIAL_START_POINT_F1_MASK 0x007F0000U
#define LPDDR4__DENALI_PI_237__PI_WDQLVL_VREF_INITIAL_START_POINT_F1_SHIFT   16U
#define LPDDR4__DENALI_PI_237__PI_WDQLVL_VREF_INITIAL_START_POINT_F1_WIDTH    7U
#define LPDDR4__PI_WDQLVL_VREF_INITIAL_START_POINT_F1__REG DENALI_PI_237
#define LPDDR4__PI_WDQLVL_VREF_INITIAL_START_POINT_F1__FLD LPDDR4__DENALI_PI_237__PI_WDQLVL_VREF_INITIAL_START_POINT_F1

#define LPDDR4__DENALI_PI_237__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F1_MASK 0x7F000000U
#define LPDDR4__DENALI_PI_237__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F1_SHIFT    24U
#define LPDDR4__DENALI_PI_237__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F1_WIDTH     7U
#define LPDDR4__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F1__REG DENALI_PI_237
#define LPDDR4__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F1__FLD LPDDR4__DENALI_PI_237__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F1

#define LPDDR4__DENALI_PI_238_READ_MASK                              0x1F03030FU
#define LPDDR4__DENALI_PI_238_WRITE_MASK                             0x1F03030FU
#define LPDDR4__DENALI_PI_238__PI_WDQLVL_VREF_DELTA_F1_MASK          0x0000000FU
#define LPDDR4__DENALI_PI_238__PI_WDQLVL_VREF_DELTA_F1_SHIFT                  0U
#define LPDDR4__DENALI_PI_238__PI_WDQLVL_VREF_DELTA_F1_WIDTH                  4U
#define LPDDR4__PI_WDQLVL_VREF_DELTA_F1__REG DENALI_PI_238
#define LPDDR4__PI_WDQLVL_VREF_DELTA_F1__FLD LPDDR4__DENALI_PI_238__PI_WDQLVL_VREF_DELTA_F1

#define LPDDR4__DENALI_PI_238__PI_WDQLVL_EN_F1_MASK                  0x00000300U
#define LPDDR4__DENALI_PI_238__PI_WDQLVL_EN_F1_SHIFT                          8U
#define LPDDR4__DENALI_PI_238__PI_WDQLVL_EN_F1_WIDTH                          2U
#define LPDDR4__PI_WDQLVL_EN_F1__REG DENALI_PI_238
#define LPDDR4__PI_WDQLVL_EN_F1__FLD LPDDR4__DENALI_PI_238__PI_WDQLVL_EN_F1

#define LPDDR4__DENALI_PI_238__PI_NTP_TRAIN_EN_F1_MASK               0x00030000U
#define LPDDR4__DENALI_PI_238__PI_NTP_TRAIN_EN_F1_SHIFT                      16U
#define LPDDR4__DENALI_PI_238__PI_NTP_TRAIN_EN_F1_WIDTH                       2U
#define LPDDR4__PI_NTP_TRAIN_EN_F1__REG DENALI_PI_238
#define LPDDR4__PI_NTP_TRAIN_EN_F1__FLD LPDDR4__DENALI_PI_238__PI_NTP_TRAIN_EN_F1

#define LPDDR4__DENALI_PI_238__PI_WDQLVL_CL_F1_MASK                  0x1F000000U
#define LPDDR4__DENALI_PI_238__PI_WDQLVL_CL_F1_SHIFT                         24U
#define LPDDR4__DENALI_PI_238__PI_WDQLVL_CL_F1_WIDTH                          5U
#define LPDDR4__PI_WDQLVL_CL_F1__REG DENALI_PI_238
#define LPDDR4__PI_WDQLVL_CL_F1__FLD LPDDR4__DENALI_PI_238__PI_WDQLVL_CL_F1

#define LPDDR4__DENALI_PI_239_READ_MASK                              0x03FFFFFFU
#define LPDDR4__DENALI_PI_239_WRITE_MASK                             0x03FFFFFFU
#define LPDDR4__DENALI_PI_239__PI_WDQLVL_RDLAT_ADJ_F1_MASK           0x000000FFU
#define LPDDR4__DENALI_PI_239__PI_WDQLVL_RDLAT_ADJ_F1_SHIFT                   0U
#define LPDDR4__DENALI_PI_239__PI_WDQLVL_RDLAT_ADJ_F1_WIDTH                   8U
#define LPDDR4__PI_WDQLVL_RDLAT_ADJ_F1__REG DENALI_PI_239
#define LPDDR4__PI_WDQLVL_RDLAT_ADJ_F1__FLD LPDDR4__DENALI_PI_239__PI_WDQLVL_RDLAT_ADJ_F1

#define LPDDR4__DENALI_PI_239__PI_WDQLVL_WRLAT_ADJ_F1_MASK           0x0000FF00U
#define LPDDR4__DENALI_PI_239__PI_WDQLVL_WRLAT_ADJ_F1_SHIFT                   8U
#define LPDDR4__DENALI_PI_239__PI_WDQLVL_WRLAT_ADJ_F1_WIDTH                   8U
#define LPDDR4__PI_WDQLVL_WRLAT_ADJ_F1__REG DENALI_PI_239
#define LPDDR4__PI_WDQLVL_WRLAT_ADJ_F1__FLD LPDDR4__DENALI_PI_239__PI_WDQLVL_WRLAT_ADJ_F1

#define LPDDR4__DENALI_PI_239__PI_TDFI_WDQLVL_WR_F2_MASK             0x03FF0000U
#define LPDDR4__DENALI_PI_239__PI_TDFI_WDQLVL_WR_F2_SHIFT                    16U
#define LPDDR4__DENALI_PI_239__PI_TDFI_WDQLVL_WR_F2_WIDTH                    10U
#define LPDDR4__PI_TDFI_WDQLVL_WR_F2__REG DENALI_PI_239
#define LPDDR4__PI_TDFI_WDQLVL_WR_F2__FLD LPDDR4__DENALI_PI_239__PI_TDFI_WDQLVL_WR_F2

#define LPDDR4__DENALI_PI_240_READ_MASK                              0x7F7F03FFU
#define LPDDR4__DENALI_PI_240_WRITE_MASK                             0x7F7F03FFU
#define LPDDR4__DENALI_PI_240__PI_TDFI_WDQLVL_RW_F2_MASK             0x000003FFU
#define LPDDR4__DENALI_PI_240__PI_TDFI_WDQLVL_RW_F2_SHIFT                     0U
#define LPDDR4__DENALI_PI_240__PI_TDFI_WDQLVL_RW_F2_WIDTH                    10U
#define LPDDR4__PI_TDFI_WDQLVL_RW_F2__REG DENALI_PI_240
#define LPDDR4__PI_TDFI_WDQLVL_RW_F2__FLD LPDDR4__DENALI_PI_240__PI_TDFI_WDQLVL_RW_F2

#define LPDDR4__DENALI_PI_240__PI_WDQLVL_VREF_INITIAL_START_POINT_F2_MASK 0x007F0000U
#define LPDDR4__DENALI_PI_240__PI_WDQLVL_VREF_INITIAL_START_POINT_F2_SHIFT   16U
#define LPDDR4__DENALI_PI_240__PI_WDQLVL_VREF_INITIAL_START_POINT_F2_WIDTH    7U
#define LPDDR4__PI_WDQLVL_VREF_INITIAL_START_POINT_F2__REG DENALI_PI_240
#define LPDDR4__PI_WDQLVL_VREF_INITIAL_START_POINT_F2__FLD LPDDR4__DENALI_PI_240__PI_WDQLVL_VREF_INITIAL_START_POINT_F2

#define LPDDR4__DENALI_PI_240__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F2_MASK 0x7F000000U
#define LPDDR4__DENALI_PI_240__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F2_SHIFT    24U
#define LPDDR4__DENALI_PI_240__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F2_WIDTH     7U
#define LPDDR4__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F2__REG DENALI_PI_240
#define LPDDR4__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F2__FLD LPDDR4__DENALI_PI_240__PI_WDQLVL_VREF_INITIAL_STOP_POINT_F2

#define LPDDR4__DENALI_PI_241_READ_MASK                              0x1F03030FU
#define LPDDR4__DENALI_PI_241_WRITE_MASK                             0x1F03030FU
#define LPDDR4__DENALI_PI_241__PI_WDQLVL_VREF_DELTA_F2_MASK          0x0000000FU
#define LPDDR4__DENALI_PI_241__PI_WDQLVL_VREF_DELTA_F2_SHIFT                  0U
#define LPDDR4__DENALI_PI_241__PI_WDQLVL_VREF_DELTA_F2_WIDTH                  4U
#define LPDDR4__PI_WDQLVL_VREF_DELTA_F2__REG DENALI_PI_241
#define LPDDR4__PI_WDQLVL_VREF_DELTA_F2__FLD LPDDR4__DENALI_PI_241__PI_WDQLVL_VREF_DELTA_F2

#define LPDDR4__DENALI_PI_241__PI_WDQLVL_EN_F2_MASK                  0x00000300U
#define LPDDR4__DENALI_PI_241__PI_WDQLVL_EN_F2_SHIFT                          8U
#define LPDDR4__DENALI_PI_241__PI_WDQLVL_EN_F2_WIDTH                          2U
#define LPDDR4__PI_WDQLVL_EN_F2__REG DENALI_PI_241
#define LPDDR4__PI_WDQLVL_EN_F2__FLD LPDDR4__DENALI_PI_241__PI_WDQLVL_EN_F2

#define LPDDR4__DENALI_PI_241__PI_NTP_TRAIN_EN_F2_MASK               0x00030000U
#define LPDDR4__DENALI_PI_241__PI_NTP_TRAIN_EN_F2_SHIFT                      16U
#define LPDDR4__DENALI_PI_241__PI_NTP_TRAIN_EN_F2_WIDTH                       2U
#define LPDDR4__PI_NTP_TRAIN_EN_F2__REG DENALI_PI_241
#define LPDDR4__PI_NTP_TRAIN_EN_F2__FLD LPDDR4__DENALI_PI_241__PI_NTP_TRAIN_EN_F2

#define LPDDR4__DENALI_PI_241__PI_WDQLVL_CL_F2_MASK                  0x1F000000U
#define LPDDR4__DENALI_PI_241__PI_WDQLVL_CL_F2_SHIFT                         24U
#define LPDDR4__DENALI_PI_241__PI_WDQLVL_CL_F2_WIDTH                          5U
#define LPDDR4__PI_WDQLVL_CL_F2__REG DENALI_PI_241
#define LPDDR4__PI_WDQLVL_CL_F2__FLD LPDDR4__DENALI_PI_241__PI_WDQLVL_CL_F2

#define LPDDR4__DENALI_PI_242_READ_MASK                              0x0303FFFFU
#define LPDDR4__DENALI_PI_242_WRITE_MASK                             0x0303FFFFU
#define LPDDR4__DENALI_PI_242__PI_WDQLVL_RDLAT_ADJ_F2_MASK           0x000000FFU
#define LPDDR4__DENALI_PI_242__PI_WDQLVL_RDLAT_ADJ_F2_SHIFT                   0U
#define LPDDR4__DENALI_PI_242__PI_WDQLVL_RDLAT_ADJ_F2_WIDTH                   8U
#define LPDDR4__PI_WDQLVL_RDLAT_ADJ_F2__REG DENALI_PI_242
#define LPDDR4__PI_WDQLVL_RDLAT_ADJ_F2__FLD LPDDR4__DENALI_PI_242__PI_WDQLVL_RDLAT_ADJ_F2

#define LPDDR4__DENALI_PI_242__PI_WDQLVL_WRLAT_ADJ_F2_MASK           0x0000FF00U
#define LPDDR4__DENALI_PI_242__PI_WDQLVL_WRLAT_ADJ_F2_SHIFT                   8U
#define LPDDR4__DENALI_PI_242__PI_WDQLVL_WRLAT_ADJ_F2_WIDTH                   8U
#define LPDDR4__PI_WDQLVL_WRLAT_ADJ_F2__REG DENALI_PI_242
#define LPDDR4__PI_WDQLVL_WRLAT_ADJ_F2__FLD LPDDR4__DENALI_PI_242__PI_WDQLVL_WRLAT_ADJ_F2

#define LPDDR4__DENALI_PI_242__PI_RD_DBI_LEVEL_EN_F0_MASK            0x00030000U
#define LPDDR4__DENALI_PI_242__PI_RD_DBI_LEVEL_EN_F0_SHIFT                   16U
#define LPDDR4__DENALI_PI_242__PI_RD_DBI_LEVEL_EN_F0_WIDTH                    2U
#define LPDDR4__PI_RD_DBI_LEVEL_EN_F0__REG DENALI_PI_242
#define LPDDR4__PI_RD_DBI_LEVEL_EN_F0__FLD LPDDR4__DENALI_PI_242__PI_RD_DBI_LEVEL_EN_F0

#define LPDDR4__DENALI_PI_242__PI_RD_DBI_LEVEL_EN_F1_MASK            0x03000000U
#define LPDDR4__DENALI_PI_242__PI_RD_DBI_LEVEL_EN_F1_SHIFT                   24U
#define LPDDR4__DENALI_PI_242__PI_RD_DBI_LEVEL_EN_F1_WIDTH                    2U
#define LPDDR4__PI_RD_DBI_LEVEL_EN_F1__REG DENALI_PI_242
#define LPDDR4__PI_RD_DBI_LEVEL_EN_F1__FLD LPDDR4__DENALI_PI_242__PI_RD_DBI_LEVEL_EN_F1

#define LPDDR4__DENALI_PI_243_READ_MASK                              0xFFFFFF03U
#define LPDDR4__DENALI_PI_243_WRITE_MASK                             0xFFFFFF03U
#define LPDDR4__DENALI_PI_243__PI_RD_DBI_LEVEL_EN_F2_MASK            0x00000003U
#define LPDDR4__DENALI_PI_243__PI_RD_DBI_LEVEL_EN_F2_SHIFT                    0U
#define LPDDR4__DENALI_PI_243__PI_RD_DBI_LEVEL_EN_F2_WIDTH                    2U
#define LPDDR4__PI_RD_DBI_LEVEL_EN_F2__REG DENALI_PI_243
#define LPDDR4__PI_RD_DBI_LEVEL_EN_F2__FLD LPDDR4__DENALI_PI_243__PI_RD_DBI_LEVEL_EN_F2

#define LPDDR4__DENALI_PI_243__PI_TRTP_F0_MASK                       0x0000FF00U
#define LPDDR4__DENALI_PI_243__PI_TRTP_F0_SHIFT                               8U
#define LPDDR4__DENALI_PI_243__PI_TRTP_F0_WIDTH                               8U
#define LPDDR4__PI_TRTP_F0__REG DENALI_PI_243
#define LPDDR4__PI_TRTP_F0__FLD LPDDR4__DENALI_PI_243__PI_TRTP_F0

#define LPDDR4__DENALI_PI_243__PI_TRP_F0_MASK                        0x00FF0000U
#define LPDDR4__DENALI_PI_243__PI_TRP_F0_SHIFT                               16U
#define LPDDR4__DENALI_PI_243__PI_TRP_F0_WIDTH                                8U
#define LPDDR4__PI_TRP_F0__REG DENALI_PI_243
#define LPDDR4__PI_TRP_F0__FLD LPDDR4__DENALI_PI_243__PI_TRP_F0

#define LPDDR4__DENALI_PI_243__PI_TRCD_F0_MASK                       0xFF000000U
#define LPDDR4__DENALI_PI_243__PI_TRCD_F0_SHIFT                              24U
#define LPDDR4__DENALI_PI_243__PI_TRCD_F0_WIDTH                               8U
#define LPDDR4__PI_TRCD_F0__REG DENALI_PI_243
#define LPDDR4__PI_TRCD_F0__FLD LPDDR4__DENALI_PI_243__PI_TRCD_F0

#define LPDDR4__DENALI_PI_244_READ_MASK                              0x00FF3F1FU
#define LPDDR4__DENALI_PI_244_WRITE_MASK                             0x00FF3F1FU
#define LPDDR4__DENALI_PI_244__PI_TCCD_L_F0_MASK                     0x0000001FU
#define LPDDR4__DENALI_PI_244__PI_TCCD_L_F0_SHIFT                             0U
#define LPDDR4__DENALI_PI_244__PI_TCCD_L_F0_WIDTH                             5U
#define LPDDR4__PI_TCCD_L_F0__REG DENALI_PI_244
#define LPDDR4__PI_TCCD_L_F0__FLD LPDDR4__DENALI_PI_244__PI_TCCD_L_F0

#define LPDDR4__DENALI_PI_244__PI_TWTR_F0_MASK                       0x00003F00U
#define LPDDR4__DENALI_PI_244__PI_TWTR_F0_SHIFT                               8U
#define LPDDR4__DENALI_PI_244__PI_TWTR_F0_WIDTH                               6U
#define LPDDR4__PI_TWTR_F0__REG DENALI_PI_244
#define LPDDR4__PI_TWTR_F0__FLD LPDDR4__DENALI_PI_244__PI_TWTR_F0

#define LPDDR4__DENALI_PI_244__PI_TWR_F0_MASK                        0x00FF0000U
#define LPDDR4__DENALI_PI_244__PI_TWR_F0_SHIFT                               16U
#define LPDDR4__DENALI_PI_244__PI_TWR_F0_WIDTH                                8U
#define LPDDR4__PI_TWR_F0__REG DENALI_PI_244
#define LPDDR4__PI_TWR_F0__FLD LPDDR4__DENALI_PI_244__PI_TWR_F0

#define LPDDR4__DENALI_PI_245_READ_MASK                              0x000FFFFFU
#define LPDDR4__DENALI_PI_245_WRITE_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PI_245__PI_TRAS_MAX_F0_MASK                   0x000FFFFFU
#define LPDDR4__DENALI_PI_245__PI_TRAS_MAX_F0_SHIFT                           0U
#define LPDDR4__DENALI_PI_245__PI_TRAS_MAX_F0_WIDTH                          20U
#define LPDDR4__PI_TRAS_MAX_F0__REG DENALI_PI_245
#define LPDDR4__PI_TRAS_MAX_F0__FLD LPDDR4__DENALI_PI_245__PI_TRAS_MAX_F0

#define LPDDR4__DENALI_PI_246_READ_MASK                              0x3F0F01FFU
#define LPDDR4__DENALI_PI_246_WRITE_MASK                             0x3F0F01FFU
#define LPDDR4__DENALI_PI_246__PI_TRAS_MIN_F0_MASK                   0x000001FFU
#define LPDDR4__DENALI_PI_246__PI_TRAS_MIN_F0_SHIFT                           0U
#define LPDDR4__DENALI_PI_246__PI_TRAS_MIN_F0_WIDTH                           9U
#define LPDDR4__PI_TRAS_MIN_F0__REG DENALI_PI_246
#define LPDDR4__PI_TRAS_MIN_F0__FLD LPDDR4__DENALI_PI_246__PI_TRAS_MIN_F0

#define LPDDR4__DENALI_PI_246__PI_TDQSCK_MAX_F0_MASK                 0x000F0000U
#define LPDDR4__DENALI_PI_246__PI_TDQSCK_MAX_F0_SHIFT                        16U
#define LPDDR4__DENALI_PI_246__PI_TDQSCK_MAX_F0_WIDTH                         4U
#define LPDDR4__PI_TDQSCK_MAX_F0__REG DENALI_PI_246
#define LPDDR4__PI_TDQSCK_MAX_F0__FLD LPDDR4__DENALI_PI_246__PI_TDQSCK_MAX_F0

#define LPDDR4__DENALI_PI_246__PI_TCCDMW_F0_MASK                     0x3F000000U
#define LPDDR4__DENALI_PI_246__PI_TCCDMW_F0_SHIFT                            24U
#define LPDDR4__DENALI_PI_246__PI_TCCDMW_F0_WIDTH                             6U
#define LPDDR4__PI_TCCDMW_F0__REG DENALI_PI_246
#define LPDDR4__PI_TCCDMW_F0__FLD LPDDR4__DENALI_PI_246__PI_TCCDMW_F0

#define LPDDR4__DENALI_PI_247_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_247_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_247__PI_TSR_F0_MASK                        0x000000FFU
#define LPDDR4__DENALI_PI_247__PI_TSR_F0_SHIFT                                0U
#define LPDDR4__DENALI_PI_247__PI_TSR_F0_WIDTH                                8U
#define LPDDR4__PI_TSR_F0__REG DENALI_PI_247
#define LPDDR4__PI_TSR_F0__FLD LPDDR4__DENALI_PI_247__PI_TSR_F0

#define LPDDR4__DENALI_PI_247__PI_TMRD_F0_MASK                       0x0000FF00U
#define LPDDR4__DENALI_PI_247__PI_TMRD_F0_SHIFT                               8U
#define LPDDR4__DENALI_PI_247__PI_TMRD_F0_WIDTH                               8U
#define LPDDR4__PI_TMRD_F0__REG DENALI_PI_247
#define LPDDR4__PI_TMRD_F0__FLD LPDDR4__DENALI_PI_247__PI_TMRD_F0

#define LPDDR4__DENALI_PI_247__PI_TMRW_F0_MASK                       0x00FF0000U
#define LPDDR4__DENALI_PI_247__PI_TMRW_F0_SHIFT                              16U
#define LPDDR4__DENALI_PI_247__PI_TMRW_F0_WIDTH                               8U
#define LPDDR4__PI_TMRW_F0__REG DENALI_PI_247
#define LPDDR4__PI_TMRW_F0__FLD LPDDR4__DENALI_PI_247__PI_TMRW_F0

#define LPDDR4__DENALI_PI_247__PI_TMOD_F0_MASK                       0xFF000000U
#define LPDDR4__DENALI_PI_247__PI_TMOD_F0_SHIFT                              24U
#define LPDDR4__DENALI_PI_247__PI_TMOD_F0_WIDTH                               8U
#define LPDDR4__PI_TMOD_F0__REG DENALI_PI_247
#define LPDDR4__PI_TMOD_F0__FLD LPDDR4__DENALI_PI_247__PI_TMOD_F0

#define LPDDR4__DENALI_PI_248_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_248_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_248__PI_TMOD_PAR_F0_MASK                   0x000000FFU
#define LPDDR4__DENALI_PI_248__PI_TMOD_PAR_F0_SHIFT                           0U
#define LPDDR4__DENALI_PI_248__PI_TMOD_PAR_F0_WIDTH                           8U
#define LPDDR4__PI_TMOD_PAR_F0__REG DENALI_PI_248
#define LPDDR4__PI_TMOD_PAR_F0__FLD LPDDR4__DENALI_PI_248__PI_TMOD_PAR_F0

#define LPDDR4__DENALI_PI_248__PI_TMRD_PAR_F0_MASK                   0x0000FF00U
#define LPDDR4__DENALI_PI_248__PI_TMRD_PAR_F0_SHIFT                           8U
#define LPDDR4__DENALI_PI_248__PI_TMRD_PAR_F0_WIDTH                           8U
#define LPDDR4__PI_TMRD_PAR_F0__REG DENALI_PI_248
#define LPDDR4__PI_TMRD_PAR_F0__FLD LPDDR4__DENALI_PI_248__PI_TMRD_PAR_F0

#define LPDDR4__DENALI_PI_248__PI_TRTP_F1_MASK                       0x00FF0000U
#define LPDDR4__DENALI_PI_248__PI_TRTP_F1_SHIFT                              16U
#define LPDDR4__DENALI_PI_248__PI_TRTP_F1_WIDTH                               8U
#define LPDDR4__PI_TRTP_F1__REG DENALI_PI_248
#define LPDDR4__PI_TRTP_F1__FLD LPDDR4__DENALI_PI_248__PI_TRTP_F1

#define LPDDR4__DENALI_PI_248__PI_TRP_F1_MASK                        0xFF000000U
#define LPDDR4__DENALI_PI_248__PI_TRP_F1_SHIFT                               24U
#define LPDDR4__DENALI_PI_248__PI_TRP_F1_WIDTH                                8U
#define LPDDR4__PI_TRP_F1__REG DENALI_PI_248
#define LPDDR4__PI_TRP_F1__FLD LPDDR4__DENALI_PI_248__PI_TRP_F1

#define LPDDR4__DENALI_PI_249_READ_MASK                              0xFF3F1FFFU
#define LPDDR4__DENALI_PI_249_WRITE_MASK                             0xFF3F1FFFU
#define LPDDR4__DENALI_PI_249__PI_TRCD_F1_MASK                       0x000000FFU
#define LPDDR4__DENALI_PI_249__PI_TRCD_F1_SHIFT                               0U
#define LPDDR4__DENALI_PI_249__PI_TRCD_F1_WIDTH                               8U
#define LPDDR4__PI_TRCD_F1__REG DENALI_PI_249
#define LPDDR4__PI_TRCD_F1__FLD LPDDR4__DENALI_PI_249__PI_TRCD_F1

#define LPDDR4__DENALI_PI_249__PI_TCCD_L_F1_MASK                     0x00001F00U
#define LPDDR4__DENALI_PI_249__PI_TCCD_L_F1_SHIFT                             8U
#define LPDDR4__DENALI_PI_249__PI_TCCD_L_F1_WIDTH                             5U
#define LPDDR4__PI_TCCD_L_F1__REG DENALI_PI_249
#define LPDDR4__PI_TCCD_L_F1__FLD LPDDR4__DENALI_PI_249__PI_TCCD_L_F1

#define LPDDR4__DENALI_PI_249__PI_TWTR_F1_MASK                       0x003F0000U
#define LPDDR4__DENALI_PI_249__PI_TWTR_F1_SHIFT                              16U
#define LPDDR4__DENALI_PI_249__PI_TWTR_F1_WIDTH                               6U
#define LPDDR4__PI_TWTR_F1__REG DENALI_PI_249
#define LPDDR4__PI_TWTR_F1__FLD LPDDR4__DENALI_PI_249__PI_TWTR_F1

#define LPDDR4__DENALI_PI_249__PI_TWR_F1_MASK                        0xFF000000U
#define LPDDR4__DENALI_PI_249__PI_TWR_F1_SHIFT                               24U
#define LPDDR4__DENALI_PI_249__PI_TWR_F1_WIDTH                                8U
#define LPDDR4__PI_TWR_F1__REG DENALI_PI_249
#define LPDDR4__PI_TWR_F1__FLD LPDDR4__DENALI_PI_249__PI_TWR_F1

#define LPDDR4__DENALI_PI_250_READ_MASK                              0x000FFFFFU
#define LPDDR4__DENALI_PI_250_WRITE_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PI_250__PI_TRAS_MAX_F1_MASK                   0x000FFFFFU
#define LPDDR4__DENALI_PI_250__PI_TRAS_MAX_F1_SHIFT                           0U
#define LPDDR4__DENALI_PI_250__PI_TRAS_MAX_F1_WIDTH                          20U
#define LPDDR4__PI_TRAS_MAX_F1__REG DENALI_PI_250
#define LPDDR4__PI_TRAS_MAX_F1__FLD LPDDR4__DENALI_PI_250__PI_TRAS_MAX_F1

#define LPDDR4__DENALI_PI_251_READ_MASK                              0x3F0F01FFU
#define LPDDR4__DENALI_PI_251_WRITE_MASK                             0x3F0F01FFU
#define LPDDR4__DENALI_PI_251__PI_TRAS_MIN_F1_MASK                   0x000001FFU
#define LPDDR4__DENALI_PI_251__PI_TRAS_MIN_F1_SHIFT                           0U
#define LPDDR4__DENALI_PI_251__PI_TRAS_MIN_F1_WIDTH                           9U
#define LPDDR4__PI_TRAS_MIN_F1__REG DENALI_PI_251
#define LPDDR4__PI_TRAS_MIN_F1__FLD LPDDR4__DENALI_PI_251__PI_TRAS_MIN_F1

#define LPDDR4__DENALI_PI_251__PI_TDQSCK_MAX_F1_MASK                 0x000F0000U
#define LPDDR4__DENALI_PI_251__PI_TDQSCK_MAX_F1_SHIFT                        16U
#define LPDDR4__DENALI_PI_251__PI_TDQSCK_MAX_F1_WIDTH                         4U
#define LPDDR4__PI_TDQSCK_MAX_F1__REG DENALI_PI_251
#define LPDDR4__PI_TDQSCK_MAX_F1__FLD LPDDR4__DENALI_PI_251__PI_TDQSCK_MAX_F1

#define LPDDR4__DENALI_PI_251__PI_TCCDMW_F1_MASK                     0x3F000000U
#define LPDDR4__DENALI_PI_251__PI_TCCDMW_F1_SHIFT                            24U
#define LPDDR4__DENALI_PI_251__PI_TCCDMW_F1_WIDTH                             6U
#define LPDDR4__PI_TCCDMW_F1__REG DENALI_PI_251
#define LPDDR4__PI_TCCDMW_F1__FLD LPDDR4__DENALI_PI_251__PI_TCCDMW_F1

#define LPDDR4__DENALI_PI_252_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_252_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_252__PI_TSR_F1_MASK                        0x000000FFU
#define LPDDR4__DENALI_PI_252__PI_TSR_F1_SHIFT                                0U
#define LPDDR4__DENALI_PI_252__PI_TSR_F1_WIDTH                                8U
#define LPDDR4__PI_TSR_F1__REG DENALI_PI_252
#define LPDDR4__PI_TSR_F1__FLD LPDDR4__DENALI_PI_252__PI_TSR_F1

#define LPDDR4__DENALI_PI_252__PI_TMRD_F1_MASK                       0x0000FF00U
#define LPDDR4__DENALI_PI_252__PI_TMRD_F1_SHIFT                               8U
#define LPDDR4__DENALI_PI_252__PI_TMRD_F1_WIDTH                               8U
#define LPDDR4__PI_TMRD_F1__REG DENALI_PI_252
#define LPDDR4__PI_TMRD_F1__FLD LPDDR4__DENALI_PI_252__PI_TMRD_F1

#define LPDDR4__DENALI_PI_252__PI_TMRW_F1_MASK                       0x00FF0000U
#define LPDDR4__DENALI_PI_252__PI_TMRW_F1_SHIFT                              16U
#define LPDDR4__DENALI_PI_252__PI_TMRW_F1_WIDTH                               8U
#define LPDDR4__PI_TMRW_F1__REG DENALI_PI_252
#define LPDDR4__PI_TMRW_F1__FLD LPDDR4__DENALI_PI_252__PI_TMRW_F1

#define LPDDR4__DENALI_PI_252__PI_TMOD_F1_MASK                       0xFF000000U
#define LPDDR4__DENALI_PI_252__PI_TMOD_F1_SHIFT                              24U
#define LPDDR4__DENALI_PI_252__PI_TMOD_F1_WIDTH                               8U
#define LPDDR4__PI_TMOD_F1__REG DENALI_PI_252
#define LPDDR4__PI_TMOD_F1__FLD LPDDR4__DENALI_PI_252__PI_TMOD_F1

#define LPDDR4__DENALI_PI_253_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_253_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_253__PI_TMOD_PAR_F1_MASK                   0x000000FFU
#define LPDDR4__DENALI_PI_253__PI_TMOD_PAR_F1_SHIFT                           0U
#define LPDDR4__DENALI_PI_253__PI_TMOD_PAR_F1_WIDTH                           8U
#define LPDDR4__PI_TMOD_PAR_F1__REG DENALI_PI_253
#define LPDDR4__PI_TMOD_PAR_F1__FLD LPDDR4__DENALI_PI_253__PI_TMOD_PAR_F1

#define LPDDR4__DENALI_PI_253__PI_TMRD_PAR_F1_MASK                   0x0000FF00U
#define LPDDR4__DENALI_PI_253__PI_TMRD_PAR_F1_SHIFT                           8U
#define LPDDR4__DENALI_PI_253__PI_TMRD_PAR_F1_WIDTH                           8U
#define LPDDR4__PI_TMRD_PAR_F1__REG DENALI_PI_253
#define LPDDR4__PI_TMRD_PAR_F1__FLD LPDDR4__DENALI_PI_253__PI_TMRD_PAR_F1

#define LPDDR4__DENALI_PI_253__PI_TRTP_F2_MASK                       0x00FF0000U
#define LPDDR4__DENALI_PI_253__PI_TRTP_F2_SHIFT                              16U
#define LPDDR4__DENALI_PI_253__PI_TRTP_F2_WIDTH                               8U
#define LPDDR4__PI_TRTP_F2__REG DENALI_PI_253
#define LPDDR4__PI_TRTP_F2__FLD LPDDR4__DENALI_PI_253__PI_TRTP_F2

#define LPDDR4__DENALI_PI_253__PI_TRP_F2_MASK                        0xFF000000U
#define LPDDR4__DENALI_PI_253__PI_TRP_F2_SHIFT                               24U
#define LPDDR4__DENALI_PI_253__PI_TRP_F2_WIDTH                                8U
#define LPDDR4__PI_TRP_F2__REG DENALI_PI_253
#define LPDDR4__PI_TRP_F2__FLD LPDDR4__DENALI_PI_253__PI_TRP_F2

#define LPDDR4__DENALI_PI_254_READ_MASK                              0xFF3F1FFFU
#define LPDDR4__DENALI_PI_254_WRITE_MASK                             0xFF3F1FFFU
#define LPDDR4__DENALI_PI_254__PI_TRCD_F2_MASK                       0x000000FFU
#define LPDDR4__DENALI_PI_254__PI_TRCD_F2_SHIFT                               0U
#define LPDDR4__DENALI_PI_254__PI_TRCD_F2_WIDTH                               8U
#define LPDDR4__PI_TRCD_F2__REG DENALI_PI_254
#define LPDDR4__PI_TRCD_F2__FLD LPDDR4__DENALI_PI_254__PI_TRCD_F2

#define LPDDR4__DENALI_PI_254__PI_TCCD_L_F2_MASK                     0x00001F00U
#define LPDDR4__DENALI_PI_254__PI_TCCD_L_F2_SHIFT                             8U
#define LPDDR4__DENALI_PI_254__PI_TCCD_L_F2_WIDTH                             5U
#define LPDDR4__PI_TCCD_L_F2__REG DENALI_PI_254
#define LPDDR4__PI_TCCD_L_F2__FLD LPDDR4__DENALI_PI_254__PI_TCCD_L_F2

#define LPDDR4__DENALI_PI_254__PI_TWTR_F2_MASK                       0x003F0000U
#define LPDDR4__DENALI_PI_254__PI_TWTR_F2_SHIFT                              16U
#define LPDDR4__DENALI_PI_254__PI_TWTR_F2_WIDTH                               6U
#define LPDDR4__PI_TWTR_F2__REG DENALI_PI_254
#define LPDDR4__PI_TWTR_F2__FLD LPDDR4__DENALI_PI_254__PI_TWTR_F2

#define LPDDR4__DENALI_PI_254__PI_TWR_F2_MASK                        0xFF000000U
#define LPDDR4__DENALI_PI_254__PI_TWR_F2_SHIFT                               24U
#define LPDDR4__DENALI_PI_254__PI_TWR_F2_WIDTH                                8U
#define LPDDR4__PI_TWR_F2__REG DENALI_PI_254
#define LPDDR4__PI_TWR_F2__FLD LPDDR4__DENALI_PI_254__PI_TWR_F2

#define LPDDR4__DENALI_PI_255_READ_MASK                              0x000FFFFFU
#define LPDDR4__DENALI_PI_255_WRITE_MASK                             0x000FFFFFU
#define LPDDR4__DENALI_PI_255__PI_TRAS_MAX_F2_MASK                   0x000FFFFFU
#define LPDDR4__DENALI_PI_255__PI_TRAS_MAX_F2_SHIFT                           0U
#define LPDDR4__DENALI_PI_255__PI_TRAS_MAX_F2_WIDTH                          20U
#define LPDDR4__PI_TRAS_MAX_F2__REG DENALI_PI_255
#define LPDDR4__PI_TRAS_MAX_F2__FLD LPDDR4__DENALI_PI_255__PI_TRAS_MAX_F2

#define LPDDR4__DENALI_PI_256_READ_MASK                              0x3F0F01FFU
#define LPDDR4__DENALI_PI_256_WRITE_MASK                             0x3F0F01FFU
#define LPDDR4__DENALI_PI_256__PI_TRAS_MIN_F2_MASK                   0x000001FFU
#define LPDDR4__DENALI_PI_256__PI_TRAS_MIN_F2_SHIFT                           0U
#define LPDDR4__DENALI_PI_256__PI_TRAS_MIN_F2_WIDTH                           9U
#define LPDDR4__PI_TRAS_MIN_F2__REG DENALI_PI_256
#define LPDDR4__PI_TRAS_MIN_F2__FLD LPDDR4__DENALI_PI_256__PI_TRAS_MIN_F2

#define LPDDR4__DENALI_PI_256__PI_TDQSCK_MAX_F2_MASK                 0x000F0000U
#define LPDDR4__DENALI_PI_256__PI_TDQSCK_MAX_F2_SHIFT                        16U
#define LPDDR4__DENALI_PI_256__PI_TDQSCK_MAX_F2_WIDTH                         4U
#define LPDDR4__PI_TDQSCK_MAX_F2__REG DENALI_PI_256
#define LPDDR4__PI_TDQSCK_MAX_F2__FLD LPDDR4__DENALI_PI_256__PI_TDQSCK_MAX_F2

#define LPDDR4__DENALI_PI_256__PI_TCCDMW_F2_MASK                     0x3F000000U
#define LPDDR4__DENALI_PI_256__PI_TCCDMW_F2_SHIFT                            24U
#define LPDDR4__DENALI_PI_256__PI_TCCDMW_F2_WIDTH                             6U
#define LPDDR4__PI_TCCDMW_F2__REG DENALI_PI_256
#define LPDDR4__PI_TCCDMW_F2__FLD LPDDR4__DENALI_PI_256__PI_TCCDMW_F2

#define LPDDR4__DENALI_PI_257_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_257_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_257__PI_TSR_F2_MASK                        0x000000FFU
#define LPDDR4__DENALI_PI_257__PI_TSR_F2_SHIFT                                0U
#define LPDDR4__DENALI_PI_257__PI_TSR_F2_WIDTH                                8U
#define LPDDR4__PI_TSR_F2__REG DENALI_PI_257
#define LPDDR4__PI_TSR_F2__FLD LPDDR4__DENALI_PI_257__PI_TSR_F2

#define LPDDR4__DENALI_PI_257__PI_TMRD_F2_MASK                       0x0000FF00U
#define LPDDR4__DENALI_PI_257__PI_TMRD_F2_SHIFT                               8U
#define LPDDR4__DENALI_PI_257__PI_TMRD_F2_WIDTH                               8U
#define LPDDR4__PI_TMRD_F2__REG DENALI_PI_257
#define LPDDR4__PI_TMRD_F2__FLD LPDDR4__DENALI_PI_257__PI_TMRD_F2

#define LPDDR4__DENALI_PI_257__PI_TMRW_F2_MASK                       0x00FF0000U
#define LPDDR4__DENALI_PI_257__PI_TMRW_F2_SHIFT                              16U
#define LPDDR4__DENALI_PI_257__PI_TMRW_F2_WIDTH                               8U
#define LPDDR4__PI_TMRW_F2__REG DENALI_PI_257
#define LPDDR4__PI_TMRW_F2__FLD LPDDR4__DENALI_PI_257__PI_TMRW_F2

#define LPDDR4__DENALI_PI_257__PI_TMOD_F2_MASK                       0xFF000000U
#define LPDDR4__DENALI_PI_257__PI_TMOD_F2_SHIFT                              24U
#define LPDDR4__DENALI_PI_257__PI_TMOD_F2_WIDTH                               8U
#define LPDDR4__PI_TMOD_F2__REG DENALI_PI_257
#define LPDDR4__PI_TMOD_F2__FLD LPDDR4__DENALI_PI_257__PI_TMOD_F2

#define LPDDR4__DENALI_PI_258_READ_MASK                              0x0000FFFFU
#define LPDDR4__DENALI_PI_258_WRITE_MASK                             0x0000FFFFU
#define LPDDR4__DENALI_PI_258__PI_TMOD_PAR_F2_MASK                   0x000000FFU
#define LPDDR4__DENALI_PI_258__PI_TMOD_PAR_F2_SHIFT                           0U
#define LPDDR4__DENALI_PI_258__PI_TMOD_PAR_F2_WIDTH                           8U
#define LPDDR4__PI_TMOD_PAR_F2__REG DENALI_PI_258
#define LPDDR4__PI_TMOD_PAR_F2__FLD LPDDR4__DENALI_PI_258__PI_TMOD_PAR_F2

#define LPDDR4__DENALI_PI_258__PI_TMRD_PAR_F2_MASK                   0x0000FF00U
#define LPDDR4__DENALI_PI_258__PI_TMRD_PAR_F2_SHIFT                           8U
#define LPDDR4__DENALI_PI_258__PI_TMRD_PAR_F2_WIDTH                           8U
#define LPDDR4__PI_TMRD_PAR_F2__REG DENALI_PI_258
#define LPDDR4__PI_TMRD_PAR_F2__FLD LPDDR4__DENALI_PI_258__PI_TMRD_PAR_F2

#define LPDDR4__DENALI_PI_259_READ_MASK                              0x001FFFFFU
#define LPDDR4__DENALI_PI_259_WRITE_MASK                             0x001FFFFFU
#define LPDDR4__DENALI_PI_259__PI_TDFI_CTRLUPD_MAX_F0_MASK           0x001FFFFFU
#define LPDDR4__DENALI_PI_259__PI_TDFI_CTRLUPD_MAX_F0_SHIFT                   0U
#define LPDDR4__DENALI_PI_259__PI_TDFI_CTRLUPD_MAX_F0_WIDTH                  21U
#define LPDDR4__PI_TDFI_CTRLUPD_MAX_F0__REG DENALI_PI_259
#define LPDDR4__PI_TDFI_CTRLUPD_MAX_F0__FLD LPDDR4__DENALI_PI_259__PI_TDFI_CTRLUPD_MAX_F0

#define LPDDR4__DENALI_PI_260_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_260_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_260__PI_TDFI_CTRLUPD_INTERVAL_F0_MASK      0xFFFFFFFFU
#define LPDDR4__DENALI_PI_260__PI_TDFI_CTRLUPD_INTERVAL_F0_SHIFT              0U
#define LPDDR4__DENALI_PI_260__PI_TDFI_CTRLUPD_INTERVAL_F0_WIDTH             32U
#define LPDDR4__PI_TDFI_CTRLUPD_INTERVAL_F0__REG DENALI_PI_260
#define LPDDR4__PI_TDFI_CTRLUPD_INTERVAL_F0__FLD LPDDR4__DENALI_PI_260__PI_TDFI_CTRLUPD_INTERVAL_F0

#define LPDDR4__DENALI_PI_261_READ_MASK                              0x001FFFFFU
#define LPDDR4__DENALI_PI_261_WRITE_MASK                             0x001FFFFFU
#define LPDDR4__DENALI_PI_261__PI_TDFI_CTRLUPD_MAX_F1_MASK           0x001FFFFFU
#define LPDDR4__DENALI_PI_261__PI_TDFI_CTRLUPD_MAX_F1_SHIFT                   0U
#define LPDDR4__DENALI_PI_261__PI_TDFI_CTRLUPD_MAX_F1_WIDTH                  21U
#define LPDDR4__PI_TDFI_CTRLUPD_MAX_F1__REG DENALI_PI_261
#define LPDDR4__PI_TDFI_CTRLUPD_MAX_F1__FLD LPDDR4__DENALI_PI_261__PI_TDFI_CTRLUPD_MAX_F1

#define LPDDR4__DENALI_PI_262_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_262_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_262__PI_TDFI_CTRLUPD_INTERVAL_F1_MASK      0xFFFFFFFFU
#define LPDDR4__DENALI_PI_262__PI_TDFI_CTRLUPD_INTERVAL_F1_SHIFT              0U
#define LPDDR4__DENALI_PI_262__PI_TDFI_CTRLUPD_INTERVAL_F1_WIDTH             32U
#define LPDDR4__PI_TDFI_CTRLUPD_INTERVAL_F1__REG DENALI_PI_262
#define LPDDR4__PI_TDFI_CTRLUPD_INTERVAL_F1__FLD LPDDR4__DENALI_PI_262__PI_TDFI_CTRLUPD_INTERVAL_F1

#define LPDDR4__DENALI_PI_263_READ_MASK                              0x001FFFFFU
#define LPDDR4__DENALI_PI_263_WRITE_MASK                             0x001FFFFFU
#define LPDDR4__DENALI_PI_263__PI_TDFI_CTRLUPD_MAX_F2_MASK           0x001FFFFFU
#define LPDDR4__DENALI_PI_263__PI_TDFI_CTRLUPD_MAX_F2_SHIFT                   0U
#define LPDDR4__DENALI_PI_263__PI_TDFI_CTRLUPD_MAX_F2_WIDTH                  21U
#define LPDDR4__PI_TDFI_CTRLUPD_MAX_F2__REG DENALI_PI_263
#define LPDDR4__PI_TDFI_CTRLUPD_MAX_F2__FLD LPDDR4__DENALI_PI_263__PI_TDFI_CTRLUPD_MAX_F2

#define LPDDR4__DENALI_PI_264_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_264_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_264__PI_TDFI_CTRLUPD_INTERVAL_F2_MASK      0xFFFFFFFFU
#define LPDDR4__DENALI_PI_264__PI_TDFI_CTRLUPD_INTERVAL_F2_SHIFT              0U
#define LPDDR4__DENALI_PI_264__PI_TDFI_CTRLUPD_INTERVAL_F2_WIDTH             32U
#define LPDDR4__PI_TDFI_CTRLUPD_INTERVAL_F2__REG DENALI_PI_264
#define LPDDR4__PI_TDFI_CTRLUPD_INTERVAL_F2__FLD LPDDR4__DENALI_PI_264__PI_TDFI_CTRLUPD_INTERVAL_F2

#define LPDDR4__DENALI_PI_265_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_265_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_265__PI_TXSR_F0_MASK                       0x0000FFFFU
#define LPDDR4__DENALI_PI_265__PI_TXSR_F0_SHIFT                               0U
#define LPDDR4__DENALI_PI_265__PI_TXSR_F0_WIDTH                              16U
#define LPDDR4__PI_TXSR_F0__REG DENALI_PI_265
#define LPDDR4__PI_TXSR_F0__FLD LPDDR4__DENALI_PI_265__PI_TXSR_F0

#define LPDDR4__DENALI_PI_265__PI_TXSR_F1_MASK                       0xFFFF0000U
#define LPDDR4__DENALI_PI_265__PI_TXSR_F1_SHIFT                              16U
#define LPDDR4__DENALI_PI_265__PI_TXSR_F1_WIDTH                              16U
#define LPDDR4__PI_TXSR_F1__REG DENALI_PI_265
#define LPDDR4__PI_TXSR_F1__FLD LPDDR4__DENALI_PI_265__PI_TXSR_F1

#define LPDDR4__DENALI_PI_266_READ_MASK                              0x3F3FFFFFU
#define LPDDR4__DENALI_PI_266_WRITE_MASK                             0x3F3FFFFFU
#define LPDDR4__DENALI_PI_266__PI_TXSR_F2_MASK                       0x0000FFFFU
#define LPDDR4__DENALI_PI_266__PI_TXSR_F2_SHIFT                               0U
#define LPDDR4__DENALI_PI_266__PI_TXSR_F2_WIDTH                              16U
#define LPDDR4__PI_TXSR_F2__REG DENALI_PI_266
#define LPDDR4__PI_TXSR_F2__FLD LPDDR4__DENALI_PI_266__PI_TXSR_F2

#define LPDDR4__DENALI_PI_266__PI_TEXCKE_F0_MASK                     0x003F0000U
#define LPDDR4__DENALI_PI_266__PI_TEXCKE_F0_SHIFT                            16U
#define LPDDR4__DENALI_PI_266__PI_TEXCKE_F0_WIDTH                             6U
#define LPDDR4__PI_TEXCKE_F0__REG DENALI_PI_266
#define LPDDR4__PI_TEXCKE_F0__FLD LPDDR4__DENALI_PI_266__PI_TEXCKE_F0

#define LPDDR4__DENALI_PI_266__PI_TEXCKE_F1_MASK                     0x3F000000U
#define LPDDR4__DENALI_PI_266__PI_TEXCKE_F1_SHIFT                            24U
#define LPDDR4__DENALI_PI_266__PI_TEXCKE_F1_WIDTH                             6U
#define LPDDR4__PI_TEXCKE_F1__REG DENALI_PI_266
#define LPDDR4__PI_TEXCKE_F1__FLD LPDDR4__DENALI_PI_266__PI_TEXCKE_F1

#define LPDDR4__DENALI_PI_267_READ_MASK                              0x00FFFF3FU
#define LPDDR4__DENALI_PI_267_WRITE_MASK                             0x00FFFF3FU
#define LPDDR4__DENALI_PI_267__PI_TEXCKE_F2_MASK                     0x0000003FU
#define LPDDR4__DENALI_PI_267__PI_TEXCKE_F2_SHIFT                             0U
#define LPDDR4__DENALI_PI_267__PI_TEXCKE_F2_WIDTH                             6U
#define LPDDR4__PI_TEXCKE_F2__REG DENALI_PI_267
#define LPDDR4__PI_TEXCKE_F2__FLD LPDDR4__DENALI_PI_267__PI_TEXCKE_F2

#define LPDDR4__DENALI_PI_267__PI_TDLL_F0_MASK                       0x00FFFF00U
#define LPDDR4__DENALI_PI_267__PI_TDLL_F0_SHIFT                               8U
#define LPDDR4__DENALI_PI_267__PI_TDLL_F0_WIDTH                              16U
#define LPDDR4__PI_TDLL_F0__REG DENALI_PI_267
#define LPDDR4__PI_TDLL_F0__FLD LPDDR4__DENALI_PI_267__PI_TDLL_F0

#define LPDDR4__DENALI_PI_268_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_268_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_268__PI_TDLL_F1_MASK                       0x0000FFFFU
#define LPDDR4__DENALI_PI_268__PI_TDLL_F1_SHIFT                               0U
#define LPDDR4__DENALI_PI_268__PI_TDLL_F1_WIDTH                              16U
#define LPDDR4__PI_TDLL_F1__REG DENALI_PI_268
#define LPDDR4__PI_TDLL_F1__FLD LPDDR4__DENALI_PI_268__PI_TDLL_F1

#define LPDDR4__DENALI_PI_268__PI_TDLL_F2_MASK                       0xFFFF0000U
#define LPDDR4__DENALI_PI_268__PI_TDLL_F2_SHIFT                              16U
#define LPDDR4__DENALI_PI_268__PI_TDLL_F2_WIDTH                              16U
#define LPDDR4__PI_TDLL_F2__REG DENALI_PI_268
#define LPDDR4__PI_TDLL_F2__FLD LPDDR4__DENALI_PI_268__PI_TDLL_F2

#define LPDDR4__DENALI_PI_269_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_269_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_269__PI_TCKSRX_F0_MASK                     0x000000FFU
#define LPDDR4__DENALI_PI_269__PI_TCKSRX_F0_SHIFT                             0U
#define LPDDR4__DENALI_PI_269__PI_TCKSRX_F0_WIDTH                             8U
#define LPDDR4__PI_TCKSRX_F0__REG DENALI_PI_269
#define LPDDR4__PI_TCKSRX_F0__FLD LPDDR4__DENALI_PI_269__PI_TCKSRX_F0

#define LPDDR4__DENALI_PI_269__PI_TCKSRE_F0_MASK                     0x0000FF00U
#define LPDDR4__DENALI_PI_269__PI_TCKSRE_F0_SHIFT                             8U
#define LPDDR4__DENALI_PI_269__PI_TCKSRE_F0_WIDTH                             8U
#define LPDDR4__PI_TCKSRE_F0__REG DENALI_PI_269
#define LPDDR4__PI_TCKSRE_F0__FLD LPDDR4__DENALI_PI_269__PI_TCKSRE_F0

#define LPDDR4__DENALI_PI_269__PI_TCKSRX_F1_MASK                     0x00FF0000U
#define LPDDR4__DENALI_PI_269__PI_TCKSRX_F1_SHIFT                            16U
#define LPDDR4__DENALI_PI_269__PI_TCKSRX_F1_WIDTH                             8U
#define LPDDR4__PI_TCKSRX_F1__REG DENALI_PI_269
#define LPDDR4__PI_TCKSRX_F1__FLD LPDDR4__DENALI_PI_269__PI_TCKSRX_F1

#define LPDDR4__DENALI_PI_269__PI_TCKSRE_F1_MASK                     0xFF000000U
#define LPDDR4__DENALI_PI_269__PI_TCKSRE_F1_SHIFT                            24U
#define LPDDR4__DENALI_PI_269__PI_TCKSRE_F1_WIDTH                             8U
#define LPDDR4__PI_TCKSRE_F1__REG DENALI_PI_269
#define LPDDR4__PI_TCKSRE_F1__FLD LPDDR4__DENALI_PI_269__PI_TCKSRE_F1

#define LPDDR4__DENALI_PI_270_READ_MASK                              0x0000FFFFU
#define LPDDR4__DENALI_PI_270_WRITE_MASK                             0x0000FFFFU
#define LPDDR4__DENALI_PI_270__PI_TCKSRX_F2_MASK                     0x000000FFU
#define LPDDR4__DENALI_PI_270__PI_TCKSRX_F2_SHIFT                             0U
#define LPDDR4__DENALI_PI_270__PI_TCKSRX_F2_WIDTH                             8U
#define LPDDR4__PI_TCKSRX_F2__REG DENALI_PI_270
#define LPDDR4__PI_TCKSRX_F2__FLD LPDDR4__DENALI_PI_270__PI_TCKSRX_F2

#define LPDDR4__DENALI_PI_270__PI_TCKSRE_F2_MASK                     0x0000FF00U
#define LPDDR4__DENALI_PI_270__PI_TCKSRE_F2_SHIFT                             8U
#define LPDDR4__DENALI_PI_270__PI_TCKSRE_F2_WIDTH                             8U
#define LPDDR4__PI_TCKSRE_F2__REG DENALI_PI_270
#define LPDDR4__PI_TCKSRE_F2__FLD LPDDR4__DENALI_PI_270__PI_TCKSRE_F2

#define LPDDR4__DENALI_PI_271_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_271_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_271__PI_TINIT_F0_MASK                      0x00FFFFFFU
#define LPDDR4__DENALI_PI_271__PI_TINIT_F0_SHIFT                              0U
#define LPDDR4__DENALI_PI_271__PI_TINIT_F0_WIDTH                             24U
#define LPDDR4__PI_TINIT_F0__REG DENALI_PI_271
#define LPDDR4__PI_TINIT_F0__FLD LPDDR4__DENALI_PI_271__PI_TINIT_F0

#define LPDDR4__DENALI_PI_272_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_272_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_272__PI_TINIT3_F0_MASK                     0x00FFFFFFU
#define LPDDR4__DENALI_PI_272__PI_TINIT3_F0_SHIFT                             0U
#define LPDDR4__DENALI_PI_272__PI_TINIT3_F0_WIDTH                            24U
#define LPDDR4__PI_TINIT3_F0__REG DENALI_PI_272
#define LPDDR4__PI_TINIT3_F0__FLD LPDDR4__DENALI_PI_272__PI_TINIT3_F0

#define LPDDR4__DENALI_PI_273_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_273_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_273__PI_TINIT4_F0_MASK                     0x00FFFFFFU
#define LPDDR4__DENALI_PI_273__PI_TINIT4_F0_SHIFT                             0U
#define LPDDR4__DENALI_PI_273__PI_TINIT4_F0_WIDTH                            24U
#define LPDDR4__PI_TINIT4_F0__REG DENALI_PI_273
#define LPDDR4__PI_TINIT4_F0__FLD LPDDR4__DENALI_PI_273__PI_TINIT4_F0

#define LPDDR4__DENALI_PI_274_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_274_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_274__PI_TINIT5_F0_MASK                     0x00FFFFFFU
#define LPDDR4__DENALI_PI_274__PI_TINIT5_F0_SHIFT                             0U
#define LPDDR4__DENALI_PI_274__PI_TINIT5_F0_WIDTH                            24U
#define LPDDR4__PI_TINIT5_F0__REG DENALI_PI_274
#define LPDDR4__PI_TINIT5_F0__FLD LPDDR4__DENALI_PI_274__PI_TINIT5_F0

#define LPDDR4__DENALI_PI_275_READ_MASK                              0x0000FFFFU
#define LPDDR4__DENALI_PI_275_WRITE_MASK                             0x0000FFFFU
#define LPDDR4__DENALI_PI_275__PI_TXSNR_F0_MASK                      0x0000FFFFU
#define LPDDR4__DENALI_PI_275__PI_TXSNR_F0_SHIFT                              0U
#define LPDDR4__DENALI_PI_275__PI_TXSNR_F0_WIDTH                             16U
#define LPDDR4__PI_TXSNR_F0__REG DENALI_PI_275
#define LPDDR4__PI_TXSNR_F0__FLD LPDDR4__DENALI_PI_275__PI_TXSNR_F0

#define LPDDR4__DENALI_PI_276_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_276_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_276__PI_TINIT_F1_MASK                      0x00FFFFFFU
#define LPDDR4__DENALI_PI_276__PI_TINIT_F1_SHIFT                              0U
#define LPDDR4__DENALI_PI_276__PI_TINIT_F1_WIDTH                             24U
#define LPDDR4__PI_TINIT_F1__REG DENALI_PI_276
#define LPDDR4__PI_TINIT_F1__FLD LPDDR4__DENALI_PI_276__PI_TINIT_F1

#define LPDDR4__DENALI_PI_277_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_277_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_277__PI_TINIT3_F1_MASK                     0x00FFFFFFU
#define LPDDR4__DENALI_PI_277__PI_TINIT3_F1_SHIFT                             0U
#define LPDDR4__DENALI_PI_277__PI_TINIT3_F1_WIDTH                            24U
#define LPDDR4__PI_TINIT3_F1__REG DENALI_PI_277
#define LPDDR4__PI_TINIT3_F1__FLD LPDDR4__DENALI_PI_277__PI_TINIT3_F1

#define LPDDR4__DENALI_PI_278_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_278_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_278__PI_TINIT4_F1_MASK                     0x00FFFFFFU
#define LPDDR4__DENALI_PI_278__PI_TINIT4_F1_SHIFT                             0U
#define LPDDR4__DENALI_PI_278__PI_TINIT4_F1_WIDTH                            24U
#define LPDDR4__PI_TINIT4_F1__REG DENALI_PI_278
#define LPDDR4__PI_TINIT4_F1__FLD LPDDR4__DENALI_PI_278__PI_TINIT4_F1

#define LPDDR4__DENALI_PI_279_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_279_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_279__PI_TINIT5_F1_MASK                     0x00FFFFFFU
#define LPDDR4__DENALI_PI_279__PI_TINIT5_F1_SHIFT                             0U
#define LPDDR4__DENALI_PI_279__PI_TINIT5_F1_WIDTH                            24U
#define LPDDR4__PI_TINIT5_F1__REG DENALI_PI_279
#define LPDDR4__PI_TINIT5_F1__FLD LPDDR4__DENALI_PI_279__PI_TINIT5_F1

#define LPDDR4__DENALI_PI_280_READ_MASK                              0x0000FFFFU
#define LPDDR4__DENALI_PI_280_WRITE_MASK                             0x0000FFFFU
#define LPDDR4__DENALI_PI_280__PI_TXSNR_F1_MASK                      0x0000FFFFU
#define LPDDR4__DENALI_PI_280__PI_TXSNR_F1_SHIFT                              0U
#define LPDDR4__DENALI_PI_280__PI_TXSNR_F1_WIDTH                             16U
#define LPDDR4__PI_TXSNR_F1__REG DENALI_PI_280
#define LPDDR4__PI_TXSNR_F1__FLD LPDDR4__DENALI_PI_280__PI_TXSNR_F1

#define LPDDR4__DENALI_PI_281_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_281_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_281__PI_TINIT_F2_MASK                      0x00FFFFFFU
#define LPDDR4__DENALI_PI_281__PI_TINIT_F2_SHIFT                              0U
#define LPDDR4__DENALI_PI_281__PI_TINIT_F2_WIDTH                             24U
#define LPDDR4__PI_TINIT_F2__REG DENALI_PI_281
#define LPDDR4__PI_TINIT_F2__FLD LPDDR4__DENALI_PI_281__PI_TINIT_F2

#define LPDDR4__DENALI_PI_282_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_282_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_282__PI_TINIT3_F2_MASK                     0x00FFFFFFU
#define LPDDR4__DENALI_PI_282__PI_TINIT3_F2_SHIFT                             0U
#define LPDDR4__DENALI_PI_282__PI_TINIT3_F2_WIDTH                            24U
#define LPDDR4__PI_TINIT3_F2__REG DENALI_PI_282
#define LPDDR4__PI_TINIT3_F2__FLD LPDDR4__DENALI_PI_282__PI_TINIT3_F2

#define LPDDR4__DENALI_PI_283_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_283_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_283__PI_TINIT4_F2_MASK                     0x00FFFFFFU
#define LPDDR4__DENALI_PI_283__PI_TINIT4_F2_SHIFT                             0U
#define LPDDR4__DENALI_PI_283__PI_TINIT4_F2_WIDTH                            24U
#define LPDDR4__PI_TINIT4_F2__REG DENALI_PI_283
#define LPDDR4__PI_TINIT4_F2__FLD LPDDR4__DENALI_PI_283__PI_TINIT4_F2

#define LPDDR4__DENALI_PI_284_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_284_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_284__PI_TINIT5_F2_MASK                     0x00FFFFFFU
#define LPDDR4__DENALI_PI_284__PI_TINIT5_F2_SHIFT                             0U
#define LPDDR4__DENALI_PI_284__PI_TINIT5_F2_WIDTH                            24U
#define LPDDR4__PI_TINIT5_F2__REG DENALI_PI_284
#define LPDDR4__PI_TINIT5_F2__FLD LPDDR4__DENALI_PI_284__PI_TINIT5_F2

#define LPDDR4__DENALI_PI_285_READ_MASK                              0x0FFFFFFFU
#define LPDDR4__DENALI_PI_285_WRITE_MASK                             0x0FFFFFFFU
#define LPDDR4__DENALI_PI_285__PI_TXSNR_F2_MASK                      0x0000FFFFU
#define LPDDR4__DENALI_PI_285__PI_TXSNR_F2_SHIFT                              0U
#define LPDDR4__DENALI_PI_285__PI_TXSNR_F2_WIDTH                             16U
#define LPDDR4__PI_TXSNR_F2__REG DENALI_PI_285
#define LPDDR4__PI_TXSNR_F2__FLD LPDDR4__DENALI_PI_285__PI_TXSNR_F2

#define LPDDR4__DENALI_PI_285__PI_RESERVED54_MASK                    0x0FFF0000U
#define LPDDR4__DENALI_PI_285__PI_RESERVED54_SHIFT                           16U
#define LPDDR4__DENALI_PI_285__PI_RESERVED54_WIDTH                           12U
#define LPDDR4__PI_RESERVED54__REG DENALI_PI_285
#define LPDDR4__PI_RESERVED54__FLD LPDDR4__DENALI_PI_285__PI_RESERVED54

#define LPDDR4__DENALI_PI_286_READ_MASK                              0x0FFF0FFFU
#define LPDDR4__DENALI_PI_286_WRITE_MASK                             0x0FFF0FFFU
#define LPDDR4__DENALI_PI_286__PI_RESERVED55_MASK                    0x00000FFFU
#define LPDDR4__DENALI_PI_286__PI_RESERVED55_SHIFT                            0U
#define LPDDR4__DENALI_PI_286__PI_RESERVED55_WIDTH                           12U
#define LPDDR4__PI_RESERVED55__REG DENALI_PI_286
#define LPDDR4__PI_RESERVED55__FLD LPDDR4__DENALI_PI_286__PI_RESERVED55

#define LPDDR4__DENALI_PI_286__PI_TZQCAL_F0_MASK                     0x0FFF0000U
#define LPDDR4__DENALI_PI_286__PI_TZQCAL_F0_SHIFT                            16U
#define LPDDR4__DENALI_PI_286__PI_TZQCAL_F0_WIDTH                            12U
#define LPDDR4__PI_TZQCAL_F0__REG DENALI_PI_286
#define LPDDR4__PI_TZQCAL_F0__FLD LPDDR4__DENALI_PI_286__PI_TZQCAL_F0

#define LPDDR4__DENALI_PI_287_READ_MASK                              0x000FFF7FU
#define LPDDR4__DENALI_PI_287_WRITE_MASK                             0x000FFF7FU
#define LPDDR4__DENALI_PI_287__PI_TZQLAT_F0_MASK                     0x0000007FU
#define LPDDR4__DENALI_PI_287__PI_TZQLAT_F0_SHIFT                             0U
#define LPDDR4__DENALI_PI_287__PI_TZQLAT_F0_WIDTH                             7U
#define LPDDR4__PI_TZQLAT_F0__REG DENALI_PI_287
#define LPDDR4__PI_TZQLAT_F0__FLD LPDDR4__DENALI_PI_287__PI_TZQLAT_F0

#define LPDDR4__DENALI_PI_287__PI_RESERVED56_MASK                    0x000FFF00U
#define LPDDR4__DENALI_PI_287__PI_RESERVED56_SHIFT                            8U
#define LPDDR4__DENALI_PI_287__PI_RESERVED56_WIDTH                           12U
#define LPDDR4__PI_RESERVED56__REG DENALI_PI_287
#define LPDDR4__PI_RESERVED56__FLD LPDDR4__DENALI_PI_287__PI_RESERVED56

#define LPDDR4__DENALI_PI_288_READ_MASK                              0x0FFF0FFFU
#define LPDDR4__DENALI_PI_288_WRITE_MASK                             0x0FFF0FFFU
#define LPDDR4__DENALI_PI_288__PI_RESERVED57_MASK                    0x00000FFFU
#define LPDDR4__DENALI_PI_288__PI_RESERVED57_SHIFT                            0U
#define LPDDR4__DENALI_PI_288__PI_RESERVED57_WIDTH                           12U
#define LPDDR4__PI_RESERVED57__REG DENALI_PI_288
#define LPDDR4__PI_RESERVED57__FLD LPDDR4__DENALI_PI_288__PI_RESERVED57

#define LPDDR4__DENALI_PI_288__PI_TZQCAL_F1_MASK                     0x0FFF0000U
#define LPDDR4__DENALI_PI_288__PI_TZQCAL_F1_SHIFT                            16U
#define LPDDR4__DENALI_PI_288__PI_TZQCAL_F1_WIDTH                            12U
#define LPDDR4__PI_TZQCAL_F1__REG DENALI_PI_288
#define LPDDR4__PI_TZQCAL_F1__FLD LPDDR4__DENALI_PI_288__PI_TZQCAL_F1

#define LPDDR4__DENALI_PI_289_READ_MASK                              0x000FFF7FU
#define LPDDR4__DENALI_PI_289_WRITE_MASK                             0x000FFF7FU
#define LPDDR4__DENALI_PI_289__PI_TZQLAT_F1_MASK                     0x0000007FU
#define LPDDR4__DENALI_PI_289__PI_TZQLAT_F1_SHIFT                             0U
#define LPDDR4__DENALI_PI_289__PI_TZQLAT_F1_WIDTH                             7U
#define LPDDR4__PI_TZQLAT_F1__REG DENALI_PI_289
#define LPDDR4__PI_TZQLAT_F1__FLD LPDDR4__DENALI_PI_289__PI_TZQLAT_F1

#define LPDDR4__DENALI_PI_289__PI_RESERVED58_MASK                    0x000FFF00U
#define LPDDR4__DENALI_PI_289__PI_RESERVED58_SHIFT                            8U
#define LPDDR4__DENALI_PI_289__PI_RESERVED58_WIDTH                           12U
#define LPDDR4__PI_RESERVED58__REG DENALI_PI_289
#define LPDDR4__PI_RESERVED58__FLD LPDDR4__DENALI_PI_289__PI_RESERVED58

#define LPDDR4__DENALI_PI_290_READ_MASK                              0x0FFF0FFFU
#define LPDDR4__DENALI_PI_290_WRITE_MASK                             0x0FFF0FFFU
#define LPDDR4__DENALI_PI_290__PI_RESERVED59_MASK                    0x00000FFFU
#define LPDDR4__DENALI_PI_290__PI_RESERVED59_SHIFT                            0U
#define LPDDR4__DENALI_PI_290__PI_RESERVED59_WIDTH                           12U
#define LPDDR4__PI_RESERVED59__REG DENALI_PI_290
#define LPDDR4__PI_RESERVED59__FLD LPDDR4__DENALI_PI_290__PI_RESERVED59

#define LPDDR4__DENALI_PI_290__PI_TZQCAL_F2_MASK                     0x0FFF0000U
#define LPDDR4__DENALI_PI_290__PI_TZQCAL_F2_SHIFT                            16U
#define LPDDR4__DENALI_PI_290__PI_TZQCAL_F2_WIDTH                            12U
#define LPDDR4__PI_TZQCAL_F2__REG DENALI_PI_290
#define LPDDR4__PI_TZQCAL_F2__FLD LPDDR4__DENALI_PI_290__PI_TZQCAL_F2

#define LPDDR4__DENALI_PI_291_READ_MASK                              0x000FFF7FU
#define LPDDR4__DENALI_PI_291_WRITE_MASK                             0x000FFF7FU
#define LPDDR4__DENALI_PI_291__PI_TZQLAT_F2_MASK                     0x0000007FU
#define LPDDR4__DENALI_PI_291__PI_TZQLAT_F2_SHIFT                             0U
#define LPDDR4__DENALI_PI_291__PI_TZQLAT_F2_WIDTH                             7U
#define LPDDR4__PI_TZQLAT_F2__REG DENALI_PI_291
#define LPDDR4__PI_TZQLAT_F2__FLD LPDDR4__DENALI_PI_291__PI_TZQLAT_F2

#define LPDDR4__DENALI_PI_291__PI_RESERVED60_MASK                    0x000FFF00U
#define LPDDR4__DENALI_PI_291__PI_RESERVED60_SHIFT                            8U
#define LPDDR4__DENALI_PI_291__PI_RESERVED60_WIDTH                           12U
#define LPDDR4__PI_RESERVED60__REG DENALI_PI_291
#define LPDDR4__PI_RESERVED60__FLD LPDDR4__DENALI_PI_291__PI_RESERVED60

#define LPDDR4__DENALI_PI_292_READ_MASK                              0x0FFF0FFFU
#define LPDDR4__DENALI_PI_292_WRITE_MASK                             0x0FFF0FFFU
#define LPDDR4__DENALI_PI_292__PI_RESERVED61_MASK                    0x00000FFFU
#define LPDDR4__DENALI_PI_292__PI_RESERVED61_SHIFT                            0U
#define LPDDR4__DENALI_PI_292__PI_RESERVED61_WIDTH                           12U
#define LPDDR4__PI_RESERVED61__REG DENALI_PI_292
#define LPDDR4__PI_RESERVED61__FLD LPDDR4__DENALI_PI_292__PI_RESERVED61

#define LPDDR4__DENALI_PI_292__PI_RESERVED62_MASK                    0x0FFF0000U
#define LPDDR4__DENALI_PI_292__PI_RESERVED62_SHIFT                           16U
#define LPDDR4__DENALI_PI_292__PI_RESERVED62_WIDTH                           12U
#define LPDDR4__PI_RESERVED62__REG DENALI_PI_292
#define LPDDR4__PI_RESERVED62__FLD LPDDR4__DENALI_PI_292__PI_RESERVED62

#define LPDDR4__DENALI_PI_293_READ_MASK                              0x030F0F0FU
#define LPDDR4__DENALI_PI_293_WRITE_MASK                             0x030F0F0FU
#define LPDDR4__DENALI_PI_293__PI_WDQ_OSC_DELTA_INDEX_F0_MASK        0x0000000FU
#define LPDDR4__DENALI_PI_293__PI_WDQ_OSC_DELTA_INDEX_F0_SHIFT                0U
#define LPDDR4__DENALI_PI_293__PI_WDQ_OSC_DELTA_INDEX_F0_WIDTH                4U
#define LPDDR4__PI_WDQ_OSC_DELTA_INDEX_F0__REG DENALI_PI_293
#define LPDDR4__PI_WDQ_OSC_DELTA_INDEX_F0__FLD LPDDR4__DENALI_PI_293__PI_WDQ_OSC_DELTA_INDEX_F0

#define LPDDR4__DENALI_PI_293__PI_WDQ_OSC_DELTA_INDEX_F1_MASK        0x00000F00U
#define LPDDR4__DENALI_PI_293__PI_WDQ_OSC_DELTA_INDEX_F1_SHIFT                8U
#define LPDDR4__DENALI_PI_293__PI_WDQ_OSC_DELTA_INDEX_F1_WIDTH                4U
#define LPDDR4__PI_WDQ_OSC_DELTA_INDEX_F1__REG DENALI_PI_293
#define LPDDR4__PI_WDQ_OSC_DELTA_INDEX_F1__FLD LPDDR4__DENALI_PI_293__PI_WDQ_OSC_DELTA_INDEX_F1

#define LPDDR4__DENALI_PI_293__PI_WDQ_OSC_DELTA_INDEX_F2_MASK        0x000F0000U
#define LPDDR4__DENALI_PI_293__PI_WDQ_OSC_DELTA_INDEX_F2_SHIFT               16U
#define LPDDR4__DENALI_PI_293__PI_WDQ_OSC_DELTA_INDEX_F2_WIDTH                4U
#define LPDDR4__PI_WDQ_OSC_DELTA_INDEX_F2__REG DENALI_PI_293
#define LPDDR4__PI_WDQ_OSC_DELTA_INDEX_F2__FLD LPDDR4__DENALI_PI_293__PI_WDQ_OSC_DELTA_INDEX_F2

#define LPDDR4__DENALI_PI_293__PI_PREAMBLE_SUPPORT_F0_MASK           0x03000000U
#define LPDDR4__DENALI_PI_293__PI_PREAMBLE_SUPPORT_F0_SHIFT                  24U
#define LPDDR4__DENALI_PI_293__PI_PREAMBLE_SUPPORT_F0_WIDTH                   2U
#define LPDDR4__PI_PREAMBLE_SUPPORT_F0__REG DENALI_PI_293
#define LPDDR4__PI_PREAMBLE_SUPPORT_F0__FLD LPDDR4__DENALI_PI_293__PI_PREAMBLE_SUPPORT_F0

#define LPDDR4__DENALI_PI_294_READ_MASK                              0x07070303U
#define LPDDR4__DENALI_PI_294_WRITE_MASK                             0x07070303U
#define LPDDR4__DENALI_PI_294__PI_PREAMBLE_SUPPORT_F1_MASK           0x00000003U
#define LPDDR4__DENALI_PI_294__PI_PREAMBLE_SUPPORT_F1_SHIFT                   0U
#define LPDDR4__DENALI_PI_294__PI_PREAMBLE_SUPPORT_F1_WIDTH                   2U
#define LPDDR4__PI_PREAMBLE_SUPPORT_F1__REG DENALI_PI_294
#define LPDDR4__PI_PREAMBLE_SUPPORT_F1__FLD LPDDR4__DENALI_PI_294__PI_PREAMBLE_SUPPORT_F1

#define LPDDR4__DENALI_PI_294__PI_PREAMBLE_SUPPORT_F2_MASK           0x00000300U
#define LPDDR4__DENALI_PI_294__PI_PREAMBLE_SUPPORT_F2_SHIFT                   8U
#define LPDDR4__DENALI_PI_294__PI_PREAMBLE_SUPPORT_F2_WIDTH                   2U
#define LPDDR4__PI_PREAMBLE_SUPPORT_F2__REG DENALI_PI_294
#define LPDDR4__PI_PREAMBLE_SUPPORT_F2__FLD LPDDR4__DENALI_PI_294__PI_PREAMBLE_SUPPORT_F2

#define LPDDR4__DENALI_PI_294__PI_MEMDATA_RATIO_0_MASK               0x00070000U
#define LPDDR4__DENALI_PI_294__PI_MEMDATA_RATIO_0_SHIFT                      16U
#define LPDDR4__DENALI_PI_294__PI_MEMDATA_RATIO_0_WIDTH                       3U
#define LPDDR4__PI_MEMDATA_RATIO_0__REG DENALI_PI_294
#define LPDDR4__PI_MEMDATA_RATIO_0__FLD LPDDR4__DENALI_PI_294__PI_MEMDATA_RATIO_0

#define LPDDR4__DENALI_PI_294__PI_MEMDATA_RATIO_1_MASK               0x07000000U
#define LPDDR4__DENALI_PI_294__PI_MEMDATA_RATIO_1_SHIFT                      24U
#define LPDDR4__DENALI_PI_294__PI_MEMDATA_RATIO_1_WIDTH                       3U
#define LPDDR4__PI_MEMDATA_RATIO_1__REG DENALI_PI_294
#define LPDDR4__PI_MEMDATA_RATIO_1__FLD LPDDR4__DENALI_PI_294__PI_MEMDATA_RATIO_1

#define LPDDR4__DENALI_PI_295_READ_MASK                              0x0F0F0707U
#define LPDDR4__DENALI_PI_295_WRITE_MASK                             0x0F0F0707U
#define LPDDR4__DENALI_PI_295__PI_MEMDATA_RATIO_2_MASK               0x00000007U
#define LPDDR4__DENALI_PI_295__PI_MEMDATA_RATIO_2_SHIFT                       0U
#define LPDDR4__DENALI_PI_295__PI_MEMDATA_RATIO_2_WIDTH                       3U
#define LPDDR4__PI_MEMDATA_RATIO_2__REG DENALI_PI_295
#define LPDDR4__PI_MEMDATA_RATIO_2__FLD LPDDR4__DENALI_PI_295__PI_MEMDATA_RATIO_2

#define LPDDR4__DENALI_PI_295__PI_MEMDATA_RATIO_3_MASK               0x00000700U
#define LPDDR4__DENALI_PI_295__PI_MEMDATA_RATIO_3_SHIFT                       8U
#define LPDDR4__DENALI_PI_295__PI_MEMDATA_RATIO_3_WIDTH                       3U
#define LPDDR4__PI_MEMDATA_RATIO_3__REG DENALI_PI_295
#define LPDDR4__PI_MEMDATA_RATIO_3__FLD LPDDR4__DENALI_PI_295__PI_MEMDATA_RATIO_3

#define LPDDR4__DENALI_PI_295__PI_ODT_RD_MAP_CS0_MASK                0x000F0000U
#define LPDDR4__DENALI_PI_295__PI_ODT_RD_MAP_CS0_SHIFT                       16U
#define LPDDR4__DENALI_PI_295__PI_ODT_RD_MAP_CS0_WIDTH                        4U
#define LPDDR4__PI_ODT_RD_MAP_CS0__REG DENALI_PI_295
#define LPDDR4__PI_ODT_RD_MAP_CS0__FLD LPDDR4__DENALI_PI_295__PI_ODT_RD_MAP_CS0

#define LPDDR4__DENALI_PI_295__PI_ODT_WR_MAP_CS0_MASK                0x0F000000U
#define LPDDR4__DENALI_PI_295__PI_ODT_WR_MAP_CS0_SHIFT                       24U
#define LPDDR4__DENALI_PI_295__PI_ODT_WR_MAP_CS0_WIDTH                        4U
#define LPDDR4__PI_ODT_WR_MAP_CS0__REG DENALI_PI_295
#define LPDDR4__PI_ODT_WR_MAP_CS0__FLD LPDDR4__DENALI_PI_295__PI_ODT_WR_MAP_CS0

#define LPDDR4__DENALI_PI_296_READ_MASK                              0x0F0F0F0FU
#define LPDDR4__DENALI_PI_296_WRITE_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_PI_296__PI_ODT_RD_MAP_CS1_MASK                0x0000000FU
#define LPDDR4__DENALI_PI_296__PI_ODT_RD_MAP_CS1_SHIFT                        0U
#define LPDDR4__DENALI_PI_296__PI_ODT_RD_MAP_CS1_WIDTH                        4U
#define LPDDR4__PI_ODT_RD_MAP_CS1__REG DENALI_PI_296
#define LPDDR4__PI_ODT_RD_MAP_CS1__FLD LPDDR4__DENALI_PI_296__PI_ODT_RD_MAP_CS1

#define LPDDR4__DENALI_PI_296__PI_ODT_WR_MAP_CS1_MASK                0x00000F00U
#define LPDDR4__DENALI_PI_296__PI_ODT_WR_MAP_CS1_SHIFT                        8U
#define LPDDR4__DENALI_PI_296__PI_ODT_WR_MAP_CS1_WIDTH                        4U
#define LPDDR4__PI_ODT_WR_MAP_CS1__REG DENALI_PI_296
#define LPDDR4__PI_ODT_WR_MAP_CS1__FLD LPDDR4__DENALI_PI_296__PI_ODT_WR_MAP_CS1

#define LPDDR4__DENALI_PI_296__PI_ODT_RD_MAP_CS2_MASK                0x000F0000U
#define LPDDR4__DENALI_PI_296__PI_ODT_RD_MAP_CS2_SHIFT                       16U
#define LPDDR4__DENALI_PI_296__PI_ODT_RD_MAP_CS2_WIDTH                        4U
#define LPDDR4__PI_ODT_RD_MAP_CS2__REG DENALI_PI_296
#define LPDDR4__PI_ODT_RD_MAP_CS2__FLD LPDDR4__DENALI_PI_296__PI_ODT_RD_MAP_CS2

#define LPDDR4__DENALI_PI_296__PI_ODT_WR_MAP_CS2_MASK                0x0F000000U
#define LPDDR4__DENALI_PI_296__PI_ODT_WR_MAP_CS2_SHIFT                       24U
#define LPDDR4__DENALI_PI_296__PI_ODT_WR_MAP_CS2_WIDTH                        4U
#define LPDDR4__PI_ODT_WR_MAP_CS2__REG DENALI_PI_296
#define LPDDR4__PI_ODT_WR_MAP_CS2__FLD LPDDR4__DENALI_PI_296__PI_ODT_WR_MAP_CS2

#define LPDDR4__DENALI_PI_297_READ_MASK                              0x7F7F0F0FU
#define LPDDR4__DENALI_PI_297_WRITE_MASK                             0x7F7F0F0FU
#define LPDDR4__DENALI_PI_297__PI_ODT_RD_MAP_CS3_MASK                0x0000000FU
#define LPDDR4__DENALI_PI_297__PI_ODT_RD_MAP_CS3_SHIFT                        0U
#define LPDDR4__DENALI_PI_297__PI_ODT_RD_MAP_CS3_WIDTH                        4U
#define LPDDR4__PI_ODT_RD_MAP_CS3__REG DENALI_PI_297
#define LPDDR4__PI_ODT_RD_MAP_CS3__FLD LPDDR4__DENALI_PI_297__PI_ODT_RD_MAP_CS3

#define LPDDR4__DENALI_PI_297__PI_ODT_WR_MAP_CS3_MASK                0x00000F00U
#define LPDDR4__DENALI_PI_297__PI_ODT_WR_MAP_CS3_SHIFT                        8U
#define LPDDR4__DENALI_PI_297__PI_ODT_WR_MAP_CS3_WIDTH                        4U
#define LPDDR4__PI_ODT_WR_MAP_CS3__REG DENALI_PI_297
#define LPDDR4__PI_ODT_WR_MAP_CS3__FLD LPDDR4__DENALI_PI_297__PI_ODT_WR_MAP_CS3

#define LPDDR4__DENALI_PI_297__PI_VREF_VAL_DEV0_0_MASK               0x007F0000U
#define LPDDR4__DENALI_PI_297__PI_VREF_VAL_DEV0_0_SHIFT                      16U
#define LPDDR4__DENALI_PI_297__PI_VREF_VAL_DEV0_0_WIDTH                       7U
#define LPDDR4__PI_VREF_VAL_DEV0_0__REG DENALI_PI_297
#define LPDDR4__PI_VREF_VAL_DEV0_0__FLD LPDDR4__DENALI_PI_297__PI_VREF_VAL_DEV0_0

#define LPDDR4__DENALI_PI_297__PI_VREF_VAL_DEV0_1_MASK               0x7F000000U
#define LPDDR4__DENALI_PI_297__PI_VREF_VAL_DEV0_1_SHIFT                      24U
#define LPDDR4__DENALI_PI_297__PI_VREF_VAL_DEV0_1_WIDTH                       7U
#define LPDDR4__PI_VREF_VAL_DEV0_1__REG DENALI_PI_297
#define LPDDR4__PI_VREF_VAL_DEV0_1__FLD LPDDR4__DENALI_PI_297__PI_VREF_VAL_DEV0_1

#define LPDDR4__DENALI_PI_298_READ_MASK                              0x7F7F7F7FU
#define LPDDR4__DENALI_PI_298_WRITE_MASK                             0x7F7F7F7FU
#define LPDDR4__DENALI_PI_298__PI_VREF_VAL_DEV0_2_MASK               0x0000007FU
#define LPDDR4__DENALI_PI_298__PI_VREF_VAL_DEV0_2_SHIFT                       0U
#define LPDDR4__DENALI_PI_298__PI_VREF_VAL_DEV0_2_WIDTH                       7U
#define LPDDR4__PI_VREF_VAL_DEV0_2__REG DENALI_PI_298
#define LPDDR4__PI_VREF_VAL_DEV0_2__FLD LPDDR4__DENALI_PI_298__PI_VREF_VAL_DEV0_2

#define LPDDR4__DENALI_PI_298__PI_VREF_VAL_DEV0_3_MASK               0x00007F00U
#define LPDDR4__DENALI_PI_298__PI_VREF_VAL_DEV0_3_SHIFT                       8U
#define LPDDR4__DENALI_PI_298__PI_VREF_VAL_DEV0_3_WIDTH                       7U
#define LPDDR4__PI_VREF_VAL_DEV0_3__REG DENALI_PI_298
#define LPDDR4__PI_VREF_VAL_DEV0_3__FLD LPDDR4__DENALI_PI_298__PI_VREF_VAL_DEV0_3

#define LPDDR4__DENALI_PI_298__PI_VREF_VAL_DEV1_0_MASK               0x007F0000U
#define LPDDR4__DENALI_PI_298__PI_VREF_VAL_DEV1_0_SHIFT                      16U
#define LPDDR4__DENALI_PI_298__PI_VREF_VAL_DEV1_0_WIDTH                       7U
#define LPDDR4__PI_VREF_VAL_DEV1_0__REG DENALI_PI_298
#define LPDDR4__PI_VREF_VAL_DEV1_0__FLD LPDDR4__DENALI_PI_298__PI_VREF_VAL_DEV1_0

#define LPDDR4__DENALI_PI_298__PI_VREF_VAL_DEV1_1_MASK               0x7F000000U
#define LPDDR4__DENALI_PI_298__PI_VREF_VAL_DEV1_1_SHIFT                      24U
#define LPDDR4__DENALI_PI_298__PI_VREF_VAL_DEV1_1_WIDTH                       7U
#define LPDDR4__PI_VREF_VAL_DEV1_1__REG DENALI_PI_298
#define LPDDR4__PI_VREF_VAL_DEV1_1__FLD LPDDR4__DENALI_PI_298__PI_VREF_VAL_DEV1_1

#define LPDDR4__DENALI_PI_299_READ_MASK                              0x7F7F7F7FU
#define LPDDR4__DENALI_PI_299_WRITE_MASK                             0x7F7F7F7FU
#define LPDDR4__DENALI_PI_299__PI_VREF_VAL_DEV1_2_MASK               0x0000007FU
#define LPDDR4__DENALI_PI_299__PI_VREF_VAL_DEV1_2_SHIFT                       0U
#define LPDDR4__DENALI_PI_299__PI_VREF_VAL_DEV1_2_WIDTH                       7U
#define LPDDR4__PI_VREF_VAL_DEV1_2__REG DENALI_PI_299
#define LPDDR4__PI_VREF_VAL_DEV1_2__FLD LPDDR4__DENALI_PI_299__PI_VREF_VAL_DEV1_2

#define LPDDR4__DENALI_PI_299__PI_VREF_VAL_DEV1_3_MASK               0x00007F00U
#define LPDDR4__DENALI_PI_299__PI_VREF_VAL_DEV1_3_SHIFT                       8U
#define LPDDR4__DENALI_PI_299__PI_VREF_VAL_DEV1_3_WIDTH                       7U
#define LPDDR4__PI_VREF_VAL_DEV1_3__REG DENALI_PI_299
#define LPDDR4__PI_VREF_VAL_DEV1_3__FLD LPDDR4__DENALI_PI_299__PI_VREF_VAL_DEV1_3

#define LPDDR4__DENALI_PI_299__PI_VREF_VAL_DEV2_0_MASK               0x007F0000U
#define LPDDR4__DENALI_PI_299__PI_VREF_VAL_DEV2_0_SHIFT                      16U
#define LPDDR4__DENALI_PI_299__PI_VREF_VAL_DEV2_0_WIDTH                       7U
#define LPDDR4__PI_VREF_VAL_DEV2_0__REG DENALI_PI_299
#define LPDDR4__PI_VREF_VAL_DEV2_0__FLD LPDDR4__DENALI_PI_299__PI_VREF_VAL_DEV2_0

#define LPDDR4__DENALI_PI_299__PI_VREF_VAL_DEV2_1_MASK               0x7F000000U
#define LPDDR4__DENALI_PI_299__PI_VREF_VAL_DEV2_1_SHIFT                      24U
#define LPDDR4__DENALI_PI_299__PI_VREF_VAL_DEV2_1_WIDTH                       7U
#define LPDDR4__PI_VREF_VAL_DEV2_1__REG DENALI_PI_299
#define LPDDR4__PI_VREF_VAL_DEV2_1__FLD LPDDR4__DENALI_PI_299__PI_VREF_VAL_DEV2_1

#define LPDDR4__DENALI_PI_300_READ_MASK                              0x7F7F7F7FU
#define LPDDR4__DENALI_PI_300_WRITE_MASK                             0x7F7F7F7FU
#define LPDDR4__DENALI_PI_300__PI_VREF_VAL_DEV2_2_MASK               0x0000007FU
#define LPDDR4__DENALI_PI_300__PI_VREF_VAL_DEV2_2_SHIFT                       0U
#define LPDDR4__DENALI_PI_300__PI_VREF_VAL_DEV2_2_WIDTH                       7U
#define LPDDR4__PI_VREF_VAL_DEV2_2__REG DENALI_PI_300
#define LPDDR4__PI_VREF_VAL_DEV2_2__FLD LPDDR4__DENALI_PI_300__PI_VREF_VAL_DEV2_2

#define LPDDR4__DENALI_PI_300__PI_VREF_VAL_DEV2_3_MASK               0x00007F00U
#define LPDDR4__DENALI_PI_300__PI_VREF_VAL_DEV2_3_SHIFT                       8U
#define LPDDR4__DENALI_PI_300__PI_VREF_VAL_DEV2_3_WIDTH                       7U
#define LPDDR4__PI_VREF_VAL_DEV2_3__REG DENALI_PI_300
#define LPDDR4__PI_VREF_VAL_DEV2_3__FLD LPDDR4__DENALI_PI_300__PI_VREF_VAL_DEV2_3

#define LPDDR4__DENALI_PI_300__PI_VREF_VAL_DEV3_0_MASK               0x007F0000U
#define LPDDR4__DENALI_PI_300__PI_VREF_VAL_DEV3_0_SHIFT                      16U
#define LPDDR4__DENALI_PI_300__PI_VREF_VAL_DEV3_0_WIDTH                       7U
#define LPDDR4__PI_VREF_VAL_DEV3_0__REG DENALI_PI_300
#define LPDDR4__PI_VREF_VAL_DEV3_0__FLD LPDDR4__DENALI_PI_300__PI_VREF_VAL_DEV3_0

#define LPDDR4__DENALI_PI_300__PI_VREF_VAL_DEV3_1_MASK               0x7F000000U
#define LPDDR4__DENALI_PI_300__PI_VREF_VAL_DEV3_1_SHIFT                      24U
#define LPDDR4__DENALI_PI_300__PI_VREF_VAL_DEV3_1_WIDTH                       7U
#define LPDDR4__PI_VREF_VAL_DEV3_1__REG DENALI_PI_300
#define LPDDR4__PI_VREF_VAL_DEV3_1__FLD LPDDR4__DENALI_PI_300__PI_VREF_VAL_DEV3_1

#define LPDDR4__DENALI_PI_301_READ_MASK                              0x03037F7FU
#define LPDDR4__DENALI_PI_301_WRITE_MASK                             0x03037F7FU
#define LPDDR4__DENALI_PI_301__PI_VREF_VAL_DEV3_2_MASK               0x0000007FU
#define LPDDR4__DENALI_PI_301__PI_VREF_VAL_DEV3_2_SHIFT                       0U
#define LPDDR4__DENALI_PI_301__PI_VREF_VAL_DEV3_2_WIDTH                       7U
#define LPDDR4__PI_VREF_VAL_DEV3_2__REG DENALI_PI_301
#define LPDDR4__PI_VREF_VAL_DEV3_2__FLD LPDDR4__DENALI_PI_301__PI_VREF_VAL_DEV3_2

#define LPDDR4__DENALI_PI_301__PI_VREF_VAL_DEV3_3_MASK               0x00007F00U
#define LPDDR4__DENALI_PI_301__PI_VREF_VAL_DEV3_3_SHIFT                       8U
#define LPDDR4__DENALI_PI_301__PI_VREF_VAL_DEV3_3_WIDTH                       7U
#define LPDDR4__PI_VREF_VAL_DEV3_3__REG DENALI_PI_301
#define LPDDR4__PI_VREF_VAL_DEV3_3__FLD LPDDR4__DENALI_PI_301__PI_VREF_VAL_DEV3_3

#define LPDDR4__DENALI_PI_301__PI_SLICE_PER_DEV_0_MASK               0x00030000U
#define LPDDR4__DENALI_PI_301__PI_SLICE_PER_DEV_0_SHIFT                      16U
#define LPDDR4__DENALI_PI_301__PI_SLICE_PER_DEV_0_WIDTH                       2U
#define LPDDR4__PI_SLICE_PER_DEV_0__REG DENALI_PI_301
#define LPDDR4__PI_SLICE_PER_DEV_0__FLD LPDDR4__DENALI_PI_301__PI_SLICE_PER_DEV_0

#define LPDDR4__DENALI_PI_301__PI_SLICE_PER_DEV_1_MASK               0x03000000U
#define LPDDR4__DENALI_PI_301__PI_SLICE_PER_DEV_1_SHIFT                      24U
#define LPDDR4__DENALI_PI_301__PI_SLICE_PER_DEV_1_WIDTH                       2U
#define LPDDR4__PI_SLICE_PER_DEV_1__REG DENALI_PI_301
#define LPDDR4__PI_SLICE_PER_DEV_1__FLD LPDDR4__DENALI_PI_301__PI_SLICE_PER_DEV_1

#define LPDDR4__DENALI_PI_302_READ_MASK                              0x3F3F0303U
#define LPDDR4__DENALI_PI_302_WRITE_MASK                             0x3F3F0303U
#define LPDDR4__DENALI_PI_302__PI_SLICE_PER_DEV_2_MASK               0x00000003U
#define LPDDR4__DENALI_PI_302__PI_SLICE_PER_DEV_2_SHIFT                       0U
#define LPDDR4__DENALI_PI_302__PI_SLICE_PER_DEV_2_WIDTH                       2U
#define LPDDR4__PI_SLICE_PER_DEV_2__REG DENALI_PI_302
#define LPDDR4__PI_SLICE_PER_DEV_2__FLD LPDDR4__DENALI_PI_302__PI_SLICE_PER_DEV_2

#define LPDDR4__DENALI_PI_302__PI_SLICE_PER_DEV_3_MASK               0x00000300U
#define LPDDR4__DENALI_PI_302__PI_SLICE_PER_DEV_3_SHIFT                       8U
#define LPDDR4__DENALI_PI_302__PI_SLICE_PER_DEV_3_WIDTH                       2U
#define LPDDR4__PI_SLICE_PER_DEV_3__REG DENALI_PI_302
#define LPDDR4__PI_SLICE_PER_DEV_3__FLD LPDDR4__DENALI_PI_302__PI_SLICE_PER_DEV_3

#define LPDDR4__DENALI_PI_302__PI_MR6_VREF_0_0_MASK                  0x003F0000U
#define LPDDR4__DENALI_PI_302__PI_MR6_VREF_0_0_SHIFT                         16U
#define LPDDR4__DENALI_PI_302__PI_MR6_VREF_0_0_WIDTH                          6U
#define LPDDR4__PI_MR6_VREF_0_0__REG DENALI_PI_302
#define LPDDR4__PI_MR6_VREF_0_0__FLD LPDDR4__DENALI_PI_302__PI_MR6_VREF_0_0

#define LPDDR4__DENALI_PI_302__PI_MR6_VREF_0_1_MASK                  0x3F000000U
#define LPDDR4__DENALI_PI_302__PI_MR6_VREF_0_1_SHIFT                         24U
#define LPDDR4__DENALI_PI_302__PI_MR6_VREF_0_1_WIDTH                          6U
#define LPDDR4__PI_MR6_VREF_0_1__REG DENALI_PI_302
#define LPDDR4__PI_MR6_VREF_0_1__FLD LPDDR4__DENALI_PI_302__PI_MR6_VREF_0_1

#define LPDDR4__DENALI_PI_303_READ_MASK                              0x3F3F3F3FU
#define LPDDR4__DENALI_PI_303_WRITE_MASK                             0x3F3F3F3FU
#define LPDDR4__DENALI_PI_303__PI_MR6_VREF_0_2_MASK                  0x0000003FU
#define LPDDR4__DENALI_PI_303__PI_MR6_VREF_0_2_SHIFT                          0U
#define LPDDR4__DENALI_PI_303__PI_MR6_VREF_0_2_WIDTH                          6U
#define LPDDR4__PI_MR6_VREF_0_2__REG DENALI_PI_303
#define LPDDR4__PI_MR6_VREF_0_2__FLD LPDDR4__DENALI_PI_303__PI_MR6_VREF_0_2

#define LPDDR4__DENALI_PI_303__PI_MR6_VREF_0_3_MASK                  0x00003F00U
#define LPDDR4__DENALI_PI_303__PI_MR6_VREF_0_3_SHIFT                          8U
#define LPDDR4__DENALI_PI_303__PI_MR6_VREF_0_3_WIDTH                          6U
#define LPDDR4__PI_MR6_VREF_0_3__REG DENALI_PI_303
#define LPDDR4__PI_MR6_VREF_0_3__FLD LPDDR4__DENALI_PI_303__PI_MR6_VREF_0_3

#define LPDDR4__DENALI_PI_303__PI_MR6_VREF_1_0_MASK                  0x003F0000U
#define LPDDR4__DENALI_PI_303__PI_MR6_VREF_1_0_SHIFT                         16U
#define LPDDR4__DENALI_PI_303__PI_MR6_VREF_1_0_WIDTH                          6U
#define LPDDR4__PI_MR6_VREF_1_0__REG DENALI_PI_303
#define LPDDR4__PI_MR6_VREF_1_0__FLD LPDDR4__DENALI_PI_303__PI_MR6_VREF_1_0

#define LPDDR4__DENALI_PI_303__PI_MR6_VREF_1_1_MASK                  0x3F000000U
#define LPDDR4__DENALI_PI_303__PI_MR6_VREF_1_1_SHIFT                         24U
#define LPDDR4__DENALI_PI_303__PI_MR6_VREF_1_1_WIDTH                          6U
#define LPDDR4__PI_MR6_VREF_1_1__REG DENALI_PI_303
#define LPDDR4__PI_MR6_VREF_1_1__FLD LPDDR4__DENALI_PI_303__PI_MR6_VREF_1_1

#define LPDDR4__DENALI_PI_304_READ_MASK                              0x3F3F3F3FU
#define LPDDR4__DENALI_PI_304_WRITE_MASK                             0x3F3F3F3FU
#define LPDDR4__DENALI_PI_304__PI_MR6_VREF_1_2_MASK                  0x0000003FU
#define LPDDR4__DENALI_PI_304__PI_MR6_VREF_1_2_SHIFT                          0U
#define LPDDR4__DENALI_PI_304__PI_MR6_VREF_1_2_WIDTH                          6U
#define LPDDR4__PI_MR6_VREF_1_2__REG DENALI_PI_304
#define LPDDR4__PI_MR6_VREF_1_2__FLD LPDDR4__DENALI_PI_304__PI_MR6_VREF_1_2

#define LPDDR4__DENALI_PI_304__PI_MR6_VREF_1_3_MASK                  0x00003F00U
#define LPDDR4__DENALI_PI_304__PI_MR6_VREF_1_3_SHIFT                          8U
#define LPDDR4__DENALI_PI_304__PI_MR6_VREF_1_3_WIDTH                          6U
#define LPDDR4__PI_MR6_VREF_1_3__REG DENALI_PI_304
#define LPDDR4__PI_MR6_VREF_1_3__FLD LPDDR4__DENALI_PI_304__PI_MR6_VREF_1_3

#define LPDDR4__DENALI_PI_304__PI_MR6_VREF_2_0_MASK                  0x003F0000U
#define LPDDR4__DENALI_PI_304__PI_MR6_VREF_2_0_SHIFT                         16U
#define LPDDR4__DENALI_PI_304__PI_MR6_VREF_2_0_WIDTH                          6U
#define LPDDR4__PI_MR6_VREF_2_0__REG DENALI_PI_304
#define LPDDR4__PI_MR6_VREF_2_0__FLD LPDDR4__DENALI_PI_304__PI_MR6_VREF_2_0

#define LPDDR4__DENALI_PI_304__PI_MR6_VREF_2_1_MASK                  0x3F000000U
#define LPDDR4__DENALI_PI_304__PI_MR6_VREF_2_1_SHIFT                         24U
#define LPDDR4__DENALI_PI_304__PI_MR6_VREF_2_1_WIDTH                          6U
#define LPDDR4__PI_MR6_VREF_2_1__REG DENALI_PI_304
#define LPDDR4__PI_MR6_VREF_2_1__FLD LPDDR4__DENALI_PI_304__PI_MR6_VREF_2_1

#define LPDDR4__DENALI_PI_305_READ_MASK                              0x3F3F3F3FU
#define LPDDR4__DENALI_PI_305_WRITE_MASK                             0x3F3F3F3FU
#define LPDDR4__DENALI_PI_305__PI_MR6_VREF_2_2_MASK                  0x0000003FU
#define LPDDR4__DENALI_PI_305__PI_MR6_VREF_2_2_SHIFT                          0U
#define LPDDR4__DENALI_PI_305__PI_MR6_VREF_2_2_WIDTH                          6U
#define LPDDR4__PI_MR6_VREF_2_2__REG DENALI_PI_305
#define LPDDR4__PI_MR6_VREF_2_2__FLD LPDDR4__DENALI_PI_305__PI_MR6_VREF_2_2

#define LPDDR4__DENALI_PI_305__PI_MR6_VREF_2_3_MASK                  0x00003F00U
#define LPDDR4__DENALI_PI_305__PI_MR6_VREF_2_3_SHIFT                          8U
#define LPDDR4__DENALI_PI_305__PI_MR6_VREF_2_3_WIDTH                          6U
#define LPDDR4__PI_MR6_VREF_2_3__REG DENALI_PI_305
#define LPDDR4__PI_MR6_VREF_2_3__FLD LPDDR4__DENALI_PI_305__PI_MR6_VREF_2_3

#define LPDDR4__DENALI_PI_305__PI_MR6_VREF_3_0_MASK                  0x003F0000U
#define LPDDR4__DENALI_PI_305__PI_MR6_VREF_3_0_SHIFT                         16U
#define LPDDR4__DENALI_PI_305__PI_MR6_VREF_3_0_WIDTH                          6U
#define LPDDR4__PI_MR6_VREF_3_0__REG DENALI_PI_305
#define LPDDR4__PI_MR6_VREF_3_0__FLD LPDDR4__DENALI_PI_305__PI_MR6_VREF_3_0

#define LPDDR4__DENALI_PI_305__PI_MR6_VREF_3_1_MASK                  0x3F000000U
#define LPDDR4__DENALI_PI_305__PI_MR6_VREF_3_1_SHIFT                         24U
#define LPDDR4__DENALI_PI_305__PI_MR6_VREF_3_1_WIDTH                          6U
#define LPDDR4__PI_MR6_VREF_3_1__REG DENALI_PI_305
#define LPDDR4__PI_MR6_VREF_3_1__FLD LPDDR4__DENALI_PI_305__PI_MR6_VREF_3_1

#define LPDDR4__DENALI_PI_306_READ_MASK                              0xFFFF3F3FU
#define LPDDR4__DENALI_PI_306_WRITE_MASK                             0xFFFF3F3FU
#define LPDDR4__DENALI_PI_306__PI_MR6_VREF_3_2_MASK                  0x0000003FU
#define LPDDR4__DENALI_PI_306__PI_MR6_VREF_3_2_SHIFT                          0U
#define LPDDR4__DENALI_PI_306__PI_MR6_VREF_3_2_WIDTH                          6U
#define LPDDR4__PI_MR6_VREF_3_2__REG DENALI_PI_306
#define LPDDR4__PI_MR6_VREF_3_2__FLD LPDDR4__DENALI_PI_306__PI_MR6_VREF_3_2

#define LPDDR4__DENALI_PI_306__PI_MR6_VREF_3_3_MASK                  0x00003F00U
#define LPDDR4__DENALI_PI_306__PI_MR6_VREF_3_3_SHIFT                          8U
#define LPDDR4__DENALI_PI_306__PI_MR6_VREF_3_3_WIDTH                          6U
#define LPDDR4__PI_MR6_VREF_3_3__REG DENALI_PI_306
#define LPDDR4__PI_MR6_VREF_3_3__FLD LPDDR4__DENALI_PI_306__PI_MR6_VREF_3_3

#define LPDDR4__DENALI_PI_306__PI_MR13_DATA_0_MASK                   0x00FF0000U
#define LPDDR4__DENALI_PI_306__PI_MR13_DATA_0_SHIFT                          16U
#define LPDDR4__DENALI_PI_306__PI_MR13_DATA_0_WIDTH                           8U
#define LPDDR4__PI_MR13_DATA_0__REG DENALI_PI_306
#define LPDDR4__PI_MR13_DATA_0__FLD LPDDR4__DENALI_PI_306__PI_MR13_DATA_0

#define LPDDR4__DENALI_PI_306__PI_MR15_DATA_0_MASK                   0xFF000000U
#define LPDDR4__DENALI_PI_306__PI_MR15_DATA_0_SHIFT                          24U
#define LPDDR4__DENALI_PI_306__PI_MR15_DATA_0_WIDTH                           8U
#define LPDDR4__PI_MR15_DATA_0__REG DENALI_PI_306
#define LPDDR4__PI_MR15_DATA_0__FLD LPDDR4__DENALI_PI_306__PI_MR15_DATA_0

#define LPDDR4__DENALI_PI_307_READ_MASK                              0x00FFFFFFU
#define LPDDR4__DENALI_PI_307_WRITE_MASK                             0x00FFFFFFU
#define LPDDR4__DENALI_PI_307__PI_MR16_DATA_0_MASK                   0x000000FFU
#define LPDDR4__DENALI_PI_307__PI_MR16_DATA_0_SHIFT                           0U
#define LPDDR4__DENALI_PI_307__PI_MR16_DATA_0_WIDTH                           8U
#define LPDDR4__PI_MR16_DATA_0__REG DENALI_PI_307
#define LPDDR4__PI_MR16_DATA_0__FLD LPDDR4__DENALI_PI_307__PI_MR16_DATA_0

#define LPDDR4__DENALI_PI_307__PI_MR17_DATA_0_MASK                   0x0000FF00U
#define LPDDR4__DENALI_PI_307__PI_MR17_DATA_0_SHIFT                           8U
#define LPDDR4__DENALI_PI_307__PI_MR17_DATA_0_WIDTH                           8U
#define LPDDR4__PI_MR17_DATA_0__REG DENALI_PI_307
#define LPDDR4__PI_MR17_DATA_0__FLD LPDDR4__DENALI_PI_307__PI_MR17_DATA_0

#define LPDDR4__DENALI_PI_307__PI_MR20_DATA_0_MASK                   0x00FF0000U
#define LPDDR4__DENALI_PI_307__PI_MR20_DATA_0_SHIFT                          16U
#define LPDDR4__DENALI_PI_307__PI_MR20_DATA_0_WIDTH                           8U
#define LPDDR4__PI_MR20_DATA_0__REG DENALI_PI_307
#define LPDDR4__PI_MR20_DATA_0__FLD LPDDR4__DENALI_PI_307__PI_MR20_DATA_0

#define LPDDR4__DENALI_PI_308_READ_MASK                              0xFF01FFFFU
#define LPDDR4__DENALI_PI_308_WRITE_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_PI_308__PI_MR32_DATA_0_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_PI_308__PI_MR32_DATA_0_SHIFT                           0U
#define LPDDR4__DENALI_PI_308__PI_MR32_DATA_0_WIDTH                          17U
#define LPDDR4__PI_MR32_DATA_0__REG DENALI_PI_308
#define LPDDR4__PI_MR32_DATA_0__FLD LPDDR4__DENALI_PI_308__PI_MR32_DATA_0

#define LPDDR4__DENALI_PI_308__PI_MR40_DATA_0_MASK                   0xFF000000U
#define LPDDR4__DENALI_PI_308__PI_MR40_DATA_0_SHIFT                          24U
#define LPDDR4__DENALI_PI_308__PI_MR40_DATA_0_WIDTH                           8U
#define LPDDR4__PI_MR40_DATA_0__REG DENALI_PI_308
#define LPDDR4__PI_MR40_DATA_0__FLD LPDDR4__DENALI_PI_308__PI_MR40_DATA_0

#define LPDDR4__DENALI_PI_309_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_309_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_309__PI_MR13_DATA_1_MASK                   0x000000FFU
#define LPDDR4__DENALI_PI_309__PI_MR13_DATA_1_SHIFT                           0U
#define LPDDR4__DENALI_PI_309__PI_MR13_DATA_1_WIDTH                           8U
#define LPDDR4__PI_MR13_DATA_1__REG DENALI_PI_309
#define LPDDR4__PI_MR13_DATA_1__FLD LPDDR4__DENALI_PI_309__PI_MR13_DATA_1

#define LPDDR4__DENALI_PI_309__PI_MR15_DATA_1_MASK                   0x0000FF00U
#define LPDDR4__DENALI_PI_309__PI_MR15_DATA_1_SHIFT                           8U
#define LPDDR4__DENALI_PI_309__PI_MR15_DATA_1_WIDTH                           8U
#define LPDDR4__PI_MR15_DATA_1__REG DENALI_PI_309
#define LPDDR4__PI_MR15_DATA_1__FLD LPDDR4__DENALI_PI_309__PI_MR15_DATA_1

#define LPDDR4__DENALI_PI_309__PI_MR16_DATA_1_MASK                   0x00FF0000U
#define LPDDR4__DENALI_PI_309__PI_MR16_DATA_1_SHIFT                          16U
#define LPDDR4__DENALI_PI_309__PI_MR16_DATA_1_WIDTH                           8U
#define LPDDR4__PI_MR16_DATA_1__REG DENALI_PI_309
#define LPDDR4__PI_MR16_DATA_1__FLD LPDDR4__DENALI_PI_309__PI_MR16_DATA_1

#define LPDDR4__DENALI_PI_309__PI_MR17_DATA_1_MASK                   0xFF000000U
#define LPDDR4__DENALI_PI_309__PI_MR17_DATA_1_SHIFT                          24U
#define LPDDR4__DENALI_PI_309__PI_MR17_DATA_1_WIDTH                           8U
#define LPDDR4__PI_MR17_DATA_1__REG DENALI_PI_309
#define LPDDR4__PI_MR17_DATA_1__FLD LPDDR4__DENALI_PI_309__PI_MR17_DATA_1

#define LPDDR4__DENALI_PI_310_READ_MASK                              0x01FFFFFFU
#define LPDDR4__DENALI_PI_310_WRITE_MASK                             0x01FFFFFFU
#define LPDDR4__DENALI_PI_310__PI_MR20_DATA_1_MASK                   0x000000FFU
#define LPDDR4__DENALI_PI_310__PI_MR20_DATA_1_SHIFT                           0U
#define LPDDR4__DENALI_PI_310__PI_MR20_DATA_1_WIDTH                           8U
#define LPDDR4__PI_MR20_DATA_1__REG DENALI_PI_310
#define LPDDR4__PI_MR20_DATA_1__FLD LPDDR4__DENALI_PI_310__PI_MR20_DATA_1

#define LPDDR4__DENALI_PI_310__PI_MR32_DATA_1_MASK                   0x01FFFF00U
#define LPDDR4__DENALI_PI_310__PI_MR32_DATA_1_SHIFT                           8U
#define LPDDR4__DENALI_PI_310__PI_MR32_DATA_1_WIDTH                          17U
#define LPDDR4__PI_MR32_DATA_1__REG DENALI_PI_310
#define LPDDR4__PI_MR32_DATA_1__FLD LPDDR4__DENALI_PI_310__PI_MR32_DATA_1

#define LPDDR4__DENALI_PI_311_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_311_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_311__PI_MR40_DATA_1_MASK                   0x000000FFU
#define LPDDR4__DENALI_PI_311__PI_MR40_DATA_1_SHIFT                           0U
#define LPDDR4__DENALI_PI_311__PI_MR40_DATA_1_WIDTH                           8U
#define LPDDR4__PI_MR40_DATA_1__REG DENALI_PI_311
#define LPDDR4__PI_MR40_DATA_1__FLD LPDDR4__DENALI_PI_311__PI_MR40_DATA_1

#define LPDDR4__DENALI_PI_311__PI_MR13_DATA_2_MASK                   0x0000FF00U
#define LPDDR4__DENALI_PI_311__PI_MR13_DATA_2_SHIFT                           8U
#define LPDDR4__DENALI_PI_311__PI_MR13_DATA_2_WIDTH                           8U
#define LPDDR4__PI_MR13_DATA_2__REG DENALI_PI_311
#define LPDDR4__PI_MR13_DATA_2__FLD LPDDR4__DENALI_PI_311__PI_MR13_DATA_2

#define LPDDR4__DENALI_PI_311__PI_MR15_DATA_2_MASK                   0x00FF0000U
#define LPDDR4__DENALI_PI_311__PI_MR15_DATA_2_SHIFT                          16U
#define LPDDR4__DENALI_PI_311__PI_MR15_DATA_2_WIDTH                           8U
#define LPDDR4__PI_MR15_DATA_2__REG DENALI_PI_311
#define LPDDR4__PI_MR15_DATA_2__FLD LPDDR4__DENALI_PI_311__PI_MR15_DATA_2

#define LPDDR4__DENALI_PI_311__PI_MR16_DATA_2_MASK                   0xFF000000U
#define LPDDR4__DENALI_PI_311__PI_MR16_DATA_2_SHIFT                          24U
#define LPDDR4__DENALI_PI_311__PI_MR16_DATA_2_WIDTH                           8U
#define LPDDR4__PI_MR16_DATA_2__REG DENALI_PI_311
#define LPDDR4__PI_MR16_DATA_2__FLD LPDDR4__DENALI_PI_311__PI_MR16_DATA_2

#define LPDDR4__DENALI_PI_312_READ_MASK                              0x0000FFFFU
#define LPDDR4__DENALI_PI_312_WRITE_MASK                             0x0000FFFFU
#define LPDDR4__DENALI_PI_312__PI_MR17_DATA_2_MASK                   0x000000FFU
#define LPDDR4__DENALI_PI_312__PI_MR17_DATA_2_SHIFT                           0U
#define LPDDR4__DENALI_PI_312__PI_MR17_DATA_2_WIDTH                           8U
#define LPDDR4__PI_MR17_DATA_2__REG DENALI_PI_312
#define LPDDR4__PI_MR17_DATA_2__FLD LPDDR4__DENALI_PI_312__PI_MR17_DATA_2

#define LPDDR4__DENALI_PI_312__PI_MR20_DATA_2_MASK                   0x0000FF00U
#define LPDDR4__DENALI_PI_312__PI_MR20_DATA_2_SHIFT                           8U
#define LPDDR4__DENALI_PI_312__PI_MR20_DATA_2_WIDTH                           8U
#define LPDDR4__PI_MR20_DATA_2__REG DENALI_PI_312
#define LPDDR4__PI_MR20_DATA_2__FLD LPDDR4__DENALI_PI_312__PI_MR20_DATA_2

#define LPDDR4__DENALI_PI_313_READ_MASK                              0xFF01FFFFU
#define LPDDR4__DENALI_PI_313_WRITE_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_PI_313__PI_MR32_DATA_2_MASK                   0x0001FFFFU
#define LPDDR4__DENALI_PI_313__PI_MR32_DATA_2_SHIFT                           0U
#define LPDDR4__DENALI_PI_313__PI_MR32_DATA_2_WIDTH                          17U
#define LPDDR4__PI_MR32_DATA_2__REG DENALI_PI_313
#define LPDDR4__PI_MR32_DATA_2__FLD LPDDR4__DENALI_PI_313__PI_MR32_DATA_2

#define LPDDR4__DENALI_PI_313__PI_MR40_DATA_2_MASK                   0xFF000000U
#define LPDDR4__DENALI_PI_313__PI_MR40_DATA_2_SHIFT                          24U
#define LPDDR4__DENALI_PI_313__PI_MR40_DATA_2_WIDTH                           8U
#define LPDDR4__PI_MR40_DATA_2__REG DENALI_PI_313
#define LPDDR4__PI_MR40_DATA_2__FLD LPDDR4__DENALI_PI_313__PI_MR40_DATA_2

#define LPDDR4__DENALI_PI_314_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_314_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_314__PI_MR13_DATA_3_MASK                   0x000000FFU
#define LPDDR4__DENALI_PI_314__PI_MR13_DATA_3_SHIFT                           0U
#define LPDDR4__DENALI_PI_314__PI_MR13_DATA_3_WIDTH                           8U
#define LPDDR4__PI_MR13_DATA_3__REG DENALI_PI_314
#define LPDDR4__PI_MR13_DATA_3__FLD LPDDR4__DENALI_PI_314__PI_MR13_DATA_3

#define LPDDR4__DENALI_PI_314__PI_MR15_DATA_3_MASK                   0x0000FF00U
#define LPDDR4__DENALI_PI_314__PI_MR15_DATA_3_SHIFT                           8U
#define LPDDR4__DENALI_PI_314__PI_MR15_DATA_3_WIDTH                           8U
#define LPDDR4__PI_MR15_DATA_3__REG DENALI_PI_314
#define LPDDR4__PI_MR15_DATA_3__FLD LPDDR4__DENALI_PI_314__PI_MR15_DATA_3

#define LPDDR4__DENALI_PI_314__PI_MR16_DATA_3_MASK                   0x00FF0000U
#define LPDDR4__DENALI_PI_314__PI_MR16_DATA_3_SHIFT                          16U
#define LPDDR4__DENALI_PI_314__PI_MR16_DATA_3_WIDTH                           8U
#define LPDDR4__PI_MR16_DATA_3__REG DENALI_PI_314
#define LPDDR4__PI_MR16_DATA_3__FLD LPDDR4__DENALI_PI_314__PI_MR16_DATA_3

#define LPDDR4__DENALI_PI_314__PI_MR17_DATA_3_MASK                   0xFF000000U
#define LPDDR4__DENALI_PI_314__PI_MR17_DATA_3_SHIFT                          24U
#define LPDDR4__DENALI_PI_314__PI_MR17_DATA_3_WIDTH                           8U
#define LPDDR4__PI_MR17_DATA_3__REG DENALI_PI_314
#define LPDDR4__PI_MR17_DATA_3__FLD LPDDR4__DENALI_PI_314__PI_MR17_DATA_3

#define LPDDR4__DENALI_PI_315_READ_MASK                              0x01FFFFFFU
#define LPDDR4__DENALI_PI_315_WRITE_MASK                             0x01FFFFFFU
#define LPDDR4__DENALI_PI_315__PI_MR20_DATA_3_MASK                   0x000000FFU
#define LPDDR4__DENALI_PI_315__PI_MR20_DATA_3_SHIFT                           0U
#define LPDDR4__DENALI_PI_315__PI_MR20_DATA_3_WIDTH                           8U
#define LPDDR4__PI_MR20_DATA_3__REG DENALI_PI_315
#define LPDDR4__PI_MR20_DATA_3__FLD LPDDR4__DENALI_PI_315__PI_MR20_DATA_3

#define LPDDR4__DENALI_PI_315__PI_MR32_DATA_3_MASK                   0x01FFFF00U
#define LPDDR4__DENALI_PI_315__PI_MR32_DATA_3_SHIFT                           8U
#define LPDDR4__DENALI_PI_315__PI_MR32_DATA_3_WIDTH                          17U
#define LPDDR4__PI_MR32_DATA_3__REG DENALI_PI_315
#define LPDDR4__PI_MR32_DATA_3__FLD LPDDR4__DENALI_PI_315__PI_MR32_DATA_3

#define LPDDR4__DENALI_PI_316_READ_MASK                              0x1F1F1FFFU
#define LPDDR4__DENALI_PI_316_WRITE_MASK                             0x1F1F1FFFU
#define LPDDR4__DENALI_PI_316__PI_MR40_DATA_3_MASK                   0x000000FFU
#define LPDDR4__DENALI_PI_316__PI_MR40_DATA_3_SHIFT                           0U
#define LPDDR4__DENALI_PI_316__PI_MR40_DATA_3_WIDTH                           8U
#define LPDDR4__PI_MR40_DATA_3__REG DENALI_PI_316
#define LPDDR4__PI_MR40_DATA_3__FLD LPDDR4__DENALI_PI_316__PI_MR40_DATA_3

#define LPDDR4__DENALI_PI_316__PI_CKE_MUX_0_MASK                     0x00001F00U
#define LPDDR4__DENALI_PI_316__PI_CKE_MUX_0_SHIFT                             8U
#define LPDDR4__DENALI_PI_316__PI_CKE_MUX_0_WIDTH                             5U
#define LPDDR4__PI_CKE_MUX_0__REG DENALI_PI_316
#define LPDDR4__PI_CKE_MUX_0__FLD LPDDR4__DENALI_PI_316__PI_CKE_MUX_0

#define LPDDR4__DENALI_PI_316__PI_CKE_MUX_1_MASK                     0x001F0000U
#define LPDDR4__DENALI_PI_316__PI_CKE_MUX_1_SHIFT                            16U
#define LPDDR4__DENALI_PI_316__PI_CKE_MUX_1_WIDTH                             5U
#define LPDDR4__PI_CKE_MUX_1__REG DENALI_PI_316
#define LPDDR4__PI_CKE_MUX_1__FLD LPDDR4__DENALI_PI_316__PI_CKE_MUX_1

#define LPDDR4__DENALI_PI_316__PI_CKE_MUX_2_MASK                     0x1F000000U
#define LPDDR4__DENALI_PI_316__PI_CKE_MUX_2_SHIFT                            24U
#define LPDDR4__DENALI_PI_316__PI_CKE_MUX_2_WIDTH                             5U
#define LPDDR4__PI_CKE_MUX_2__REG DENALI_PI_316
#define LPDDR4__PI_CKE_MUX_2__FLD LPDDR4__DENALI_PI_316__PI_CKE_MUX_2

#define LPDDR4__DENALI_PI_317_READ_MASK                              0x1F1F1F1FU
#define LPDDR4__DENALI_PI_317_WRITE_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_PI_317__PI_CKE_MUX_3_MASK                     0x0000001FU
#define LPDDR4__DENALI_PI_317__PI_CKE_MUX_3_SHIFT                             0U
#define LPDDR4__DENALI_PI_317__PI_CKE_MUX_3_WIDTH                             5U
#define LPDDR4__PI_CKE_MUX_3__REG DENALI_PI_317
#define LPDDR4__PI_CKE_MUX_3__FLD LPDDR4__DENALI_PI_317__PI_CKE_MUX_3

#define LPDDR4__DENALI_PI_317__PI_CS_MUX_0_MASK                      0x00001F00U
#define LPDDR4__DENALI_PI_317__PI_CS_MUX_0_SHIFT                              8U
#define LPDDR4__DENALI_PI_317__PI_CS_MUX_0_WIDTH                              5U
#define LPDDR4__PI_CS_MUX_0__REG DENALI_PI_317
#define LPDDR4__PI_CS_MUX_0__FLD LPDDR4__DENALI_PI_317__PI_CS_MUX_0

#define LPDDR4__DENALI_PI_317__PI_CS_MUX_1_MASK                      0x001F0000U
#define LPDDR4__DENALI_PI_317__PI_CS_MUX_1_SHIFT                             16U
#define LPDDR4__DENALI_PI_317__PI_CS_MUX_1_WIDTH                              5U
#define LPDDR4__PI_CS_MUX_1__REG DENALI_PI_317
#define LPDDR4__PI_CS_MUX_1__FLD LPDDR4__DENALI_PI_317__PI_CS_MUX_1

#define LPDDR4__DENALI_PI_317__PI_CS_MUX_2_MASK                      0x1F000000U
#define LPDDR4__DENALI_PI_317__PI_CS_MUX_2_SHIFT                             24U
#define LPDDR4__DENALI_PI_317__PI_CS_MUX_2_WIDTH                              5U
#define LPDDR4__PI_CS_MUX_2__REG DENALI_PI_317
#define LPDDR4__PI_CS_MUX_2__FLD LPDDR4__DENALI_PI_317__PI_CS_MUX_2

#define LPDDR4__DENALI_PI_318_READ_MASK                              0x1F1F1F1FU
#define LPDDR4__DENALI_PI_318_WRITE_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_PI_318__PI_CS_MUX_3_MASK                      0x0000001FU
#define LPDDR4__DENALI_PI_318__PI_CS_MUX_3_SHIFT                              0U
#define LPDDR4__DENALI_PI_318__PI_CS_MUX_3_WIDTH                              5U
#define LPDDR4__PI_CS_MUX_3__REG DENALI_PI_318
#define LPDDR4__PI_CS_MUX_3__FLD LPDDR4__DENALI_PI_318__PI_CS_MUX_3

#define LPDDR4__DENALI_PI_318__PI_ODT_MUX_0_MASK                     0x00001F00U
#define LPDDR4__DENALI_PI_318__PI_ODT_MUX_0_SHIFT                             8U
#define LPDDR4__DENALI_PI_318__PI_ODT_MUX_0_WIDTH                             5U
#define LPDDR4__PI_ODT_MUX_0__REG DENALI_PI_318
#define LPDDR4__PI_ODT_MUX_0__FLD LPDDR4__DENALI_PI_318__PI_ODT_MUX_0

#define LPDDR4__DENALI_PI_318__PI_ODT_MUX_1_MASK                     0x001F0000U
#define LPDDR4__DENALI_PI_318__PI_ODT_MUX_1_SHIFT                            16U
#define LPDDR4__DENALI_PI_318__PI_ODT_MUX_1_WIDTH                             5U
#define LPDDR4__PI_ODT_MUX_1__REG DENALI_PI_318
#define LPDDR4__PI_ODT_MUX_1__FLD LPDDR4__DENALI_PI_318__PI_ODT_MUX_1

#define LPDDR4__DENALI_PI_318__PI_ODT_MUX_2_MASK                     0x1F000000U
#define LPDDR4__DENALI_PI_318__PI_ODT_MUX_2_SHIFT                            24U
#define LPDDR4__DENALI_PI_318__PI_ODT_MUX_2_WIDTH                             5U
#define LPDDR4__PI_ODT_MUX_2__REG DENALI_PI_318
#define LPDDR4__PI_ODT_MUX_2__FLD LPDDR4__DENALI_PI_318__PI_ODT_MUX_2

#define LPDDR4__DENALI_PI_319_READ_MASK                              0x1F1F1F1FU
#define LPDDR4__DENALI_PI_319_WRITE_MASK                             0x1F1F1F1FU
#define LPDDR4__DENALI_PI_319__PI_ODT_MUX_3_MASK                     0x0000001FU
#define LPDDR4__DENALI_PI_319__PI_ODT_MUX_3_SHIFT                             0U
#define LPDDR4__DENALI_PI_319__PI_ODT_MUX_3_WIDTH                             5U
#define LPDDR4__PI_ODT_MUX_3__REG DENALI_PI_319
#define LPDDR4__PI_ODT_MUX_3__FLD LPDDR4__DENALI_PI_319__PI_ODT_MUX_3

#define LPDDR4__DENALI_PI_319__PI_RESET_N_MUX_0_MASK                 0x00001F00U
#define LPDDR4__DENALI_PI_319__PI_RESET_N_MUX_0_SHIFT                         8U
#define LPDDR4__DENALI_PI_319__PI_RESET_N_MUX_0_WIDTH                         5U
#define LPDDR4__PI_RESET_N_MUX_0__REG DENALI_PI_319
#define LPDDR4__PI_RESET_N_MUX_0__FLD LPDDR4__DENALI_PI_319__PI_RESET_N_MUX_0

#define LPDDR4__DENALI_PI_319__PI_RESET_N_MUX_1_MASK                 0x001F0000U
#define LPDDR4__DENALI_PI_319__PI_RESET_N_MUX_1_SHIFT                        16U
#define LPDDR4__DENALI_PI_319__PI_RESET_N_MUX_1_WIDTH                         5U
#define LPDDR4__PI_RESET_N_MUX_1__REG DENALI_PI_319
#define LPDDR4__PI_RESET_N_MUX_1__FLD LPDDR4__DENALI_PI_319__PI_RESET_N_MUX_1

#define LPDDR4__DENALI_PI_319__PI_RESET_N_MUX_2_MASK                 0x1F000000U
#define LPDDR4__DENALI_PI_319__PI_RESET_N_MUX_2_SHIFT                        24U
#define LPDDR4__DENALI_PI_319__PI_RESET_N_MUX_2_WIDTH                         5U
#define LPDDR4__PI_RESET_N_MUX_2__REG DENALI_PI_319
#define LPDDR4__PI_RESET_N_MUX_2__FLD LPDDR4__DENALI_PI_319__PI_RESET_N_MUX_2

#define LPDDR4__DENALI_PI_320_READ_MASK                              0x01FFFF1FU
#define LPDDR4__DENALI_PI_320_WRITE_MASK                             0x01FFFF1FU
#define LPDDR4__DENALI_PI_320__PI_RESET_N_MUX_3_MASK                 0x0000001FU
#define LPDDR4__DENALI_PI_320__PI_RESET_N_MUX_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_320__PI_RESET_N_MUX_3_WIDTH                         5U
#define LPDDR4__PI_RESET_N_MUX_3__REG DENALI_PI_320
#define LPDDR4__PI_RESET_N_MUX_3__FLD LPDDR4__DENALI_PI_320__PI_RESET_N_MUX_3

#define LPDDR4__DENALI_PI_320__PI_MRSINGLE_DATA_0_MASK               0x01FFFF00U
#define LPDDR4__DENALI_PI_320__PI_MRSINGLE_DATA_0_SHIFT                       8U
#define LPDDR4__DENALI_PI_320__PI_MRSINGLE_DATA_0_WIDTH                      17U
#define LPDDR4__PI_MRSINGLE_DATA_0__REG DENALI_PI_320
#define LPDDR4__PI_MRSINGLE_DATA_0__FLD LPDDR4__DENALI_PI_320__PI_MRSINGLE_DATA_0

#define LPDDR4__DENALI_PI_321_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_321_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_321__PI_MRSINGLE_DATA_1_MASK               0x0001FFFFU
#define LPDDR4__DENALI_PI_321__PI_MRSINGLE_DATA_1_SHIFT                       0U
#define LPDDR4__DENALI_PI_321__PI_MRSINGLE_DATA_1_WIDTH                      17U
#define LPDDR4__PI_MRSINGLE_DATA_1__REG DENALI_PI_321
#define LPDDR4__PI_MRSINGLE_DATA_1__FLD LPDDR4__DENALI_PI_321__PI_MRSINGLE_DATA_1

#define LPDDR4__DENALI_PI_322_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_322_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_322__PI_MRSINGLE_DATA_2_MASK               0x0001FFFFU
#define LPDDR4__DENALI_PI_322__PI_MRSINGLE_DATA_2_SHIFT                       0U
#define LPDDR4__DENALI_PI_322__PI_MRSINGLE_DATA_2_WIDTH                      17U
#define LPDDR4__PI_MRSINGLE_DATA_2__REG DENALI_PI_322
#define LPDDR4__PI_MRSINGLE_DATA_2__FLD LPDDR4__DENALI_PI_322__PI_MRSINGLE_DATA_2

#define LPDDR4__DENALI_PI_323_READ_MASK                              0x0F01FFFFU
#define LPDDR4__DENALI_PI_323_WRITE_MASK                             0x0F01FFFFU
#define LPDDR4__DENALI_PI_323__PI_MRSINGLE_DATA_3_MASK               0x0001FFFFU
#define LPDDR4__DENALI_PI_323__PI_MRSINGLE_DATA_3_SHIFT                       0U
#define LPDDR4__DENALI_PI_323__PI_MRSINGLE_DATA_3_WIDTH                      17U
#define LPDDR4__PI_MRSINGLE_DATA_3__REG DENALI_PI_323
#define LPDDR4__PI_MRSINGLE_DATA_3__FLD LPDDR4__DENALI_PI_323__PI_MRSINGLE_DATA_3

#define LPDDR4__DENALI_PI_323__PI_ZQ_CAL_START_MAP_0_MASK            0x0F000000U
#define LPDDR4__DENALI_PI_323__PI_ZQ_CAL_START_MAP_0_SHIFT                   24U
#define LPDDR4__DENALI_PI_323__PI_ZQ_CAL_START_MAP_0_WIDTH                    4U
#define LPDDR4__PI_ZQ_CAL_START_MAP_0__REG DENALI_PI_323
#define LPDDR4__PI_ZQ_CAL_START_MAP_0__FLD LPDDR4__DENALI_PI_323__PI_ZQ_CAL_START_MAP_0

#define LPDDR4__DENALI_PI_324_READ_MASK                              0x0F0F0F0FU
#define LPDDR4__DENALI_PI_324_WRITE_MASK                             0x0F0F0F0FU
#define LPDDR4__DENALI_PI_324__PI_ZQ_CAL_LATCH_MAP_0_MASK            0x0000000FU
#define LPDDR4__DENALI_PI_324__PI_ZQ_CAL_LATCH_MAP_0_SHIFT                    0U
#define LPDDR4__DENALI_PI_324__PI_ZQ_CAL_LATCH_MAP_0_WIDTH                    4U
#define LPDDR4__PI_ZQ_CAL_LATCH_MAP_0__REG DENALI_PI_324
#define LPDDR4__PI_ZQ_CAL_LATCH_MAP_0__FLD LPDDR4__DENALI_PI_324__PI_ZQ_CAL_LATCH_MAP_0

#define LPDDR4__DENALI_PI_324__PI_ZQ_CAL_START_MAP_1_MASK            0x00000F00U
#define LPDDR4__DENALI_PI_324__PI_ZQ_CAL_START_MAP_1_SHIFT                    8U
#define LPDDR4__DENALI_PI_324__PI_ZQ_CAL_START_MAP_1_WIDTH                    4U
#define LPDDR4__PI_ZQ_CAL_START_MAP_1__REG DENALI_PI_324
#define LPDDR4__PI_ZQ_CAL_START_MAP_1__FLD LPDDR4__DENALI_PI_324__PI_ZQ_CAL_START_MAP_1

#define LPDDR4__DENALI_PI_324__PI_ZQ_CAL_LATCH_MAP_1_MASK            0x000F0000U
#define LPDDR4__DENALI_PI_324__PI_ZQ_CAL_LATCH_MAP_1_SHIFT                   16U
#define LPDDR4__DENALI_PI_324__PI_ZQ_CAL_LATCH_MAP_1_WIDTH                    4U
#define LPDDR4__PI_ZQ_CAL_LATCH_MAP_1__REG DENALI_PI_324
#define LPDDR4__PI_ZQ_CAL_LATCH_MAP_1__FLD LPDDR4__DENALI_PI_324__PI_ZQ_CAL_LATCH_MAP_1

#define LPDDR4__DENALI_PI_324__PI_ZQ_CAL_START_MAP_2_MASK            0x0F000000U
#define LPDDR4__DENALI_PI_324__PI_ZQ_CAL_START_MAP_2_SHIFT                   24U
#define LPDDR4__DENALI_PI_324__PI_ZQ_CAL_START_MAP_2_WIDTH                    4U
#define LPDDR4__PI_ZQ_CAL_START_MAP_2__REG DENALI_PI_324
#define LPDDR4__PI_ZQ_CAL_START_MAP_2__FLD LPDDR4__DENALI_PI_324__PI_ZQ_CAL_START_MAP_2

#define LPDDR4__DENALI_PI_325_READ_MASK                              0x000F0F0FU
#define LPDDR4__DENALI_PI_325_WRITE_MASK                             0x000F0F0FU
#define LPDDR4__DENALI_PI_325__PI_ZQ_CAL_LATCH_MAP_2_MASK            0x0000000FU
#define LPDDR4__DENALI_PI_325__PI_ZQ_CAL_LATCH_MAP_2_SHIFT                    0U
#define LPDDR4__DENALI_PI_325__PI_ZQ_CAL_LATCH_MAP_2_WIDTH                    4U
#define LPDDR4__PI_ZQ_CAL_LATCH_MAP_2__REG DENALI_PI_325
#define LPDDR4__PI_ZQ_CAL_LATCH_MAP_2__FLD LPDDR4__DENALI_PI_325__PI_ZQ_CAL_LATCH_MAP_2

#define LPDDR4__DENALI_PI_325__PI_ZQ_CAL_START_MAP_3_MASK            0x00000F00U
#define LPDDR4__DENALI_PI_325__PI_ZQ_CAL_START_MAP_3_SHIFT                    8U
#define LPDDR4__DENALI_PI_325__PI_ZQ_CAL_START_MAP_3_WIDTH                    4U
#define LPDDR4__PI_ZQ_CAL_START_MAP_3__REG DENALI_PI_325
#define LPDDR4__PI_ZQ_CAL_START_MAP_3__FLD LPDDR4__DENALI_PI_325__PI_ZQ_CAL_START_MAP_3

#define LPDDR4__DENALI_PI_325__PI_ZQ_CAL_LATCH_MAP_3_MASK            0x000F0000U
#define LPDDR4__DENALI_PI_325__PI_ZQ_CAL_LATCH_MAP_3_SHIFT                   16U
#define LPDDR4__DENALI_PI_325__PI_ZQ_CAL_LATCH_MAP_3_WIDTH                    4U
#define LPDDR4__PI_ZQ_CAL_LATCH_MAP_3__REG DENALI_PI_325
#define LPDDR4__PI_ZQ_CAL_LATCH_MAP_3__FLD LPDDR4__DENALI_PI_325__PI_ZQ_CAL_LATCH_MAP_3

#define LPDDR4__DENALI_PI_326_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_326_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_326__PI_DQS_OSC_BASE_VALUE_0_0_MASK        0x0000FFFFU
#define LPDDR4__DENALI_PI_326__PI_DQS_OSC_BASE_VALUE_0_0_SHIFT                0U
#define LPDDR4__DENALI_PI_326__PI_DQS_OSC_BASE_VALUE_0_0_WIDTH               16U
#define LPDDR4__PI_DQS_OSC_BASE_VALUE_0_0__REG DENALI_PI_326
#define LPDDR4__PI_DQS_OSC_BASE_VALUE_0_0__FLD LPDDR4__DENALI_PI_326__PI_DQS_OSC_BASE_VALUE_0_0

#define LPDDR4__DENALI_PI_326__PI_DQS_OSC_BASE_VALUE_1_0_MASK        0xFFFF0000U
#define LPDDR4__DENALI_PI_326__PI_DQS_OSC_BASE_VALUE_1_0_SHIFT               16U
#define LPDDR4__DENALI_PI_326__PI_DQS_OSC_BASE_VALUE_1_0_WIDTH               16U
#define LPDDR4__PI_DQS_OSC_BASE_VALUE_1_0__REG DENALI_PI_326
#define LPDDR4__PI_DQS_OSC_BASE_VALUE_1_0__FLD LPDDR4__DENALI_PI_326__PI_DQS_OSC_BASE_VALUE_1_0

#define LPDDR4__DENALI_PI_327_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_327_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_327__PI_DQS_OSC_BASE_VALUE_0_1_MASK        0x0000FFFFU
#define LPDDR4__DENALI_PI_327__PI_DQS_OSC_BASE_VALUE_0_1_SHIFT                0U
#define LPDDR4__DENALI_PI_327__PI_DQS_OSC_BASE_VALUE_0_1_WIDTH               16U
#define LPDDR4__PI_DQS_OSC_BASE_VALUE_0_1__REG DENALI_PI_327
#define LPDDR4__PI_DQS_OSC_BASE_VALUE_0_1__FLD LPDDR4__DENALI_PI_327__PI_DQS_OSC_BASE_VALUE_0_1

#define LPDDR4__DENALI_PI_327__PI_DQS_OSC_BASE_VALUE_1_1_MASK        0xFFFF0000U
#define LPDDR4__DENALI_PI_327__PI_DQS_OSC_BASE_VALUE_1_1_SHIFT               16U
#define LPDDR4__DENALI_PI_327__PI_DQS_OSC_BASE_VALUE_1_1_WIDTH               16U
#define LPDDR4__PI_DQS_OSC_BASE_VALUE_1_1__REG DENALI_PI_327
#define LPDDR4__PI_DQS_OSC_BASE_VALUE_1_1__FLD LPDDR4__DENALI_PI_327__PI_DQS_OSC_BASE_VALUE_1_1

#define LPDDR4__DENALI_PI_328_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_328_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_328__PI_MR0_DATA_F0_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_328__PI_MR0_DATA_F0_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_328__PI_MR0_DATA_F0_0_WIDTH                        17U
#define LPDDR4__PI_MR0_DATA_F0_0__REG DENALI_PI_328
#define LPDDR4__PI_MR0_DATA_F0_0__FLD LPDDR4__DENALI_PI_328__PI_MR0_DATA_F0_0

#define LPDDR4__DENALI_PI_329_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_329_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_329__PI_MR1_DATA_F0_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_329__PI_MR1_DATA_F0_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_329__PI_MR1_DATA_F0_0_WIDTH                        17U
#define LPDDR4__PI_MR1_DATA_F0_0__REG DENALI_PI_329
#define LPDDR4__PI_MR1_DATA_F0_0__FLD LPDDR4__DENALI_PI_329__PI_MR1_DATA_F0_0

#define LPDDR4__DENALI_PI_330_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_330_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_330__PI_MR2_DATA_F0_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_330__PI_MR2_DATA_F0_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_330__PI_MR2_DATA_F0_0_WIDTH                        17U
#define LPDDR4__PI_MR2_DATA_F0_0__REG DENALI_PI_330
#define LPDDR4__PI_MR2_DATA_F0_0__FLD LPDDR4__DENALI_PI_330__PI_MR2_DATA_F0_0

#define LPDDR4__DENALI_PI_331_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_331_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_331__PI_MR3_DATA_F0_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_331__PI_MR3_DATA_F0_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_331__PI_MR3_DATA_F0_0_WIDTH                        17U
#define LPDDR4__PI_MR3_DATA_F0_0__REG DENALI_PI_331
#define LPDDR4__PI_MR3_DATA_F0_0__FLD LPDDR4__DENALI_PI_331__PI_MR3_DATA_F0_0

#define LPDDR4__DENALI_PI_332_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_332_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_332__PI_MR4_DATA_F0_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_332__PI_MR4_DATA_F0_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_332__PI_MR4_DATA_F0_0_WIDTH                        17U
#define LPDDR4__PI_MR4_DATA_F0_0__REG DENALI_PI_332
#define LPDDR4__PI_MR4_DATA_F0_0__FLD LPDDR4__DENALI_PI_332__PI_MR4_DATA_F0_0

#define LPDDR4__DENALI_PI_333_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_333_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_333__PI_MR5_DATA_F0_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_333__PI_MR5_DATA_F0_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_333__PI_MR5_DATA_F0_0_WIDTH                        17U
#define LPDDR4__PI_MR5_DATA_F0_0__REG DENALI_PI_333
#define LPDDR4__PI_MR5_DATA_F0_0__FLD LPDDR4__DENALI_PI_333__PI_MR5_DATA_F0_0

#define LPDDR4__DENALI_PI_334_READ_MASK                              0xFF01FFFFU
#define LPDDR4__DENALI_PI_334_WRITE_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_PI_334__PI_MR6_DATA_F0_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_334__PI_MR6_DATA_F0_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_334__PI_MR6_DATA_F0_0_WIDTH                        17U
#define LPDDR4__PI_MR6_DATA_F0_0__REG DENALI_PI_334
#define LPDDR4__PI_MR6_DATA_F0_0__FLD LPDDR4__DENALI_PI_334__PI_MR6_DATA_F0_0

#define LPDDR4__DENALI_PI_334__PI_MR11_DATA_F0_0_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_334__PI_MR11_DATA_F0_0_SHIFT                       24U
#define LPDDR4__DENALI_PI_334__PI_MR11_DATA_F0_0_WIDTH                        8U
#define LPDDR4__PI_MR11_DATA_F0_0__REG DENALI_PI_334
#define LPDDR4__PI_MR11_DATA_F0_0__FLD LPDDR4__DENALI_PI_334__PI_MR11_DATA_F0_0

#define LPDDR4__DENALI_PI_335_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_335_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_335__PI_MR12_DATA_F0_0_MASK                0x000000FFU
#define LPDDR4__DENALI_PI_335__PI_MR12_DATA_F0_0_SHIFT                        0U
#define LPDDR4__DENALI_PI_335__PI_MR12_DATA_F0_0_WIDTH                        8U
#define LPDDR4__PI_MR12_DATA_F0_0__REG DENALI_PI_335
#define LPDDR4__PI_MR12_DATA_F0_0__FLD LPDDR4__DENALI_PI_335__PI_MR12_DATA_F0_0

#define LPDDR4__DENALI_PI_335__PI_MR14_DATA_F0_0_MASK                0x0000FF00U
#define LPDDR4__DENALI_PI_335__PI_MR14_DATA_F0_0_SHIFT                        8U
#define LPDDR4__DENALI_PI_335__PI_MR14_DATA_F0_0_WIDTH                        8U
#define LPDDR4__PI_MR14_DATA_F0_0__REG DENALI_PI_335
#define LPDDR4__PI_MR14_DATA_F0_0__FLD LPDDR4__DENALI_PI_335__PI_MR14_DATA_F0_0

#define LPDDR4__DENALI_PI_335__PI_MR22_DATA_F0_0_MASK                0x00FF0000U
#define LPDDR4__DENALI_PI_335__PI_MR22_DATA_F0_0_SHIFT                       16U
#define LPDDR4__DENALI_PI_335__PI_MR22_DATA_F0_0_WIDTH                        8U
#define LPDDR4__PI_MR22_DATA_F0_0__REG DENALI_PI_335
#define LPDDR4__PI_MR22_DATA_F0_0__FLD LPDDR4__DENALI_PI_335__PI_MR22_DATA_F0_0

#define LPDDR4__DENALI_PI_335__PI_MR23_DATA_F0_0_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_335__PI_MR23_DATA_F0_0_SHIFT                       24U
#define LPDDR4__DENALI_PI_335__PI_MR23_DATA_F0_0_WIDTH                        8U
#define LPDDR4__PI_MR23_DATA_F0_0__REG DENALI_PI_335
#define LPDDR4__PI_MR23_DATA_F0_0__FLD LPDDR4__DENALI_PI_335__PI_MR23_DATA_F0_0

#define LPDDR4__DENALI_PI_336_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_336_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_336__PI_MR0_DATA_F1_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_336__PI_MR0_DATA_F1_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_336__PI_MR0_DATA_F1_0_WIDTH                        17U
#define LPDDR4__PI_MR0_DATA_F1_0__REG DENALI_PI_336
#define LPDDR4__PI_MR0_DATA_F1_0__FLD LPDDR4__DENALI_PI_336__PI_MR0_DATA_F1_0

#define LPDDR4__DENALI_PI_337_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_337_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_337__PI_MR1_DATA_F1_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_337__PI_MR1_DATA_F1_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_337__PI_MR1_DATA_F1_0_WIDTH                        17U
#define LPDDR4__PI_MR1_DATA_F1_0__REG DENALI_PI_337
#define LPDDR4__PI_MR1_DATA_F1_0__FLD LPDDR4__DENALI_PI_337__PI_MR1_DATA_F1_0

#define LPDDR4__DENALI_PI_338_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_338_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_338__PI_MR2_DATA_F1_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_338__PI_MR2_DATA_F1_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_338__PI_MR2_DATA_F1_0_WIDTH                        17U
#define LPDDR4__PI_MR2_DATA_F1_0__REG DENALI_PI_338
#define LPDDR4__PI_MR2_DATA_F1_0__FLD LPDDR4__DENALI_PI_338__PI_MR2_DATA_F1_0

#define LPDDR4__DENALI_PI_339_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_339_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_339__PI_MR3_DATA_F1_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_339__PI_MR3_DATA_F1_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_339__PI_MR3_DATA_F1_0_WIDTH                        17U
#define LPDDR4__PI_MR3_DATA_F1_0__REG DENALI_PI_339
#define LPDDR4__PI_MR3_DATA_F1_0__FLD LPDDR4__DENALI_PI_339__PI_MR3_DATA_F1_0

#define LPDDR4__DENALI_PI_340_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_340_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_340__PI_MR4_DATA_F1_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_340__PI_MR4_DATA_F1_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_340__PI_MR4_DATA_F1_0_WIDTH                        17U
#define LPDDR4__PI_MR4_DATA_F1_0__REG DENALI_PI_340
#define LPDDR4__PI_MR4_DATA_F1_0__FLD LPDDR4__DENALI_PI_340__PI_MR4_DATA_F1_0

#define LPDDR4__DENALI_PI_341_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_341_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_341__PI_MR5_DATA_F1_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_341__PI_MR5_DATA_F1_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_341__PI_MR5_DATA_F1_0_WIDTH                        17U
#define LPDDR4__PI_MR5_DATA_F1_0__REG DENALI_PI_341
#define LPDDR4__PI_MR5_DATA_F1_0__FLD LPDDR4__DENALI_PI_341__PI_MR5_DATA_F1_0

#define LPDDR4__DENALI_PI_342_READ_MASK                              0xFF01FFFFU
#define LPDDR4__DENALI_PI_342_WRITE_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_PI_342__PI_MR6_DATA_F1_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_342__PI_MR6_DATA_F1_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_342__PI_MR6_DATA_F1_0_WIDTH                        17U
#define LPDDR4__PI_MR6_DATA_F1_0__REG DENALI_PI_342
#define LPDDR4__PI_MR6_DATA_F1_0__FLD LPDDR4__DENALI_PI_342__PI_MR6_DATA_F1_0

#define LPDDR4__DENALI_PI_342__PI_MR11_DATA_F1_0_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_342__PI_MR11_DATA_F1_0_SHIFT                       24U
#define LPDDR4__DENALI_PI_342__PI_MR11_DATA_F1_0_WIDTH                        8U
#define LPDDR4__PI_MR11_DATA_F1_0__REG DENALI_PI_342
#define LPDDR4__PI_MR11_DATA_F1_0__FLD LPDDR4__DENALI_PI_342__PI_MR11_DATA_F1_0

#define LPDDR4__DENALI_PI_343_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_343_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_343__PI_MR12_DATA_F1_0_MASK                0x000000FFU
#define LPDDR4__DENALI_PI_343__PI_MR12_DATA_F1_0_SHIFT                        0U
#define LPDDR4__DENALI_PI_343__PI_MR12_DATA_F1_0_WIDTH                        8U
#define LPDDR4__PI_MR12_DATA_F1_0__REG DENALI_PI_343
#define LPDDR4__PI_MR12_DATA_F1_0__FLD LPDDR4__DENALI_PI_343__PI_MR12_DATA_F1_0

#define LPDDR4__DENALI_PI_343__PI_MR14_DATA_F1_0_MASK                0x0000FF00U
#define LPDDR4__DENALI_PI_343__PI_MR14_DATA_F1_0_SHIFT                        8U
#define LPDDR4__DENALI_PI_343__PI_MR14_DATA_F1_0_WIDTH                        8U
#define LPDDR4__PI_MR14_DATA_F1_0__REG DENALI_PI_343
#define LPDDR4__PI_MR14_DATA_F1_0__FLD LPDDR4__DENALI_PI_343__PI_MR14_DATA_F1_0

#define LPDDR4__DENALI_PI_343__PI_MR22_DATA_F1_0_MASK                0x00FF0000U
#define LPDDR4__DENALI_PI_343__PI_MR22_DATA_F1_0_SHIFT                       16U
#define LPDDR4__DENALI_PI_343__PI_MR22_DATA_F1_0_WIDTH                        8U
#define LPDDR4__PI_MR22_DATA_F1_0__REG DENALI_PI_343
#define LPDDR4__PI_MR22_DATA_F1_0__FLD LPDDR4__DENALI_PI_343__PI_MR22_DATA_F1_0

#define LPDDR4__DENALI_PI_343__PI_MR23_DATA_F1_0_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_343__PI_MR23_DATA_F1_0_SHIFT                       24U
#define LPDDR4__DENALI_PI_343__PI_MR23_DATA_F1_0_WIDTH                        8U
#define LPDDR4__PI_MR23_DATA_F1_0__REG DENALI_PI_343
#define LPDDR4__PI_MR23_DATA_F1_0__FLD LPDDR4__DENALI_PI_343__PI_MR23_DATA_F1_0

#define LPDDR4__DENALI_PI_344_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_344_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_344__PI_MR0_DATA_F2_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_344__PI_MR0_DATA_F2_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_344__PI_MR0_DATA_F2_0_WIDTH                        17U
#define LPDDR4__PI_MR0_DATA_F2_0__REG DENALI_PI_344
#define LPDDR4__PI_MR0_DATA_F2_0__FLD LPDDR4__DENALI_PI_344__PI_MR0_DATA_F2_0

#define LPDDR4__DENALI_PI_345_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_345_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_345__PI_MR1_DATA_F2_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_345__PI_MR1_DATA_F2_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_345__PI_MR1_DATA_F2_0_WIDTH                        17U
#define LPDDR4__PI_MR1_DATA_F2_0__REG DENALI_PI_345
#define LPDDR4__PI_MR1_DATA_F2_0__FLD LPDDR4__DENALI_PI_345__PI_MR1_DATA_F2_0

#define LPDDR4__DENALI_PI_346_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_346_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_346__PI_MR2_DATA_F2_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_346__PI_MR2_DATA_F2_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_346__PI_MR2_DATA_F2_0_WIDTH                        17U
#define LPDDR4__PI_MR2_DATA_F2_0__REG DENALI_PI_346
#define LPDDR4__PI_MR2_DATA_F2_0__FLD LPDDR4__DENALI_PI_346__PI_MR2_DATA_F2_0

#define LPDDR4__DENALI_PI_347_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_347_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_347__PI_MR3_DATA_F2_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_347__PI_MR3_DATA_F2_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_347__PI_MR3_DATA_F2_0_WIDTH                        17U
#define LPDDR4__PI_MR3_DATA_F2_0__REG DENALI_PI_347
#define LPDDR4__PI_MR3_DATA_F2_0__FLD LPDDR4__DENALI_PI_347__PI_MR3_DATA_F2_0

#define LPDDR4__DENALI_PI_348_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_348_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_348__PI_MR4_DATA_F2_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_348__PI_MR4_DATA_F2_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_348__PI_MR4_DATA_F2_0_WIDTH                        17U
#define LPDDR4__PI_MR4_DATA_F2_0__REG DENALI_PI_348
#define LPDDR4__PI_MR4_DATA_F2_0__FLD LPDDR4__DENALI_PI_348__PI_MR4_DATA_F2_0

#define LPDDR4__DENALI_PI_349_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_349_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_349__PI_MR5_DATA_F2_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_349__PI_MR5_DATA_F2_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_349__PI_MR5_DATA_F2_0_WIDTH                        17U
#define LPDDR4__PI_MR5_DATA_F2_0__REG DENALI_PI_349
#define LPDDR4__PI_MR5_DATA_F2_0__FLD LPDDR4__DENALI_PI_349__PI_MR5_DATA_F2_0

#define LPDDR4__DENALI_PI_350_READ_MASK                              0xFF01FFFFU
#define LPDDR4__DENALI_PI_350_WRITE_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_PI_350__PI_MR6_DATA_F2_0_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_350__PI_MR6_DATA_F2_0_SHIFT                         0U
#define LPDDR4__DENALI_PI_350__PI_MR6_DATA_F2_0_WIDTH                        17U
#define LPDDR4__PI_MR6_DATA_F2_0__REG DENALI_PI_350
#define LPDDR4__PI_MR6_DATA_F2_0__FLD LPDDR4__DENALI_PI_350__PI_MR6_DATA_F2_0

#define LPDDR4__DENALI_PI_350__PI_MR11_DATA_F2_0_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_350__PI_MR11_DATA_F2_0_SHIFT                       24U
#define LPDDR4__DENALI_PI_350__PI_MR11_DATA_F2_0_WIDTH                        8U
#define LPDDR4__PI_MR11_DATA_F2_0__REG DENALI_PI_350
#define LPDDR4__PI_MR11_DATA_F2_0__FLD LPDDR4__DENALI_PI_350__PI_MR11_DATA_F2_0

#define LPDDR4__DENALI_PI_351_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_351_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_351__PI_MR12_DATA_F2_0_MASK                0x000000FFU
#define LPDDR4__DENALI_PI_351__PI_MR12_DATA_F2_0_SHIFT                        0U
#define LPDDR4__DENALI_PI_351__PI_MR12_DATA_F2_0_WIDTH                        8U
#define LPDDR4__PI_MR12_DATA_F2_0__REG DENALI_PI_351
#define LPDDR4__PI_MR12_DATA_F2_0__FLD LPDDR4__DENALI_PI_351__PI_MR12_DATA_F2_0

#define LPDDR4__DENALI_PI_351__PI_MR14_DATA_F2_0_MASK                0x0000FF00U
#define LPDDR4__DENALI_PI_351__PI_MR14_DATA_F2_0_SHIFT                        8U
#define LPDDR4__DENALI_PI_351__PI_MR14_DATA_F2_0_WIDTH                        8U
#define LPDDR4__PI_MR14_DATA_F2_0__REG DENALI_PI_351
#define LPDDR4__PI_MR14_DATA_F2_0__FLD LPDDR4__DENALI_PI_351__PI_MR14_DATA_F2_0

#define LPDDR4__DENALI_PI_351__PI_MR22_DATA_F2_0_MASK                0x00FF0000U
#define LPDDR4__DENALI_PI_351__PI_MR22_DATA_F2_0_SHIFT                       16U
#define LPDDR4__DENALI_PI_351__PI_MR22_DATA_F2_0_WIDTH                        8U
#define LPDDR4__PI_MR22_DATA_F2_0__REG DENALI_PI_351
#define LPDDR4__PI_MR22_DATA_F2_0__FLD LPDDR4__DENALI_PI_351__PI_MR22_DATA_F2_0

#define LPDDR4__DENALI_PI_351__PI_MR23_DATA_F2_0_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_351__PI_MR23_DATA_F2_0_SHIFT                       24U
#define LPDDR4__DENALI_PI_351__PI_MR23_DATA_F2_0_WIDTH                        8U
#define LPDDR4__PI_MR23_DATA_F2_0__REG DENALI_PI_351
#define LPDDR4__PI_MR23_DATA_F2_0__FLD LPDDR4__DENALI_PI_351__PI_MR23_DATA_F2_0

#define LPDDR4__DENALI_PI_352_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_352_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_352__PI_MR0_DATA_F0_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_352__PI_MR0_DATA_F0_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_352__PI_MR0_DATA_F0_1_WIDTH                        17U
#define LPDDR4__PI_MR0_DATA_F0_1__REG DENALI_PI_352
#define LPDDR4__PI_MR0_DATA_F0_1__FLD LPDDR4__DENALI_PI_352__PI_MR0_DATA_F0_1

#define LPDDR4__DENALI_PI_353_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_353_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_353__PI_MR1_DATA_F0_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_353__PI_MR1_DATA_F0_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_353__PI_MR1_DATA_F0_1_WIDTH                        17U
#define LPDDR4__PI_MR1_DATA_F0_1__REG DENALI_PI_353
#define LPDDR4__PI_MR1_DATA_F0_1__FLD LPDDR4__DENALI_PI_353__PI_MR1_DATA_F0_1

#define LPDDR4__DENALI_PI_354_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_354_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_354__PI_MR2_DATA_F0_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_354__PI_MR2_DATA_F0_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_354__PI_MR2_DATA_F0_1_WIDTH                        17U
#define LPDDR4__PI_MR2_DATA_F0_1__REG DENALI_PI_354
#define LPDDR4__PI_MR2_DATA_F0_1__FLD LPDDR4__DENALI_PI_354__PI_MR2_DATA_F0_1

#define LPDDR4__DENALI_PI_355_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_355_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_355__PI_MR3_DATA_F0_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_355__PI_MR3_DATA_F0_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_355__PI_MR3_DATA_F0_1_WIDTH                        17U
#define LPDDR4__PI_MR3_DATA_F0_1__REG DENALI_PI_355
#define LPDDR4__PI_MR3_DATA_F0_1__FLD LPDDR4__DENALI_PI_355__PI_MR3_DATA_F0_1

#define LPDDR4__DENALI_PI_356_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_356_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_356__PI_MR4_DATA_F0_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_356__PI_MR4_DATA_F0_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_356__PI_MR4_DATA_F0_1_WIDTH                        17U
#define LPDDR4__PI_MR4_DATA_F0_1__REG DENALI_PI_356
#define LPDDR4__PI_MR4_DATA_F0_1__FLD LPDDR4__DENALI_PI_356__PI_MR4_DATA_F0_1

#define LPDDR4__DENALI_PI_357_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_357_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_357__PI_MR5_DATA_F0_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_357__PI_MR5_DATA_F0_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_357__PI_MR5_DATA_F0_1_WIDTH                        17U
#define LPDDR4__PI_MR5_DATA_F0_1__REG DENALI_PI_357
#define LPDDR4__PI_MR5_DATA_F0_1__FLD LPDDR4__DENALI_PI_357__PI_MR5_DATA_F0_1

#define LPDDR4__DENALI_PI_358_READ_MASK                              0xFF01FFFFU
#define LPDDR4__DENALI_PI_358_WRITE_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_PI_358__PI_MR6_DATA_F0_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_358__PI_MR6_DATA_F0_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_358__PI_MR6_DATA_F0_1_WIDTH                        17U
#define LPDDR4__PI_MR6_DATA_F0_1__REG DENALI_PI_358
#define LPDDR4__PI_MR6_DATA_F0_1__FLD LPDDR4__DENALI_PI_358__PI_MR6_DATA_F0_1

#define LPDDR4__DENALI_PI_358__PI_MR11_DATA_F0_1_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_358__PI_MR11_DATA_F0_1_SHIFT                       24U
#define LPDDR4__DENALI_PI_358__PI_MR11_DATA_F0_1_WIDTH                        8U
#define LPDDR4__PI_MR11_DATA_F0_1__REG DENALI_PI_358
#define LPDDR4__PI_MR11_DATA_F0_1__FLD LPDDR4__DENALI_PI_358__PI_MR11_DATA_F0_1

#define LPDDR4__DENALI_PI_359_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_359_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_359__PI_MR12_DATA_F0_1_MASK                0x000000FFU
#define LPDDR4__DENALI_PI_359__PI_MR12_DATA_F0_1_SHIFT                        0U
#define LPDDR4__DENALI_PI_359__PI_MR12_DATA_F0_1_WIDTH                        8U
#define LPDDR4__PI_MR12_DATA_F0_1__REG DENALI_PI_359
#define LPDDR4__PI_MR12_DATA_F0_1__FLD LPDDR4__DENALI_PI_359__PI_MR12_DATA_F0_1

#define LPDDR4__DENALI_PI_359__PI_MR14_DATA_F0_1_MASK                0x0000FF00U
#define LPDDR4__DENALI_PI_359__PI_MR14_DATA_F0_1_SHIFT                        8U
#define LPDDR4__DENALI_PI_359__PI_MR14_DATA_F0_1_WIDTH                        8U
#define LPDDR4__PI_MR14_DATA_F0_1__REG DENALI_PI_359
#define LPDDR4__PI_MR14_DATA_F0_1__FLD LPDDR4__DENALI_PI_359__PI_MR14_DATA_F0_1

#define LPDDR4__DENALI_PI_359__PI_MR22_DATA_F0_1_MASK                0x00FF0000U
#define LPDDR4__DENALI_PI_359__PI_MR22_DATA_F0_1_SHIFT                       16U
#define LPDDR4__DENALI_PI_359__PI_MR22_DATA_F0_1_WIDTH                        8U
#define LPDDR4__PI_MR22_DATA_F0_1__REG DENALI_PI_359
#define LPDDR4__PI_MR22_DATA_F0_1__FLD LPDDR4__DENALI_PI_359__PI_MR22_DATA_F0_1

#define LPDDR4__DENALI_PI_359__PI_MR23_DATA_F0_1_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_359__PI_MR23_DATA_F0_1_SHIFT                       24U
#define LPDDR4__DENALI_PI_359__PI_MR23_DATA_F0_1_WIDTH                        8U
#define LPDDR4__PI_MR23_DATA_F0_1__REG DENALI_PI_359
#define LPDDR4__PI_MR23_DATA_F0_1__FLD LPDDR4__DENALI_PI_359__PI_MR23_DATA_F0_1

#define LPDDR4__DENALI_PI_360_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_360_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_360__PI_MR0_DATA_F1_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_360__PI_MR0_DATA_F1_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_360__PI_MR0_DATA_F1_1_WIDTH                        17U
#define LPDDR4__PI_MR0_DATA_F1_1__REG DENALI_PI_360
#define LPDDR4__PI_MR0_DATA_F1_1__FLD LPDDR4__DENALI_PI_360__PI_MR0_DATA_F1_1

#define LPDDR4__DENALI_PI_361_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_361_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_361__PI_MR1_DATA_F1_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_361__PI_MR1_DATA_F1_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_361__PI_MR1_DATA_F1_1_WIDTH                        17U
#define LPDDR4__PI_MR1_DATA_F1_1__REG DENALI_PI_361
#define LPDDR4__PI_MR1_DATA_F1_1__FLD LPDDR4__DENALI_PI_361__PI_MR1_DATA_F1_1

#define LPDDR4__DENALI_PI_362_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_362_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_362__PI_MR2_DATA_F1_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_362__PI_MR2_DATA_F1_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_362__PI_MR2_DATA_F1_1_WIDTH                        17U
#define LPDDR4__PI_MR2_DATA_F1_1__REG DENALI_PI_362
#define LPDDR4__PI_MR2_DATA_F1_1__FLD LPDDR4__DENALI_PI_362__PI_MR2_DATA_F1_1

#define LPDDR4__DENALI_PI_363_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_363_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_363__PI_MR3_DATA_F1_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_363__PI_MR3_DATA_F1_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_363__PI_MR3_DATA_F1_1_WIDTH                        17U
#define LPDDR4__PI_MR3_DATA_F1_1__REG DENALI_PI_363
#define LPDDR4__PI_MR3_DATA_F1_1__FLD LPDDR4__DENALI_PI_363__PI_MR3_DATA_F1_1

#define LPDDR4__DENALI_PI_364_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_364_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_364__PI_MR4_DATA_F1_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_364__PI_MR4_DATA_F1_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_364__PI_MR4_DATA_F1_1_WIDTH                        17U
#define LPDDR4__PI_MR4_DATA_F1_1__REG DENALI_PI_364
#define LPDDR4__PI_MR4_DATA_F1_1__FLD LPDDR4__DENALI_PI_364__PI_MR4_DATA_F1_1

#define LPDDR4__DENALI_PI_365_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_365_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_365__PI_MR5_DATA_F1_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_365__PI_MR5_DATA_F1_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_365__PI_MR5_DATA_F1_1_WIDTH                        17U
#define LPDDR4__PI_MR5_DATA_F1_1__REG DENALI_PI_365
#define LPDDR4__PI_MR5_DATA_F1_1__FLD LPDDR4__DENALI_PI_365__PI_MR5_DATA_F1_1

#define LPDDR4__DENALI_PI_366_READ_MASK                              0xFF01FFFFU
#define LPDDR4__DENALI_PI_366_WRITE_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_PI_366__PI_MR6_DATA_F1_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_366__PI_MR6_DATA_F1_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_366__PI_MR6_DATA_F1_1_WIDTH                        17U
#define LPDDR4__PI_MR6_DATA_F1_1__REG DENALI_PI_366
#define LPDDR4__PI_MR6_DATA_F1_1__FLD LPDDR4__DENALI_PI_366__PI_MR6_DATA_F1_1

#define LPDDR4__DENALI_PI_366__PI_MR11_DATA_F1_1_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_366__PI_MR11_DATA_F1_1_SHIFT                       24U
#define LPDDR4__DENALI_PI_366__PI_MR11_DATA_F1_1_WIDTH                        8U
#define LPDDR4__PI_MR11_DATA_F1_1__REG DENALI_PI_366
#define LPDDR4__PI_MR11_DATA_F1_1__FLD LPDDR4__DENALI_PI_366__PI_MR11_DATA_F1_1

#define LPDDR4__DENALI_PI_367_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_367_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_367__PI_MR12_DATA_F1_1_MASK                0x000000FFU
#define LPDDR4__DENALI_PI_367__PI_MR12_DATA_F1_1_SHIFT                        0U
#define LPDDR4__DENALI_PI_367__PI_MR12_DATA_F1_1_WIDTH                        8U
#define LPDDR4__PI_MR12_DATA_F1_1__REG DENALI_PI_367
#define LPDDR4__PI_MR12_DATA_F1_1__FLD LPDDR4__DENALI_PI_367__PI_MR12_DATA_F1_1

#define LPDDR4__DENALI_PI_367__PI_MR14_DATA_F1_1_MASK                0x0000FF00U
#define LPDDR4__DENALI_PI_367__PI_MR14_DATA_F1_1_SHIFT                        8U
#define LPDDR4__DENALI_PI_367__PI_MR14_DATA_F1_1_WIDTH                        8U
#define LPDDR4__PI_MR14_DATA_F1_1__REG DENALI_PI_367
#define LPDDR4__PI_MR14_DATA_F1_1__FLD LPDDR4__DENALI_PI_367__PI_MR14_DATA_F1_1

#define LPDDR4__DENALI_PI_367__PI_MR22_DATA_F1_1_MASK                0x00FF0000U
#define LPDDR4__DENALI_PI_367__PI_MR22_DATA_F1_1_SHIFT                       16U
#define LPDDR4__DENALI_PI_367__PI_MR22_DATA_F1_1_WIDTH                        8U
#define LPDDR4__PI_MR22_DATA_F1_1__REG DENALI_PI_367
#define LPDDR4__PI_MR22_DATA_F1_1__FLD LPDDR4__DENALI_PI_367__PI_MR22_DATA_F1_1

#define LPDDR4__DENALI_PI_367__PI_MR23_DATA_F1_1_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_367__PI_MR23_DATA_F1_1_SHIFT                       24U
#define LPDDR4__DENALI_PI_367__PI_MR23_DATA_F1_1_WIDTH                        8U
#define LPDDR4__PI_MR23_DATA_F1_1__REG DENALI_PI_367
#define LPDDR4__PI_MR23_DATA_F1_1__FLD LPDDR4__DENALI_PI_367__PI_MR23_DATA_F1_1

#define LPDDR4__DENALI_PI_368_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_368_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_368__PI_MR0_DATA_F2_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_368__PI_MR0_DATA_F2_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_368__PI_MR0_DATA_F2_1_WIDTH                        17U
#define LPDDR4__PI_MR0_DATA_F2_1__REG DENALI_PI_368
#define LPDDR4__PI_MR0_DATA_F2_1__FLD LPDDR4__DENALI_PI_368__PI_MR0_DATA_F2_1

#define LPDDR4__DENALI_PI_369_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_369_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_369__PI_MR1_DATA_F2_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_369__PI_MR1_DATA_F2_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_369__PI_MR1_DATA_F2_1_WIDTH                        17U
#define LPDDR4__PI_MR1_DATA_F2_1__REG DENALI_PI_369
#define LPDDR4__PI_MR1_DATA_F2_1__FLD LPDDR4__DENALI_PI_369__PI_MR1_DATA_F2_1

#define LPDDR4__DENALI_PI_370_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_370_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_370__PI_MR2_DATA_F2_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_370__PI_MR2_DATA_F2_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_370__PI_MR2_DATA_F2_1_WIDTH                        17U
#define LPDDR4__PI_MR2_DATA_F2_1__REG DENALI_PI_370
#define LPDDR4__PI_MR2_DATA_F2_1__FLD LPDDR4__DENALI_PI_370__PI_MR2_DATA_F2_1

#define LPDDR4__DENALI_PI_371_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_371_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_371__PI_MR3_DATA_F2_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_371__PI_MR3_DATA_F2_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_371__PI_MR3_DATA_F2_1_WIDTH                        17U
#define LPDDR4__PI_MR3_DATA_F2_1__REG DENALI_PI_371
#define LPDDR4__PI_MR3_DATA_F2_1__FLD LPDDR4__DENALI_PI_371__PI_MR3_DATA_F2_1

#define LPDDR4__DENALI_PI_372_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_372_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_372__PI_MR4_DATA_F2_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_372__PI_MR4_DATA_F2_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_372__PI_MR4_DATA_F2_1_WIDTH                        17U
#define LPDDR4__PI_MR4_DATA_F2_1__REG DENALI_PI_372
#define LPDDR4__PI_MR4_DATA_F2_1__FLD LPDDR4__DENALI_PI_372__PI_MR4_DATA_F2_1

#define LPDDR4__DENALI_PI_373_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_373_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_373__PI_MR5_DATA_F2_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_373__PI_MR5_DATA_F2_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_373__PI_MR5_DATA_F2_1_WIDTH                        17U
#define LPDDR4__PI_MR5_DATA_F2_1__REG DENALI_PI_373
#define LPDDR4__PI_MR5_DATA_F2_1__FLD LPDDR4__DENALI_PI_373__PI_MR5_DATA_F2_1

#define LPDDR4__DENALI_PI_374_READ_MASK                              0xFF01FFFFU
#define LPDDR4__DENALI_PI_374_WRITE_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_PI_374__PI_MR6_DATA_F2_1_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_374__PI_MR6_DATA_F2_1_SHIFT                         0U
#define LPDDR4__DENALI_PI_374__PI_MR6_DATA_F2_1_WIDTH                        17U
#define LPDDR4__PI_MR6_DATA_F2_1__REG DENALI_PI_374
#define LPDDR4__PI_MR6_DATA_F2_1__FLD LPDDR4__DENALI_PI_374__PI_MR6_DATA_F2_1

#define LPDDR4__DENALI_PI_374__PI_MR11_DATA_F2_1_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_374__PI_MR11_DATA_F2_1_SHIFT                       24U
#define LPDDR4__DENALI_PI_374__PI_MR11_DATA_F2_1_WIDTH                        8U
#define LPDDR4__PI_MR11_DATA_F2_1__REG DENALI_PI_374
#define LPDDR4__PI_MR11_DATA_F2_1__FLD LPDDR4__DENALI_PI_374__PI_MR11_DATA_F2_1

#define LPDDR4__DENALI_PI_375_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_375_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_375__PI_MR12_DATA_F2_1_MASK                0x000000FFU
#define LPDDR4__DENALI_PI_375__PI_MR12_DATA_F2_1_SHIFT                        0U
#define LPDDR4__DENALI_PI_375__PI_MR12_DATA_F2_1_WIDTH                        8U
#define LPDDR4__PI_MR12_DATA_F2_1__REG DENALI_PI_375
#define LPDDR4__PI_MR12_DATA_F2_1__FLD LPDDR4__DENALI_PI_375__PI_MR12_DATA_F2_1

#define LPDDR4__DENALI_PI_375__PI_MR14_DATA_F2_1_MASK                0x0000FF00U
#define LPDDR4__DENALI_PI_375__PI_MR14_DATA_F2_1_SHIFT                        8U
#define LPDDR4__DENALI_PI_375__PI_MR14_DATA_F2_1_WIDTH                        8U
#define LPDDR4__PI_MR14_DATA_F2_1__REG DENALI_PI_375
#define LPDDR4__PI_MR14_DATA_F2_1__FLD LPDDR4__DENALI_PI_375__PI_MR14_DATA_F2_1

#define LPDDR4__DENALI_PI_375__PI_MR22_DATA_F2_1_MASK                0x00FF0000U
#define LPDDR4__DENALI_PI_375__PI_MR22_DATA_F2_1_SHIFT                       16U
#define LPDDR4__DENALI_PI_375__PI_MR22_DATA_F2_1_WIDTH                        8U
#define LPDDR4__PI_MR22_DATA_F2_1__REG DENALI_PI_375
#define LPDDR4__PI_MR22_DATA_F2_1__FLD LPDDR4__DENALI_PI_375__PI_MR22_DATA_F2_1

#define LPDDR4__DENALI_PI_375__PI_MR23_DATA_F2_1_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_375__PI_MR23_DATA_F2_1_SHIFT                       24U
#define LPDDR4__DENALI_PI_375__PI_MR23_DATA_F2_1_WIDTH                        8U
#define LPDDR4__PI_MR23_DATA_F2_1__REG DENALI_PI_375
#define LPDDR4__PI_MR23_DATA_F2_1__FLD LPDDR4__DENALI_PI_375__PI_MR23_DATA_F2_1

#define LPDDR4__DENALI_PI_376_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_376_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_376__PI_MR0_DATA_F0_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_376__PI_MR0_DATA_F0_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_376__PI_MR0_DATA_F0_2_WIDTH                        17U
#define LPDDR4__PI_MR0_DATA_F0_2__REG DENALI_PI_376
#define LPDDR4__PI_MR0_DATA_F0_2__FLD LPDDR4__DENALI_PI_376__PI_MR0_DATA_F0_2

#define LPDDR4__DENALI_PI_377_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_377_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_377__PI_MR1_DATA_F0_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_377__PI_MR1_DATA_F0_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_377__PI_MR1_DATA_F0_2_WIDTH                        17U
#define LPDDR4__PI_MR1_DATA_F0_2__REG DENALI_PI_377
#define LPDDR4__PI_MR1_DATA_F0_2__FLD LPDDR4__DENALI_PI_377__PI_MR1_DATA_F0_2

#define LPDDR4__DENALI_PI_378_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_378_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_378__PI_MR2_DATA_F0_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_378__PI_MR2_DATA_F0_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_378__PI_MR2_DATA_F0_2_WIDTH                        17U
#define LPDDR4__PI_MR2_DATA_F0_2__REG DENALI_PI_378
#define LPDDR4__PI_MR2_DATA_F0_2__FLD LPDDR4__DENALI_PI_378__PI_MR2_DATA_F0_2

#define LPDDR4__DENALI_PI_379_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_379_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_379__PI_MR3_DATA_F0_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_379__PI_MR3_DATA_F0_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_379__PI_MR3_DATA_F0_2_WIDTH                        17U
#define LPDDR4__PI_MR3_DATA_F0_2__REG DENALI_PI_379
#define LPDDR4__PI_MR3_DATA_F0_2__FLD LPDDR4__DENALI_PI_379__PI_MR3_DATA_F0_2

#define LPDDR4__DENALI_PI_380_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_380_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_380__PI_MR4_DATA_F0_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_380__PI_MR4_DATA_F0_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_380__PI_MR4_DATA_F0_2_WIDTH                        17U
#define LPDDR4__PI_MR4_DATA_F0_2__REG DENALI_PI_380
#define LPDDR4__PI_MR4_DATA_F0_2__FLD LPDDR4__DENALI_PI_380__PI_MR4_DATA_F0_2

#define LPDDR4__DENALI_PI_381_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_381_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_381__PI_MR5_DATA_F0_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_381__PI_MR5_DATA_F0_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_381__PI_MR5_DATA_F0_2_WIDTH                        17U
#define LPDDR4__PI_MR5_DATA_F0_2__REG DENALI_PI_381
#define LPDDR4__PI_MR5_DATA_F0_2__FLD LPDDR4__DENALI_PI_381__PI_MR5_DATA_F0_2

#define LPDDR4__DENALI_PI_382_READ_MASK                              0xFF01FFFFU
#define LPDDR4__DENALI_PI_382_WRITE_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_PI_382__PI_MR6_DATA_F0_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_382__PI_MR6_DATA_F0_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_382__PI_MR6_DATA_F0_2_WIDTH                        17U
#define LPDDR4__PI_MR6_DATA_F0_2__REG DENALI_PI_382
#define LPDDR4__PI_MR6_DATA_F0_2__FLD LPDDR4__DENALI_PI_382__PI_MR6_DATA_F0_2

#define LPDDR4__DENALI_PI_382__PI_MR11_DATA_F0_2_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_382__PI_MR11_DATA_F0_2_SHIFT                       24U
#define LPDDR4__DENALI_PI_382__PI_MR11_DATA_F0_2_WIDTH                        8U
#define LPDDR4__PI_MR11_DATA_F0_2__REG DENALI_PI_382
#define LPDDR4__PI_MR11_DATA_F0_2__FLD LPDDR4__DENALI_PI_382__PI_MR11_DATA_F0_2

#define LPDDR4__DENALI_PI_383_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_383_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_383__PI_MR12_DATA_F0_2_MASK                0x000000FFU
#define LPDDR4__DENALI_PI_383__PI_MR12_DATA_F0_2_SHIFT                        0U
#define LPDDR4__DENALI_PI_383__PI_MR12_DATA_F0_2_WIDTH                        8U
#define LPDDR4__PI_MR12_DATA_F0_2__REG DENALI_PI_383
#define LPDDR4__PI_MR12_DATA_F0_2__FLD LPDDR4__DENALI_PI_383__PI_MR12_DATA_F0_2

#define LPDDR4__DENALI_PI_383__PI_MR14_DATA_F0_2_MASK                0x0000FF00U
#define LPDDR4__DENALI_PI_383__PI_MR14_DATA_F0_2_SHIFT                        8U
#define LPDDR4__DENALI_PI_383__PI_MR14_DATA_F0_2_WIDTH                        8U
#define LPDDR4__PI_MR14_DATA_F0_2__REG DENALI_PI_383
#define LPDDR4__PI_MR14_DATA_F0_2__FLD LPDDR4__DENALI_PI_383__PI_MR14_DATA_F0_2

#define LPDDR4__DENALI_PI_383__PI_MR22_DATA_F0_2_MASK                0x00FF0000U
#define LPDDR4__DENALI_PI_383__PI_MR22_DATA_F0_2_SHIFT                       16U
#define LPDDR4__DENALI_PI_383__PI_MR22_DATA_F0_2_WIDTH                        8U
#define LPDDR4__PI_MR22_DATA_F0_2__REG DENALI_PI_383
#define LPDDR4__PI_MR22_DATA_F0_2__FLD LPDDR4__DENALI_PI_383__PI_MR22_DATA_F0_2

#define LPDDR4__DENALI_PI_383__PI_MR23_DATA_F0_2_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_383__PI_MR23_DATA_F0_2_SHIFT                       24U
#define LPDDR4__DENALI_PI_383__PI_MR23_DATA_F0_2_WIDTH                        8U
#define LPDDR4__PI_MR23_DATA_F0_2__REG DENALI_PI_383
#define LPDDR4__PI_MR23_DATA_F0_2__FLD LPDDR4__DENALI_PI_383__PI_MR23_DATA_F0_2

#define LPDDR4__DENALI_PI_384_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_384_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_384__PI_MR0_DATA_F1_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_384__PI_MR0_DATA_F1_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_384__PI_MR0_DATA_F1_2_WIDTH                        17U
#define LPDDR4__PI_MR0_DATA_F1_2__REG DENALI_PI_384
#define LPDDR4__PI_MR0_DATA_F1_2__FLD LPDDR4__DENALI_PI_384__PI_MR0_DATA_F1_2

#define LPDDR4__DENALI_PI_385_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_385_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_385__PI_MR1_DATA_F1_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_385__PI_MR1_DATA_F1_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_385__PI_MR1_DATA_F1_2_WIDTH                        17U
#define LPDDR4__PI_MR1_DATA_F1_2__REG DENALI_PI_385
#define LPDDR4__PI_MR1_DATA_F1_2__FLD LPDDR4__DENALI_PI_385__PI_MR1_DATA_F1_2

#define LPDDR4__DENALI_PI_386_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_386_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_386__PI_MR2_DATA_F1_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_386__PI_MR2_DATA_F1_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_386__PI_MR2_DATA_F1_2_WIDTH                        17U
#define LPDDR4__PI_MR2_DATA_F1_2__REG DENALI_PI_386
#define LPDDR4__PI_MR2_DATA_F1_2__FLD LPDDR4__DENALI_PI_386__PI_MR2_DATA_F1_2

#define LPDDR4__DENALI_PI_387_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_387_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_387__PI_MR3_DATA_F1_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_387__PI_MR3_DATA_F1_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_387__PI_MR3_DATA_F1_2_WIDTH                        17U
#define LPDDR4__PI_MR3_DATA_F1_2__REG DENALI_PI_387
#define LPDDR4__PI_MR3_DATA_F1_2__FLD LPDDR4__DENALI_PI_387__PI_MR3_DATA_F1_2

#define LPDDR4__DENALI_PI_388_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_388_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_388__PI_MR4_DATA_F1_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_388__PI_MR4_DATA_F1_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_388__PI_MR4_DATA_F1_2_WIDTH                        17U
#define LPDDR4__PI_MR4_DATA_F1_2__REG DENALI_PI_388
#define LPDDR4__PI_MR4_DATA_F1_2__FLD LPDDR4__DENALI_PI_388__PI_MR4_DATA_F1_2

#define LPDDR4__DENALI_PI_389_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_389_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_389__PI_MR5_DATA_F1_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_389__PI_MR5_DATA_F1_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_389__PI_MR5_DATA_F1_2_WIDTH                        17U
#define LPDDR4__PI_MR5_DATA_F1_2__REG DENALI_PI_389
#define LPDDR4__PI_MR5_DATA_F1_2__FLD LPDDR4__DENALI_PI_389__PI_MR5_DATA_F1_2

#define LPDDR4__DENALI_PI_390_READ_MASK                              0xFF01FFFFU
#define LPDDR4__DENALI_PI_390_WRITE_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_PI_390__PI_MR6_DATA_F1_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_390__PI_MR6_DATA_F1_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_390__PI_MR6_DATA_F1_2_WIDTH                        17U
#define LPDDR4__PI_MR6_DATA_F1_2__REG DENALI_PI_390
#define LPDDR4__PI_MR6_DATA_F1_2__FLD LPDDR4__DENALI_PI_390__PI_MR6_DATA_F1_2

#define LPDDR4__DENALI_PI_390__PI_MR11_DATA_F1_2_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_390__PI_MR11_DATA_F1_2_SHIFT                       24U
#define LPDDR4__DENALI_PI_390__PI_MR11_DATA_F1_2_WIDTH                        8U
#define LPDDR4__PI_MR11_DATA_F1_2__REG DENALI_PI_390
#define LPDDR4__PI_MR11_DATA_F1_2__FLD LPDDR4__DENALI_PI_390__PI_MR11_DATA_F1_2

#define LPDDR4__DENALI_PI_391_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_391_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_391__PI_MR12_DATA_F1_2_MASK                0x000000FFU
#define LPDDR4__DENALI_PI_391__PI_MR12_DATA_F1_2_SHIFT                        0U
#define LPDDR4__DENALI_PI_391__PI_MR12_DATA_F1_2_WIDTH                        8U
#define LPDDR4__PI_MR12_DATA_F1_2__REG DENALI_PI_391
#define LPDDR4__PI_MR12_DATA_F1_2__FLD LPDDR4__DENALI_PI_391__PI_MR12_DATA_F1_2

#define LPDDR4__DENALI_PI_391__PI_MR14_DATA_F1_2_MASK                0x0000FF00U
#define LPDDR4__DENALI_PI_391__PI_MR14_DATA_F1_2_SHIFT                        8U
#define LPDDR4__DENALI_PI_391__PI_MR14_DATA_F1_2_WIDTH                        8U
#define LPDDR4__PI_MR14_DATA_F1_2__REG DENALI_PI_391
#define LPDDR4__PI_MR14_DATA_F1_2__FLD LPDDR4__DENALI_PI_391__PI_MR14_DATA_F1_2

#define LPDDR4__DENALI_PI_391__PI_MR22_DATA_F1_2_MASK                0x00FF0000U
#define LPDDR4__DENALI_PI_391__PI_MR22_DATA_F1_2_SHIFT                       16U
#define LPDDR4__DENALI_PI_391__PI_MR22_DATA_F1_2_WIDTH                        8U
#define LPDDR4__PI_MR22_DATA_F1_2__REG DENALI_PI_391
#define LPDDR4__PI_MR22_DATA_F1_2__FLD LPDDR4__DENALI_PI_391__PI_MR22_DATA_F1_2

#define LPDDR4__DENALI_PI_391__PI_MR23_DATA_F1_2_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_391__PI_MR23_DATA_F1_2_SHIFT                       24U
#define LPDDR4__DENALI_PI_391__PI_MR23_DATA_F1_2_WIDTH                        8U
#define LPDDR4__PI_MR23_DATA_F1_2__REG DENALI_PI_391
#define LPDDR4__PI_MR23_DATA_F1_2__FLD LPDDR4__DENALI_PI_391__PI_MR23_DATA_F1_2

#define LPDDR4__DENALI_PI_392_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_392_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_392__PI_MR0_DATA_F2_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_392__PI_MR0_DATA_F2_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_392__PI_MR0_DATA_F2_2_WIDTH                        17U
#define LPDDR4__PI_MR0_DATA_F2_2__REG DENALI_PI_392
#define LPDDR4__PI_MR0_DATA_F2_2__FLD LPDDR4__DENALI_PI_392__PI_MR0_DATA_F2_2

#define LPDDR4__DENALI_PI_393_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_393_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_393__PI_MR1_DATA_F2_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_393__PI_MR1_DATA_F2_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_393__PI_MR1_DATA_F2_2_WIDTH                        17U
#define LPDDR4__PI_MR1_DATA_F2_2__REG DENALI_PI_393
#define LPDDR4__PI_MR1_DATA_F2_2__FLD LPDDR4__DENALI_PI_393__PI_MR1_DATA_F2_2

#define LPDDR4__DENALI_PI_394_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_394_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_394__PI_MR2_DATA_F2_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_394__PI_MR2_DATA_F2_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_394__PI_MR2_DATA_F2_2_WIDTH                        17U
#define LPDDR4__PI_MR2_DATA_F2_2__REG DENALI_PI_394
#define LPDDR4__PI_MR2_DATA_F2_2__FLD LPDDR4__DENALI_PI_394__PI_MR2_DATA_F2_2

#define LPDDR4__DENALI_PI_395_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_395_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_395__PI_MR3_DATA_F2_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_395__PI_MR3_DATA_F2_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_395__PI_MR3_DATA_F2_2_WIDTH                        17U
#define LPDDR4__PI_MR3_DATA_F2_2__REG DENALI_PI_395
#define LPDDR4__PI_MR3_DATA_F2_2__FLD LPDDR4__DENALI_PI_395__PI_MR3_DATA_F2_2

#define LPDDR4__DENALI_PI_396_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_396_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_396__PI_MR4_DATA_F2_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_396__PI_MR4_DATA_F2_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_396__PI_MR4_DATA_F2_2_WIDTH                        17U
#define LPDDR4__PI_MR4_DATA_F2_2__REG DENALI_PI_396
#define LPDDR4__PI_MR4_DATA_F2_2__FLD LPDDR4__DENALI_PI_396__PI_MR4_DATA_F2_2

#define LPDDR4__DENALI_PI_397_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_397_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_397__PI_MR5_DATA_F2_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_397__PI_MR5_DATA_F2_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_397__PI_MR5_DATA_F2_2_WIDTH                        17U
#define LPDDR4__PI_MR5_DATA_F2_2__REG DENALI_PI_397
#define LPDDR4__PI_MR5_DATA_F2_2__FLD LPDDR4__DENALI_PI_397__PI_MR5_DATA_F2_2

#define LPDDR4__DENALI_PI_398_READ_MASK                              0xFF01FFFFU
#define LPDDR4__DENALI_PI_398_WRITE_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_PI_398__PI_MR6_DATA_F2_2_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_398__PI_MR6_DATA_F2_2_SHIFT                         0U
#define LPDDR4__DENALI_PI_398__PI_MR6_DATA_F2_2_WIDTH                        17U
#define LPDDR4__PI_MR6_DATA_F2_2__REG DENALI_PI_398
#define LPDDR4__PI_MR6_DATA_F2_2__FLD LPDDR4__DENALI_PI_398__PI_MR6_DATA_F2_2

#define LPDDR4__DENALI_PI_398__PI_MR11_DATA_F2_2_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_398__PI_MR11_DATA_F2_2_SHIFT                       24U
#define LPDDR4__DENALI_PI_398__PI_MR11_DATA_F2_2_WIDTH                        8U
#define LPDDR4__PI_MR11_DATA_F2_2__REG DENALI_PI_398
#define LPDDR4__PI_MR11_DATA_F2_2__FLD LPDDR4__DENALI_PI_398__PI_MR11_DATA_F2_2

#define LPDDR4__DENALI_PI_399_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_399_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_399__PI_MR12_DATA_F2_2_MASK                0x000000FFU
#define LPDDR4__DENALI_PI_399__PI_MR12_DATA_F2_2_SHIFT                        0U
#define LPDDR4__DENALI_PI_399__PI_MR12_DATA_F2_2_WIDTH                        8U
#define LPDDR4__PI_MR12_DATA_F2_2__REG DENALI_PI_399
#define LPDDR4__PI_MR12_DATA_F2_2__FLD LPDDR4__DENALI_PI_399__PI_MR12_DATA_F2_2

#define LPDDR4__DENALI_PI_399__PI_MR14_DATA_F2_2_MASK                0x0000FF00U
#define LPDDR4__DENALI_PI_399__PI_MR14_DATA_F2_2_SHIFT                        8U
#define LPDDR4__DENALI_PI_399__PI_MR14_DATA_F2_2_WIDTH                        8U
#define LPDDR4__PI_MR14_DATA_F2_2__REG DENALI_PI_399
#define LPDDR4__PI_MR14_DATA_F2_2__FLD LPDDR4__DENALI_PI_399__PI_MR14_DATA_F2_2

#define LPDDR4__DENALI_PI_399__PI_MR22_DATA_F2_2_MASK                0x00FF0000U
#define LPDDR4__DENALI_PI_399__PI_MR22_DATA_F2_2_SHIFT                       16U
#define LPDDR4__DENALI_PI_399__PI_MR22_DATA_F2_2_WIDTH                        8U
#define LPDDR4__PI_MR22_DATA_F2_2__REG DENALI_PI_399
#define LPDDR4__PI_MR22_DATA_F2_2__FLD LPDDR4__DENALI_PI_399__PI_MR22_DATA_F2_2

#define LPDDR4__DENALI_PI_399__PI_MR23_DATA_F2_2_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_399__PI_MR23_DATA_F2_2_SHIFT                       24U
#define LPDDR4__DENALI_PI_399__PI_MR23_DATA_F2_2_WIDTH                        8U
#define LPDDR4__PI_MR23_DATA_F2_2__REG DENALI_PI_399
#define LPDDR4__PI_MR23_DATA_F2_2__FLD LPDDR4__DENALI_PI_399__PI_MR23_DATA_F2_2

#define LPDDR4__DENALI_PI_400_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_400_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_400__PI_MR0_DATA_F0_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_400__PI_MR0_DATA_F0_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_400__PI_MR0_DATA_F0_3_WIDTH                        17U
#define LPDDR4__PI_MR0_DATA_F0_3__REG DENALI_PI_400
#define LPDDR4__PI_MR0_DATA_F0_3__FLD LPDDR4__DENALI_PI_400__PI_MR0_DATA_F0_3

#define LPDDR4__DENALI_PI_401_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_401_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_401__PI_MR1_DATA_F0_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_401__PI_MR1_DATA_F0_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_401__PI_MR1_DATA_F0_3_WIDTH                        17U
#define LPDDR4__PI_MR1_DATA_F0_3__REG DENALI_PI_401
#define LPDDR4__PI_MR1_DATA_F0_3__FLD LPDDR4__DENALI_PI_401__PI_MR1_DATA_F0_3

#define LPDDR4__DENALI_PI_402_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_402_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_402__PI_MR2_DATA_F0_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_402__PI_MR2_DATA_F0_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_402__PI_MR2_DATA_F0_3_WIDTH                        17U
#define LPDDR4__PI_MR2_DATA_F0_3__REG DENALI_PI_402
#define LPDDR4__PI_MR2_DATA_F0_3__FLD LPDDR4__DENALI_PI_402__PI_MR2_DATA_F0_3

#define LPDDR4__DENALI_PI_403_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_403_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_403__PI_MR3_DATA_F0_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_403__PI_MR3_DATA_F0_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_403__PI_MR3_DATA_F0_3_WIDTH                        17U
#define LPDDR4__PI_MR3_DATA_F0_3__REG DENALI_PI_403
#define LPDDR4__PI_MR3_DATA_F0_3__FLD LPDDR4__DENALI_PI_403__PI_MR3_DATA_F0_3

#define LPDDR4__DENALI_PI_404_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_404_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_404__PI_MR4_DATA_F0_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_404__PI_MR4_DATA_F0_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_404__PI_MR4_DATA_F0_3_WIDTH                        17U
#define LPDDR4__PI_MR4_DATA_F0_3__REG DENALI_PI_404
#define LPDDR4__PI_MR4_DATA_F0_3__FLD LPDDR4__DENALI_PI_404__PI_MR4_DATA_F0_3

#define LPDDR4__DENALI_PI_405_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_405_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_405__PI_MR5_DATA_F0_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_405__PI_MR5_DATA_F0_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_405__PI_MR5_DATA_F0_3_WIDTH                        17U
#define LPDDR4__PI_MR5_DATA_F0_3__REG DENALI_PI_405
#define LPDDR4__PI_MR5_DATA_F0_3__FLD LPDDR4__DENALI_PI_405__PI_MR5_DATA_F0_3

#define LPDDR4__DENALI_PI_406_READ_MASK                              0xFF01FFFFU
#define LPDDR4__DENALI_PI_406_WRITE_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_PI_406__PI_MR6_DATA_F0_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_406__PI_MR6_DATA_F0_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_406__PI_MR6_DATA_F0_3_WIDTH                        17U
#define LPDDR4__PI_MR6_DATA_F0_3__REG DENALI_PI_406
#define LPDDR4__PI_MR6_DATA_F0_3__FLD LPDDR4__DENALI_PI_406__PI_MR6_DATA_F0_3

#define LPDDR4__DENALI_PI_406__PI_MR11_DATA_F0_3_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_406__PI_MR11_DATA_F0_3_SHIFT                       24U
#define LPDDR4__DENALI_PI_406__PI_MR11_DATA_F0_3_WIDTH                        8U
#define LPDDR4__PI_MR11_DATA_F0_3__REG DENALI_PI_406
#define LPDDR4__PI_MR11_DATA_F0_3__FLD LPDDR4__DENALI_PI_406__PI_MR11_DATA_F0_3

#define LPDDR4__DENALI_PI_407_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_407_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_407__PI_MR12_DATA_F0_3_MASK                0x000000FFU
#define LPDDR4__DENALI_PI_407__PI_MR12_DATA_F0_3_SHIFT                        0U
#define LPDDR4__DENALI_PI_407__PI_MR12_DATA_F0_3_WIDTH                        8U
#define LPDDR4__PI_MR12_DATA_F0_3__REG DENALI_PI_407
#define LPDDR4__PI_MR12_DATA_F0_3__FLD LPDDR4__DENALI_PI_407__PI_MR12_DATA_F0_3

#define LPDDR4__DENALI_PI_407__PI_MR14_DATA_F0_3_MASK                0x0000FF00U
#define LPDDR4__DENALI_PI_407__PI_MR14_DATA_F0_3_SHIFT                        8U
#define LPDDR4__DENALI_PI_407__PI_MR14_DATA_F0_3_WIDTH                        8U
#define LPDDR4__PI_MR14_DATA_F0_3__REG DENALI_PI_407
#define LPDDR4__PI_MR14_DATA_F0_3__FLD LPDDR4__DENALI_PI_407__PI_MR14_DATA_F0_3

#define LPDDR4__DENALI_PI_407__PI_MR22_DATA_F0_3_MASK                0x00FF0000U
#define LPDDR4__DENALI_PI_407__PI_MR22_DATA_F0_3_SHIFT                       16U
#define LPDDR4__DENALI_PI_407__PI_MR22_DATA_F0_3_WIDTH                        8U
#define LPDDR4__PI_MR22_DATA_F0_3__REG DENALI_PI_407
#define LPDDR4__PI_MR22_DATA_F0_3__FLD LPDDR4__DENALI_PI_407__PI_MR22_DATA_F0_3

#define LPDDR4__DENALI_PI_407__PI_MR23_DATA_F0_3_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_407__PI_MR23_DATA_F0_3_SHIFT                       24U
#define LPDDR4__DENALI_PI_407__PI_MR23_DATA_F0_3_WIDTH                        8U
#define LPDDR4__PI_MR23_DATA_F0_3__REG DENALI_PI_407
#define LPDDR4__PI_MR23_DATA_F0_3__FLD LPDDR4__DENALI_PI_407__PI_MR23_DATA_F0_3

#define LPDDR4__DENALI_PI_408_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_408_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_408__PI_MR0_DATA_F1_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_408__PI_MR0_DATA_F1_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_408__PI_MR0_DATA_F1_3_WIDTH                        17U
#define LPDDR4__PI_MR0_DATA_F1_3__REG DENALI_PI_408
#define LPDDR4__PI_MR0_DATA_F1_3__FLD LPDDR4__DENALI_PI_408__PI_MR0_DATA_F1_3

#define LPDDR4__DENALI_PI_409_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_409_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_409__PI_MR1_DATA_F1_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_409__PI_MR1_DATA_F1_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_409__PI_MR1_DATA_F1_3_WIDTH                        17U
#define LPDDR4__PI_MR1_DATA_F1_3__REG DENALI_PI_409
#define LPDDR4__PI_MR1_DATA_F1_3__FLD LPDDR4__DENALI_PI_409__PI_MR1_DATA_F1_3

#define LPDDR4__DENALI_PI_410_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_410_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_410__PI_MR2_DATA_F1_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_410__PI_MR2_DATA_F1_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_410__PI_MR2_DATA_F1_3_WIDTH                        17U
#define LPDDR4__PI_MR2_DATA_F1_3__REG DENALI_PI_410
#define LPDDR4__PI_MR2_DATA_F1_3__FLD LPDDR4__DENALI_PI_410__PI_MR2_DATA_F1_3

#define LPDDR4__DENALI_PI_411_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_411_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_411__PI_MR3_DATA_F1_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_411__PI_MR3_DATA_F1_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_411__PI_MR3_DATA_F1_3_WIDTH                        17U
#define LPDDR4__PI_MR3_DATA_F1_3__REG DENALI_PI_411
#define LPDDR4__PI_MR3_DATA_F1_3__FLD LPDDR4__DENALI_PI_411__PI_MR3_DATA_F1_3

#define LPDDR4__DENALI_PI_412_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_412_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_412__PI_MR4_DATA_F1_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_412__PI_MR4_DATA_F1_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_412__PI_MR4_DATA_F1_3_WIDTH                        17U
#define LPDDR4__PI_MR4_DATA_F1_3__REG DENALI_PI_412
#define LPDDR4__PI_MR4_DATA_F1_3__FLD LPDDR4__DENALI_PI_412__PI_MR4_DATA_F1_3

#define LPDDR4__DENALI_PI_413_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_413_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_413__PI_MR5_DATA_F1_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_413__PI_MR5_DATA_F1_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_413__PI_MR5_DATA_F1_3_WIDTH                        17U
#define LPDDR4__PI_MR5_DATA_F1_3__REG DENALI_PI_413
#define LPDDR4__PI_MR5_DATA_F1_3__FLD LPDDR4__DENALI_PI_413__PI_MR5_DATA_F1_3

#define LPDDR4__DENALI_PI_414_READ_MASK                              0xFF01FFFFU
#define LPDDR4__DENALI_PI_414_WRITE_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_PI_414__PI_MR6_DATA_F1_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_414__PI_MR6_DATA_F1_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_414__PI_MR6_DATA_F1_3_WIDTH                        17U
#define LPDDR4__PI_MR6_DATA_F1_3__REG DENALI_PI_414
#define LPDDR4__PI_MR6_DATA_F1_3__FLD LPDDR4__DENALI_PI_414__PI_MR6_DATA_F1_3

#define LPDDR4__DENALI_PI_414__PI_MR11_DATA_F1_3_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_414__PI_MR11_DATA_F1_3_SHIFT                       24U
#define LPDDR4__DENALI_PI_414__PI_MR11_DATA_F1_3_WIDTH                        8U
#define LPDDR4__PI_MR11_DATA_F1_3__REG DENALI_PI_414
#define LPDDR4__PI_MR11_DATA_F1_3__FLD LPDDR4__DENALI_PI_414__PI_MR11_DATA_F1_3

#define LPDDR4__DENALI_PI_415_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_415_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_415__PI_MR12_DATA_F1_3_MASK                0x000000FFU
#define LPDDR4__DENALI_PI_415__PI_MR12_DATA_F1_3_SHIFT                        0U
#define LPDDR4__DENALI_PI_415__PI_MR12_DATA_F1_3_WIDTH                        8U
#define LPDDR4__PI_MR12_DATA_F1_3__REG DENALI_PI_415
#define LPDDR4__PI_MR12_DATA_F1_3__FLD LPDDR4__DENALI_PI_415__PI_MR12_DATA_F1_3

#define LPDDR4__DENALI_PI_415__PI_MR14_DATA_F1_3_MASK                0x0000FF00U
#define LPDDR4__DENALI_PI_415__PI_MR14_DATA_F1_3_SHIFT                        8U
#define LPDDR4__DENALI_PI_415__PI_MR14_DATA_F1_3_WIDTH                        8U
#define LPDDR4__PI_MR14_DATA_F1_3__REG DENALI_PI_415
#define LPDDR4__PI_MR14_DATA_F1_3__FLD LPDDR4__DENALI_PI_415__PI_MR14_DATA_F1_3

#define LPDDR4__DENALI_PI_415__PI_MR22_DATA_F1_3_MASK                0x00FF0000U
#define LPDDR4__DENALI_PI_415__PI_MR22_DATA_F1_3_SHIFT                       16U
#define LPDDR4__DENALI_PI_415__PI_MR22_DATA_F1_3_WIDTH                        8U
#define LPDDR4__PI_MR22_DATA_F1_3__REG DENALI_PI_415
#define LPDDR4__PI_MR22_DATA_F1_3__FLD LPDDR4__DENALI_PI_415__PI_MR22_DATA_F1_3

#define LPDDR4__DENALI_PI_415__PI_MR23_DATA_F1_3_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_415__PI_MR23_DATA_F1_3_SHIFT                       24U
#define LPDDR4__DENALI_PI_415__PI_MR23_DATA_F1_3_WIDTH                        8U
#define LPDDR4__PI_MR23_DATA_F1_3__REG DENALI_PI_415
#define LPDDR4__PI_MR23_DATA_F1_3__FLD LPDDR4__DENALI_PI_415__PI_MR23_DATA_F1_3

#define LPDDR4__DENALI_PI_416_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_416_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_416__PI_MR0_DATA_F2_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_416__PI_MR0_DATA_F2_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_416__PI_MR0_DATA_F2_3_WIDTH                        17U
#define LPDDR4__PI_MR0_DATA_F2_3__REG DENALI_PI_416
#define LPDDR4__PI_MR0_DATA_F2_3__FLD LPDDR4__DENALI_PI_416__PI_MR0_DATA_F2_3

#define LPDDR4__DENALI_PI_417_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_417_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_417__PI_MR1_DATA_F2_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_417__PI_MR1_DATA_F2_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_417__PI_MR1_DATA_F2_3_WIDTH                        17U
#define LPDDR4__PI_MR1_DATA_F2_3__REG DENALI_PI_417
#define LPDDR4__PI_MR1_DATA_F2_3__FLD LPDDR4__DENALI_PI_417__PI_MR1_DATA_F2_3

#define LPDDR4__DENALI_PI_418_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_418_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_418__PI_MR2_DATA_F2_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_418__PI_MR2_DATA_F2_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_418__PI_MR2_DATA_F2_3_WIDTH                        17U
#define LPDDR4__PI_MR2_DATA_F2_3__REG DENALI_PI_418
#define LPDDR4__PI_MR2_DATA_F2_3__FLD LPDDR4__DENALI_PI_418__PI_MR2_DATA_F2_3

#define LPDDR4__DENALI_PI_419_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_419_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_419__PI_MR3_DATA_F2_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_419__PI_MR3_DATA_F2_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_419__PI_MR3_DATA_F2_3_WIDTH                        17U
#define LPDDR4__PI_MR3_DATA_F2_3__REG DENALI_PI_419
#define LPDDR4__PI_MR3_DATA_F2_3__FLD LPDDR4__DENALI_PI_419__PI_MR3_DATA_F2_3

#define LPDDR4__DENALI_PI_420_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_420_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_420__PI_MR4_DATA_F2_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_420__PI_MR4_DATA_F2_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_420__PI_MR4_DATA_F2_3_WIDTH                        17U
#define LPDDR4__PI_MR4_DATA_F2_3__REG DENALI_PI_420
#define LPDDR4__PI_MR4_DATA_F2_3__FLD LPDDR4__DENALI_PI_420__PI_MR4_DATA_F2_3

#define LPDDR4__DENALI_PI_421_READ_MASK                              0x0001FFFFU
#define LPDDR4__DENALI_PI_421_WRITE_MASK                             0x0001FFFFU
#define LPDDR4__DENALI_PI_421__PI_MR5_DATA_F2_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_421__PI_MR5_DATA_F2_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_421__PI_MR5_DATA_F2_3_WIDTH                        17U
#define LPDDR4__PI_MR5_DATA_F2_3__REG DENALI_PI_421
#define LPDDR4__PI_MR5_DATA_F2_3__FLD LPDDR4__DENALI_PI_421__PI_MR5_DATA_F2_3

#define LPDDR4__DENALI_PI_422_READ_MASK                              0xFF01FFFFU
#define LPDDR4__DENALI_PI_422_WRITE_MASK                             0xFF01FFFFU
#define LPDDR4__DENALI_PI_422__PI_MR6_DATA_F2_3_MASK                 0x0001FFFFU
#define LPDDR4__DENALI_PI_422__PI_MR6_DATA_F2_3_SHIFT                         0U
#define LPDDR4__DENALI_PI_422__PI_MR6_DATA_F2_3_WIDTH                        17U
#define LPDDR4__PI_MR6_DATA_F2_3__REG DENALI_PI_422
#define LPDDR4__PI_MR6_DATA_F2_3__FLD LPDDR4__DENALI_PI_422__PI_MR6_DATA_F2_3

#define LPDDR4__DENALI_PI_422__PI_MR11_DATA_F2_3_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_422__PI_MR11_DATA_F2_3_SHIFT                       24U
#define LPDDR4__DENALI_PI_422__PI_MR11_DATA_F2_3_WIDTH                        8U
#define LPDDR4__PI_MR11_DATA_F2_3__REG DENALI_PI_422
#define LPDDR4__PI_MR11_DATA_F2_3__FLD LPDDR4__DENALI_PI_422__PI_MR11_DATA_F2_3

#define LPDDR4__DENALI_PI_423_READ_MASK                              0xFFFFFFFFU
#define LPDDR4__DENALI_PI_423_WRITE_MASK                             0xFFFFFFFFU
#define LPDDR4__DENALI_PI_423__PI_MR12_DATA_F2_3_MASK                0x000000FFU
#define LPDDR4__DENALI_PI_423__PI_MR12_DATA_F2_3_SHIFT                        0U
#define LPDDR4__DENALI_PI_423__PI_MR12_DATA_F2_3_WIDTH                        8U
#define LPDDR4__PI_MR12_DATA_F2_3__REG DENALI_PI_423
#define LPDDR4__PI_MR12_DATA_F2_3__FLD LPDDR4__DENALI_PI_423__PI_MR12_DATA_F2_3

#define LPDDR4__DENALI_PI_423__PI_MR14_DATA_F2_3_MASK                0x0000FF00U
#define LPDDR4__DENALI_PI_423__PI_MR14_DATA_F2_3_SHIFT                        8U
#define LPDDR4__DENALI_PI_423__PI_MR14_DATA_F2_3_WIDTH                        8U
#define LPDDR4__PI_MR14_DATA_F2_3__REG DENALI_PI_423
#define LPDDR4__PI_MR14_DATA_F2_3__FLD LPDDR4__DENALI_PI_423__PI_MR14_DATA_F2_3

#define LPDDR4__DENALI_PI_423__PI_MR22_DATA_F2_3_MASK                0x00FF0000U
#define LPDDR4__DENALI_PI_423__PI_MR22_DATA_F2_3_SHIFT                       16U
#define LPDDR4__DENALI_PI_423__PI_MR22_DATA_F2_3_WIDTH                        8U
#define LPDDR4__PI_MR22_DATA_F2_3__REG DENALI_PI_423
#define LPDDR4__PI_MR22_DATA_F2_3__FLD LPDDR4__DENALI_PI_423__PI_MR22_DATA_F2_3

#define LPDDR4__DENALI_PI_423__PI_MR23_DATA_F2_3_MASK                0xFF000000U
#define LPDDR4__DENALI_PI_423__PI_MR23_DATA_F2_3_SHIFT                       24U
#define LPDDR4__DENALI_PI_423__PI_MR23_DATA_F2_3_WIDTH                        8U
#define LPDDR4__PI_MR23_DATA_F2_3__REG DENALI_PI_423
#define LPDDR4__PI_MR23_DATA_F2_3__FLD LPDDR4__DENALI_PI_423__PI_MR23_DATA_F2_3

#endif /* REG_LPDDR4_PI_MACROS_H_ */

/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c99-2 "C99 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-5_4_b_c90-2 "C90 - similar names" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c99-2 "C99 - limits" */
/* parasoft-end-suppress MISRA2012-RULE-1_1_b_c90-2 "C90 - limits" */

